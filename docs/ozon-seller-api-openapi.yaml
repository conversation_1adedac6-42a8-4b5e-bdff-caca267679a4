openapi: 3.0.3
info:
  title: Ozon Seller API
  description: |
    Ozon Seller API — программный интерфейс для работы с маркетплейсом Ozon. 
    Он даёт возможность обмениваться информацией между системой продавца и Ozon.
    
    Методы Seller API позволяют изменять данные магазина, например, остатки товаров 
    или их стоимость, и получать данные, такие как информация о возвратах или список складов.
    
    ## Возможности Seller API:
    - Загружать и обновлять товары
    - Управлять ценами и остатками по товарам
    - Получать информацию о возвратах товаров
    - Управлять заказами FBO, FBS и rFBS
    - Управлять чатами
    - Работать с накладными
    - Получать финансовую и аналитическую информацию
    - Получать выгрузку атрибутов и характеристик Ozon
    
    ## Авторизация
    Для работы с Seller API необходим API-ключ. API-ключи хранятся в личном кабинете в скрытом виде.
    
    ## Ограничения
    - Максимум 50 запросов в секунду на все методы с одного Client ID
    - Seller API работает по UTC
    
  version: "2.1"
  contact:
    name: Ozon Seller Support
    url: https://seller.ozon.ru
  externalDocs:
    description: Ozon for dev - Информационная платформа и сообщество разработчиков
    url: https://dev.ozon.ru/

servers:
  - url: https://api-seller.ozon.ru
    description: Рабочая среда

security:
  - ApiKeyAuth: []
  - ClientIdAuth: []

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Api-Key
      description: API ключ для авторизации
    ClientIdAuth:
      type: apiKey
      in: header
      name: Client-Id
      description: Идентификатор клиента

  schemas:
    Error:
      type: object
      properties:
        code:
          type: integer
          description: Код ошибки
        message:
          type: string
          description: Описание ошибки
        details:
          type: array
          items:
            type: object
            properties:
              typeUrl:
                type: string
              value:
                type: string

    CategoryTreeRequest:
      type: object
      properties:
        language:
          type: string
          enum: [DEFAULT, RU, EN, TR, ZH_HANS]
          default: DEFAULT
          description: Язык ответа

    CategoryTreeResponse:
      type: object
      properties:
        result:
          type: array
          items:
            $ref: '#/components/schemas/Category'

    Category:
      type: object
      properties:
        category_id:
          type: integer
          format: int64
          description: Идентификатор категории
        title:
          type: string
          description: Название категории
        disabled:
          type: boolean
          description: Признак неактивной категории
        children:
          type: array
          items:
            $ref: '#/components/schemas/Category'

    ProductImportRequest:
      type: object
      properties:
        items:
          type: array
          maxItems: 100
          items:
            $ref: '#/components/schemas/ProductImportItem'

    ProductImportItem:
      type: object
      required:
        - offer_id
        - name
        - category_id
      properties:
        offer_id:
          type: string
          maxLength: 50
          description: Идентификатор товара в системе продавца
        name:
          type: string
          description: Название товара
        category_id:
          type: integer
          format: int64
          description: Идентификатор категории
        price:
          type: string
          description: Цена товара
        old_price:
          type: string
          description: Цена до скидки
        premium_price:
          type: string
          description: Цена для Premium покупателей
        vat:
          type: string
          description: Ставка НДС
        height:
          type: integer
          description: Высота упаковки в мм
        depth:
          type: integer
          description: Глубина упаковки в мм
        width:
          type: integer
          description: Ширина упаковки в мм
        dimension_unit:
          type: string
          enum: [mm, cm, in]
          description: Единица измерения размеров
        weight:
          type: integer
          description: Вес товара в граммах
        weight_unit:
          type: string
          enum: [g, kg, lb]
          description: Единица измерения веса
        images:
          type: array
          items:
            type: string
            format: uri
          description: Ссылки на изображения товара
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductAttribute'

    ProductAttribute:
      type: object
      properties:
        attribute_id:
          type: integer
          format: int64
          description: Идентификатор характеристики
        complex_id:
          type: integer
          format: int64
          description: Идентификатор характеристики в комплексе
        values:
          type: array
          items:
            $ref: '#/components/schemas/AttributeValue'

    AttributeValue:
      type: object
      properties:
        dictionary_value_id:
          type: integer
          format: int64
          description: Идентификатор значения из справочника
        value:
          type: string
          description: Значение характеристики

    ProductImportResponse:
      type: object
      properties:
        result:
          type: object
          properties:
            task_id:
              type: integer
              format: int64
              description: Идентификатор задания на загрузку товаров

    ProductStocksRequest:
      type: object
      properties:
        stocks:
          type: array
          maxItems: 100
          items:
            $ref: '#/components/schemas/ProductStock'

    ProductStock:
      type: object
      required:
        - offer_id
        - stock
      properties:
        offer_id:
          type: string
          description: Идентификатор товара в системе продавца
        product_id:
          type: integer
          format: int64
          description: Идентификатор товара в системе Ozon
        stock:
          type: integer
          description: Количество товара на складе
        warehouse_id:
          type: integer
          format: int64
          description: Идентификатор склада

    ProductStocksResponse:
      type: object
      properties:
        result:
          type: array
          items:
            $ref: '#/components/schemas/ProductStockResult'

    ProductStockResult:
      type: object
      properties:
        offer_id:
          type: string
          description: Идентификатор товара в системе продавца
        product_id:
          type: integer
          format: int64
          description: Идентификатор товара в системе Ozon
        updated:
          type: boolean
          description: Признак успешного обновления
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'

paths:
  /v1/description-category/tree:
    post:
      tags:
        - Атрибуты и характеристики Ozon
      summary: Дерево категорий и типов товаров
      description: |
        Получите список категорий и типов в виде дерева и используйте значение 
        последнего уровня выбранной категории.
      operationId: DescriptionCategoryAPI_GetTree
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CategoryTreeRequest'
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategoryTreeResponse'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/description-category/attribute/values/search:
    post:
      tags:
        - Атрибуты и характеристики Ozon
      summary: Поиск по справочным значениям характеристики
      description: |
        Выполняет поиск по справочным значениям характеристики товара.
        Позволяет найти подходящие значения для заполнения характеристик товара.
      operationId: DescriptionCategoryAPI_SearchAttributeValues
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                attribute_id:
                  type: integer
                  format: int64
                  description: Идентификатор характеристики
                category_id:
                  type: integer
                  format: int64
                  description: Идентификатор категории
                type_id:
                  type: integer
                  format: int64
                  description: Идентификатор типа товара
                search_value:
                  type: string
                  maxLength: 255
                  description: Поисковый запрос для поиска значений
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество значений в ответе
                last_value_id:
                  type: integer
                  format: int64
                  description: Идентификатор последнего значения из предыдущего запроса для пагинации
              required:
                - attribute_id
                - category_id
                - type_id
                - search_value
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          description: Идентификатор значения характеристики
                        value:
                          type: string
                          description: Значение характеристики
                        info:
                          type: string
                          description: Дополнительная информация о значении
                        picture:
                          type: string
                          format: uri
                          description: Ссылка на изображение значения
                  has_next:
                    type: boolean
                    description: Признак наличия следующей страницы результатов
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v3/product/import:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Загрузить товары
      description: |
        Загрузите товары и услуги. Этот метод также позволяет обновить уже загруженные товары. 
        В запросе устанавливается первичная цена и загружаются изображения товара.
        
        В одном запросе можно передать до 100 товаров. Изображения загружаются прямой ссылкой 
        на облачное хранилище, где они хранятся.
      operationId: ProductAPI_ImportProductsV3
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductImportRequest'
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductImportResponse'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/products/stocks:
    post:
      tags:
        - Цены и остатки товаров
      summary: Обновить остатки товаров
      description: |
        Позволяет одним запросом обновить информацию об остатках сразу 100 товаров.
      operationId: ProductAPI_ProductsStocksV2
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductStocksRequest'
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductStocksResponse'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/description-category/attribute:
    post:
      tags:
        - Атрибуты и характеристики Ozon
      summary: Список характеристик категории
      description: |
        Получите характеристики для выбранных категории и типа.
      operationId: DescriptionCategoryAPI_GetAttributes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                category_id:
                  type: integer
                  format: int64
                  description: Идентификатор категории
                type_id:
                  type: integer
                  format: int64
                  description: Идентификатор типа
                language:
                  type: string
                  enum: [DEFAULT, RU, EN, TR, ZH_HANS]
                  default: DEFAULT
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        attribute_id:
                          type: integer
                          format: int64
                        name:
                          type: string
                        description:
                          type: string
                        type:
                          type: string
                        is_collection:
                          type: boolean
                        is_required:
                          type: boolean
                        category_dependent:
                          type: boolean
                        is_aspect:
                          type: boolean

  /v1/description-category/attribute/values:
    post:
      tags:
        - Атрибуты и характеристики Ozon
      summary: Справочник значений характеристики
      description: |
        Получите список значений для выбранной характеристики.
      operationId: DescriptionCategoryAPI_GetAttributeValues
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                attribute_id:
                  type: integer
                  format: int64
                  description: Идентификатор характеристики
                category_id:
                  type: integer
                  format: int64
                  description: Идентификатор категории
                type_id:
                  type: integer
                  format: int64
                  description: Идентификатор типа
                language:
                  type: string
                  enum: [DEFAULT, RU, EN, TR, ZH_HANS]
                  default: DEFAULT
                last_value_id:
                  type: integer
                  format: int64
                  description: Идентификатор последнего значения
                limit:
                  type: integer
                  minimum: 1
                  maximum: 5000
                  default: 5000
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                        value:
                          type: string
                        info:
                          type: string
                        picture:
                          type: string

  /v1/product/import/info:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Статус загрузки товаров
      description: |
        Проверьте task_id, который вы получили при загрузке товаров.
        Метод вернёт информацию, успешно ли загрузились товары или при импорте была ошибка.
      operationId: ProductAPI_GetImportProductsInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: integer
                  format: int64
                  description: Идентификатор задания на загрузку товаров
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            offer_id:
                              type: string
                            product_id:
                              type: integer
                              format: int64
                            status:
                              type: string
                              enum: [imported, failed, processing]
                            errors:
                              type: array
                              items:
                                $ref: '#/components/schemas/Error'

  /v3/product/list:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Список товаров
      description: |
        Получите список созданных товаров после загрузки товаров.
        Метод позволяет использовать фильтры, чтобы разбить товары на группы по статусу видимости.
      operationId: ProductAPI_GetProductList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    offer_id:
                      type: array
                      items:
                        type: string
                    product_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                    visibility:
                      type: string
                      enum: [ALL, VISIBLE, INVISIBLE, EMPTY_STOCK, NOT_MODERATED, MODERATED, DISABLED, STATE_FAILED, READY_TO_SUPPLY, VALIDATION_STATE_PENDING, VALIDATION_STATE_FAIL, VALIDATION_STATE_SUCCESS, TO_SUPPLY, IN_SALE, REMOVED_FROM_SALE, BANNED, OVERPRICED, CRITICALLY_OVERPRICED, EMPTY_BARCODE, BARCODE_EXISTS, QUARANTINE, ARCHIVED, OVERPRICED_WITH_STOCK, PARTIAL_APPROVED, IMAGE_ABSENT, MODERATION_BLOCK]
                last_id:
                  type: string
                  description: Идентификатор последнего товара
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            product_id:
                              type: integer
                              format: int64
                            offer_id:
                              type: string
                      total:
                        type: integer
                      last_id:
                        type: string

  /v1/product/import/prices:
    post:
      tags:
        - Цены и остатки товаров
      summary: Обновить цены товаров
      description: |
        Позволяет обновить цены товаров.
      operationId: ProductAPI_ImportProductsPrices
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                prices:
                  type: array
                  maxItems: 1000
                  items:
                    type: object
                    properties:
                      offer_id:
                        type: string
                        description: Идентификатор товара в системе продавца
                      product_id:
                        type: integer
                        format: int64
                        description: Идентификатор товара в системе Ozon
                      price:
                        type: string
                        description: Цена товара
                      old_price:
                        type: string
                        description: Цена до скидки
                      premium_price:
                        type: string
                        description: Цена для Premium покупателей
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        offer_id:
                          type: string
                        product_id:
                          type: integer
                          format: int64
                        updated:
                          type: boolean
                        errors:
                          type: array
                          items:
                            $ref: '#/components/schemas/Error'

  /v3/posting/fbs/list:
    post:
      tags:
        - Обработка заказов FBS и rFBS
      summary: Список отправлений FBS
      description: |
        Получить список отправлений FBS.
      operationId: PostingAPI_GetFbsPostingListV3
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                dir:
                  type: string
                  enum: [ASC, DESC]
                  default: ASC
                filter:
                  type: object
                  properties:
                    since:
                      type: string
                      format: date-time
                      description: Начальная дата
                    to:
                      type: string
                      format: date-time
                      description: Конечная дата
                    status:
                      type: string
                      enum: [awaiting_packaging, awaiting_deliver, arbitration, delivering, delivered, cancelled]
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                with:
                  type: object
                  properties:
                    analytics_data:
                      type: boolean
                    financial_data:
                      type: boolean
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      postings:
                        type: array
                        items:
                          type: object
                          properties:
                            posting_number:
                              type: string
                            order_id:
                              type: integer
                              format: int64
                            order_number:
                              type: string
                            status:
                              type: string
                            substatus:
                              type: string
                            created_at:
                              type: string
                              format: date-time
                            in_process_at:
                              type: string
                              format: date-time
                            shipment_date:
                              type: string
                              format: date-time
                            delivering_date:
                              type: string
                              format: date-time
                            is_multibox:
                              type: boolean
                            multi_box_qty:
                              type: integer
                            products:
                              type: array
                              items:
                                type: object
                                properties:
                                  sku:
                                    type: integer
                                    format: int64
                                  name:
                                    type: string
                                  offer_id:
                                    type: string
                                  price:
                                    type: string
                                  quantity:
                                    type: integer

  /v3/posting/fbs/get:
    post:
      tags:
        - Обработка заказов FBS и rFBS
      summary: Получить отправление FBS
      description: |
        Получить информацию об отправлении FBS.
      operationId: PostingAPI_GetFbsPostingV3
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                posting_number:
                  type: string
                  description: Номер отправления
                with:
                  type: object
                  properties:
                    analytics_data:
                      type: boolean
                    financial_data:
                      type: boolean
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      posting_number:
                        type: string
                      order_id:
                        type: integer
                        format: int64
                      order_number:
                        type: string
                      status:
                        type: string
                      substatus:
                        type: string
                      created_at:
                        type: string
                        format: date-time
                      is_multibox:
                        type: boolean
                      multi_box_qty:
                        type: integer

  /v3/finance/transaction/list:
    post:
      tags:
        - Финансовые отчёты
      summary: Список финансовых операций
      description: |
        Получить список финансовых операций. Максимальный период, за который можно
        получить информацию в одном запросе — 1 месяц.
      operationId: FinanceAPI_FinanceTransactionListV3
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    date:
                      type: object
                      properties:
                        from:
                          type: string
                          format: date-time
                        to:
                          type: string
                          format: date-time
                    operation_type:
                      type: array
                      items:
                        type: string
                        enum: [OperationAgentDeliveredToCustomer, OperationAgentDeliveredToCustomerCanceled, OperationClientReturnAgentOperation, OperationItemReturn, OperationMarketplaceRedistributionOfAcquiringOperation, OperationMarketplaceServiceItemDirectFlowLogistic, OperationMarketplaceServiceItemDropoffPVZ, OperationMarketplaceServiceItemDropoffSC, OperationMarketplaceServiceItemDropoffFF, OperationMarketplaceServiceItemFulfillment, OperationMarketplaceServiceItemReturnFlowLogistic, OperationMarketplaceServiceItemReturnNotFulfillment, OperationMarketplaceServiceItemReturnPartGoodsCustomer, OperationMarketplaceServiceItemReturnAfterDeliveredToCustomer, OperationMarketplaceServicePremiumCashbackIndividualPoints, OperationTypeCorrection, OperationMarketplaceServiceStoragePartiallyDeliveredOrder, OperationMarketplaceServiceStorageFulfillment, OperationMarketplaceRedistributionOfAcquiringOperation, OperationReturnGoodsFBSofRMS]
                    posting_number:
                      type: string
                    transaction_type:
                      type: string
                      enum: [all, orders, returns, services, compensation, transferDelivery, other]
                page:
                  type: integer
                  minimum: 1
                  default: 1
                page_size:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      operations:
                        type: array
                        items:
                          type: object
                          properties:
                            operation_id:
                              type: integer
                              format: int64
                            operation_type:
                              type: string
                            operation_date:
                              type: string
                              format: date-time
                            operation_type_name:
                              type: string
                            delivery_charge:
                              type: number
                            return_delivery_charge:
                              type: number
                            accruals_for_sale:
                              type: number
                            sale_commission:
                              type: number
                            amount:
                              type: number
                            type:
                              type: string
                            posting:
                              type: object
                              properties:
                                posting_number:
                                  type: string
                                order_date:
                                  type: string
                                  format: date-time
                            items:
                              type: array
                              items:
                                type: object
                                properties:
                                  sku:
                                    type: integer
                                    format: int64
                                  name:
                                    type: string

  /v1/analytics/data:
    post:
      tags:
        - Аналитические отчёты
      summary: Данные аналитики
      description: |
        Получить данные аналитики по товарам.
      operationId: AnalyticsAPI_GetAnalyticsData
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date_from:
                  type: string
                  format: date
                  description: Дата начала периода
                date_to:
                  type: string
                  format: date
                  description: Дата окончания периода
                metrics:
                  type: array
                  items:
                    type: string
                    enum: [hits_view_search, hits_view_pdp, hits_tocart_search, hits_tocart_pdp, session_view_search, session_view_pdp, session_tocart_search, session_tocart_pdp, conv_tocart_search, conv_tocart_pdp, revenue, ordered_units, returns, cancellations]
                  description: Список метрик для получения
                dimension:
                  type: array
                  items:
                    type: string
                    enum: [sku, spu, day, week, month]
                  description: Измерения для группировки данных
                filters:
                  type: object
                  properties:
                    sku:
                      type: array
                      items:
                        type: integer
                        format: int64
                      description: Список SKU товаров
                    offer_id:
                      type: array
                      items:
                        type: string
                      description: Список артикулов товаров
                sort:
                  type: array
                  items:
                    type: object
                    properties:
                      key:
                        type: string
                        description: Поле для сортировки
                      order:
                        type: string
                        enum: [ASC, DESC]
                        description: Направление сортировки
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество записей в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Смещение для пагинации
              required:
                - date_from
                - date_to
                - metrics
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            dimensions:
                              type: object
                              description: Значения измерений
                            metrics:
                              type: object
                              description: Значения метрик
                      totals:
                        type: object
                        description: Итоговые значения метрик
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/analytics/stock_on_warehouses:
    post:
      tags:
        - Аналитические отчёты
      summary: Отчёт по товарам и остаткам
      description: |
        Получить отчёт по товарам и остаткам.
      operationId: AnalyticsAPI_AnalyticsGetStockOnWarehousesV2
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                warehouse_type:
                  type: string
                  enum: [ALL, fbo, fbs, express]
                  default: ALL
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      rows:
                        type: array
                        items:
                          type: object
                          properties:
                            sku:
                              type: integer
                              format: int64
                            offer_id:
                              type: string
                            product_name:
                              type: string
                            free_to_sell_amount:
                              type: integer
                            promised_amount:
                              type: integer
                            reserved_amount:
                              type: integer
                            warehouse_id:
                              type: integer
                              format: int64
                            warehouse_name:
                              type: string
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/analytics/turnover/stocks:
    post:
      tags:
        - Аналитические отчёты
      summary: Оборачиваемость товара
      description: |
        Получить данные об оборачиваемости товаров.
      operationId: AnalyticsAPI_GetTurnoverStocks
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date_from:
                  type: string
                  format: date
                  description: Дата начала периода
                date_to:
                  type: string
                  format: date
                  description: Дата окончания периода
                warehouse_type:
                  type: string
                  enum: [ALL, fbo, fbs, express]
                  default: ALL
                  description: Тип склада
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество записей в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Смещение для пагинации
              required:
                - date_from
                - date_to
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            sku:
                              type: integer
                              format: int64
                              description: SKU товара
                            offer_id:
                              type: string
                              description: Артикул товара
                            product_name:
                              type: string
                              description: Название товара
                            average_stock:
                              type: number
                              format: float
                              description: Средний остаток
                            sold_units:
                              type: integer
                              description: Продано единиц
                            turnover_days:
                              type: number
                              format: float
                              description: Оборачиваемость в днях
                            warehouse_type:
                              type: string
                              description: Тип склада
                      total:
                        type: integer
                        description: Общее количество записей
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/analytics/product-queries:
    post:
      tags:
        - Аналитические отчёты
      summary: Получить информацию о запросах моих товаров
      description: |
        Получить информацию о поисковых запросах, по которым находят ваши товары.
      operationId: AnalyticsAPI_GetProductQueries
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date_from:
                  type: string
                  format: date
                  description: Дата начала периода
                date_to:
                  type: string
                  format: date
                  description: Дата окончания периода
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество записей в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Смещение для пагинации
              required:
                - date_from
                - date_to
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            query:
                              type: string
                              description: Поисковый запрос
                            hits:
                              type: integer
                              description: Количество показов
                            clicks:
                              type: integer
                              description: Количество кликов
                            ctr:
                              type: number
                              format: float
                              description: CTR (Click Through Rate)
                            products_count:
                              type: integer
                              description: Количество товаров по запросу
                      total:
                        type: integer
                        description: Общее количество записей
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/analytics/product-queries/details:
    post:
      tags:
        - Аналитические отчёты
      summary: Получить детализацию запросов по товару
      description: |
        Получить детальную информацию о поисковых запросах для конкретного товара.
      operationId: AnalyticsAPI_GetProductQueriesDetails
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date_from:
                  type: string
                  format: date
                  description: Дата начала периода
                date_to:
                  type: string
                  format: date
                  description: Дата окончания периода
                sku:
                  type: integer
                  format: int64
                  description: SKU товара
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество записей в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Смещение для пагинации
              required:
                - date_from
                - date_to
                - sku
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          type: object
                          properties:
                            query:
                              type: string
                              description: Поисковый запрос
                            position:
                              type: integer
                              description: Позиция товара в поиске
                            hits:
                              type: integer
                              description: Количество показов
                            clicks:
                              type: integer
                              description: Количество кликов
                            ctr:
                              type: number
                              format: float
                              description: CTR (Click Through Rate)
                            orders:
                              type: integer
                              description: Количество заказов
                            conversion:
                              type: number
                              format: float
                              description: Конверсия в заказ
                      total:
                        type: integer
                        description: Общее количество записей
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/returns/company/fbs:
    post:
      tags:
        - Возвраты товаров FBO и FBS
      summary: Список возвратов FBS
      description: |
        Получить информацию о возвратах FBS.
      operationId: ReturnsAPI_GetReturnsCompanyFBS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    accepted_from_customer_moment:
                      type: object
                      properties:
                        time_from:
                          type: string
                          format: date-time
                        time_to:
                          type: string
                          format: date-time
                    last_free_waiting_day:
                      type: object
                      properties:
                        time_from:
                          type: string
                          format: date-time
                        time_to:
                          type: string
                          format: date-time
                    product_name:
                      type: string
                    product_offer_id:
                      type: string
                    status:
                      type: array
                      items:
                        type: string
                        enum: [returned_to_seller, created, awaiting_return_confirmation, returned_to_ozon, cancelled, client_arbitrage, moving, disposed, disposing]
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                last_id:
                  type: integer
                  format: int64
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  returns:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                        posting_number:
                          type: string
                        order_date:
                          type: string
                          format: date-time
                        status:
                          type: string
                        product_id:
                          type: integer
                          format: int64
                        product_name:
                          type: string
                        offer_id:
                          type: string
                        quantity:
                          type: integer
                        returned_to_seller_date_time:
                          type: string
                          format: date-time

  /v1/supply-order/list:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Список заявок на поставку
      description: |
        Получить список заявок на поставку на склад Ozon.
      operationId: SupplyOrderAPI_GetSupplyOrdersList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                dir:
                  type: string
                  enum: [ASC, DESC]
                  default: ASC
                filter:
                  type: object
                  properties:
                    since:
                      type: string
                      format: date-time
                    to:
                      type: string
                      format: date-time
                    states:
                      type: array
                      items:
                        type: string
                        enum: [new, confirmed, on_delivery, delivered, cancelled]
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                offset:
                  type: integer
                  minimum: 0
                  default: 0
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                        number:
                          type: string
                        created_at:
                          type: string
                          format: date-time
                        updated_at:
                          type: string
                          format: date-time
                        state:
                          type: string
                        warehouse_id:
                          type: integer
                          format: int64
                        warehouse_name:
                          type: string

  /v1/supply-order/get:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Информация о заявке на поставку
      description: |
        Получить информацию о заявке на поставку.
      operationId: SupplyOrderAPI_GetSupplyOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      id:
                        type: integer
                        format: int64
                      number:
                        type: string
                      created_at:
                        type: string
                        format: date-time
                      updated_at:
                        type: string
                        format: date-time
                      state:
                        type: string
                      warehouse_id:
                        type: integer
                        format: int64
                      warehouse_name:
                        type: string

  /v1/product/info/subscription:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Количество подписчиков на товары
      description: |
        Получить количество пользователей, подписанных на товары.
      operationId: ProductAPI_GetProductInfoSubscription
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                sku:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: Список SKU товаров
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        sku:
                          type: integer
                          format: int64
                        subscription_count:
                          type: integer
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/rating-by-sku:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Получить контент-рейтинг товаров по SKU
      description: |
        Получает контент-рейтинг товаров по их SKU.
        Контент-рейтинг показывает качество заполнения карточки товара.
      operationId: ProductAPI_GetProductRatingBySku
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                skus:
                  type: array
                  items:
                    type: integer
                    format: int64
                  maxItems: 100
                  description: Список SKU товаров для получения рейтинга
              required:
                - skus
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        sku:
                          type: integer
                          format: int64
                          description: SKU товара
                        rating:
                          type: number
                          format: float
                          minimum: 0
                          maximum: 5
                          description: Контент-рейтинг товара (от 0 до 5)
                        rating_details:
                          type: object
                          properties:
                            images_count:
                              type: integer
                              description: Количество изображений
                            description_quality:
                              type: number
                              format: float
                              description: Качество описания
                            attributes_filled:
                              type: integer
                              description: Количество заполненных атрибутов
                            total_attributes:
                              type: integer
                              description: Общее количество атрибутов
                        recommendations:
                          type: array
                          items:
                            type: string
                          description: Рекомендации по улучшению рейтинга
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v4/product/info/limit:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Лимиты на ассортимент
      description: |
        Получить лимиты на ассортимент, создание и обновление товаров.
      operationId: ProductAPI_GetUploadQuota
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      daily_create:
                        type: object
                        properties:
                          limit:
                            type: integer
                          reset_at:
                            type: string
                            format: date-time
                          usage:
                            type: integer
                      daily_update:
                        type: object
                        properties:
                          limit:
                            type: integer
                          reset_at:
                            type: string
                            format: date-time
                          usage:
                            type: integer
                      total:
                        type: object
                        properties:
                          limit:
                            type: integer
                          usage:
                            type: integer
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/related-sku/get:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Получить связанные SKU
      description: |
        Получает список связанных SKU для указанных товаров.
        Связанные SKU - это товары, которые объединены в одну карточку.
      operationId: ProductAPI_GetRelatedSku
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                skus:
                  type: array
                  items:
                    type: integer
                    format: int64
                  maxItems: 100
                  description: Список SKU товаров для поиска связанных
              required:
                - skus
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        sku:
                          type: integer
                          format: int64
                          description: Исходный SKU товара
                        related_skus:
                          type: array
                          items:
                            type: object
                            properties:
                              sku:
                                type: integer
                                format: int64
                                description: Связанный SKU
                              offer_id:
                                type: string
                                description: Артикул связанного товара
                              name:
                                type: string
                                description: Название связанного товара
                          description: Список связанных SKU
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/product/pictures/info:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Получить изображения товаров
      description: |
        Получает информацию об изображениях товаров по их SKU или артикулам.
      operationId: ProductAPI_GetProductPicturesInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    offer_id:
                      type: array
                      items:
                        type: string
                      maxItems: 100
                      description: Список артикулов товаров
                    product_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                      maxItems: 100
                      description: Список идентификаторов товаров
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество товаров в ответе
                last_id:
                  type: string
                  description: Идентификатор последнего товара из предыдущего запроса
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            product_id:
                              type: integer
                              format: int64
                              description: Идентификатор товара
                            offer_id:
                              type: string
                              description: Артикул товара
                            pictures:
                              type: array
                              items:
                                type: object
                                properties:
                                  file_name:
                                    type: string
                                    description: Имя файла изображения
                                  default:
                                    type: boolean
                                    description: Признак основного изображения
                                  state:
                                    type: string
                                    enum: [uploaded, processing, processed, failed]
                                    description: Статус обработки изображения
                                  url:
                                    type: string
                                    format: uri
                                    description: URL изображения
                              description: Список изображений товара
                      last_id:
                        type: string
                        description: Идентификатор последнего товара для пагинации
                      total:
                        type: integer
                        description: Общее количество товаров
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/barcode/add:
    post:
      tags:
        - Штрихкоды товаров
      summary: Привязать штрихкод к товару
      description: |
        Привязывает штрихкод к товару. Используйте этот метод для добавления
        существующего штрихкода к товару.
      operationId: BarcodeAPI_AddBarcode
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                barcode:
                  type: string
                  description: Штрихкод товара
                offer_id:
                  type: string
                  description: Идентификатор товара в системе продавца
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      success:
                        type: boolean
                        description: Признак успешного выполнения операции
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/barcode/generate:
    post:
      tags:
        - Штрихкоды товаров
      summary: Создать штрихкод для товара
      description: |
        Создает новый штрихкод для товара. Используйте этот метод для генерации
        уникального штрихкода для товара.
      operationId: BarcodeAPI_GenerateBarcode
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                offer_id:
                  type: string
                  description: Идентификатор товара в системе продавца
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      barcode:
                        type: string
                        description: Сгенерированный штрихкод
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/update/discount:
    post:
      tags:
        - Цены и остатки товаров
      summary: Установить скидку на уценённые товары FBS
      description: |
        Устанавливает скидку на уценённые товары, продающиеся по схеме FBS.
      operationId: ProductAPI_ProductUpdateDiscount
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                discount_percent:
                  type: number
                  minimum: 0
                  maximum: 100
                  description: Процент скидки
                offer_id:
                  type: string
                  description: Идентификатор товара в системе продавца
                product_id:
                  type: integer
                  format: int64
                  description: Идентификатор товара в системе Ozon
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      updated:
                        type: boolean
                        description: Признак успешного обновления
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v3/posting/multiboxqty/set:
    post:
      tags:
        - Обработка заказов FBS и rFBS
      summary: Передать количество коробок в многокоробочных отправлениях
      description: |
        Передает количество коробок в многокоробочных отправлениях.
      operationId: PostingAPI_PostingMultiBoxQtySetV3
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                posting_number:
                  type: string
                  description: Номер отправления
                multi_box_qty:
                  type: integer
                  minimum: 1
                  description: Количество коробок
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      success:
                        type: boolean
                        description: Признак успешного выполнения операции
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/posting/fbs/timeslot/set:
    post:
      tags:
        - Доставка FBS
      summary: Перенести дату доставки отправления
      description: |
        Переносит дату доставки отправления.
      operationId: PostingAPI_SetPostingTimeslot
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                posting_number:
                  type: string
                  description: Номер отправления
                timeslot:
                  type: object
                  properties:
                    from:
                      type: string
                      format: date-time
                      description: Начало временного слота
                    to:
                      type: string
                      format: date-time
                      description: Конец временного слота
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      success:
                        type: boolean
                        description: Признак успешного выполнения операции
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/posting/fbs/timeslot/change-restrictions:
    post:
      tags:
        - Доставка FBS
      summary: Получить доступные даты для переноса доставки
      description: |
        Получает доступные даты для переноса доставки и количество доступных переносов.
      operationId: PostingAPI_PostingTimeslotChangeRestrictions
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                posting_number:
                  type: string
                  description: Номер отправления
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      available_dates:
                        type: array
                        items:
                          type: string
                          format: date
                        description: Доступные даты для переноса
                      changes_left:
                        type: integer
                        description: Количество доступных переносов
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/posting/fbs/package-label/create:
    post:
      tags:
        - Доставка FBS
      summary: Создать задание на формирование этикеток
      description: |
        Создает задание на асинхронное формирование этикеток для отправлений.
      operationId: PostingAPI_CreateLabelBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                posting_numbers:
                  type: array
                  items:
                    type: string
                  description: Номера отправлений
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: string
                        description: Идентификатор задания
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/posting/fbs/package-label/get:
    post:
      tags:
        - Доставка FBS
      summary: Получить результат формирования этикеток
      description: |
        Получает результат асинхронного формирования этикеток.
      operationId: PostingAPI_GetLabelBatch
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: string
                  description: Идентификатор задания
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      status:
                        type: string
                        enum: [processing, completed, failed]
                        description: Статус задания
                      file_url:
                        type: string
                        format: uri
                        description: Ссылка на файл с этикетками
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/posting/global/etgb:
    post:
      tags:
        - Доставка FBO
      summary: Получить таможенные декларации ETGB
      description: |
        Получает таможенные декларации ETGB для международных отправлений.
      operationId: PostingAPI_GetEtgb
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                posting_number:
                  type: string
                  description: Номер отправления
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      etgb_number:
                        type: string
                        description: Номер таможенной декларации ETGB
                      status:
                        type: string
                        description: Статус декларации
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/items:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Получить список товаров в заявке на поставку
      description: |
        Получает список товаров в заявке на поставку.
      operationId: SupplyOrderAPI_GetSupplyOrderItems
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        sku:
                          type: integer
                          format: int64
                          description: SKU товара
                        offer_id:
                          type: string
                          description: Идентификатор товара в системе продавца
                        name:
                          type: string
                          description: Название товара
                        quantity:
                          type: integer
                          description: Количество товара
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v3/returns/company/fbs:
    post:
      tags:
        - Возвраты товаров FBO и FBS
      summary: Получить информацию о возвратах FBS (v3)
      description: |
        Получает информацию о возвратах FBS. Новая версия метода с расширенной функциональностью.
      operationId: ReturnsAPI_GetReturnsCompanyFBSV3
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    accepted_from_customer_moment:
                      type: object
                      properties:
                        time_from:
                          type: string
                          format: date-time
                        time_to:
                          type: string
                          format: date-time
                    last_free_waiting_day:
                      type: object
                      properties:
                        time_from:
                          type: string
                          format: date-time
                        time_to:
                          type: string
                          format: date-time
                    product_name:
                      type: string
                    product_offer_id:
                      type: string
                    status:
                      type: array
                      items:
                        type: string
                        enum: [returned_to_seller, created, awaiting_return_confirmation, returned_to_ozon, cancelled, client_arbitrage, moving, disposed, disposing]
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                last_id:
                  type: integer
                  format: int64
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  returns:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                        posting_number:
                          type: string
                        order_date:
                          type: string
                          format: date-time
                        status:
                          type: string
                        product_id:
                          type: integer
                          format: int64
                        product_name:
                          type: string
                        offer_id:
                          type: string
                        quantity:
                          type: integer
                        returned_to_seller_date_time:
                          type: string
                          format: date-time
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/import-by-sku:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Обновить товары по SKU
      description: |
        Обновляет товары по их SKU. Позволяет изменить характеристики уже загруженных товаров.
      operationId: ProductAPI_ImportProductsBySKU
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                items:
                  type: array
                  maxItems: 100
                  items:
                    type: object
                    properties:
                      sku:
                        type: integer
                        format: int64
                        description: SKU товара в системе Ozon
                      name:
                        type: string
                        description: Название товара
                      price:
                        type: string
                        description: Цена товара
                      old_price:
                        type: string
                        description: Цена до скидки
                      premium_price:
                        type: string
                        description: Цена для Premium покупателей
                      attributes:
                        type: array
                        items:
                          $ref: '#/components/schemas/ProductAttribute'
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductImportResponse'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/update/offer-id:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Изменить идентификатор товара
      description: |
        Изменяет идентификатор товара в системе продавца (offer_id).
      operationId: ProductAPI_ProductUpdateOfferID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                offer_id:
                  type: string
                  maxLength: 50
                  description: Текущий идентификатор товара в системе продавца
                new_offer_id:
                  type: string
                  maxLength: 50
                  description: Новый идентификатор товара в системе продавца
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      updated:
                        type: boolean
                        description: Признак успешного обновления
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/product/info:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Получить информацию о товаре
      description: |
        Получает подробную информацию о товаре по его идентификатору.
      operationId: ProductAPI_GetProductInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                offer_id:
                  type: string
                  description: Идентификатор товара в системе продавца
                product_id:
                  type: integer
                  format: int64
                  description: Идентификатор товара в системе Ozon
                sku:
                  type: integer
                  format: int64
                  description: SKU товара
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      id:
                        type: integer
                        format: int64
                        description: Идентификатор товара
                      name:
                        type: string
                        description: Название товара
                      offer_id:
                        type: string
                        description: Идентификатор товара в системе продавца
                      barcode:
                        type: string
                        description: Штрихкод товара
                      barcodes:
                        type: array
                        items:
                          type: string
                        description: Все штрихкоды товара
                      buybox_price:
                        type: string
                        description: Цена в buybox
                      category_id:
                        type: integer
                        format: int64
                        description: Идентификатор категории
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания товара
                      updated_at:
                        type: string
                        format: date-time
                        description: Дата последнего обновления товара
                      images:
                        type: array
                        items:
                          type: string
                          format: uri
                        description: Изображения товара
                      marketing_price:
                        type: string
                        description: Маркетинговая цена
                      min_price:
                        type: string
                        description: Минимальная цена
                      old_price:
                        type: string
                        description: Цена до скидки
                      premium_price:
                        type: string
                        description: Цена для Premium покупателей
                      price:
                        type: string
                        description: Цена товара
                      recommended_price:
                        type: string
                        description: Рекомендованная цена
                      min_ozon_price:
                        type: string
                        description: Минимальная цена на Ozon
                      vat:
                        type: string
                        description: Ставка НДС
                      visible:
                        type: boolean
                        description: Видимость товара
                      visibility_details:
                        type: object
                        properties:
                          has_price:
                            type: boolean
                          has_stock:
                            type: boolean
                          active_product:
                            type: boolean
                      price_indexes:
                        type: object
                        description: Индексы цен
                      commissions:
                        type: array
                        items:
                          type: object
                          properties:
                            sale_schema:
                              type: string
                            percent:
                              type: number
                            min_value:
                              type: number
                      volume_weight:
                        type: number
                        description: Объемный вес
                      is_prepayment:
                        type: boolean
                        description: Предоплата
                      is_prepayment_allowed:
                        type: boolean
                        description: Разрешена ли предоплата
                      images360:
                        type: array
                        items:
                          type: string
                          format: uri
                        description: 360-градусные изображения
                      color_image:
                        type: string
                        format: uri
                        description: Цветное изображение
                      primary_image:
                        type: string
                        format: uri
                        description: Основное изображение
                      state:
                        type: string
                        description: Состояние товара
                      state_failed_moderation_reasons:
                        type: array
                        items:
                          type: string
                        description: Причины отклонения модерации
                      state_description:
                        type: string
                        description: Описание состояния
                      state_name:
                        type: string
                        description: Название состояния
                      state_tooltip:
                        type: string
                        description: Подсказка по состоянию
                      errors:
                        type: array
                        items:
                          $ref: '#/components/schemas/Error'
                        description: Ошибки товара
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/info/list:
    post:
      tags:
        - Загрузка и обновление товаров
      summary: Получить список товаров с информацией
      description: |
        Получает список товаров с подробной информацией.
      operationId: ProductAPI_GetProductInfoListV2
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    offer_id:
                      type: array
                      items:
                        type: string
                    product_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                    visibility:
                      type: string
                      enum: [ALL, VISIBLE, INVISIBLE, EMPTY_STOCK, NOT_MODERATED, MODERATED, DISABLED, STATE_FAILED, READY_TO_SUPPLY, VALIDATION_STATE_PENDING, VALIDATION_STATE_FAIL, VALIDATION_STATE_SUCCESS, TO_SUPPLY, IN_SALE, REMOVED_FROM_SALE, BANNED, OVERPRICED, CRITICALLY_OVERPRICED, EMPTY_BARCODE, BARCODE_EXISTS, QUARANTINE, ARCHIVED, OVERPRICED_WITH_STOCK, PARTIAL_APPROVED, IMAGE_ABSENT, MODERATION_BLOCK]
                last_id:
                  type: string
                  description: Идентификатор последнего товара
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              format: int64
                            name:
                              type: string
                            offer_id:
                              type: string
                            barcode:
                              type: string
                            barcodes:
                              type: array
                              items:
                                type: string
                            buybox_price:
                              type: string
                            category_id:
                              type: integer
                              format: int64
                            created_at:
                              type: string
                              format: date-time
                            updated_at:
                              type: string
                              format: date-time
                            images:
                              type: array
                              items:
                                type: string
                                format: uri
                            marketing_price:
                              type: string
                            min_price:
                              type: string
                            old_price:
                              type: string
                            premium_price:
                              type: string
                            price:
                              type: string
                            recommended_price:
                              type: string
                            min_ozon_price:
                              type: string
                            vat:
                              type: string
                            visible:
                              type: boolean
                            visibility_details:
                              type: object
                              properties:
                                has_price:
                                  type: boolean
                                has_stock:
                                  type: boolean
                                active_product:
                                  type: boolean
                            price_indexes:
                              type: object
                            commissions:
                              type: array
                              items:
                                type: object
                                properties:
                                  sale_schema:
                                    type: string
                                  percent:
                                    type: number
                                  min_value:
                                    type: number
                            volume_weight:
                              type: number
                            is_prepayment:
                              type: boolean
                            is_prepayment_allowed:
                              type: boolean
                            images360:
                              type: array
                              items:
                                type: string
                                format: uri
                            color_image:
                              type: string
                              format: uri
                            primary_image:
                              type: string
                              format: uri
                            state:
                              type: string
                            state_failed_moderation_reasons:
                              type: array
                              items:
                                type: string
                            state_description:
                              type: string
                            state_name:
                              type: string
                            state_tooltip:
                              type: string
                            errors:
                              type: array
                              items:
                                $ref: '#/components/schemas/Error'
                      total:
                        type: integer
                      last_id:
                        type: string
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v4/product/info/prices:
    post:
      tags:
        - Цены и остатки товаров
      summary: Получить информацию о ценах товаров
      description: |
        Получает информацию о ценах товаров.
      operationId: ProductAPI_GetProductInfoPrices
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    offer_id:
                      type: array
                      items:
                        type: string
                    product_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                    visibility:
                      type: string
                      enum: [ALL, VISIBLE, INVISIBLE]
                last_id:
                  type: string
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            product_id:
                              type: integer
                              format: int64
                            offer_id:
                              type: string
                            price:
                              type: object
                              properties:
                                price:
                                  type: string
                                old_price:
                                  type: string
                                premium_price:
                                  type: string
                                recommended_price:
                                  type: string
                                min_price:
                                  type: string
                                buybox_price:
                                  type: string
                                marketing_price:
                                  type: string
                                min_ozon_price:
                                  type: string
                            commissions:
                              type: array
                              items:
                                type: object
                                properties:
                                  sale_schema:
                                    type: string
                                  percent:
                                    type: number
                                  min_value:
                                    type: number
                            marketing_actions:
                              type: array
                              items:
                                type: object
                                properties:
                                  title:
                                    type: string
                                  action_id:
                                    type: integer
                                    format: int64
                            price_indexes:
                              type: object
                      total:
                        type: integer
                      last_id:
                        type: string
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v5/product/info/prices:
    post:
      tags:
        - Цены и остатки товаров
      summary: Получить информацию о цене товара (v5)
      description: |
        Получает информацию о цене товара. Новая версия метода с расширенной функциональностью.
      operationId: ProductAPI_GetProductInfoPricesV5
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    offer_id:
                      type: array
                      items:
                        type: string
                      maxItems: 1000
                      description: Список артикулов товаров
                    product_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                      maxItems: 1000
                      description: Список идентификаторов товаров
                    visibility:
                      type: string
                      enum: [ALL, VISIBLE, INVISIBLE]
                      default: ALL
                      description: Фильтр по видимости товаров
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество товаров в ответе
                last_id:
                  type: string
                  description: Идентификатор последнего товара из предыдущего запроса
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            product_id:
                              type: integer
                              format: int64
                              description: Идентификатор товара
                            offer_id:
                              type: string
                              description: Артикул товара
                            price:
                              type: object
                              properties:
                                price:
                                  type: string
                                  description: Цена товара
                                old_price:
                                  type: string
                                  description: Старая цена товара
                                premium_price:
                                  type: string
                                  description: Цена для Premium покупателей
                                recommended_price:
                                  type: string
                                  description: Рекомендованная цена
                                retail_price:
                                  type: string
                                  description: Розничная цена
                                vat:
                                  type: string
                                  description: НДС
                                min_ozon_price:
                                  type: string
                                  description: Минимальная цена на Ozon
                                marketing_price:
                                  type: string
                                  description: Цена с учетом акций
                                marketing_seller_price:
                                  type: string
                                  description: Цена продавца с учетом акций
                            commissions:
                              type: object
                              properties:
                                sales_percent:
                                  type: number
                                  description: Процент комиссии за продажу
                                fbo_fulfillment_amount:
                                  type: number
                                  description: Стоимость обработки FBO
                                fbo_direct_flow_trans_min_amount:
                                  type: number
                                  description: Минимальная стоимость обработки прямого потока FBO
                                fbo_direct_flow_trans_max_amount:
                                  type: number
                                  description: Максимальная стоимость обработки прямого потока FBO
                                fbo_deliv_to_customer_amount:
                                  type: number
                                  description: Стоимость доставки FBO до покупателя
                                fbo_return_flow_amount:
                                  type: number
                                  description: Стоимость обработки возврата FBO
                                fbo_return_flow_trans_min_amount:
                                  type: number
                                  description: Минимальная стоимость обработки возврата FBO
                                fbo_return_flow_trans_max_amount:
                                  type: number
                                  description: Максимальная стоимость обработки возврата FBO
                                fbs_first_mile_min_amount:
                                  type: number
                                  description: Минимальная стоимость первой мили FBS
                                fbs_first_mile_max_amount:
                                  type: number
                                  description: Максимальная стоимость первой мили FBS
                                fbs_direct_flow_trans_min_amount:
                                  type: number
                                  description: Минимальная стоимость обработки прямого потока FBS
                                fbs_direct_flow_trans_max_amount:
                                  type: number
                                  description: Максимальная стоимость обработки прямого потока FBS
                                fbs_deliv_to_customer_amount:
                                  type: number
                                  description: Стоимость доставки FBS до покупателя
                                fbs_return_flow_amount:
                                  type: number
                                  description: Стоимость обработки возврата FBS
                                fbs_return_flow_trans_min_amount:
                                  type: number
                                  description: Минимальная стоимость обработки возврата FBS
                                fbs_return_flow_trans_max_amount:
                                  type: number
                                  description: Максимальная стоимость обработки возврата FBS
                            volume_weight:
                              type: number
                              description: Объемный вес товара
                      total:
                        type: integer
                        description: Общее количество товаров
                      last_id:
                        type: string
                        description: Идентификатор последнего товара для пагинации
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v4/product/info/stocks:
    post:
      tags:
        - Цены и остатки товаров
      summary: Информация о количестве товаров
      description: |
        Получает информацию о количестве товаров на складах.
      operationId: ProductAPI_GetProductInfoStocks
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    offer_id:
                      type: array
                      items:
                        type: string
                      maxItems: 1000
                      description: Список артикулов товаров
                    product_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                      maxItems: 1000
                      description: Список идентификаторов товаров
                    visibility:
                      type: string
                      enum: [ALL, VISIBLE, INVISIBLE]
                      default: ALL
                      description: Фильтр по видимости товаров
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество товаров в ответе
                last_id:
                  type: string
                  description: Идентификатор последнего товара из предыдущего запроса
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            product_id:
                              type: integer
                              format: int64
                              description: Идентификатор товара
                            offer_id:
                              type: string
                              description: Артикул товара
                            stocks:
                              type: array
                              items:
                                type: object
                                properties:
                                  type:
                                    type: string
                                    enum: [fbo, fbs, crossborder]
                                    description: Тип склада
                                  present:
                                    type: integer
                                    description: Количество товара в наличии
                                  reserved:
                                    type: integer
                                    description: Зарезервированное количество товара
                              description: Остатки товара по типам складов
                      total:
                        type: integer
                        description: Общее количество товаров
                      last_id:
                        type: string
                        description: Идентификатор последнего товара для пагинации
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/info/stocks-by-warehouse/fbs:
    post:
      tags:
        - Цены и остатки товаров
      summary: Информация об остатках на складах продавца (FBS и rFBS)
      description: |
        Получает информацию об остатках товаров на складах продавца для схем FBS и rFBS.
      operationId: ProductAPI_GetProductInfoStocksByWarehouseFbs
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                fbs_sku:
                  type: array
                  items:
                    type: integer
                    format: int64
                  maxItems: 100
                  description: Список FBS SKU товаров
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        fbs_sku:
                          type: integer
                          format: int64
                          description: FBS SKU товара
                        product_id:
                          type: integer
                          format: int64
                          description: Идентификатор товара
                        offer_id:
                          type: string
                          description: Артикул товара
                        warehouses:
                          type: array
                          items:
                            type: object
                            properties:
                              warehouse_id:
                                type: integer
                                format: int64
                                description: Идентификатор склада
                              warehouse_name:
                                type: string
                                description: Название склада
                              present:
                                type: integer
                                description: Количество товара в наличии
                              reserved:
                                type: integer
                                description: Зарезервированное количество товара
                          description: Остатки по складам
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/info/discounted:
    post:
      tags:
        - Цены и остатки товаров
      summary: Узнать информацию об уценке и основном товаре по SKU уценённого товара
      description: |
        Получает информацию об уценке и основном товаре по SKU уценённого товара.
      operationId: ProductAPI_GetProductInfoDiscounted
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                discounted_skus:
                  type: array
                  items:
                    type: integer
                    format: int64
                  maxItems: 100
                  description: Список SKU уценённых товаров
              required:
                - discounted_skus
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        discounted_sku:
                          type: integer
                          format: int64
                          description: SKU уценённого товара
                        basic_product_sku:
                          type: integer
                          format: int64
                          description: SKU основного товара
                        basic_product_offer_id:
                          type: string
                          description: Артикул основного товара
                        discount_percent:
                          type: number
                          description: Процент скидки
                        discounted_price:
                          type: string
                          description: Цена уценённого товара
                        basic_price:
                          type: string
                          description: Цена основного товара
                        discount_reason:
                          type: string
                          description: Причина уценки
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v3/posting/fbs/unfulfilled/list:
    post:
      tags:
        - Обработка заказов FBS и rFBS
      summary: Получить список необработанных отправлений FBS
      description: |
        Получает список необработанных отправлений FBS.
      operationId: PostingAPI_GetFbsPostingUnfulfilledList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                dir:
                  type: string
                  enum: [ASC, DESC]
                  default: ASC
                filter:
                  type: object
                  properties:
                    cutoff_from:
                      type: string
                      format: date-time
                    cutoff_to:
                      type: string
                      format: date-time
                    delivering_date_from:
                      type: string
                      format: date-time
                    delivering_date_to:
                      type: string
                      format: date-time
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                offset:
                  type: integer
                  minimum: 0
                  default: 0
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      postings:
                        type: array
                        items:
                          type: object
                          properties:
                            posting_number:
                              type: string
                            order_id:
                              type: integer
                              format: int64
                            order_number:
                              type: string
                            status:
                              type: string
                            substatus:
                              type: string
                            created_at:
                              type: string
                              format: date-time
                            in_process_at:
                              type: string
                              format: date-time
                            shipment_date:
                              type: string
                              format: date-time
                            delivering_date:
                              type: string
                              format: date-time
                            cutoff:
                              type: string
                              format: date-time
                            is_multibox:
                              type: boolean
                            multi_box_qty:
                              type: integer
                            products:
                              type: array
                              items:
                                type: object
                                properties:
                                  sku:
                                    type: integer
                                    format: int64
                                  name:
                                    type: string
                                  offer_id:
                                    type: string
                                  price:
                                    type: string
                                  quantity:
                                    type: integer
                      has_next:
                        type: boolean
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/posting/fbs/act/list:
    post:
      tags:
        - Обработка заказов FBS и rFBS
      summary: Получить список актов FBS
      description: |
        Получает список актов приема-передачи для отправлений FBS.
      operationId: PostingAPI_FbsActList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    integration_type:
                      type: string
                      enum: [ozon_integration, 3pl_integration]
                    status:
                      type: string
                      enum: [formed, confirmed, not_accepted, acceptance_in_progress, awaiting_registration, arbitration, client_arbitration, delivered, not_delivered, cancelled]
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                offset:
                  type: integer
                  minimum: 0
                  default: 0
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                        number:
                          type: string
                        status:
                          type: string
                        created_at:
                          type: string
                          format: date-time
                        updated_at:
                          type: string
                          format: date-time
                        integration_type:
                          type: string
                        containers_count:
                          type: integer
                        has_postings_for_next_carriage:
                          type: boolean
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/posting/fbs/act/create:
    post:
      tags:
        - Обработка заказов FBS и rFBS
      summary: Создать акт приема-передачи FBS
      description: |
        Создает акт приема-передачи для отправлений FBS.
      operationId: PostingAPI_PostingFBSActCreate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                containers:
                  type: array
                  items:
                    type: object
                    properties:
                      container_number:
                        type: string
                        description: Номер контейнера
                delivery_method_id:
                  type: integer
                  format: int64
                  description: Идентификатор способа доставки
                departure_date:
                  type: string
                  format: date-time
                  description: Дата отправления
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      id:
                        type: integer
                        format: int64
                        description: Идентификатор созданного акта
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/posting/fbs/act/get-postings:
    post:
      tags:
        - Обработка заказов FBS и rFBS
      summary: Получить отправления в акте FBS
      description: |
        Получает список отправлений в акте приема-передачи FBS.
      operationId: PostingAPI_ActPostingList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  format: int64
                  description: Идентификатор акта
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        posting_number:
                          type: string
                        order_id:
                          type: integer
                          format: int64
                        order_number:
                          type: string
                        status:
                          type: string
                        multi_box_qty:
                          type: integer
                        products:
                          type: array
                          items:
                            type: object
                            properties:
                              sku:
                                type: integer
                                format: int64
                              name:
                                type: string
                              offer_id:
                                type: string
                              price:
                                type: string
                              quantity:
                                type: integer
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/actions:
    get:
      tags:
        - Акции
      summary: Список акций
      description: |
        Получает список доступных акций для продавца.
      operationId: ActionsAPI_GetActionsList
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          description: Идентификатор акции
                        title:
                          type: string
                          description: Название акции
                        description:
                          type: string
                          description: Описание акции
                        date_start:
                          type: string
                          format: date-time
                          description: Дата начала акции
                        date_end:
                          type: string
                          format: date-time
                          description: Дата окончания акции
                        is_participating:
                          type: boolean
                          description: Участвует ли продавец в акции
                        order_amount:
                          type: number
                          description: Минимальная сумма заказа
                        action_price:
                          type: number
                          description: Цена участия в акции
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/actions/candidates:
    post:
      tags:
        - Акции
      summary: Список доступных для акции товаров
      description: |
        Получает список товаров, которые могут участвовать в указанной акции.
      operationId: ActionsAPI_GetActionCandidates
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action_id:
                  type: integer
                  format: int64
                  description: Идентификатор акции
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество товаров в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество товаров, которое будет пропущено в ответе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      products:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              format: int64
                              description: Идентификатор товара
                            offer_id:
                              type: string
                              description: Идентификатор товара в системе продавца
                            name:
                              type: string
                              description: Название товара
                            price:
                              type: string
                              description: Цена товара
                            action_price:
                              type: string
                              description: Цена товара в акции
                            stock:
                              type: integer
                              description: Остаток товара
                      total:
                        type: integer
                        description: Общее количество товаров
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/actions/products:
    post:
      tags:
        - Акции
      summary: Список участвующих в акции товаров
      description: |
        Получает список товаров, которые участвуют в указанной акции.
      operationId: ActionsAPI_GetActionProducts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action_id:
                  type: integer
                  format: int64
                  description: Идентификатор акции
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество товаров в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество товаров, которое будет пропущено в ответе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      products:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              format: int64
                              description: Идентификатор товара
                            offer_id:
                              type: string
                              description: Идентификатор товара в системе продавца
                            name:
                              type: string
                              description: Название товара
                            price:
                              type: string
                              description: Цена товара
                            action_price:
                              type: string
                              description: Цена товара в акции
                            stock:
                              type: integer
                              description: Остаток товара
                            participating:
                              type: boolean
                              description: Участвует ли товар в акции
                      total:
                        type: integer
                        description: Общее количество товаров
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/actions/products/activate:
    post:
      tags:
        - Акции
      summary: Добавить товар в акцию
      description: |
        Добавляет товары в указанную акцию.
      operationId: ActionsAPI_ActivateActionProducts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action_id:
                  type: integer
                  format: int64
                  description: Идентификатор акции
                products:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        format: int64
                        description: Идентификатор товара
                      action_price:
                        type: string
                        description: Цена товара в акции
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        product_id:
                          type: integer
                          format: int64
                          description: Идентификатор товара
                        updated:
                          type: boolean
                          description: Признак успешного добавления в акцию
                        errors:
                          type: array
                          items:
                            $ref: '#/components/schemas/Error'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/actions/products/deactivate:
    post:
      tags:
        - Акции
      summary: Удалить товары из акции
      description: |
        Удаляет товары из указанной акции.
      operationId: ActionsAPI_DeactivateActionProducts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                action_id:
                  type: integer
                  format: int64
                  description: Идентификатор акции
                product_ids:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: Идентификаторы товаров для удаления из акции
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        product_id:
                          type: integer
                          format: int64
                          description: Идентификатор товара
                        updated:
                          type: boolean
                          description: Признак успешного удаления из акции
                        errors:
                          type: array
                          items:
                            $ref: '#/components/schemas/Error'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/actions/discounts-task/list:
    post:
      tags:
        - Акции
      summary: Список заявок на скидку
      description: |
        Получает список заявок на скидку.
      operationId: ActionsAPI_GetDiscountTasksList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество заявок в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество заявок, которое будет пропущено в ответе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      tasks:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              format: int64
                              description: Идентификатор заявки
                            product_id:
                              type: integer
                              format: int64
                              description: Идентификатор товара
                            offer_id:
                              type: string
                              description: Идентификатор товара в системе продавца
                            product_name:
                              type: string
                              description: Название товара
                            current_price:
                              type: string
                              description: Текущая цена товара
                            discount_price:
                              type: string
                              description: Цена со скидкой
                            discount_percent:
                              type: number
                              description: Процент скидки
                            status:
                              type: string
                              enum: [pending, approved, declined]
                              description: Статус заявки
                            created_at:
                              type: string
                              format: date-time
                              description: Дата создания заявки
                      total:
                        type: integer
                        description: Общее количество заявок
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/actions/discounts-task/approve:
    post:
      tags:
        - Акции
      summary: Согласовать заявку на скидку
      description: |
        Согласовывает заявку на скидку.
      operationId: ActionsAPI_ApproveDiscountTask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на скидку
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      success:
                        type: boolean
                        description: Признак успешного согласования
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/actions/discounts-task/decline:
    post:
      tags:
        - Акции
      summary: Отклонить заявку на скидку
      description: |
        Отклоняет заявку на скидку.
      operationId: ActionsAPI_DeclineDiscountTask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на скидку
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      success:
                        type: boolean
                        description: Признак успешного отклонения
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/competitors/list:
    post:
      tags:
        - Стратегии ценообразования
      summary: Список конкурентов
      description: |
        Получает список конкурентов для настройки стратегий ценообразования.
      operationId: PricingStrategyAPI_GetCompetitorsList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество конкурентов в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество конкурентов, которое будет пропущено в ответе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      competitors:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              format: int64
                              description: Идентификатор конкурента
                            name:
                              type: string
                              description: Название конкурента
                            url:
                              type: string
                              format: uri
                              description: URL конкурента
                      total:
                        type: integer
                        description: Общее количество конкурентов
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/list:
    post:
      tags:
        - Стратегии ценообразования
      summary: Список стратегий
      description: |
        Получает список стратегий ценообразования.
      operationId: PricingStrategyAPI_GetStrategiesList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество стратегий в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество стратегий, которое будет пропущено в ответе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      strategies:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              format: int64
                              description: Идентификатор стратегии
                            name:
                              type: string
                              description: Название стратегии
                            description:
                              type: string
                              description: Описание стратегии
                            status:
                              type: string
                              enum: [active, inactive, paused]
                              description: Статус стратегии
                            created_at:
                              type: string
                              format: date-time
                              description: Дата создания стратегии
                            updated_at:
                              type: string
                              format: date-time
                              description: Дата последнего обновления стратегии
                            products_count:
                              type: integer
                              description: Количество товаров в стратегии
                      total:
                        type: integer
                        description: Общее количество стратегий
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/create:
    post:
      tags:
        - Стратегии ценообразования
      summary: Создать стратегию
      description: |
        Создает новую стратегию ценообразования.
      operationId: PricingStrategyAPI_CreateStrategy
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  maxLength: 255
                  description: Название стратегии
                description:
                  type: string
                  maxLength: 1000
                  description: Описание стратегии
                strategy_type:
                  type: string
                  enum: [competitor_based, margin_based, fixed_price]
                  description: Тип стратегии
                competitor_id:
                  type: integer
                  format: int64
                  description: Идентификатор конкурента (для стратегии на основе конкурентов)
                margin_percent:
                  type: number
                  minimum: 0
                  maximum: 100
                  description: Процент маржи (для стратегии на основе маржи)
                price_adjustment:
                  type: number
                  description: Корректировка цены
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      strategy_id:
                        type: integer
                        format: int64
                        description: Идентификатор созданной стратегии
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/info:
    post:
      tags:
        - Стратегии ценообразования
      summary: Информация о стратегии
      description: |
        Получает подробную информацию о стратегии ценообразования.
      operationId: PricingStrategyAPI_GetStrategyInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                strategy_id:
                  type: integer
                  format: int64
                  description: Идентификатор стратегии
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      id:
                        type: integer
                        format: int64
                        description: Идентификатор стратегии
                      name:
                        type: string
                        description: Название стратегии
                      description:
                        type: string
                        description: Описание стратегии
                      status:
                        type: string
                        enum: [active, inactive, paused]
                        description: Статус стратегии
                      strategy_type:
                        type: string
                        enum: [competitor_based, margin_based, fixed_price]
                        description: Тип стратегии
                      competitor_id:
                        type: integer
                        format: int64
                        description: Идентификатор конкурента
                      margin_percent:
                        type: number
                        description: Процент маржи
                      price_adjustment:
                        type: number
                        description: Корректировка цены
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания стратегии
                      updated_at:
                        type: string
                        format: date-time
                        description: Дата последнего обновления стратегии
                      products_count:
                        type: integer
                        description: Количество товаров в стратегии
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/update:
    post:
      tags:
        - Стратегии ценообразования
      summary: Обновить стратегию
      description: |
        Обновляет существующую стратегию ценообразования.
      operationId: PricingStrategyAPI_UpdateStrategy
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                strategy_id:
                  type: integer
                  format: int64
                  description: Идентификатор стратегии
                name:
                  type: string
                  maxLength: 255
                  description: Название стратегии
                description:
                  type: string
                  maxLength: 1000
                  description: Описание стратегии
                strategy_type:
                  type: string
                  enum: [competitor_based, margin_based, fixed_price]
                  description: Тип стратегии
                competitor_id:
                  type: integer
                  format: int64
                  description: Идентификатор конкурента
                margin_percent:
                  type: number
                  minimum: 0
                  maximum: 100
                  description: Процент маржи
                price_adjustment:
                  type: number
                  description: Корректировка цены
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      updated:
                        type: boolean
                        description: Признак успешного обновления
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/products/add:
    post:
      tags:
        - Стратегии ценообразования
      summary: Добавить товары в стратегию
      description: |
        Добавляет товары в стратегию ценообразования.
      operationId: PricingStrategyAPI_AddProductsToStrategy
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                strategy_id:
                  type: integer
                  format: int64
                  description: Идентификатор стратегии
                product_ids:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: Идентификаторы товаров для добавления в стратегию
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        product_id:
                          type: integer
                          format: int64
                          description: Идентификатор товара
                        added:
                          type: boolean
                          description: Признак успешного добавления в стратегию
                        errors:
                          type: array
                          items:
                            $ref: '#/components/schemas/Error'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/strategy-ids-by-product-ids:
    post:
      tags:
        - Стратегии ценообразования
      summary: Список идентификаторов стратегий
      description: |
        Получает список идентификаторов стратегий по идентификаторам товаров.
      operationId: PricingStrategyAPI_GetStrategyIdsByProductIds
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                product_ids:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: Идентификаторы товаров
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        product_id:
                          type: integer
                          format: int64
                          description: Идентификатор товара
                        strategy_ids:
                          type: array
                          items:
                            type: integer
                            format: int64
                          description: Идентификаторы стратегий для товара
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/products/list:
    post:
      tags:
        - Стратегии ценообразования
      summary: Список товаров в стратегии
      description: |
        Получает список товаров в стратегии ценообразования.
      operationId: PricingStrategyAPI_GetStrategyProducts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                strategy_id:
                  type: integer
                  format: int64
                  description: Идентификатор стратегии
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество товаров в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество товаров, которое будет пропущено в ответе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      products:
                        type: array
                        items:
                          type: object
                          properties:
                            product_id:
                              type: integer
                              format: int64
                              description: Идентификатор товара
                            offer_id:
                              type: string
                              description: Идентификатор товара в системе продавца
                            name:
                              type: string
                              description: Название товара
                            current_price:
                              type: string
                              description: Текущая цена товара
                            strategy_price:
                              type: string
                              description: Цена по стратегии
                            competitor_price:
                              type: string
                              description: Цена конкурента
                            last_updated:
                              type: string
                              format: date-time
                              description: Дата последнего обновления цены
                      total:
                        type: integer
                        description: Общее количество товаров в стратегии
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/product/info:
    post:
      tags:
        - Стратегии ценообразования
      summary: Цена товара у конкурента
      description: |
        Получает информацию о цене товара у конкурента.
      operationId: PricingStrategyAPI_GetProductCompetitorInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                product_id:
                  type: integer
                  format: int64
                  description: Идентификатор товара
                competitor_id:
                  type: integer
                  format: int64
                  description: Идентификатор конкурента
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      product_id:
                        type: integer
                        format: int64
                        description: Идентификатор товара
                      competitor_id:
                        type: integer
                        format: int64
                        description: Идентификатор конкурента
                      competitor_price:
                        type: string
                        description: Цена у конкурента
                      our_price:
                        type: string
                        description: Наша цена
                      price_difference:
                        type: string
                        description: Разница в цене
                      last_updated:
                        type: string
                        format: date-time
                        description: Дата последнего обновления информации
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/products/delete:
    post:
      tags:
        - Стратегии ценообразования
      summary: Удалить товары из стратегии
      description: |
        Удаляет товары из стратегии ценообразования.
      operationId: PricingStrategyAPI_DeleteProductsFromStrategy
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                strategy_id:
                  type: integer
                  format: int64
                  description: Идентификатор стратегии
                product_ids:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: Идентификаторы товаров для удаления из стратегии
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        product_id:
                          type: integer
                          format: int64
                          description: Идентификатор товара
                        deleted:
                          type: boolean
                          description: Признак успешного удаления из стратегии
                        errors:
                          type: array
                          items:
                            $ref: '#/components/schemas/Error'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/status:
    post:
      tags:
        - Стратегии ценообразования
      summary: Изменить статус стратегии
      description: |
        Изменяет статус стратегии ценообразования (активна/неактивна/приостановлена).
      operationId: PricingStrategyAPI_ChangeStrategyStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                strategy_id:
                  type: integer
                  format: int64
                  description: Идентификатор стратегии
                status:
                  type: string
                  enum: [active, inactive, paused]
                  description: Новый статус стратегии
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      updated:
                        type: boolean
                        description: Признак успешного изменения статуса
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/pricing-strategy/delete:
    post:
      tags:
        - Стратегии ценообразования
      summary: Удалить стратегию
      description: |
        Удаляет стратегию ценообразования.
      operationId: PricingStrategyAPI_DeleteStrategy
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                strategy_id:
                  type: integer
                  format: int64
                  description: Идентификатор стратегии для удаления
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      deleted:
                        type: boolean
                        description: Признак успешного удаления стратегии
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/brand/company-certification/list:
    post:
      tags:
        - Сертификаты брендов
      summary: Список сертифицируемых брендов
      description: |
        Получает список брендов, которые могут быть сертифицированы для продавца.
      operationId: BrandAPI_GetCompanyCertificationList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество брендов в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество брендов, которое будет пропущено в ответе
                filter:
                  type: object
                  properties:
                    brand_name:
                      type: string
                      description: Название бренда для фильтрации
                    certification_status:
                      type: string
                      enum: [pending, approved, rejected, not_required]
                      description: Статус сертификации
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      brands:
                        type: array
                        items:
                          type: object
                          properties:
                            brand_id:
                              type: integer
                              format: int64
                              description: Идентификатор бренда
                            brand_name:
                              type: string
                              description: Название бренда
                            certification_status:
                              type: string
                              enum: [pending, approved, rejected, not_required]
                              description: Статус сертификации
                            certification_required:
                              type: boolean
                              description: Требуется ли сертификация для данного бренда
                            certificate_url:
                              type: string
                              format: uri
                              description: Ссылка на сертификат (если есть)
                            expiry_date:
                              type: string
                              format: date
                              description: Дата истечения сертификата
                            created_at:
                              type: string
                              format: date-time
                              description: Дата создания записи
                            updated_at:
                              type: string
                              format: date-time
                              description: Дата последнего обновления
                      total:
                        type: integer
                        description: Общее количество брендов
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/accordance-types:
    get:
      tags:
        - Сертификаты качества
      summary: Список типов соответствия требованиям (версия 1)
      description: |
        Получает список типов соответствия требованиям для сертификации товаров.
      operationId: CertificationAPI_GetAccordanceTypes
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          description: Идентификатор типа соответствия
                        name:
                          type: string
                          description: Название типа соответствия
                        description:
                          type: string
                          description: Описание типа соответствия
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/product/certificate/accordance-types/list:
    get:
      tags:
        - Сертификаты качества
      summary: Список типов соответствия требованиям (версия 2)
      description: |
        Получает список типов соответствия требованиям для сертификации товаров (улучшенная версия).
      operationId: CertificationAPI_GetAccordanceTypesV2
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          description: Идентификатор типа соответствия
                        name:
                          type: string
                          description: Название типа соответствия
                        description:
                          type: string
                          description: Описание типа соответствия
                        is_required:
                          type: boolean
                          description: Обязательность соответствия
                        categories:
                          type: array
                          items:
                            type: integer
                            format: int64
                          description: Категории, для которых применим тип соответствия
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/types:
    get:
      tags:
        - Сертификаты качества
      summary: Справочник типов документов
      description: |
        Получает справочник типов документов для сертификации.
      operationId: CertificationAPI_GetCertificateTypes
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          description: Идентификатор типа документа
                        name:
                          type: string
                          description: Название типа документа
                        description:
                          type: string
                          description: Описание типа документа
                        is_required:
                          type: boolean
                          description: Обязательность документа
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/product/certification/list:
    post:
      tags:
        - Сертификаты качества
      summary: Список сертифицируемых категорий
      description: |
        Получает список категорий товаров, которые требуют сертификации.
      operationId: CertificationAPI_GetCertificationList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество категорий в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество категорий, которое будет пропущено в ответе
                filter:
                  type: object
                  properties:
                    category_id:
                      type: integer
                      format: int64
                      description: Идентификатор категории для фильтрации
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      categories:
                        type: array
                        items:
                          type: object
                          properties:
                            category_id:
                              type: integer
                              format: int64
                              description: Идентификатор категории
                            category_name:
                              type: string
                              description: Название категории
                            certification_required:
                              type: boolean
                              description: Требуется ли сертификация для категории
                            required_documents:
                              type: array
                              items:
                                type: object
                                properties:
                                  document_type_id:
                                    type: integer
                                    format: int64
                                  document_type_name:
                                    type: string
                              description: Обязательные документы для категории
                      total:
                        type: integer
                        description: Общее количество категорий
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/create:
    post:
      tags:
        - Сертификаты качества
      summary: Добавить сертификаты для товаров
      description: |
        Добавляет сертификаты качества для товаров.
      operationId: CertificationAPI_CreateCertificate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                certificates:
                  type: array
                  items:
                    type: object
                    properties:
                      certificate_number:
                        type: string
                        description: Номер сертификата
                      certificate_type_id:
                        type: integer
                        format: int64
                        description: Идентификатор типа сертификата
                      accordance_type_id:
                        type: integer
                        format: int64
                        description: Идентификатор типа соответствия
                      issue_date:
                        type: string
                        format: date
                        description: Дата выдачи сертификата
                      expiry_date:
                        type: string
                        format: date
                        description: Дата истечения сертификата
                      issuer:
                        type: string
                        description: Орган, выдавший сертификат
                      file_url:
                        type: string
                        format: uri
                        description: Ссылка на файл сертификата
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        certificate_id:
                          type: integer
                          format: int64
                          description: Идентификатор созданного сертификата
                        certificate_number:
                          type: string
                          description: Номер сертификата
                        created:
                          type: boolean
                          description: Признак успешного создания
                        errors:
                          type: array
                          items:
                            $ref: '#/components/schemas/Error'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/bind:
    post:
      tags:
        - Сертификаты качества
      summary: Привязать сертификат к товару
      description: |
        Привязывает существующий сертификат к товару.
      operationId: CertificationAPI_BindCertificate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                certificate_id:
                  type: integer
                  format: int64
                  description: Идентификатор сертификата
                product_ids:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: Идентификаторы товаров для привязки
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        product_id:
                          type: integer
                          format: int64
                          description: Идентификатор товара
                        bound:
                          type: boolean
                          description: Признак успешной привязки
                        errors:
                          type: array
                          items:
                            $ref: '#/components/schemas/Error'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/delete:
    post:
      tags:
        - Сертификаты качества
      summary: Удалить сертификат
      description: |
        Удаляет сертификат качества.
      operationId: CertificationAPI_DeleteCertificate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                certificate_id:
                  type: integer
                  format: int64
                  description: Идентификатор сертификата для удаления
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      deleted:
                        type: boolean
                        description: Признак успешного удаления
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/info:
    post:
      tags:
        - Сертификаты качества
      summary: Информация о сертификате
      description: |
        Получает подробную информацию о сертификате качества.
      operationId: CertificationAPI_GetCertificateInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                certificate_id:
                  type: integer
                  format: int64
                  description: Идентификатор сертификата
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      certificate_id:
                        type: integer
                        format: int64
                        description: Идентификатор сертификата
                      certificate_number:
                        type: string
                        description: Номер сертификата
                      certificate_type_id:
                        type: integer
                        format: int64
                        description: Идентификатор типа сертификата
                      certificate_type_name:
                        type: string
                        description: Название типа сертификата
                      accordance_type_id:
                        type: integer
                        format: int64
                        description: Идентификатор типа соответствия
                      accordance_type_name:
                        type: string
                        description: Название типа соответствия
                      issue_date:
                        type: string
                        format: date
                        description: Дата выдачи сертификата
                      expiry_date:
                        type: string
                        format: date
                        description: Дата истечения сертификата
                      issuer:
                        type: string
                        description: Орган, выдавший сертификат
                      file_url:
                        type: string
                        format: uri
                        description: Ссылка на файл сертификата
                      status:
                        type: string
                        description: Статус сертификата
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания записи
                      updated_at:
                        type: string
                        format: date-time
                        description: Дата последнего обновления
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/list:
    post:
      tags:
        - Сертификаты качества
      summary: Список сертификатов
      description: |
        Получает список сертификатов качества продавца.
      operationId: CertificationAPI_GetCertificateList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество сертификатов в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество сертификатов, которое будет пропущено в ответе
                filter:
                  type: object
                  properties:
                    certificate_number:
                      type: string
                      description: Номер сертификата для фильтрации
                    status:
                      type: string
                      description: Статус сертификата для фильтрации
                    certificate_type_id:
                      type: integer
                      format: int64
                      description: Идентификатор типа сертификата для фильтрации
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      certificates:
                        type: array
                        items:
                          type: object
                          properties:
                            certificate_id:
                              type: integer
                              format: int64
                              description: Идентификатор сертификата
                            certificate_number:
                              type: string
                              description: Номер сертификата
                            certificate_type_name:
                              type: string
                              description: Название типа сертификата
                            accordance_type_name:
                              type: string
                              description: Название типа соответствия
                            issue_date:
                              type: string
                              format: date
                              description: Дата выдачи сертификата
                            expiry_date:
                              type: string
                              format: date
                              description: Дата истечения сертификата
                            status:
                              type: string
                              description: Статус сертификата
                            products_count:
                              type: integer
                              description: Количество привязанных товаров
                      total:
                        type: integer
                        description: Общее количество сертификатов
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/product_status/list:
    post:
      tags:
        - Сертификаты качества
      summary: Список возможных статусов товаров
      description: |
        Получает список возможных статусов товаров в контексте сертификации.
      operationId: CertificationAPI_GetProductStatusList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties: {}
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        status:
                          type: string
                          description: Статус товара
                        description:
                          type: string
                          description: Описание статуса
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/products/list:
    post:
      tags:
        - Сертификаты качества
      summary: Список товаров, привязанных к сертификату
      description: |
        Получает список товаров, привязанных к указанному сертификату.
      operationId: CertificationAPI_GetCertificateProducts
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                certificate_id:
                  type: integer
                  format: int64
                  description: Идентификатор сертификата
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество товаров в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество товаров, которое будет пропущено в ответе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      products:
                        type: array
                        items:
                          type: object
                          properties:
                            product_id:
                              type: integer
                              format: int64
                              description: Идентификатор товара
                            offer_id:
                              type: string
                              description: Идентификатор товара в системе продавца
                            name:
                              type: string
                              description: Название товара
                            status:
                              type: string
                              description: Статус товара
                            bound_at:
                              type: string
                              format: date-time
                              description: Дата привязки к сертификату
                      total:
                        type: integer
                        description: Общее количество товаров
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/unbind:
    post:
      tags:
        - Сертификаты качества
      summary: Отвязать товар от сертификата
      description: |
        Отвязывает товар от сертификата качества.
      operationId: CertificationAPI_UnbindCertificate
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                certificate_id:
                  type: integer
                  format: int64
                  description: Идентификатор сертификата
                product_ids:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: Идентификаторы товаров для отвязки
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        product_id:
                          type: integer
                          format: int64
                          description: Идентификатор товара
                        unbound:
                          type: boolean
                          description: Признак успешной отвязки
                        errors:
                          type: array
                          items:
                            $ref: '#/components/schemas/Error'
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/rejection_reasons/list:
    post:
      tags:
        - Сертификаты качества
      summary: Возможные причины отклонения сертификата
      description: |
        Получает список возможных причин отклонения сертификата.
      operationId: CertificationAPI_GetRejectionReasons
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties: {}
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        reason_id:
                          type: integer
                          format: int64
                          description: Идентификатор причины
                        reason:
                          type: string
                          description: Описание причины отклонения
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/product/certificate/status/list:
    post:
      tags:
        - Сертификаты качества
      summary: Возможные статусы сертификатов
      description: |
        Получает список возможных статусов сертификатов.
      operationId: CertificationAPI_GetCertificateStatusList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties: {}
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        status:
                          type: string
                          description: Статус сертификата
                        description:
                          type: string
                          description: Описание статуса
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/warehouse/list:
    post:
      tags:
        - Склады
      summary: Список складов
      description: |
        Получает список складов продавца.
      operationId: WarehouseAPI_GetWarehouseList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество складов в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество складов, которое будет пропущено в ответе
                filter:
                  type: object
                  properties:
                    warehouse_type:
                      type: string
                      enum: [fbo, fbs, rfbs]
                      description: Тип склада для фильтрации
                    status:
                      type: string
                      enum: [active, inactive]
                      description: Статус склада для фильтрации
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      warehouses:
                        type: array
                        items:
                          type: object
                          properties:
                            warehouse_id:
                              type: integer
                              format: int64
                              description: Идентификатор склада
                            name:
                              type: string
                              description: Название склада
                            warehouse_type:
                              type: string
                              enum: [fbo, fbs, rfbs]
                              description: Тип склада
                            status:
                              type: string
                              enum: [active, inactive]
                              description: Статус склада
                            address:
                              type: string
                              description: Адрес склада
                            city:
                              type: string
                              description: Город
                            region:
                              type: string
                              description: Регион
                            country:
                              type: string
                              description: Страна
                            postal_code:
                              type: string
                              description: Почтовый индекс
                            working_hours:
                              type: string
                              description: Часы работы
                            contact_person:
                              type: string
                              description: Контактное лицо
                            phone:
                              type: string
                              description: Телефон
                            email:
                              type: string
                              format: email
                              description: Email
                            created_at:
                              type: string
                              format: date-time
                              description: Дата создания
                            updated_at:
                              type: string
                              format: date-time
                              description: Дата последнего обновления
                      total:
                        type: integer
                        description: Общее количество складов
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/delivery-method/list:
    post:
      tags:
        - Склады
      summary: Список методов доставки склада
      description: |
        Получает список доступных методов доставки для указанного склада.
      operationId: WarehouseAPI_GetDeliveryMethodList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                warehouse_id:
                  type: integer
                  format: int64
                  description: Идентификатор склада
                filter:
                  type: object
                  properties:
                    delivery_type:
                      type: string
                      enum: [courier, pickup, post]
                      description: Тип доставки для фильтрации
                    status:
                      type: string
                      enum: [active, inactive]
                      description: Статус метода доставки
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      delivery_methods:
                        type: array
                        items:
                          type: object
                          properties:
                            delivery_method_id:
                              type: integer
                              format: int64
                              description: Идентификатор метода доставки
                            name:
                              type: string
                              description: Название метода доставки
                            delivery_type:
                              type: string
                              enum: [courier, pickup, post]
                              description: Тип доставки
                            status:
                              type: string
                              enum: [active, inactive]
                              description: Статус метода доставки
                            min_weight:
                              type: number
                              description: Минимальный вес (кг)
                            max_weight:
                              type: number
                              description: Максимальный вес (кг)
                            min_dimensions:
                              type: object
                              properties:
                                length:
                                  type: number
                                width:
                                  type: number
                                height:
                                  type: number
                              description: Минимальные размеры (см)
                            max_dimensions:
                              type: object
                              properties:
                                length:
                                  type: number
                                width:
                                  type: number
                                height:
                                  type: number
                              description: Максимальные размеры (см)
                            delivery_time:
                              type: object
                              properties:
                                min_days:
                                  type: integer
                                max_days:
                                  type: integer
                              description: Время доставки (дни)
                            cost:
                              type: object
                              properties:
                                base_cost:
                                  type: number
                                per_kg_cost:
                                  type: number
                                currency:
                                  type: string
                                  default: RUB
                              description: Стоимость доставки
                            coverage_areas:
                              type: array
                              items:
                                type: string
                              description: Зоны покрытия доставки
                      total:
                        type: integer
                        description: Общее количество методов доставки
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/report/info:
    post:
      tags:
        - Отчёты
      summary: Информация об отчёте
      description: |
        Получает информацию о статусе и результатах генерации отчёта.
      operationId: ReportAPI_GetReportInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
                  description: Код отчёта, полученный при создании
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      code:
                        type: string
                        description: Код отчёта
                      status:
                        type: string
                        enum: [processing, success, failed, cancelled]
                        description: Статус генерации отчёта
                      kind:
                        type: string
                        description: Тип отчёта
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания отчёта
                      updated_at:
                        type: string
                        format: date-time
                        description: Дата последнего обновления
                      file:
                        type: string
                        format: uri
                        description: Ссылка на файл отчёта (если готов)
                      error:
                        type: string
                        description: Описание ошибки (если статус failed)
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/report/list:
    post:
      tags:
        - Отчёты
      summary: Список отчётов
      description: |
        Получает список всех отчётов продавца.
      operationId: ReportAPI_GetReportList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                page:
                  type: integer
                  minimum: 1
                  default: 1
                  description: Номер страницы
                page_size:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество отчётов на странице
                filter:
                  type: object
                  properties:
                    status:
                      type: array
                      items:
                        type: string
                        enum: [processing, success, failed, cancelled]
                      description: Фильтр по статусу отчётов
                    kind:
                      type: array
                      items:
                        type: string
                      description: Фильтр по типу отчётов
                    created_at_from:
                      type: string
                      format: date-time
                      description: Начальная дата создания
                    created_at_to:
                      type: string
                      format: date-time
                      description: Конечная дата создания
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      reports:
                        type: array
                        items:
                          type: object
                          properties:
                            code:
                              type: string
                              description: Код отчёта
                            status:
                              type: string
                              enum: [processing, success, failed, cancelled]
                              description: Статус генерации отчёта
                            kind:
                              type: string
                              description: Тип отчёта
                            created_at:
                              type: string
                              format: date-time
                              description: Дата создания отчёта
                            updated_at:
                              type: string
                              format: date-time
                              description: Дата последнего обновления
                            file:
                              type: string
                              format: uri
                              description: Ссылка на файл отчёта (если готов)
                      total:
                        type: integer
                        description: Общее количество отчётов
                      page:
                        type: integer
                        description: Текущая страница
                      page_size:
                        type: integer
                        description: Размер страницы
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/report/products/create:
    post:
      tags:
        - Отчёты
      summary: Отчёт по товарам
      description: |
        Создает отчёт по товарам с подробной информацией о продажах, остатках и других метриках.
      operationId: ReportAPI_CreateProductsReport
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    offer_id:
                      type: array
                      items:
                        type: string
                      description: Фильтр по идентификаторам товаров
                    product_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                      description: Фильтр по ID товаров
                    sku:
                      type: array
                      items:
                        type: integer
                        format: int64
                      description: Фильтр по SKU товаров
                    visibility:
                      type: string
                      enum: [ALL, VISIBLE, INVISIBLE]
                      description: Фильтр по видимости товаров
                language:
                  type: string
                  enum: [DEFAULT, RU, EN, TR, ZH_HANS]
                  default: DEFAULT
                  description: Язык отчёта
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      code:
                        type: string
                        description: Код созданного отчёта
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/report/returns/create:
    post:
      tags:
        - Отчёты
      summary: Отчёт о возвратах
      description: |
        Создает отчёт о возвратах товаров за указанный период.
      operationId: ReportAPI_CreateReturnsReport
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    processed_at_from:
                      type: string
                      format: date-time
                      description: Начальная дата обработки возврата
                    processed_at_to:
                      type: string
                      format: date-time
                      description: Конечная дата обработки возврата
                    order_number:
                      type: array
                      items:
                        type: string
                      description: Фильтр по номерам заказов
                    posting_number:
                      type: array
                      items:
                        type: string
                      description: Фильтр по номерам отправлений
                    status:
                      type: array
                      items:
                        type: string
                        enum: [returned_to_seller, cancelled, client_arbitration, arbitration, not_returned_to_seller, returned_to_ozon, moving, disposed, disposing]
                      description: Фильтр по статусам возвратов
                language:
                  type: string
                  enum: [DEFAULT, RU, EN, TR, ZH_HANS]
                  default: DEFAULT
                  description: Язык отчёта
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      code:
                        type: string
                        description: Код созданного отчёта
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/report/postings/create:
    post:
      tags:
        - Отчёты
      summary: Отчёт об отправлениях
      description: |
        Создает отчёт об отправлениях за указанный период.
      operationId: ReportAPI_CreatePostingsReport
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    processed_at_from:
                      type: string
                      format: date-time
                      description: Начальная дата обработки отправления
                    processed_at_to:
                      type: string
                      format: date-time
                      description: Конечная дата обработки отправления
                    delivery_schema:
                      type: array
                      items:
                        type: string
                        enum: [fbo, fbs, rfbs]
                      description: Фильтр по схемам доставки
                    status:
                      type: array
                      items:
                        type: string
                      description: Фильтр по статусам отправлений
                    warehouse_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                      description: Фильтр по складам
                language:
                  type: string
                  enum: [DEFAULT, RU, EN, TR, ZH_HANS]
                  default: DEFAULT
                  description: Язык отчёта
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      code:
                        type: string
                        description: Код созданного отчёта
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/finance/cash-flow-statement/list:
    post:
      tags:
        - Отчёты
      summary: Финансовый отчёт
      description: |
        Создает финансовый отчёт о движении денежных средств.
      operationId: ReportAPI_CreateCashFlowStatement
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date_from:
                  type: string
                  format: date
                  description: Начальная дата периода
                date_to:
                  type: string
                  format: date
                  description: Конечная дата периода
                page:
                  type: integer
                  minimum: 1
                  default: 1
                  description: Номер страницы
                page_size:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Размер страницы
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      cash_flows:
                        type: array
                        items:
                          type: object
                          properties:
                            date:
                              type: string
                              format: date
                              description: Дата операции
                            operation_type:
                              type: string
                              description: Тип операции
                            amount:
                              type: number
                              description: Сумма операции
                            currency:
                              type: string
                              description: Валюта
                            description:
                              type: string
                              description: Описание операции
                      total:
                        type: integer
                        description: Общее количество записей
                      page:
                        type: integer
                        description: Текущая страница
                      page_size:
                        type: integer
                        description: Размер страницы
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/report/discounted/create:
    post:
      tags:
        - Отчёты
      summary: Отчёт об уценённых товарах
      description: |
        Создает отчёт об уценённых товарах.
      operationId: ReportAPI_CreateDiscountedReport
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    discounted_from:
                      type: string
                      format: date-time
                      description: Начальная дата уценки
                    discounted_to:
                      type: string
                      format: date-time
                      description: Конечная дата уценки
                    offer_id:
                      type: array
                      items:
                        type: string
                      description: Фильтр по идентификаторам товаров
                    warehouse_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                      description: Фильтр по складам
                language:
                  type: string
                  enum: [DEFAULT, RU, EN, TR, ZH_HANS]
                  default: DEFAULT
                  description: Язык отчёта
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      code:
                        type: string
                        description: Код созданного отчёта
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/report/warehouse/stock:
    post:
      tags:
        - Отчёты
      summary: Отчёт об остатках на FBS-складе
      description: |
        Создает отчёт об остатках товаров на FBS-складе.
      operationId: ReportAPI_CreateWarehouseStockReport
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                warehouse_id:
                  type: integer
                  format: int64
                  description: Идентификатор склада
                filter:
                  type: object
                  properties:
                    offer_id:
                      type: array
                      items:
                        type: string
                      description: Фильтр по идентификаторам товаров
                    product_id:
                      type: array
                      items:
                        type: integer
                        format: int64
                      description: Фильтр по ID товаров
                    sku:
                      type: array
                      items:
                        type: integer
                        format: int64
                      description: Фильтр по SKU товаров
                language:
                  type: string
                  enum: [DEFAULT, RU, EN, TR, ZH_HANS]
                  default: DEFAULT
                  description: Язык отчёта
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      code:
                        type: string
                        description: Код созданного отчёта
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/posting/fbo/list:
    post:
      tags:
        - Доставка FBO
      summary: Список отправлений FBO
      description: |
        Получает список отправлений по схеме FBO.
      operationId: FboAPI_GetFboPostingList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                dir:
                  type: string
                  enum: [ASC, DESC]
                  default: ASC
                  description: Направление сортировки
                filter:
                  type: object
                  properties:
                    since:
                      type: string
                      format: date-time
                      description: Начальная дата периода
                    to:
                      type: string
                      format: date-time
                      description: Конечная дата периода
                    status:
                      type: string
                      enum: [awaiting_packaging, awaiting_deliver, arbitration, client_arbitration, delivering, delivered, cancelled]
                      description: Статус отправления
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество отправлений в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество отправлений, которое будет пропущено в ответе
                with:
                  type: object
                  properties:
                    analytics_data:
                      type: boolean
                      default: false
                      description: Добавить аналитические данные в ответ
                    financial_data:
                      type: boolean
                      default: false
                      description: Добавить финансовые данные в ответ
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      postings:
                        type: array
                        items:
                          type: object
                          properties:
                            posting_number:
                              type: string
                              description: Номер отправления
                            order_id:
                              type: integer
                              format: int64
                              description: Идентификатор заказа
                            order_number:
                              type: string
                              description: Номер заказа
                            status:
                              type: string
                              description: Статус отправления
                            created_at:
                              type: string
                              format: date-time
                              description: Дата создания отправления
                            in_process_at:
                              type: string
                              format: date-time
                              description: Дата перехода в обработку
                            products:
                              type: array
                              items:
                                type: object
                                properties:
                                  sku:
                                    type: integer
                                    format: int64
                                    description: SKU товара
                                  name:
                                    type: string
                                    description: Название товара
                                  quantity:
                                    type: integer
                                    description: Количество товара
                                  offer_id:
                                    type: string
                                    description: Идентификатор товара в системе продавца
                                  price:
                                    type: string
                                    description: Цена товара
                      has_next:
                        type: boolean
                        description: Признак наличия следующей страницы
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/posting/fbo/get:
    post:
      tags:
        - Доставка FBO
      summary: Информация об отправлении FBO
      description: |
        Получает подробную информацию об отправлении по схеме FBO.
      operationId: FboAPI_GetFboPosting
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                posting_number:
                  type: string
                  description: Номер отправления
                with:
                  type: object
                  properties:
                    analytics_data:
                      type: boolean
                      default: false
                      description: Добавить аналитические данные в ответ
                    financial_data:
                      type: boolean
                      default: false
                      description: Добавить финансовые данные в ответ
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      posting_number:
                        type: string
                        description: Номер отправления
                      order_id:
                        type: integer
                        format: int64
                        description: Идентификатор заказа
                      order_number:
                        type: string
                        description: Номер заказа
                      status:
                        type: string
                        description: Статус отправления
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания отправления
                      in_process_at:
                        type: string
                        format: date-time
                        description: Дата перехода в обработку
                      tracking_number:
                        type: string
                        description: Трек-номер отправления
                      customer:
                        type: object
                        properties:
                          customer_id:
                            type: integer
                            format: int64
                            description: Идентификатор покупателя
                          name:
                            type: string
                            description: Имя покупателя
                          address:
                            type: object
                            properties:
                              address_line:
                                type: string
                                description: Адрес доставки
                              city:
                                type: string
                                description: Город
                              country:
                                type: string
                                description: Страна
                              postal_code:
                                type: string
                                description: Почтовый индекс
                      products:
                        type: array
                        items:
                          type: object
                          properties:
                            sku:
                              type: integer
                              format: int64
                              description: SKU товара
                            name:
                              type: string
                              description: Название товара
                            quantity:
                              type: integer
                              description: Количество товара
                            offer_id:
                              type: string
                              description: Идентификатор товара в системе продавца
                            price:
                              type: string
                              description: Цена товара
                            digital_codes:
                              type: array
                              items:
                                type: string
                              description: Цифровые коды (для цифровых товаров)
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/posting/fbo/cancel-reason/list:
    post:
      tags:
        - Доставка FBO
      summary: Причины отмены отправлений FBO
      description: |
        Получает список возможных причин отмены отправлений по схеме FBO.
      operationId: FboAPI_GetCancelReasons
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties: {}
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          description: Идентификатор причины отмены
                        name:
                          type: string
                          description: Название причины отмены
                        type_id:
                          type: integer
                          description: Идентификатор типа причины
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/status/counter:
    post:
      tags:
        - Доставка FBO
      summary: Количество заявок по статусам
      description: |
        Получает количество заявок на поставку, сгруппированных по статусам.
      operationId: FboAPI_GetSupplyOrderStatusCounter
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                warehouse_id:
                  type: integer
                  format: int64
                  description: Идентификатор склада
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      statuses:
                        type: array
                        items:
                          type: object
                          properties:
                            status:
                              type: string
                              description: Статус заявки
                            count:
                              type: integer
                              description: Количество заявок с данным статусом
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/bundle:
    post:
      tags:
        - Доставка FBO
      summary: Состав поставки или заявки на поставку
      description: |
        Получает состав поставки или заявки на поставку.
      operationId: FboAPI_GetSupplyOrderBundle
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      supply_order_id:
                        type: integer
                        format: int64
                        description: Идентификатор заявки на поставку
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            sku:
                              type: integer
                              format: int64
                              description: SKU товара
                            offer_id:
                              type: string
                              description: Идентификатор товара в системе продавца
                            name:
                              type: string
                              description: Название товара
                            quantity:
                              type: integer
                              description: Количество товара в поставке
                            price:
                              type: string
                              description: Цена товара
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/supply-order/list:
    post:
      tags:
        - Доставка FBO
      summary: Список заявок на поставку на склад Ozon
      description: |
        Получает список заявок на поставку товаров на склад Ozon.
      operationId: FboAPI_GetSupplyOrderList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                dir:
                  type: string
                  enum: [ASC, DESC]
                  default: ASC
                  description: Направление сортировки
                filter:
                  type: object
                  properties:
                    since:
                      type: string
                      format: date-time
                      description: Начальная дата периода
                    to:
                      type: string
                      format: date-time
                      description: Конечная дата периода
                    status:
                      type: array
                      items:
                        type: string
                        enum: [new, in_process, sent_by_seller, delivered_to_ozon_warehouse, cancelled, error]
                      description: Статусы заявок для фильтрации
                    warehouse_id:
                      type: integer
                      format: int64
                      description: Идентификатор склада
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество заявок в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество заявок, которое будет пропущено в ответе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      supply_orders:
                        type: array
                        items:
                          type: object
                          properties:
                            supply_order_id:
                              type: integer
                              format: int64
                              description: Идентификатор заявки на поставку
                            supply_order_number:
                              type: string
                              description: Номер заявки на поставку
                            status:
                              type: string
                              description: Статус заявки
                            created_at:
                              type: string
                              format: date-time
                              description: Дата создания заявки
                            updated_at:
                              type: string
                              format: date-time
                              description: Дата последнего обновления заявки
                            warehouse_id:
                              type: integer
                              format: int64
                              description: Идентификатор склада
                            warehouse_name:
                              type: string
                              description: Название склада
                            items_count:
                              type: integer
                              description: Количество товаров в заявке
                      has_next:
                        type: boolean
                        description: Признак наличия следующей страницы
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/supply-order/get:
    post:
      tags:
        - Доставка FBO
      summary: Информация о заявке на поставку
      description: |
        Получает подробную информацию о заявке на поставку.
      operationId: FboAPI_GetSupplyOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      supply_order_id:
                        type: integer
                        format: int64
                        description: Идентификатор заявки на поставку
                      supply_order_number:
                        type: string
                        description: Номер заявки на поставку
                      status:
                        type: string
                        description: Статус заявки
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания заявки
                      updated_at:
                        type: string
                        format: date-time
                        description: Дата последнего обновления заявки
                      warehouse_id:
                        type: integer
                        format: int64
                        description: Идентификатор склада
                      warehouse_name:
                        type: string
                        description: Название склада
                      warehouse_address:
                        type: string
                        description: Адрес склада
                      timeslot:
                        type: object
                        properties:
                          from:
                            type: string
                            format: date-time
                            description: Начало временного интервала поставки
                          to:
                            type: string
                            format: date-time
                            description: Конец временного интервала поставки
                      items_count:
                        type: integer
                        description: Количество товаров в заявке
                      total_price:
                        type: string
                        description: Общая стоимость товаров в заявке
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/timeslot/get:
    post:
      tags:
        - Доставка FBO
      summary: Интервалы поставки
      description: |
        Получает доступные интервалы времени для поставки товаров на склад.
      operationId: FboAPI_GetSupplyOrderTimeslots
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                warehouse_id:
                  type: integer
                  format: int64
                  description: Идентификатор склада
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      timeslots:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              format: int64
                              description: Идентификатор временного интервала
                            from:
                              type: string
                              format: date-time
                              description: Начало временного интервала
                            to:
                              type: string
                              format: date-time
                              description: Конец временного интервала
                            quota:
                              type: integer
                              description: Доступная квота для интервала
                            is_available:
                              type: boolean
                              description: Доступность интервала для бронирования
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/timeslot/update:
    post:
      tags:
        - Доставка FBO
      summary: Обновить интервал поставки
      description: |
        Обновляет временной интервал для поставки товаров на склад.
      operationId: FboAPI_UpdateSupplyOrderTimeslot
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
                timeslot_id:
                  type: integer
                  format: int64
                  description: Идентификатор нового временного интервала
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      updated:
                        type: boolean
                        description: Признак успешного обновления интервала
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/timeslot/status:
    post:
      tags:
        - Доставка FBO
      summary: Статус интервала поставки
      description: |
        Получает статус бронирования временного интервала поставки.
      operationId: FboAPI_GetSupplyOrderTimeslotStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      status:
                        type: string
                        enum: [not_booked, booked, confirmed, cancelled]
                        description: Статус бронирования интервала
                      timeslot:
                        type: object
                        properties:
                          id:
                            type: integer
                            format: int64
                            description: Идентификатор временного интервала
                          from:
                            type: string
                            format: date-time
                            description: Начало временного интервала
                          to:
                            type: string
                            format: date-time
                            description: Конец временного интервала
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/pass/create:
    post:
      tags:
        - Доставка FBO
      summary: Указать данные о водителе и автомобиле
      description: |
        Указывает данные о водителе и автомобиле для поставки товаров на склад.
      operationId: FboAPI_CreateSupplyOrderPass
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
                driver:
                  type: object
                  properties:
                    name:
                      type: string
                      description: ФИО водителя
                    phone:
                      type: string
                      description: Телефон водителя
                    passport:
                      type: string
                      description: Паспортные данные водителя
                vehicle:
                  type: object
                  properties:
                    number:
                      type: string
                      description: Номер автомобиля
                    model:
                      type: string
                      description: Модель автомобиля
                    color:
                      type: string
                      description: Цвет автомобиля
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      pass_id:
                        type: integer
                        format: int64
                        description: Идентификатор созданного пропуска
                      status:
                        type: string
                        description: Статус создания пропуска
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/pass/status:
    post:
      tags:
        - Доставка FBO
      summary: Статус ввода данных о водителе и автомобиле
      description: |
        Получает статус ввода данных о водителе и автомобиле для поставки.
      operationId: FboAPI_GetSupplyOrderPassStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      status:
                        type: string
                        enum: [not_provided, provided, approved, rejected]
                        description: Статус данных о водителе и автомобиле
                      pass_id:
                        type: integer
                        format: int64
                        description: Идентификатор пропуска
                      driver:
                        type: object
                        properties:
                          name:
                            type: string
                            description: ФИО водителя
                          phone:
                            type: string
                            description: Телефон водителя
                      vehicle:
                        type: object
                        properties:
                          number:
                            type: string
                            description: Номер автомобиля
                          model:
                            type: string
                            description: Модель автомобиля
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supplier/available_warehouses:
    get:
      tags:
        - Доставка FBO
      summary: Загруженность складов Ozon
      description: |
        Получает информацию о загруженности складов Ozon и доступности для поставок.
      operationId: FboAPI_GetAvailableWarehouses
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        warehouse_id:
                          type: integer
                          format: int64
                          description: Идентификатор склада
                        name:
                          type: string
                          description: Название склада
                        city:
                          type: string
                          description: Город расположения склада
                        address:
                          type: string
                          description: Адрес склада
                        is_available:
                          type: boolean
                          description: Доступность склада для поставок
                        workload:
                          type: string
                          enum: [low, medium, high, full]
                          description: Уровень загруженности склада
                        next_available_date:
                          type: string
                          format: date
                          description: Ближайшая доступная дата для поставки
                        working_hours:
                          type: string
                          description: Часы работы склада
                        contact_info:
                          type: object
                          properties:
                            phone:
                              type: string
                              description: Телефон склада
                            email:
                              type: string
                              format: email
                              description: Email склада
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/cluster/list:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Информация о кластерах и их складах
      description: |
        Получает информацию о кластерах складов и их характеристиках для планирования поставок.
      operationId: FboSupplyAPI_GetClusterList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    region:
                      type: string
                      description: Фильтр по региону
                    cluster_type:
                      type: string
                      enum: [standard, express, premium]
                      description: Тип кластера
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        cluster_id:
                          type: integer
                          format: int64
                          description: Идентификатор кластера
                        name:
                          type: string
                          description: Название кластера
                        region:
                          type: string
                          description: Регион расположения кластера
                        cluster_type:
                          type: string
                          description: Тип кластера
                        warehouses:
                          type: array
                          items:
                            type: object
                            properties:
                              warehouse_id:
                                type: integer
                                format: int64
                                description: Идентификатор склада
                              name:
                                type: string
                                description: Название склада
                              address:
                                type: string
                                description: Адрес склада
                              capacity:
                                type: integer
                                description: Вместимость склада
                              current_load:
                                type: integer
                                description: Текущая загрузка склада
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/warehouse/fbo/list:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Поиск точек для отгрузки поставки
      description: |
        Получает список доступных точек для отгрузки поставки FBO.
      operationId: FboSupplyAPI_GetFboWarehouseList
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                filter:
                  type: object
                  properties:
                    region:
                      type: string
                      description: Фильтр по региону
                    city:
                      type: string
                      description: Фильтр по городу
                    warehouse_type:
                      type: string
                      enum: [standard, express, cross_dock]
                      description: Тип склада
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество складов в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Количество складов, которое будет пропущено в ответе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      warehouses:
                        type: array
                        items:
                          type: object
                          properties:
                            warehouse_id:
                              type: integer
                              format: int64
                              description: Идентификатор склада
                            name:
                              type: string
                              description: Название склада
                            address:
                              type: string
                              description: Адрес склада
                            city:
                              type: string
                              description: Город
                            region:
                              type: string
                              description: Регион
                            warehouse_type:
                              type: string
                              description: Тип склада
                            working_hours:
                              type: string
                              description: Часы работы
                            contact_info:
                              type: object
                              properties:
                                phone:
                                  type: string
                                  description: Телефон склада
                                email:
                                  type: string
                                  format: email
                                  description: Email склада
                            capacity_info:
                              type: object
                              properties:
                                max_volume:
                                  type: number
                                  description: Максимальный объем (м³)
                                current_load:
                                  type: number
                                  description: Текущая загрузка (%)
                                available_slots:
                                  type: integer
                                  description: Доступные слоты для поставки
                      total:
                        type: integer
                        description: Общее количество складов
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/draft/create:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Создать черновик заявки на поставку
      description: |
        Создает черновик заявки на поставку товаров на склад FBO.
      operationId: FboSupplyAPI_CreateDraft
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                warehouse_id:
                  type: integer
                  format: int64
                  description: Идентификатор склада для поставки
                items:
                  type: array
                  items:
                    type: object
                    properties:
                      sku:
                        type: integer
                        format: int64
                        description: SKU товара
                      quantity:
                        type: integer
                        minimum: 1
                        description: Количество товара для поставки
                      price:
                        type: string
                        description: Цена товара
                delivery_date:
                  type: string
                  format: date
                  description: Предполагаемая дата поставки
                comment:
                  type: string
                  maxLength: 500
                  description: Комментарий к заявке
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      draft_id:
                        type: integer
                        format: int64
                        description: Идентификатор созданного черновика
                      status:
                        type: string
                        enum: [created, processing, error]
                        description: Статус создания черновика
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/draft/create/info:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Информация о черновике заявки на поставку
      description: |
        Получает подробную информацию о черновике заявки на поставку.
      operationId: FboSupplyAPI_GetDraftInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                draft_id:
                  type: integer
                  format: int64
                  description: Идентификатор черновика
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      draft_id:
                        type: integer
                        format: int64
                        description: Идентификатор черновика
                      warehouse_id:
                        type: integer
                        format: int64
                        description: Идентификатор склада
                      warehouse_name:
                        type: string
                        description: Название склада
                      status:
                        type: string
                        description: Статус черновика
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания черновика
                      updated_at:
                        type: string
                        format: date-time
                        description: Дата последнего обновления
                      delivery_date:
                        type: string
                        format: date
                        description: Предполагаемая дата поставки
                      comment:
                        type: string
                        description: Комментарий к заявке
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            sku:
                              type: integer
                              format: int64
                              description: SKU товара
                            offer_id:
                              type: string
                              description: Идентификатор товара в системе продавца
                            name:
                              type: string
                              description: Название товара
                            quantity:
                              type: integer
                              description: Количество товара
                            price:
                              type: string
                              description: Цена товара
                            total_price:
                              type: string
                              description: Общая стоимость позиции
                      total_items:
                        type: integer
                        description: Общее количество товаров
                      total_amount:
                        type: string
                        description: Общая сумма заявки
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/draft/timeslot/info:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Доступные таймслоты
      description: |
        Получает список доступных временных слотов для поставки по черновику.
      operationId: FboSupplyAPI_GetDraftTimeslots
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                draft_id:
                  type: integer
                  format: int64
                  description: Идентификатор черновика
                date_from:
                  type: string
                  format: date
                  description: Начальная дата поиска слотов
                date_to:
                  type: string
                  format: date
                  description: Конечная дата поиска слотов
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      timeslots:
                        type: array
                        items:
                          type: object
                          properties:
                            timeslot_id:
                              type: integer
                              format: int64
                              description: Идентификатор временного слота
                            date:
                              type: string
                              format: date
                              description: Дата слота
                            time_from:
                              type: string
                              format: time
                              description: Начальное время слота
                            time_to:
                              type: string
                              format: time
                              description: Конечное время слота
                            is_available:
                              type: boolean
                              description: Доступность слота
                            quota:
                              type: integer
                              description: Доступная квота
                            price:
                              type: string
                              description: Стоимость слота (если есть)
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/draft/supply/create:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Создать заявку на поставку по черновику
      description: |
        Создает официальную заявку на поставку на основе черновика.
      operationId: FboSupplyAPI_CreateSupplyFromDraft
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                draft_id:
                  type: integer
                  format: int64
                  description: Идентификатор черновика
                timeslot_id:
                  type: integer
                  format: int64
                  description: Идентификатор выбранного временного слота
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи создания заявки
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус создания заявки
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/draft/supply/create/status:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Информация о создании заявки на поставку
      description: |
        Получает статус создания заявки на поставку по задаче.
      operationId: FboSupplyAPI_GetSupplyCreateStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: integer
                  format: int64
                  description: Идентификатор задачи создания заявки
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус создания заявки
                      supply_order_id:
                        type: integer
                        format: int64
                        description: Идентификатор созданной заявки на поставку
                      supply_order_number:
                        type: string
                        description: Номер заявки на поставку
                      error_message:
                        type: string
                        description: Сообщение об ошибке (если есть)
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания задачи
                      completed_at:
                        type: string
                        format: date-time
                        description: Дата завершения задачи
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/cargoes/create:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Установка грузомест
      description: |
        Устанавливает грузоместа для заявки на поставку.
      operationId: FboSupplyAPI_CreateCargoes
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
                cargoes:
                  type: array
                  items:
                    type: object
                    properties:
                      cargo_number:
                        type: string
                        description: Номер грузоместа
                      weight:
                        type: number
                        description: Вес грузоместа (кг)
                      dimensions:
                        type: object
                        properties:
                          length:
                            type: number
                            description: Длина (см)
                          width:
                            type: number
                            description: Ширина (см)
                          height:
                            type: number
                            description: Высота (см)
                      items:
                        type: array
                        items:
                          type: object
                          properties:
                            sku:
                              type: integer
                              format: int64
                              description: SKU товара
                            quantity:
                              type: integer
                              description: Количество товара в грузоместе
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи установки грузомест
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус установки грузомест
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/cargoes/create/info:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Получить информацию по установке грузомест
      description: |
        Получает информацию о статусе установки грузомест.
      operationId: FboSupplyAPI_GetCargoesCreateInfo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: integer
                  format: int64
                  description: Идентификатор задачи установки грузомест
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус установки грузомест
                      cargoes_count:
                        type: integer
                        description: Количество установленных грузомест
                      error_message:
                        type: string
                        description: Сообщение об ошибке (если есть)
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания задачи
                      completed_at:
                        type: string
                        format: date-time
                        description: Дата завершения задачи
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/cargoes/delete:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Удалить грузоместо в заявке на поставку
      description: |
        Удаляет грузоместо из заявки на поставку.
      operationId: FboSupplyAPI_DeleteCargo
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
                cargo_id:
                  type: integer
                  format: int64
                  description: Идентификатор грузоместа для удаления
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи удаления грузоместа
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус удаления грузоместа
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/cargoes/delete/status:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Информация о статусе удаления грузоместа
      description: |
        Получает информацию о статусе удаления грузоместа.
      operationId: FboSupplyAPI_GetCargoDeleteStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: integer
                  format: int64
                  description: Идентификатор задачи удаления грузоместа
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус удаления грузоместа
                      cargo_id:
                        type: integer
                        format: int64
                        description: Идентификатор удаленного грузоместа
                      error_message:
                        type: string
                        description: Сообщение об ошибке (если есть)
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания задачи
                      completed_at:
                        type: string
                        format: date-time
                        description: Дата завершения задачи
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/cargoes/rules/get:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Чек-лист по установке грузомест FBO
      description: |
        Получает чек-лист с правилами и требованиями по установке грузомест для FBO.
      operationId: FboSupplyAPI_GetCargoRules
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                warehouse_id:
                  type: integer
                  format: int64
                  description: Идентификатор склада
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      rules:
                        type: array
                        items:
                          type: object
                          properties:
                            rule_id:
                              type: integer
                              format: int64
                              description: Идентификатор правила
                            title:
                              type: string
                              description: Заголовок правила
                            description:
                              type: string
                              description: Описание правила
                            is_mandatory:
                              type: boolean
                              description: Обязательность выполнения правила
                            category:
                              type: string
                              enum: [packaging, labeling, dimensions, weight, documentation]
                              description: Категория правила
                      warehouse_requirements:
                        type: object
                        properties:
                          max_cargo_weight:
                            type: number
                            description: Максимальный вес грузоместа (кг)
                          max_dimensions:
                            type: object
                            properties:
                              length:
                                type: number
                              width:
                                type: number
                              height:
                                type: number
                            description: Максимальные размеры (см)
                          allowed_packaging_types:
                            type: array
                            items:
                              type: string
                            description: Разрешенные типы упаковки
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/cargoes-label/create:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Сгенерировать этикетки для грузомест
      description: |
        Генерирует этикетки для грузомест в заявке на поставку.
      operationId: FboSupplyAPI_CreateCargoLabels
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
                cargo_ids:
                  type: array
                  items:
                    type: integer
                    format: int64
                  description: Список идентификаторов грузомест для генерации этикеток
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи генерации этикеток
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус генерации этикеток
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/cargoes-label/get:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Получить идентификатор этикетки для грузомест
      description: |
        Получает идентификатор файла с этикетками для грузомест.
      operationId: FboSupplyAPI_GetCargoLabels
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: integer
                  format: int64
                  description: Идентификатор задачи генерации этикеток
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус генерации этикеток
                      file_guid:
                        type: string
                        description: Идентификатор файла с этикетками
                      file_name:
                        type: string
                        description: Название файла с этикетками
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания задачи
                      completed_at:
                        type: string
                        format: date-time
                        description: Дата завершения задачи
                      error_message:
                        type: string
                        description: Сообщение об ошибке (если есть)
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/cargoes-label/file/{file_guid}:
    get:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Получить PDF с этикетками грузовых мест
      description: |
        Скачивает PDF файл с этикетками для грузовых мест.
      operationId: FboSupplyAPI_DownloadCargoLabels
      parameters:
        - name: file_guid
          in: path
          required: true
          schema:
            type: string
          description: Идентификатор файла с этикетками
      responses:
        '200':
          description: PDF файл с этикетками
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Файл не найден
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/cancel:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Отменить заявку на поставку
      description: |
        Отменяет заявку на поставку товаров на склад FBO.
      operationId: FboSupplyAPI_CancelSupplyOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
                cancel_reason:
                  type: string
                  maxLength: 500
                  description: Причина отмены заявки
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи отмены заявки
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус отмены заявки
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/cancel/status:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Получить статус отмены заявки на поставку
      description: |
        Получает статус отмены заявки на поставку.
      operationId: FboSupplyAPI_GetSupplyOrderCancelStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: integer
                  format: int64
                  description: Идентификатор задачи отмены заявки
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус отмены заявки
                      supply_order_id:
                        type: integer
                        format: int64
                        description: Идентификатор заявки на поставку
                      cancel_reason:
                        type: string
                        description: Причина отмены заявки
                      error_message:
                        type: string
                        description: Сообщение об ошибке (если есть)
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания задачи
                      completed_at:
                        type: string
                        format: date-time
                        description: Дата завершения задачи
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/content/update:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Редактирование товарного состава
      description: |
        Редактирует товарный состав заявки на поставку.
      operationId: FboSupplyAPI_UpdateSupplyOrderContent
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                supply_order_id:
                  type: integer
                  format: int64
                  description: Идентификатор заявки на поставку
                items:
                  type: array
                  items:
                    type: object
                    properties:
                      sku:
                        type: integer
                        format: int64
                        description: SKU товара
                      quantity:
                        type: integer
                        minimum: 0
                        description: Новое количество товара (0 для удаления)
                      price:
                        type: string
                        description: Новая цена товара
                  description: Список товаров для обновления
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи редактирования состава
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус редактирования состава
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/supply-order/content/update/status:
    post:
      tags:
        - Создание и управление заявками на поставку FBO
      summary: Информация о статусе редактирования товарного состава
      description: |
        Получает информацию о статусе редактирования товарного состава заявки.
      operationId: FboSupplyAPI_GetSupplyOrderContentUpdateStatus
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                task_id:
                  type: integer
                  format: int64
                  description: Идентификатор задачи редактирования состава
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      task_id:
                        type: integer
                        format: int64
                        description: Идентификатор задачи
                      status:
                        type: string
                        enum: [processing, completed, error]
                        description: Статус редактирования состава
                      supply_order_id:
                        type: integer
                        format: int64
                        description: Идентификатор заявки на поставку
                      updated_items_count:
                        type: integer
                        description: Количество обновленных товаров
                      error_message:
                        type: string
                        description: Сообщение об ошибке (если есть)
                      created_at:
                        type: string
                        format: date-time
                        description: Дата создания задачи
                      completed_at:
                        type: string
                        format: date-time
                        description: Дата завершения задачи
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/rating/summary:
    post:
      tags:
        - Рейтинг продавца
      summary: Получить информацию о текущих рейтингах продавца
      description: |
        Получает информацию о текущих рейтингах продавца по различным показателям.
      operationId: SellerRatingAPI_GetRatingSummary
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties: {}
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      groups:
                        type: array
                        items:
                          type: object
                          properties:
                            group_name:
                              type: string
                              description: Название группы показателей
                            items:
                              type: array
                              items:
                                type: object
                                properties:
                                  name:
                                    type: string
                                    description: Название показателя
                                  value:
                                    type: number
                                    format: float
                                    description: Значение показателя
                                  penalty:
                                    type: number
                                    format: float
                                    description: Штраф по показателю
                                  premium:
                                    type: number
                                    format: float
                                    description: Премия по показателю
                                  status:
                                    type: string
                                    enum: [excellent, good, acceptable, critical]
                                    description: Статус показателя
                                  direction:
                                    type: string
                                    enum: [higher_better, lower_better]
                                    description: Направление улучшения показателя
                              description: Список показателей в группе
                        description: Группы показателей рейтинга
                      rating:
                        type: number
                        format: float
                        description: Общий рейтинг продавца
                      rating_period:
                        type: object
                        properties:
                          from:
                            type: string
                            format: date
                            description: Начало периода расчета рейтинга
                          to:
                            type: string
                            format: date
                            description: Конец периода расчета рейтинга
                        description: Период расчета рейтинга
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v1/rating/history:
    post:
      tags:
        - Рейтинг продавца
      summary: Получить информацию о рейтингах продавца за период
      description: |
        Получает историю изменения рейтингов продавца за указанный период.
      operationId: SellerRatingAPI_GetRatingHistory
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                date_from:
                  type: string
                  format: date
                  description: Дата начала периода
                date_to:
                  type: string
                  format: date
                  description: Дата окончания периода
                metrics:
                  type: array
                  items:
                    type: string
                  description: Список метрик для получения истории
                limit:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  default: 100
                  description: Количество записей в ответе
                offset:
                  type: integer
                  minimum: 0
                  default: 0
                  description: Смещение для пагинации
              required:
                - date_from
                - date_to
      responses:
        '200':
          description: Успешный ответ
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      rows:
                        type: array
                        items:
                          type: object
                          properties:
                            date:
                              type: string
                              format: date
                              description: Дата записи
                            rating:
                              type: number
                              format: float
                              description: Общий рейтинг на дату
                            metrics:
                              type: object
                              additionalProperties:
                                type: object
                                properties:
                                  value:
                                    type: number
                                    format: float
                                    description: Значение метрики
                                  penalty:
                                    type: number
                                    format: float
                                    description: Штраф по метрике
                                  premium:
                                    type: number
                                    format: float
                                    description: Премия по метрике
                                  status:
                                    type: string
                                    enum: [excellent, good, acceptable, critical]
                                    description: Статус метрики
                              description: Значения метрик на дату
                        description: История рейтингов
                      total:
                        type: integer
                        description: Общее количество записей
        '400':
          description: Неверный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

tags:
  - name: Атрибуты и характеристики Ozon
    description: Методы для работы с атрибутами и характеристиками товаров
  - name: Загрузка и обновление товаров
    description: Методы для загрузки и обновления товаров
  - name: Штрихкоды товаров
    description: Методы для работы со штрихкодами
  - name: Цены и остатки товаров
    description: Методы для управления ценами и остатками
  - name: Акции
    description: Методы для работы с акциями
  - name: Стратегии ценообразования
    description: Методы для настройки стратегий ценообразования
  - name: Сертификаты брендов
    description: Методы для работы с сертификатами брендов
  - name: Сертификаты качества
    description: Методы для работы с сертификатами качества
  - name: Склады
    description: Методы для работы со складами
  - name: Обработка заказов FBS и rFBS
    description: Методы для обработки заказов FBS и rFBS
  - name: Полигоны
    description: Методы для работы с полигонами
  - name: Доставка FBO
    description: Методы для работы с доставкой FBO
  - name: Создание и управление заявками на поставку FBO
    description: Методы для создания и управления заявками на поставку FBO
  - name: Управление кодами маркировки и сборкой заказов для FBS/rFBS
    description: Методы для управления кодами маркировки и сборкой заказов
  - name: Доставка FBS
    description: Методы для работы с доставкой FBS
  - name: Доставка rFBS
    description: Методы для работы с доставкой rFBS
  - name: Пропуски
    description: Методы для работы с пропусками
  - name: Возвраты товаров FBO и FBS
    description: Методы для работы с возвратами товаров FBO и FBS
  - name: Возвраты товаров rFBS
    description: Методы для работы с возвратами товаров rFBS
  - name: Возвратные отгрузки
    description: Методы для работы с возвратными отгрузками
  - name: Отмены заказов
    description: Методы для работы с отменами заказов
  - name: Чаты с покупателями
    description: Методы для работы с чатами с покупателями
  - name: Накладные
    description: Методы для работы с накладными
  - name: Отчёты
    description: Методы для получения отчётов
  - name: Аналитические отчёты
    description: Методы для получения аналитических отчётов
  - name: Финансовые отчёты
    description: Методы для получения финансовых отчётов
  - name: Рейтинг продавца
    description: Методы для работы с рейтингом продавца
