# 每日广告花费报告功能

## 功能概述

新增的每日广告花费报告功能会在每天中国时间早上8点自动统计前一天（莫斯科时间）所有广告活动的总花费，并通过钉钉机器人发送汇总报告。

## 功能特性

### 1. 自动定时执行
- **触发时间**: 每天中国时间早上8点 (UTC+8)
- **统计范围**: 莫斯科时间前一天的广告花费数据
- **执行方式**: 使用 `github.com/robfig/cron/v3` 定时任务

### 2. 数据统计
- 统计所有监控产品的广告花费
- 计算总花费金额（卢布）
- 统计活跃广告数量和总广告数量
- 计算广告活跃率

### 3. 钉钉通知
- 自动发送Markdown格式的报告
- 包含详细的统计信息
- 支持成功和失败两种通知模式

### 4. 手动触发
- 提供API端点支持手动触发报告生成
- 便于测试和紧急情况下的手动执行

## 技术实现

### 1. 服务结构扩展

在 `ProductMonitorService` 中新增了以下字段：
```go
dailyReportScheduler *cron.Cron // 每日广告花费报告定时器
```

### 2. 核心方法

#### 启动服务
```go
func (s *ProductMonitorService) StartDailyReportService()
```

#### 停止服务
```go
func (s *ProductMonitorService) StopDailyReportService()
```

#### 生成报告
```go
func (s *ProductMonitorService) generateDailyAdvertSpendReport()
```

#### 发送通知
```go
func (s *ProductMonitorService) sendDailyReportNotification(date string, totalSpend float64, activeAdverts, totalAdverts int, success bool, errorMsg string)
```

#### 手动触发
```go
func (s *ProductMonitorService) TriggerDailyReport()
```

### 3. 时区处理

- **定时器时区**: 中国时间 (Asia/Shanghai)
- **数据统计时区**: 莫斯科时间 (Europe/Moscow)
- **时间计算**: 以莫斯科时间为基准计算前一天的日期范围

### 4. API集成

复用现有的基础设施组件：
- `s.wbhubClient.Promotion.GetFullStats()` - 获取广告统计数据
- `s.dingTalkRobot` - 发送钉钉通知
- `s.products` - 获取监控产品和广告信息

## 使用方法

### 1. 自动启动

服务会在 `monitor-product` 服务启动时自动启动：

```go
// 启动每日广告花费报告服务
monitorService.StartDailyReportService()
log.Println("每日广告花费报告服务已启动")
```

### 2. 手动触发

通过API端点手动触发报告生成：

```bash
curl -X POST http://localhost:8081/api/monitor/product/report/daily/trigger
```

响应：
```json
{
  "success": true,
  "message": "每日广告花费报告任务已触发"
}
```

### 3. 钉钉通知格式

#### 成功报告示例
```markdown
📊 每日广告花费报告

📅 日期: 2024-01-14 (莫斯科时间)

💰 总花费: 1,234.56 卢布

📈 广告统计:
- 活跃广告: 15 个
- 总广告数: 20 个
- 活跃率: 75.0%

⏰ 报告时间: 2024-01-15 08:00:05 (中国时间)

---
此报告由系统自动生成
```

#### 失败报告示例
```markdown
❌ 每日广告花费报告生成失败

📅 日期: 2024-01-14 (莫斯科时间)

❌ 错误信息: 获取广告统计数据失败: API调用超时

📊 统计范围: 20 个广告

⏰ 报告时间: 2024-01-15 08:00:05 (中国时间)

---
请检查系统状态并手动重试
```

## 配置要求

确保以下配置项已正确设置：

```env
# 钉钉配置
DINGTALK_ACCESS_TOKEN=your_access_token
DINGTALK_SECRET=your_secret

# WB Hub API配置
WBHUB_API_KEY=your_api_key
```

## 错误处理

1. **API调用失败**: 自动重试机制，失败时发送错误通知
2. **时区加载失败**: 降级使用UTC时区
3. **钉钉机器人未初始化**: 跳过通知发送，仅记录日志
4. **无广告数据**: 发送空报告通知

## 日志记录

所有关键操作都会记录详细日志：
- 服务启动/停止
- 报告生成过程
- API调用结果
- 钉钉通知发送状态

## 测试

运行测试：
```bash
go test ./internal/service/test -v -run TestDailyReportService
```

## 注意事项

1. 确保系统时间准确，特别是时区设置
2. 钉钉机器人需要有发送消息的权限
3. WB Hub API需要有获取广告统计数据的权限
4. 建议在生产环境部署前先进行手动触发测试
