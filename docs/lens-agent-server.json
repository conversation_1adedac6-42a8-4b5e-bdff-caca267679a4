{"openapi": "3.1.0", "info": {"title": "Wildberries关键词分析服务", "description": "通过广告ID查询关键词排除情况的微服务", "version": "1.0.0"}, "paths": {"/api/v1/health": {"get": {"summary": "Health Check", "description": "健康检查端点", "operationId": "health_check_api_v1_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthResponse"}}}}}}}, "/api/v1/info": {"get": {"summary": "Service Info", "description": "服务信息端点", "operationId": "service_info_api_v1_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceInfoResponse"}}}}}}}, "/api/v1/campaign/{campaign_id}/keywords": {"get": {"summary": "Get Campaign Keywords Analysis", "description": "通过广告活动ID查询关键词排除情况\n\nArgs:\n    campaign_id: 广告活动ID\n    \nReturns:\n    CampaignAnalysisResponse: 包含关键词分析结果的响应", "operationId": "get_campaign_keywords_analysis_api_v1_campaign__campaign_id__keywords_get", "parameters": [{"name": "campaign_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Campaign Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CampaignAnalysisResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"CampaignAnalysisResponse": {"properties": {"campaign_id": {"type": "integer", "title": "Campaign Id", "description": "广告活动ID"}, "total_keywords": {"type": "integer", "title": "Total Keywords", "description": "关键词总数"}, "keywords": {"items": {"$ref": "#/components/schemas/KeywordAnalysisResponse"}, "type": "array", "title": "Keywords", "description": "关键词分析结果"}, "summary": {"type": "object", "title": "Summary", "description": "统计摘要"}}, "type": "object", "required": ["campaign_id", "total_keywords", "keywords", "summary"], "title": "CampaignAnalysisResponse", "description": "广告活动分析响应模型"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HealthResponse": {"properties": {"status": {"type": "string", "title": "Status", "description": "服务状态", "default": "healthy"}, "service": {"type": "string", "title": "Service", "description": "服务名称"}, "timestamp": {"type": "string", "title": "Timestamp", "description": "检查时间"}}, "type": "object", "required": ["service", "timestamp"], "title": "HealthResponse", "description": "健康检查响应模型"}, "KeywordAnalysisResponse": {"properties": {"campaign_id": {"type": "integer", "title": "Campaign Id", "description": "广告活动ID"}, "nm_id": {"type": "integer", "title": "Nm Id", "description": "商品ID"}, "keyword": {"type": "string", "title": "Keyword", "description": "关键词"}, "avg_similarity": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Avg Similarity", "description": "平均相似度"}, "similar_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Similar Count", "description": "相似商品数量"}, "competitor_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Competitor Count", "description": "竞争对手数量"}, "valid_scores": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Valid Scores", "description": "有效评分数"}, "views": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Views", "description": "展示次数"}, "sum": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Sum", "description": "花费金额"}, "clicks": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>licks", "description": "点击次数"}, "ctr": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Ctr", "description": "点击率"}, "cpc": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Cpc", "description": "平均点击成本"}, "count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Count", "description": "关键词热度"}, "order_generating": {"type": "boolean", "title": "Order Generating", "description": "是否为出单关键词", "default": false}, "exclude_irrelevant": {"type": "boolean", "title": "Exclude Irrelevant", "description": "排除不相关", "default": false}, "exclude_zero_click": {"type": "boolean", "title": "Exclude <PERSON>lick", "description": "排除零点击", "default": false}, "exclude_no_data": {"type": "boolean", "title": "Exclude No Data", "description": "排除无数据", "default": false}, "observe_low_ctr": {"type": "boolean", "title": "Observe Low Ctr", "description": "观察低CTR", "default": false}, "observe_high_cpc": {"type": "boolean", "title": "Observe High Cpc", "description": "观察高CPC", "default": false}, "observe_low_similarity": {"type": "boolean", "title": "Observe Low Similarity", "description": "观察低相似度", "default": false}, "optimize_low_perf": {"type": "boolean", "title": "Optimize Low Perf", "description": "优化低表现", "default": false}, "keep": {"type": "boolean", "title": "Keep", "description": "保持不变", "default": true}, "should_exclude": {"type": "boolean", "title": "Should Exclude", "description": "是否应该排除", "default": false}}, "type": "object", "required": ["campaign_id", "nm_id", "keyword"], "title": "KeywordAnalysisResponse", "description": "关键词分析响应模型"}, "ServiceInfoResponse": {"properties": {"service": {"type": "string", "title": "Service", "description": "服务名称"}, "version": {"type": "string", "title": "Version", "description": "服务版本"}, "environment": {"type": "string", "title": "Environment", "description": "运行环境"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "服务标签"}}, "type": "object", "required": ["service", "version", "environment", "tags"], "title": "ServiceInfoResponse", "description": "服务信息响应模型"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}