# Lens Project

This project provides APIs and services for interacting with Wildberries marketplace.

## Project Structure

```
lens/
├── cmd/                    # Application entry points
│   ├── crawler-keyword/    # Keyword crawler service
│   ├── monitor-product/    # Product monitoring service
│   ├── advert-manager/     # Advertisement management service
│   └── warehouse-service/  # Warehouse service
├── internal/               # Internal packages (not exported)
│   ├── api/                # API clients
│   │   ├── wb_buyer/       # Buyer API clients (Wildberries buyer platform)
│   │   ├── wb_seller/      # Seller API clients (Wildberries seller platform)
│   │   ├── wb/             # Official Wildberries API clients
│   │   ├── ozon/           # Official Ozon API clients
│   │   ├── ozon_buyer/     # Buyer API clients (Ozon buyer platform)
│   │   ├── ozon_seller/    # Seller API clients (Ozon seller platform)
│   │   └── monitor/        # External API services (Provided to external users)
│   ├── service/            # Business logic services
│   └── infrastructure/     # Infrastructure components
├── pkg/                    # Public packages (exported)
├── configs/                # Configuration files
├── docs/                   # Documentation
├── go.mod                  # Go module definition
└── go.sum                  # Go module checksums
```

## Services

### 1. Keyword Crawler (cmd/crawler-keyword)
Crawls trending keywords from Wildberries on a weekly basis.

### 2. Product Monitor (cmd/monitor-product)
Monitors product rankings and performance metrics.

### 3. Advertisement Manager (cmd/advert-manager)
Manages advertising campaigns on Wildberries.

### 4. Warehouse Service (cmd/warehouse-service)
Handles warehouse-related operations.

## Installation

```bash
go mod tidy
```

## Configuration

Each service now uses its own configuration file located in the `configs/` directory:

- `configs/advert-manager.env` - Advertisement manager service configuration
- `configs/crawler-keyword.env` - Keyword crawler service configuration
- `configs/monitor-product.env` - Product monitor service configuration
- `configs/warehouse-service.env` - Warehouse service configuration

See `configs/README.md` for detailed configuration options.

Copy the example configuration and modify as needed:

```bash
# Each service will automatically load its corresponding config file
# For example, advert-manager service will load configs/advert-manager.env
```

## Running Services

Each service can be run independently:

```bash
# Run keyword crawler
go run cmd/crawler-keyword/main.go

# Run product monitor
go run cmd/monitor-product/main.go

# Run advertisement manager
go run cmd/advert-manager/main.go

# Run warehouse service
go run cmd/warehouse-service/main.go
```

## API Classification

This project classifies APIs according to different platforms and purposes:

1. **WB Buyer API (internal/api/wb_buyer/)**
   - Used for interacting with the Wildberries buyer platform
   - Mainly used for search and recommendation functions
   - Simulates buyer behavior for data crawling

2. **WB Seller API (internal/api/wb_seller/)**
   - Used for interacting with the Wildberries seller platform
   - Provides seller functions such as supply management, reporting, and warehouse management
   - Requires seller authentication information

3. **WB Official API (internal/api/wb/)**
   - Official Wildberries API client
   - Includes promotion services and product card services
   - Uses official API keys for authentication

4. **Ozon API (internal/api/ozon/)**
   - Official Ozon marketplace API client
   - Provides access to Ozon's product, order, and analytics services
   - Uses official API keys for authentication

5. **Ozon Buyer API (internal/api/ozon_buyer/)**
   - Used for interacting with the Ozon buyer platform
   - Mainly used for search and recommendation functions
   - Simulates buyer behavior for data crawling

6. **Ozon Seller API (internal/api/ozon_seller/)**
   - Used for interacting with the Ozon seller platform
   - Provides seller functions such as product management, order processing, and reporting
   - Requires seller authentication information

7. **External API (internal/api/monitor/)**
   - API services provided to external users
   - Includes product monitoring and keyword management functions
   - Provides HTTP and WebSocket interfaces

## Docker Build and Deployment

This project supports Docker-based deployment with each service having its own Dockerfile.

### Prerequisites

- Docker
- Docker Compose

### Building Services

1. First, build all service binaries:
```bash
./build-docker.sh
```

2. Build and run all services with Docker Compose:
```bash
docker-compose up --build
```

### Individual Service Deployment

Each service can be built and run individually:

```bash
# Build a specific service
docker build -t lens/advert-manager -f cmd/advert-manager/Dockerfile .

# Run a specific service
docker run -p 8080:8080 lens/advert-manager
```

### Time Zone Configuration

All services are configured to use Moscow time (Europe/Moscow) by default in their Docker containers.