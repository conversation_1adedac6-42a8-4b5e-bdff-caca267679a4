# Monitor Product Service Configuration

# Redis Configuration
REDIS_ADDRESS=************:6379
REDIS_PASSWORD=Ls3903850
REDIS_DB=0

# RocketMQ Configuration
ROCKETMQ_NAMESERVER=************:9876
ROCKETMQ_GROUP=lens-group

# API Keys
WBHUB_API_KEY=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Service Configuration
MONITOR_INTERVAL=5m
CRAWLER_INTERVAL=1h

# DingTalk Configuration
DINGTALK_ACCESS_TOKEN=8d6dda43349d5a12d8c6fa5de3b26cb5c489f7809251d85fce3e6a2a5218bb7a
DINGTALK_SECRET=SECc89fb889b0a505ff11492d69927d7bac36d644e39dcdc2cb7a4a37a2a2870c33

# MyAgent Configuration
MY_AGENT_BASE_URL=http://************:8002

# Logging
LOG_LEVEL=info
LOG_FORMAT=json
SERVER_PORT=8081