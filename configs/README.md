# 项目配置说明

本项目采用统一的配置管理方式，每个服务都有独立的配置文件。

## 配置文件结构

所有配置文件都位于 `configs/` 目录下：

- `configs/.env.example` - 配置文件示例
- `configs/advert-manager.env` - 广告管理服务配置
- `configs/crawler-keyword.env` - 关键词爬取服务配置
- `configs/monitor-product.env` - 产品监控服务配置
- `configs/warehouse-service.env` - 仓库服务配置

## 配置项说明

### Redis 配置
- `REDIS_ADDRESS` - Redis 服务器地址
- `REDIS_PASSWORD` - Redis 密码
- `REDIS_DB` - Redis 数据库编号

### RocketMQ 配置
- `ROCKETMQ_NAMESERVER` - RocketMQ NameServer 地址
- `ROCKETMQ_GROUP` - RocketMQ 消费者组名称

### API 密钥
- `WB_API_KEY` - Wildberries API 密钥

### 服务配置
- `MONITOR_INTERVAL` - 监控间隔
- `CRAWLER_INTERVAL` - 爬取间隔
- `SERVER_PORT` - 服务监听端口

### 日志配置
- `LOG_LEVEL` - 日志级别
- `LOG_FORMAT` - 日志格式

## 使用方法

每个服务在启动时会自动加载对应的配置文件。例如，广告管理服务会加载 `configs/advert-manager.env` 文件。

要运行服务，请确保对应的配置文件存在且配置正确。

## 添加新服务

要为新服务添加配置：

1. 在 `configs/` 目录下创建新的 `.env` 文件
2. 参考 `.env.example` 文件添加所需配置项
3. 在服务的 `main.go` 文件中添加配置加载代码：

```go
// 加载配置文件
cfg, err := config.LoadFromFile("./configs/your-service.env")
if err != nil {
    log.Fatalf("加载配置文件失败: %v", err)
}

// 验证配置
if err := cfg.Validate(); err != nil {
    log.Fatalf("配置验证失败: %v", err)
}
```

4. 在需要的地方使用配置项，例如：
```go
// 使用配置化的 Redis 客户端
redisClient := redis.GetInstanceWithConfig(cfg)

// 使用配置化的 RocketMQ 客户端
rocketClient := rocket_mq.GetInstance()
rocketClient.InitProducerWithConfig(cfg)
```