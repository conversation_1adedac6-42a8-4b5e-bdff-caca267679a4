package main

import (
	"context"
	"fmt"
	"lens/internal/config"
	"lens/internal/service"
	wb_api "lens/internal/api/seller"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

// 计算下一个周一凌晨1点（莫斯科时间）
func getNextMondayOneAM() time.Time {
	loc, err := time.LoadLocation("Europe/Moscow")
	if err != nil {
		log.Printf("加载莫斯科时区失败，使用UTC+3: %v\n", err)
		loc = time.FixedZone("MSK", 3*60*60) // UTC+3
	}

	now := time.Now().In(loc)
	weekday := now.Weekday()
	daysUntilMonday := int(time.Monday - weekday)
	if daysUntilMonday <= 0 {
		daysUntilMonday += 7
	}

	// 如果今天是周一且还未到凌晨1点，不需要等到下周
	if weekday == time.Monday && now.Hour() < 1 {
		daysUntilMonday = 0
	}

	nextMonday := now.AddDate(0, 0, daysUntilMonday)
	nextMondayOneAM := time.Date(
		nextMonday.Year(),
		nextMonday.Month(),
		nextMonday.Day(),
		1, 0, 0, 0,
		loc,
	)

	// 如果计算出的时间已经过去，加上一周
	if nextMondayOneAM.Before(now) {
		nextMondayOneAM = nextMondayOneAM.AddDate(0, 0, 7)
	}

	return nextMondayOneAM
}

func main() {
	// 加载配置文件
	cfg, err := config.LoadFromFile("./configs/crawler-keyword.env")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 初始化服务
	api := wb_api.Init()
	keywordService := service.NewCrawlerKeywordService(api)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 计算首次执行时间
	nextRun := getNextMondayOneAM()
	log.Printf("下次执行时间: %v\n", nextRun.Format("2006-01-02 15:04:05 MST"))

	// 创建定时器
	timer := time.NewTimer(time.Until(nextRun))
	defer timer.Stop()

	// 主循环
	for {
		select {
		case <-timer.C:
			// 执行爬取任务
			if err := keywordService.CrawlKeywords(ctx); err != nil {
				log.Printf("关键词爬取失败: %v\n", err)
			}

			// 计算下次执行时间（下周一凌晨1点）
			nextRun = getNextMondayOneAM()
			log.Printf("下次执行时间: %v\n", nextRun.Format("2006-01-02 15:04:05 MST"))
			timer.Reset(time.Until(nextRun))

		case sig := <-sigChan:
			fmt.Printf("收到信号: %v\n", sig)
			return
		case <-ctx.Done():
			fmt.Println("上下文已取消")
			return
		}
	}
}
