package main

import (
	"context"
	"encoding/json"
	"fmt"
	"lens/internal/api/monitor/product/handlers"
	"lens/internal/api/monitor/product/middleware"
	"lens/internal/config"
	"lens/internal/infrastructure/redis"
	rocket_mq "lens/internal/infrastructure/rocketmq"
	"lens/internal/service"
	"log"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	REDIS_MONITOR_PRODUCTS_KEY = "monitor:products" // Redis中存储监控产品配置的key
)

var (
	broadcast      = make(chan []byte)            // 广播通道
	monitorService *service.ProductMonitorService // 全局监控服务实例
)

// waitForNextMinute 等待到下一个整分钟
func waitForNextMinute() {
	now := time.Now()
	next := now.Truncate(time.Minute).Add(time.Minute)
	duration := next.Sub(now)
	log.Printf("等待 %v 秒后在整分钟时启动监控服务...\n", duration.Seconds())
	time.Sleep(duration)
}

// loadProductsFromRedis 从Redis加载产品配置，带重试机制
func loadProductsFromRedis(ctx context.Context, redisClient *redis.RedisClient) ([]byte, error) {
	const maxRetries = 3
	const retryDelay = 2 * time.Second

	var lastErr error
	for attempt := 1; attempt <= maxRetries; attempt++ {
		// 创建带超时的上下文
		ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)

		// 从Redis获取所有产品配置
		configs, err := redisClient.HGetAll(ctxWithTimeout, REDIS_MONITOR_PRODUCTS_KEY)
		cancel()

		if err == nil {
			// 如果没有配置，返回空map
			if len(configs) == 0 {
				log.Println("Redis中没有找到产品配置，返回空配置")
				return []byte("{}"), nil
			}

			// 将配置转换为MonitorProductMap格式
			productsMap := make(map[string]json.RawMessage)
			for productId, configJSON := range configs {
				// 验证JSON格式
				if !json.Valid([]byte(configJSON)) {
					log.Printf("警告: 产品 %s 的配置JSON格式无效，跳过", productId)
					continue
				}
				productsMap[productId] = json.RawMessage(configJSON)
			}

			// 转换为JSON字节数组
			result, marshalErr := json.Marshal(productsMap)
			if marshalErr != nil {
				return nil, fmt.Errorf("序列化产品配置失败: %v", marshalErr)
			}

			log.Printf("成功从Redis加载 %d 个产品配置", len(productsMap))
			return result, nil
		}

		lastErr = err
		log.Printf("从Redis加载产品配置失败 (尝试 %d/%d): %v", attempt, maxRetries, err)

		if attempt < maxRetries {
			log.Printf("等待 %v 后重试...", retryDelay)
			time.Sleep(retryDelay)
		}
	}

	return nil, fmt.Errorf("经过 %d 次重试后仍然失败: %v", maxRetries, lastErr)
}

// initGin 初始化Gin框架
func initGin() *gin.Engine {
	// 根据环境变量设置Gin模式
	ginMode := strings.ToLower(os.Getenv("GIN_MODE"))
	switch ginMode {
	case "debug":
		gin.SetMode(gin.DebugMode)
	case "test":
		gin.SetMode(gin.TestMode)
	default:
		// 默认使用生产模式
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建gin实例
	router := gin.New()

	// 使用自定义Recovery中间件
	router.Use(gin.Recovery())

	// 在非生产模式下启用Logger中间件
	if ginMode == "debug" {
		router.Use(gin.Logger())
	}

	return router
}

func main() {
	// 加载配置文件
	cfg, err := config.LoadFromFile("./configs/monitor-product.env")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 获取Redis客户端实例
	redisClient := redis.GetInstanceWithConfig(cfg)
	ctx := context.Background()

	// 初始化产品监控服务，使用配置文件中的监控间隔
	monitorService = service.NewProductMonitorService(cfg.MonitorInterval, broadcast, cfg)

	// 从Redis加载产品配置
	productsData, err := loadProductsFromRedis(ctx, redisClient)
	if err != nil {
		log.Printf("从Redis加载产品配置失败: %v", err)
	} else {
		// 更新监控服务的产品配置
		if err := monitorService.UpdateProducts(productsData); err != nil {
			log.Printf("更新监控产品数据失败: %v", err)
		}
	}

	// 初始化处理器
	keywordHandler := handlers.NewKeywordHandler(monitorService)
	productHandler := handlers.NewProductHandler(monitorService)
	wsHandler := handlers.NewWebSocketHandler(broadcast)

	// 启动 websocket 广播协程
	go wsHandler.HandleBroadcast()

	//同步广告信息
	monitorService.SyncProductAdverts()

	// 等待到下一个整分钟
	waitForNextMinute()
	// 启动监控服务
	monitorService.StartMonitoring()
	log.Printf("产品监控服务已启动，监控间隔: %v\n", monitorService.GetInterval())
	monitorService.StartStatsMonitoring()

	// 启动自动出价调整服务
	monitorService.StartAutoBidAdjustment()
	log.Println("自动出价调整服务已启动")

	// 启动关键词排除服务
	//monitorService.StartKeywordExclusionService()
	//log.Println("关键词排除服务已启动")

	// 启动每日广告花费报告服务
	monitorService.StartDailyReportService()
	log.Println("每日广告花费报告服务已启动")

	// 初始化 Gin 路由
	router := initGin()

	// 添加 CORS 中间件
	router.Use(middleware.CORS())

	// API 路由组
	api := router.Group("/api")
	{
		// 产品管理路由
		products := api.Group("/monitor/product")
		{
			products.POST("/create", productHandler.CreateProduct)       // 创建产品
			products.DELETE("/:productId", productHandler.DeleteProduct) // 删除产品
			products.GET("/:productId", productHandler.GetProduct)       // 获取单个产品
			products.GET("", productHandler.GetAllProducts)              // 获取所有产品

			// 关键词管理路由
			products.POST("/keywords/append", keywordHandler.AppendKeywords)
			products.POST("/keywords/remove", keywordHandler.RemoveKeywords)

			// 手动触发关键词排除
			products.POST("/keywords/exclude/trigger", func(c *gin.Context) {
				log.Printf("收到手动触发关键词排除请求")
				monitorService.TriggerKeywordExclusion()
				c.JSON(200, gin.H{
					"success": true,
					"message": "关键词排除任务已触发",
				})
			})

			// 手动触发每日广告花费报告
			products.POST("/report/daily/trigger", func(c *gin.Context) {
				log.Printf("收到手动触发每日报告请求")
				go monitorService.TriggerDailyReport()
				c.JSON(200, gin.H{
					"success": true,
					"message": "每日广告花费报告任务已触发",
				})
			})
		}
	}

	// WebSocket 处理
	router.GET("/ws", func(c *gin.Context) {
		wsHandler.HandleConnection(c.Writer, c.Request)
	})

	// 启动 HTTP 服务器
	go func() {
		log.Printf("HTTP 服务已启动，监听端口: %d", cfg.ServerPort)
		if err := router.Run(fmt.Sprintf(":%d", cfg.ServerPort)); err != nil {
			log.Fatalf("HTTP 服务启动失败: %v", err)
		}
	}()

	// 注册信号处理
	stopChan := setupSignalHandler()

	// 程序保持运行，直到收到停止信号
	<-stopChan
}

// 设置信号处理函数，用于优雅关闭资源
func setupSignalHandler() chan struct{} {
	stopChan := make(chan struct{})
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM, syscall.SIGINT)

	go func() {
		sig := <-sigChan
		log.Printf("收到退出信号 [%v]，开始清理资源...\n", sig)

		// 停止监控服务
		monitorService.StopMonitoring()
		log.Println("监控服务已停止")

		// 关闭RocketMQ生产者
		client := rocket_mq.GetInstance()
		if err := client.Shutdown(); err != nil {
			log.Printf("关闭 RocketMQ 生产者失败: %v\n", err)
		} else {
			log.Println("RocketMQ 生产者已关闭")
		}

		log.Println("资源清理完成，程序退出")
		close(stopChan)
		os.Exit(0)
	}()

	return stopChan
}
