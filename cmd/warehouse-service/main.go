package main

import (
	"fmt"
	"lens/internal/config"
	"log"
)

func main() {
	// 加载配置文件
	cfg, err := config.LoadFromFile("./configs/warehouse-service.env")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	log.Printf("Warehouse service starting on port %d...", cfg.ServerPort)
	fmt.Println("Hello from warehouse!")
}
