# 使用最小的镜像作为运行环境
FROM alpine:latest

# 设置时区为莫斯科时间
ENV TZ=Europe/Moscow
RUN apk --no-cache add ca-certificates tzdata \
    && cp /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone

# 复制预编译的二进制文件
COPY warehouse-service /bin/warehouse-service

# 复制配置文件
COPY configs/warehouse-service.env /configs/warehouse-service.env

# 暴露端口（根据实际需要调整）
EXPOSE 8082

# 运行应用
ENTRYPOINT ["/bin/warehouse-service"]