package main

import (
	"context"
	"lens/internal/config"
	"lens/internal/service"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
)

// checkAndHandleAdverts 检查当前时间并决定是否暂停广告
func checkAndHandleAdverts(manager *service.AdvertManager) error {
	// 获取莫斯科时间
	loc, err := time.LoadLocation("Europe/Moscow")
	if err != nil {
		return err
	}
	now := time.Now().In(loc)
	hour := now.Hour()

	// 如果当前时间在凌晨1点到早上8点之间，暂停所有运行的广告
	if hour >= 1 && hour < 8 {
		log.Printf("当前莫斯科时间 %02d:%02d，暂停所有运行的广告", hour, now.Minute())
		if err := manager.PauseAllRunningAdverts(); err != nil {
			return err
		}
		log.Println("已暂停所有运行的广告")
	} else {
		log.Printf("当前莫斯科时间 %02d:%02d，不在广告暂停时间范围内", hour, now.Minute())
	}
	return nil
}

func main() {
	// 加载配置文件
	cfg, err := config.LoadFromFile("./configs/advert-manager.env")
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	// 使用配置中的 API Key
	apiKey := cfg.WBApiKey
	if apiKey == "" {
		log.Fatal("请在配置文件中设置 WB_API_KEY")
	}

	// 创建广告管理器
	manager, err := service.NewAdvertManager(apiKey)
	if err != nil {
		log.Fatalf("创建广告管理器失败: %v", err)
	}

	// 创建上下文和取消函数
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 检查启动时间并处理广告
	if err := checkAndHandleAdverts(manager); err != nil {
		log.Printf("启动时处理广告失败: %v", err)
	}

	// 启动广告管理器
	if err := manager.Start(ctx); err != nil {
		log.Fatalf("启动广告管理器失败: %v", err)
	}

	// 等待中断信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh

	// 优雅关闭
	log.Println("正在关闭广告管理服务...")
	cancel()
}
