# Lens Agent Server API 客户端

这是一个完整的 Wildberries 关键词分析服务 Go 客户端，基于 lens-agent-server OpenAPI 文档构建。

## 🎯 功能特性

- **健康检查** - 服务状态监控
- **服务信息** - 获取服务版本和环境信息
- **关键词分析** - 通过广告活动ID查询关键词排除情况

## 快速开始

### 基本用法

```go
package main

import (
    "fmt"
    "log"
    
    "your-project/internal/api/my_agent"
)

func main() {
    // 创建客户端
    client := my_agent.NewClient("http://localhost:8000")
    
    // 健康检查
    health, err := client.HealthCheck()
    if err != nil {
        log.Fatal(err)
    }
    fmt.Printf("服务状态: %s\n", health.Status)
    
    // 获取关键词分析
    analysis, err := client.GetCampaignKeywordsAnalysis(12345)
    if err != nil {
        log.Fatal(err)
    }
    fmt.Printf("找到 %d 个关键词\n", analysis.TotalKeywords)
}
```

### 配置选项

```go
// 自定义超时时间
client := my_agent.NewClient("http://localhost:8000",
    my_agent.WithTimeout(60*time.Second),
)

// 设置自定义请求头
client := my_agent.NewClient("http://localhost:8000",
    my_agent.WithHeaders(map[string]string{
        "Authorization": "Bearer your-token",
    }),
)
```

## API 方法

### 健康检查
```go
health, err := client.HealthCheck()
```

### 服务信息
```go
info, err := client.GetServiceInfo()
```

### 关键词分析
```go
analysis, err := client.GetCampaignKeywordsAnalysis(campaignID)
```

## 错误处理

所有API方法都返回错误，建议进行适当的错误处理：

```go
analysis, err := client.GetCampaignKeywordsAnalysis(12345)
if err != nil {
    log.Printf("获取关键词分析失败: %v", err)
    return
}

// 处理分析结果
for _, keyword := range analysis.Keywords {
    if keyword.ShouldExclude {
        fmt.Printf("建议排除关键词: %s\n", keyword.Keyword)
    }
}
```