package my_agent

// KeywordAnalysisInfo 关键词分析响应模型
type KeywordAnalysisInfo struct {
	CampaignID           int      `json:"campaign_id"`            // 广告活动ID
	NMID                 int      `json:"nm_id"`                  // 商品ID
	Keyword              string   `json:"keyword"`                // 关键词
	AvgSimilarity        *float64 `json:"avg_similarity"`         // 平均相似度
	SimilarCount         *int     `json:"similar_count"`          // 相似商品数量
	CompetitorCount      *int     `json:"competitor_count"`       // 竞争对手数量
	ValidScores          *int     `json:"valid_scores"`           // 有效评分数
	Views                *int     `json:"views"`                  // 展示次数
	Sum                  *float64 `json:"sum"`                    // 花费金额
	Clicks               *int     `json:"clicks"`                 // 点击次数
	CTR                  *float64 `json:"ctr"`                    // 点击率
	CPC                  *float64 `json:"cpc"`                    // 平均点击成本
	Count                *int     `json:"count"`                  // 关键词热度
	OrderGenerating      bool     `json:"order_generating"`       // 是否为出单关键词
	ExcludeIrrelevant    bool     `json:"exclude_irrelevant"`     // 排除不相关
	ExcludeZeroClick     bool     `json:"exclude_zero_click"`     // 排除零点击
	ExcludeNoData        bool     `json:"exclude_no_data"`        // 排除无数据
	ObserveLowCTR        bool     `json:"observe_low_ctr"`        // 观察低CTR
	ObserveHighCPC       bool     `json:"observe_high_cpc"`       // 观察高CPC
	ObserveLowSimilarity bool     `json:"observe_low_similarity"` // 观察低相似度
	OptimizeLowPerf      bool     `json:"optimize_low_perf"`      // 优化低表现
	Keep                 bool     `json:"keep"`                   // 保持不变
	ShouldExclude        bool     `json:"should_exclude"`         // 是否应该排除
}

// CampaignAnalysisResponse 广告活动分析响应模型
type CampaignAnalysisResponse struct {
	CampaignID    int                    `json:"campaign_id"`    // 广告活动ID
	TotalKeywords int                    `json:"total_keywords"` // 关键词总数
	Keywords      []KeywordAnalysisInfo  `json:"keywords"`       // 关键词分析结果
	Summary       map[string]interface{} `json:"summary"`        // 统计摘要
}

// HTTPValidationError HTTP 验证错误
type HTTPValidationError struct {
	Detail []ValidationError `json:"detail"`
}

// ValidationError 验证错误详情
type ValidationError struct {
	Loc  []interface{} `json:"loc"`  // 错误位置
	Msg  string        `json:"msg"`  // 错误消息
	Type string        `json:"type"` // 错误类型
}
