package my_agent

import (
	"fmt"
	"strconv"
)

// GetCampaignKeywordsAnalysis 通过广告活动ID查询关键词排除情况
func (c *Client) GetCampaignKeywordsAnalysis(campaignID int) (*CampaignAnalysisResponse, error) {
	var result CampaignAnalysisResponse
	resp, err := c.client.R().
		SetResult(&result).
		Get(c.baseURL + "/api/v1/campaign/" + strconv.Itoa(campaignID) + "/keywords")

	if err != nil {
		return nil, fmt.Errorf("获取广告活动关键词分析失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取广告活动关键词分析失败: %s", resp.String())
	}
	return &result, nil
}
