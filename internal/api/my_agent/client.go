// Package my_agent 提供 Wildberries 关键词分析服务的 Go 客户端实现
//
// 这个包实现了 lens-agent-server 的完整功能，包括：
// - 健康检查
// - 服务信息查询
// - 广告活动关键词分析
//
// 使用示例：
//
//	client := my_agent.NewClient("http://localhost:8000")
//	analysis, err := client.GetCampaignKeywordsAnalysis(12345)
//	if err != nil {
//		log.Fatal(err)
//	}
package my_agent

import (
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	// defaultTimeout 默认请求超时时间
	defaultTimeout = 15 * time.Minute
)

// Client lens-agent-server API 客户端
type Client struct {
	client  *resty.Client
	baseURL string
}

// ClientOption 客户端配置选项
type ClientOption func(*Client)

// NewClient 创建新的客户端实例
func NewClient(baseURL string, opts ...ClientOption) *Client {
	client := &Client{
		client:  resty.New(),
		baseURL: baseURL,
	}

	// 设置默认配置
	client.client.SetTimeout(defaultTimeout)
	client.client.SetHeader("Content-Type", "application/json")
	client.client.SetHeader("Accept", "application/json")

	// 应用配置选项
	for _, opt := range opts {
		opt(client)
	}

	return client
}

// WithTimeout 设置请求超时时间
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *Client) {
		c.client.SetTimeout(timeout)
	}
}

// WithHeaders 设置自定义请求头
func WithHeaders(headers map[string]string) ClientOption {
	return func(c *Client) {
		c.client.SetHeaders(headers)
	}
}

// GetHTTPClient 获取底层的 HTTP 客户端
func (c *Client) GetHTTPClient() *resty.Client {
	return c.client
}

// R 创建新的请求
func (c *Client) R() *resty.Request {
	return c.client.R()
}
