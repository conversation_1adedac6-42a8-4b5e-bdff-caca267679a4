package wbapi

import (
	"golang.org/x/time/rate"
)

// 接口请求限制常量
const (
	// GetCampaignCount 每秒最多5个请求
	rateLimitGetCampaignCount = 5

	// GetConfig 每分钟最多1个请求
	rateLimitGetConfig = 1

	// GetCardsList 每分钟最多100个请求
	rateLimitGetCardsList = 100

	// GetCampaignsByParams 每秒最多5个请求
	rateLimitGetCampaignsByParams = 5

	// CreateAutoCampaign 每20秒最多1个请求
	rateLimitCreateAutoCampaign = 1

	// CreateAuctionCampaign 每分钟最多5个请求
	rateLimitCreateAuctionCampaign = 5

	// GetSubjects 每12秒最多1个请求
	rateLimitGetSubjects = 1

	// GetNomenclatures 每分钟最多5个请求
	rateLimitGetNomenclatures = 5

	// UpdateBids 每秒最多5个请求
	rateLimitUpdateBids = 5

	// DeleteCampaign 每秒最多5个请求
	rateLimitDeleteCampaign = 5

	// RenameCampaign 每秒最多5个请求
	rateLimitRenameCampaign = 5

	// StartCampaign 每秒最多5个请求
	rateLimitStartCampaign = 5

	// PauseCampaign 每秒最多5个请求
	rateLimitPauseCampaign = 5

	// StopCampaign 每秒最多5个请求
	rateLimitStopCampaign = 5

	// SetPlusKeywords 每500毫秒最多1个请求
	rateLimitSetPlusKeywords = 2

	// SetPhraseKeywords 每秒最多2个请求
	rateLimitSetPhraseKeywords = 2

	// SetStrongKeywords 每秒最多2个请求
	rateLimitSetStrongKeywords = 2

	// SetExcludedKeywords 每秒最多2个请求
	rateLimitSetExcludedKeywords = 2

	// AutoExclude 每6秒最多1个请求
	rateLimitAutoExclude = 0.167 // 1/6 ≈ 0.167

	// GetAutoNms 每秒最多1个请求
	rateLimitGetAutoNms = 1

	// UpdateAutoNms 每分钟最多60个请求
	rateLimitUpdateAutoNms = 1

	// GetFullStats 每分钟1次请求
	rateLimitFullStats = 1.0 / 60.0

	// GetAutoStatWords 每秒4次请求
	rateLimitAutoStatWords = 4

	// GetStatWords 每秒4次请求
	rateLimitStatWords = 4

	// GetKeywordsStats 每秒4次请求
	rateLimitKeywordsStats = 4

	// GetMediaStats 每秒60次请求
	rateLimitMediaStats = 60
)

// RateLimiter 接口限流器
type RateLimiter struct {
	// 活动列表
	campaignCountLimiter *rate.Limiter

	// 配置信息
	configLimiter *rate.Limiter

	// 商品卡片列表
	cardsListLimiter *rate.Limiter

	// 活动信息
	campaignsLimiter *rate.Limiter

	// 创建自动广告
	autoCreateLimiter *rate.Limiter

	// 创建竞价广告
	auctionCreateLimiter *rate.Limiter

	// 获取类目
	subjectsLimiter *rate.Limiter

	// 获取商品列表
	nomenclaturesLimiter *rate.Limiter

	// 更新出价
	bidsLimiter *rate.Limiter

	// 删除活动
	deleteLimiter *rate.Limiter

	// 重命名活动
	renameLimiter *rate.Limiter

	// 启动活动
	startLimiter *rate.Limiter

	// 暂停活动
	pauseLimiter *rate.Limiter

	// 停止活动
	stopLimiter *rate.Limiter

	// 设置加词
	plusKeywordLimiter *rate.Limiter

	// 设置词组
	phraseKeywordLimiter *rate.Limiter

	// 设置强匹配词
	strongKeywordLimiter *rate.Limiter

	// 设置排除词
	excludedKeywordLimiter *rate.Limiter

	// 自动广告排除
	autoExcludeLimiter *rate.Limiter

	// 获取可添加商品
	autoNmsLimiter *rate.Limiter

	// 更新自动广告商品
	autoUpdateLimiter *rate.Limiter

	// 完整统计数据
	fullStatsLimiter *rate.Limiter

	// 自动广告词组统计
	autoStatWordsLimiter *rate.Limiter

	// 搜索广告词组统计
	statWordsLimiter *rate.Limiter

	// 关键词统计
	keywordsStatsLimiter *rate.Limiter

	// 媒体广告统计
	mediaStatsLimiter *rate.Limiter
}

// NewRateLimiter 创建新的接口限流器
func NewRateLimiter() *RateLimiter {
	return &RateLimiter{
		// 每秒5个请求
		campaignCountLimiter: rate.NewLimiter(rate.Limit(rateLimitGetCampaignCount), 1),

		// 每分钟1个请求
		configLimiter: rate.NewLimiter(rate.Limit(rateLimitGetConfig)/60.0, 1),

		// 每分钟100个请求
		cardsListLimiter: rate.NewLimiter(rate.Limit(rateLimitGetCardsList)/60.0, 1),

		// 每秒5个请求
		campaignsLimiter: rate.NewLimiter(rate.Limit(rateLimitGetCampaignsByParams), 1),

		// 每20秒1个请求
		autoCreateLimiter: rate.NewLimiter(rate.Limit(rateLimitCreateAutoCampaign)/20.0, 1),

		// 每分钟5个请求
		auctionCreateLimiter: rate.NewLimiter(rate.Limit(rateLimitCreateAuctionCampaign)/60.0, 1),

		// 每12秒1个请求
		subjectsLimiter: rate.NewLimiter(rate.Limit(rateLimitGetSubjects)/12.0, 1),

		// 每分钟5个请求
		nomenclaturesLimiter: rate.NewLimiter(rate.Limit(rateLimitGetNomenclatures)/60.0, 1),

		// 每秒5个请求
		bidsLimiter: rate.NewLimiter(rate.Limit(rateLimitUpdateBids), 1),

		// 每秒5个请求
		deleteLimiter: rate.NewLimiter(rate.Limit(rateLimitDeleteCampaign), 1),

		// 每秒5个请求
		renameLimiter: rate.NewLimiter(rate.Limit(rateLimitRenameCampaign), 1),

		// 每秒5个请求
		startLimiter: rate.NewLimiter(rate.Limit(rateLimitStartCampaign), 1),

		// 每秒5个请求
		pauseLimiter: rate.NewLimiter(rate.Limit(rateLimitPauseCampaign), 1),

		// 每秒5个请求
		stopLimiter: rate.NewLimiter(rate.Limit(rateLimitStopCampaign), 1),

		// 每500毫秒1个请求
		plusKeywordLimiter: rate.NewLimiter(rate.Limit(rateLimitSetPlusKeywords), 1),

		// 每秒2个请求
		phraseKeywordLimiter: rate.NewLimiter(rate.Limit(rateLimitSetPhraseKeywords), 1),

		// 每秒2个请求
		strongKeywordLimiter: rate.NewLimiter(rate.Limit(rateLimitSetStrongKeywords), 1),

		// 每秒2个请求
		excludedKeywordLimiter: rate.NewLimiter(rate.Limit(rateLimitSetExcludedKeywords), 1),

		// 每6秒1个请求
		autoExcludeLimiter: rate.NewLimiter(rate.Limit(rateLimitAutoExclude), 1),

		// 每秒1个请求
		autoNmsLimiter: rate.NewLimiter(rate.Limit(rateLimitGetAutoNms), 1),

		// 每分钟60个请求
		autoUpdateLimiter: rate.NewLimiter(rate.Limit(rateLimitUpdateAutoNms), 60),

		// 每分钟1次请求
		fullStatsLimiter: rate.NewLimiter(rate.Limit(rateLimitFullStats), 1),

		// 每秒4次请求
		autoStatWordsLimiter: rate.NewLimiter(rate.Limit(rateLimitAutoStatWords), 1),

		// 每秒4次请求
		statWordsLimiter: rate.NewLimiter(rate.Limit(rateLimitStatWords), 1),

		// 每秒4次请求
		keywordsStatsLimiter: rate.NewLimiter(rate.Limit(rateLimitKeywordsStats), 1),

		// 每秒60次请求
		mediaStatsLimiter: rate.NewLimiter(rate.Limit(rateLimitMediaStats), 1),
	}
}
