package wbapi

// APIResponse 通用API响应
type APIResponse struct {
    Data  interface{} `json:"data"`
    Error bool        `json:"error"`
    ErrorText string  `json:"errorText"`
    AdditionalErrors interface{} `json:"additionalErrors"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
}

// PaginationCursor 分页游标
type PaginationCursor struct {
    Limit  int    `json:"limit"`
    Offset int    `json:"offset"`
    Total  int    `json:"total"`
    Next   string `json:"next"`
}

// DateRange 日期范围
type DateRange struct {
    Begin string `json:"begin"` // 开始日期 YYYY-MM-DD
    End   string `json:"end"`   // 结束日期 YYYY-MM-DD
}

// RequestWithDates 带日期的请求
type RequestWithDates struct {
    ID    int      `json:"id"`    // ID
    Dates []string `json:"dates"` // 日期列表
}

// RequestWithInterval 带时间间隔的请求
type RequestWithInterval struct {
    ID       int       `json:"id"`       // ID
    Interval DateRange `json:"interval"` // 时间间隔
}

// RequestWithID 仅带ID的请求
type RequestWithID struct {
    ID int `json:"id"` // ID
}