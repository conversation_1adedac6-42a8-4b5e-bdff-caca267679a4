package wbapi

import "time"

// ===== 评价相关模型 =====

// FeedbacksResponse 评价列表响应
type FeedbacksResponse struct {
	Data struct {
		CountUnanswered int        `json:"countUnanswered"` // 未回复评价数量
		CountArchive    int        `json:"countArchive"`    // 归档评价数量
		Feedbacks       []Feedback `json:"feedbacks"`       // 评价列表
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// Feedback 评价信息
type Feedback struct {
	ID               string    `json:"id"`               // 评价ID
	Text             string    `json:"text"`             // 评价内容
	ProductValuation int       `json:"productValuation"` // 商品评分
	CreatedDate      time.Time `json:"createdDate"`      // 创建时间
	State            string    `json:"state"`            // 状态
	Answer           *struct {
		Text string    `json:"text"` // 回复内容
		Date time.Time `json:"date"` // 回复时间
	} `json:"answer"` // 回复信息
	ProductDetails struct {
		NMID         int    `json:"nmId"`         // 商品ID
		ProductName  string `json:"productName"`  // 商品名称
		SupplierName string `json:"supplierName"` // 供应商名称
		BrandName    string `json:"brandName"`    // 品牌名称
		Size         string `json:"size"`         // 尺寸
	} `json:"productDetails"` // 商品详情
	WasViewed bool `json:"wasViewed"` // 是否已查看
}

// ===== 问答相关模型 =====

// QuestionsResponse 问答列表响应
type QuestionsResponse struct {
	Data struct {
		CountUnanswered int        `json:"countUnanswered"` // 未回复问题数量
		CountArchive    int        `json:"countArchive"`    // 归档问题数量
		Questions       []Question `json:"questions"`       // 问题列表
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// Question 问题信息
type Question struct {
	ID          string    `json:"id"`          // 问题ID
	Text        string    `json:"text"`        // 问题内容
	CreatedDate time.Time `json:"createdDate"` // 创建时间
	State       string    `json:"state"`       // 状态
	Answer      *struct {
		Text string    `json:"text"` // 回复内容
		Date time.Time `json:"date"` // 回复时间
	} `json:"answer"` // 回复信息
	ProductDetails struct {
		NMID         int    `json:"nmId"`         // 商品ID
		ProductName  string `json:"productName"`  // 商品名称
		SupplierName string `json:"supplierName"` // 供应商名称
		BrandName    string `json:"brandName"`    // 品牌名称
	} `json:"productDetails"` // 商品详情
	WasViewed bool `json:"wasViewed"` // 是否已查看
}

// ===== 评价统计模型 =====

// FeedbackStats 评价统计
type FeedbackStats struct {
	ValuationDetails struct {
		Count1 int `json:"count1"` // 1星评价数
		Count2 int `json:"count2"` // 2星评价数
		Count3 int `json:"count3"` // 3星评价数
		Count4 int `json:"count4"` // 4星评价数
		Count5 int `json:"count5"` // 5星评价数
	} `json:"valuationDetails"` // 评分详情
	ProductsRating struct {
		NMID   int     `json:"nmId"`   // 商品ID
		Rating float64 `json:"rating"` // 平均评分
		Count  int     `json:"count"`  // 评价总数
	} `json:"productsRating"` // 商品评分
}

// ===== 请求参数模型 =====

// FeedbackRequest 评价查询请求
type FeedbackRequest struct {
	NMID       int   `json:"nmId"`                 // 商品ID
	IsAnswered *bool `json:"isAnswered,omitempty"` // 是否已回复
	Take       int   `json:"take"`                 // 获取数量
	Skip       int   `json:"skip"`                 // 跳过数量
}

// QuestionRequest 问答查询请求
type QuestionRequest struct {
	NMID       int   `json:"nmId"`                 // 商品ID
	IsAnswered *bool `json:"isAnswered,omitempty"` // 是否已回复
	Take       int   `json:"take"`                 // 获取数量
	Skip       int   `json:"skip"`                 // 跳过数量
}

// AnswerRequest 回复请求
type AnswerRequest struct {
	ID   string `json:"id"`   // 评价/问题ID
	Text string `json:"text"` // 回复内容
}

// ===== 新增模型 =====

// NewFeedbacksQuestionsResponse 新评价和问题检查响应
type NewFeedbacksQuestionsResponse struct {
	Data struct {
		HasNewQuestions bool `json:"hasNewQuestions"` // 是否有新问题
		HasNewFeedbacks bool `json:"hasNewFeedbacks"` // 是否有新评价
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// UnansweredCountResponse 未回复数量响应
type UnansweredCountResponse struct {
	Data struct {
		CountUnanswered int `json:"countUnanswered"` // 未回复数量
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// ===== 模板相关模型 =====

// TemplatesResponse 模板列表响应
type TemplatesResponse struct {
	Data struct {
		Templates []Template `json:"templates"` // 模板列表
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// Template 回复模板
type Template struct {
	ID   string `json:"id"`   // 模板ID
	Name string `json:"name"` // 模板名称
	Text string `json:"text"` // 模板内容
}

// CreateTemplateRequest 创建模板请求
type CreateTemplateRequest struct {
	Name string `json:"name"` // 模板名称
	Text string `json:"text"` // 模板内容
}

// CreateTemplateResponse 创建模板响应
type CreateTemplateResponse struct {
	Data struct {
		ID string `json:"id"` // 新创建的模板ID
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// UpdateTemplateRequest 更新模板请求
type UpdateTemplateRequest struct {
	Name string `json:"name"` // 模板名称
	Text string `json:"text"` // 模板内容
}
