// Package wbapi 提供 Wildberries Seller API 的 Go 客户端实现
//
// 这个包实现了完整的 WB Seller API 功能，包括：
// - 推广服务：广告活动管理、竞价设置、统计数据
// - 商品卡片服务：商品信息管理、媒体文件、标签
// - 内容服务：分类管理、商品内容
// - 价格服务：价格和折扣管理
// - 库存服务：库存管理和报告
// - 订单服务：订单管理和统计
// - 分析服务：数据分析和报告
//
// 使用示例：
//
//	client := wbapi.NewClient("your-api-key")
//	campaigns, err := client.Promotion.GetCampaigns()
//	if err != nil {
//		log.Fatal(err)
//	}
package wbapi

import (
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	// defaultTimeout 默认请求超时时间
	defaultTimeout = 60 * time.Second
)

// Client WB API 客户端
// 提供对所有 WB Seller API 服务的访问
type Client struct {
	httpClient *resty.Client // HTTP 请求客户端

	// 核心业务服务
	Promotion *PromotionService // 推广服务 - 广告活动管理
	Products  *ProductsService  // 商品服务 - 商品信息管理
	Content   *ContentService   // 内容服务 - 分类和内容管理
	Orders    *OrderService     // 订单服务 - 订单管理和统计
	Analytics *AnalyticsService // 分析服务 - 数据分析和报告

	// 扩展服务
	Feedbacks *FeedbackService // 反馈服务 - 评价和问答管理
	Supplies  *SuppliesService // 供应服务 - 供货单和物流管理
	Finance   *FinanceService  // 财务服务 - 财务报告和余额查询
	Chat      *ChatService     // 聊天服务 - 买家聊天管理
}

// ClientOption 客户端配置选项函数
type ClientOption func(*Client)

// WithTimeout 设置请求超时时间
//
// 参数:
//   - timeout: 超时时间
//
// 返回:
//   - ClientOption: 配置选项函数
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *Client) {
		c.httpClient.SetTimeout(timeout)
	}
}

// NewClient 创建新的 WB API 客户端
//
// 参数:
//   - apiKey: WB API 密钥
//   - opts: 可选的客户端配置选项
//
// 返回:
//   - *Client: 客户端实例
func NewClient(apiKey string, opts ...ClientOption) *Client {
	// 创建基础 HTTP 客户端
	rc := resty.New()
	rc.SetTimeout(defaultTimeout)
	rc.SetHeader("Authorization", apiKey)
	rc.SetHeader("Accept", "application/json")
	rc.SetHeader("Content-Type", "application/json")

	// 设置代理（如果需要）
	rc.SetProxy("socks5://lens:ls3903850@185.22.152.62:23481")

	c := &Client{
		httpClient: rc,
	}

	// 应用自定义配置选项
	for _, opt := range opts {
		opt(c)
	}

	// 初始化核心业务服务
	c.Promotion = newPromotionService(c)
	c.Products = newProductsService(c)
	c.Content = newContentService(c)
	c.Orders = newOrderService(c)
	c.Analytics = newAnalyticsService(c)

	// 初始化扩展服务
	c.Feedbacks = newFeedbackService(c)
	c.Supplies = newSuppliesService(c)
	c.Finance = newFinanceService(c)
	c.Chat = newChatService(c)

	return c
}

// R 返回 resty 请求对象
// 用于创建自定义 HTTP 请求
//
// 返回:
//   - *resty.Request: resty 请求对象
func (c *Client) R() *resty.Request {
	return c.httpClient.R()
}

// GetHTTPClient 获取底层 HTTP 客户端
// 用于高级配置和自定义请求
//
// 返回:
//   - *resty.Client: resty HTTP 客户端
func (c *Client) GetHTTPClient() *resty.Client {
	return c.httpClient
}

// SetProxy 设置代理
//
// 参数:
//   - proxy: 代理地址，格式如 "socks5://user:pass@host:port"
//
// 返回:
//   - *Client: 客户端实例，支持链式调用
func (c *Client) SetProxy(proxy string) *Client {
	c.httpClient.SetProxy(proxy)
	return c
}

// SetDebug 设置调试模式
//
// 参数:
//   - debug: 是否开启调试模式
//
// 返回:
//   - *Client: 客户端实例，支持链式调用
func (c *Client) SetDebug(debug bool) *Client {
	c.httpClient.SetDebug(debug)
	return c
}

// SetRetryCount 设置重试次数
//
// 参数:
//   - count: 重试次数
//
// 返回:
//   - *Client: 客户端实例，支持链式调用
func (c *Client) SetRetryCount(count int) *Client {
	c.httpClient.SetRetryCount(count)
	return c
}
