openapi: 3.0.0
info:
  title: Тарифы
  version: tariffs
  description: |
    <div class="description_important">
        Узнать больше о комиссиях и тарифах можно в <a href="https://seller.wildberries.ru/instructions/category/a04560b5-256d-48cb-8f09-60e283b5d493">справочном центре</a>
    </div>

    В данном разделе доступны методы получения:
      1. [Комиссий](/openapi/wb-tariffs#tag/Komissii)
      2. [Коэффициентов складов](/openapi/wb-tariffs#tag/Koefficienty-skladov)
      3. [Тарифов на возврат товаров продавцу](/openapi/wb-tariffs#tag/Stoimost-vozvrata-prodavcu)
  x-file-name: tariffs
security:
  - HeaderApiKey: []
tags:
  - name: Комиссии
    description: ''
  - name: Коэффициенты складов
    description: ''
  - name: Стоимость возврата продавцу
    description: ''
paths:
  /api/v1/tariffs/commission:
    servers:
      - url: https://common-api.wildberries.ru
    get:
      summary: Комиссия по категориям товаров
      tags:
        - Комиссии
      description: |
        Метод предоставляет данные о [комиссии](https://seller.wildberries.ru/dynamic-product-categories/commission) WB по [родительским категориям товаров](/openapi/work-with-products#tag/Kategorii-predmety-i-harakteristiki/paths/~1content~1v2~1object~1parent~1all/get) согласно модели продаж.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1 запрос | 1 минута | 2 запроса |
        </div>
      parameters:
        - $ref: '#/components/parameters/Locale'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/Commission'
                  - $ref: '#/components/schemas/CommissionChina'
                  - $ref: '#/components/schemas/CommissionTurkey'
                  - $ref: '#/components/schemas/CommissionUzbekistan'
                  - $ref: '#/components/schemas/CommissionUAE'
              examples:
                Commission:
                  $ref: '#/components/examples/Commission'
                CommissionChina:
                  $ref: '#/components/examples/CommissionChina'
                CommissionTurkey:
                  $ref: '#/components/examples/CommissionTurkey'
                CommissionUzbekistan:
                  $ref: '#/components/examples/CommissionUzbekistan'
                CommissionUAE:
                  $ref: '#/components/examples/CommissionUAE'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequest'
              example:
                detail: 'parameter "locale" in query has an error: value is not one of the allowed values ["ru","en","zh"]'
                origin: api-statistics
                requestId: 102d2641a931c41bed60649d6c99d80a
                title: Bad Request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v1/tariffs/box:
    servers:
      - url: https://common-api.wildberries.ru
    get:
      summary: Тарифы для коробов
      tags:
        - Коэффициенты складов
      description: "Для товаров, которые поставляются на\_склад в коробах, метод предоставляет [стоимость](https://seller.wildberries.ru/dynamic-product-categories):\n  - доставки со склада или пункта приёма до покупателя\n  - доставки от покупателя до пункта приёма\n  - хранения на\_складе WB\n\n<div class=\"description_limit\">\n<a href=\"/openapi/api-information#tag/Vvedenie/Limity-zaprosov\">Лимит запросов</a> на один аккаунт продавца:\n\n| Период | Лимит | Интервал | Всплеск |\n| --- | --- | --- | --- |\n| 1 минута | 60 запросов | 1 секунда | 5 запросов |\n</div>\n"
      parameters:
        - $ref: '#/components/parameters/Date'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TariffsBoxResponse'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequest'
              example:
                detail: Invalid date param
                origin: tariffs
                requestId: 70da980b108e6f52ce7630921bd05b1a
                title: Bad Request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v1/tariffs/pallet:
    servers:
      - url: https://common-api.wildberries.ru
    get:
      summary: Тарифы для монопаллет
      tags:
        - Коэффициенты складов
      description: "Для товаров, которые поставляются на\_склад WB на\_монопаллетах, метод предоставляет [стоимость](https://seller.wildberries.ru/dynamic-product-categories):\n  - доставки со склада до покупателя\n  - доставки от покупателя до склада\n  - хранения на\_складе WB\n\n<div class=\"description_limit\">\n<a href=\"/openapi/api-information#tag/Vvedenie/Limity-zaprosov\">Лимит запросов</a> на один аккаунт продавца:\n\n| Период | Лимит | Интервал | Всплеск |\n| --- | --- | --- | --- |\n| 1 минута | 60 запросов | 1 секунда | 5 запросов |\n</div>\n"
      parameters:
        - $ref: '#/components/parameters/Date'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TariffsPalletResponse'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequest'
              example:
                detail: 'parameter "date" in query has an error: value is required but missing'
                origin: tariffs
                requestId: 7bc5c30d95933058d0e0bddc79b9da547
                title: Bad Request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v1/tariffs/return:
    servers:
      - url: https://common-api.wildberries.ru
    get:
      summary: "Тарифы на\_возврат"
      tags:
        - Стоимость возврата продавцу
      description: "Метод предоставляет [тарифы](https://seller.wildberries.ru/dynamic-product-categories/return-cost):\n  - на\_перевозку товаров со склада WB или из пункта приёма до продавца\n  - на\_обратную перевозку возвратов, которые не забрал продавец\n\n<div class=\"description_limit\">\n<a href=\"/openapi/api-information#tag/Vvedenie/Limity-zaprosov\">Лимит запросов</a> на один аккаунт продавца:\n\n| Период | Лимит | Интервал | Всплеск |\n| --- | --- | --- | --- |\n| 1 минута | 60 запросов | 1 секунда | 5 запросов |\n</div>\n"
      parameters:
        - $ref: '#/components/parameters/Date'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReturnTariffsResponse'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequest'
              example:
                detail: 'parameter "date" in query has an error: empty value is not allowed'
                origin: tariffs
                requestId: e8096bddcfe5e3ff75f1afd26755364c
                title: Bad Request
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
components:
  examples:
    Commission:
      summary: Commission
      description: Комиссия
      value:
        report:
          - kgvpBooking: 14.5
            kgvpMarketplace: 15.5
            kgvpPickup: 14.5
            kgvpSupplier: 12.5
            kgvpSupplierExpress: 3
            paidStorageKgvp: 15.5
            parentID: 657
            parentName: Бытовая техника
            subjectID: 6461
            subjectName: Оборудование зуботехническое
    CommissionChina:
      summary: CommissionChina
      description: Комиссия Китай
      value:
        report:
          - kgvpChina: 15.5
            parentID: 657
            parentName: 国内设备
            subjectID: 6461
            subjectName: 牙齿矫正设备
    CommissionTurkey:
      summary: CommissionTurkey
      description: Комиссия Турция
      value:
        report:
          - kgvpTurkey: 15.5
            parentID: 657
            parentName: Home appliance
            subjectID: 6461
            subjectName: Dental equipment
    CommissionUzbekistan:
      summary: CommissionUzbekistan
      description: Комиссия Узбекистан
      value:
        report:
          - kgvpUzbekistan: 15.5
            parentID: 657
            parentName: Бытовая техника
            subjectID: 6461
            subjectName: Оборудование зуботехническое
    CommissionUAE:
      summary: CommissionUAE
      description: Комиссия ОАЭ
      value:
        report:
          - kgvpUAE: 15.5
            parentID: 657
            parentName: Home appliance
            subjectID: 6461
            subjectName: Dental equipment
  securitySchemes:
    HeaderApiKey:
      type: apiKey
      name: Authorization
      in: header
  parameters:
    Date:
      in: query
      name: date
      required: true
      description: Дата в формате ГГГГ-ММ-ДД
      schema:
        type: string
    Locale:
      name: locale
      in: query
      schema:
        type: string
        example: ru
      description: |
        Язык полей ответа `parentName` и `subjectName`:
          - `ru` — русский
          - `en` — английский
          - `zh` — китайский
  schemas:
    models.TariffsBoxResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/models.WarehousesBoxRates'
    models.WarehousesBoxRates:
      type: object
      properties:
        dtNextBox:
          type: string
          description: Дата начала следующего тарифа
          example: '2024-02-01'
        dtTillMax:
          type: string
          description: Дата окончания последнего установленного тарифа
          example: '2024-03-31'
        warehouseList:
          type: array
          items:
            $ref: '#/components/schemas/models.WarehouseBoxRates'
          nullable: true
          description: Тарифы для коробов, сгруппированные по складам
    models.WarehouseBoxRates:
      type: object
      properties:
        boxDeliveryAndStorageExpr:
          type: string
          description: Коэффициент, %. На него умножается стоимость доставки и хранения. Во всех тарифах этот коэффициент уже учтён
          example: '160'
        boxDeliveryBase:
          type: string
          description: "Доставка 1\_литра,\_₽"
          example: '48'
        boxDeliveryLiter:
          type: string
          description: "Доставка каждого дополнительного литра,\_₽"
          example: 11,2
        boxStorageBase:
          type: string
          description: "Хранение 1\_литра,\_₽"
          example: 0,1
        boxStorageLiter:
          type: string
          description: "Хранение каждого дополнительного литра,\_₽"
          example: 0,1
        warehouseName:
          type: string
          description: Название склада
          example: Коледино
    models.TariffsPalletResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/models.WarehousesPalletRates'
    models.WarehousesPalletRates:
      type: object
      properties:
        dtNextPallet:
          type: string
          description: Дата начала следующего тарифа
          example: '2024-02-01'
        dtTillMax:
          type: string
          description: Дата окончания последнего установленного тарифа
          example: '2024-03-31'
        warehouseList:
          type: array
          items:
            $ref: '#/components/schemas/models.WarehousePalletRates'
          nullable: true
          description: Тарифы для монопаллет, сгруппированные по складам
    models.WarehousePalletRates:
      type: object
      properties:
        palletDeliveryExpr:
          type: string
          description: Коэффициент доставки, %. На него умножается стоимость доставки. Во всех тарифах этот коэффициент уже учтён
          example: '170'
        palletDeliveryValueBase:
          type: string
          description: "Доставка 1\_литра,\_₽"
          example: '51'
        palletDeliveryValueLiter:
          type: string
          description: "Доставка каждого дополнительного литра,\_₽"
          example: 11,9
        palletStorageExpr:
          type: string
          description: Коэффициент хранения, %. На него умножается стоимость хранения. Во всех тарифах этот коэффициент уже учтён
          example: '155'
        palletStorageValueExpr:
          type: string
          description: "Хранение 1\_монопаллеты,\_₽"
          example: '35.65'
        warehouseName:
          type: string
          description: Название склада
          example: Коледино
    models.ReturnTariffsResponse:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/models.WarehousesReturnRates'
    models.WarehousesReturnRates:
      type: object
      properties:
        dtNextDeliveryDumpKgt:
          type: string
          description: Дата начала следующего тарифа при грузовой доставке
          example: '2024-02-01'
        dtNextDeliveryDumpSrg:
          type: string
          description: Дата начала следующего тарифа для неопознанных товаров
          example: '2024-02-01'
        dtNextDeliveryDumpSup:
          type: string
          description: Дата начала следующего тарифа при обычной доставке
          example: '2024-02-01'
        warehouseList:
          type: array
          items:
            $ref: '#/components/schemas/models.WarehouseReturnRates'
          nullable: true
          description: "Тарифы на\_возврат, сгруппированные по складам:\n<ul>\n  <li>стоимость возврата брака и возврата по инициативе продавца при грузовой доставке.</li>\n  <li>стоимость возврата неопознанного складом товара.</li>\n  <li>стоимость возврата брака, возврата по инициативе продавца и автовозвратов Маркетплейс (в пункт выдачи и обратно).</li>\n</ul>\nМожно получить стоимость возврата в пункт выдачи (ПВЗ) и обратной логистики\_— если продавец не забрал товары из пункта выдачи за\_7 дней.\n"
    models.WarehouseReturnRates:
      type: object
      properties:
        deliveryDumpKgtOfficeBase:
          type: string
          description: "<b>Стоимость возврата при грузовой доставке, доставка на\_ПВЗ (базовая цена\_за\_1\_л),\_₽</b>\nПрименяется для крупногабаритных товаров, когда:\n<ul>\n  <li>продавец хочет вывезти товары со склада WB;</li>\n  <li>на\_складе обнаружили бракованные товары;</li>\n  <li>покупатель возвращает товар, но его нельзя вернуть в продажу.</li>\n</ul>\n"
          example: 1 039
        deliveryDumpKgtOfficeLiter:
          type: string
          description: "<b>Стоимость возврата при грузовой доставке, доставка на\_ПВЗ (доп. литр),\_₽</b><br>\nСтоимость за\_каждый дополнительный литр.\n"
          example: 9,1
        deliveryDumpKgtReturnExpr:
          type: string
          description: "<b>Стоимость возврата при грузовой доставке, обратная логистика невостребованного возврата,\_₽</b><br>\nГрузовая доставка невостребованного возврата обратно на\_склад WB. За единицу товара.\n"
          example: 1 050
        deliveryDumpSrgOfficeExpr:
          type: string
          description: "<b>Стоимость возврата неопознанного складом товара за\_каждую единицу, доставка на\_ПВЗ, ₽</b>\n<p>Применяется для товаров, которые не смогли принять на складе.</p>\n"
          example: '170'
        deliveryDumpSrgReturnExpr:
          type: string
          description: "<p><b>Стоимость возврата неопознанного складом товара за каждую единицу, обратная логистика невостребованного возврата, ₽</b></p> Доставка невостребованного возврата обратно на\_склад WB."
          example: '170'
        deliveryDumpSupCourierBase:
          type: string
          description: |
            **Стоимость возврата, доставка курьером (базовая цена за 1 л), ₽**

            Применяется, когда:
              - продавец хочет вывезти товары со склада Wildberries
              - на складе обнаружили бракованные товары
              - покупатель возвращает товар, но его нельзя вернуть в продажу
              - подключён автовозврат товаров, продаваемых по схеме Маркетплейс
          example: '229'
        deliveryDumpSupCourierLiter:
          type: string
          description: |
            **Стоимость возврата, доставка курьером (доп. л), ₽**
            <p>Стоимость за каждый дополнительный литр.</p>
          example: 9,1
        deliveryDumpSupOfficeBase:
          type: string
          description: |
            **Стоимость возврата, доставка на ПВЗ (базовая цена за 1 л), ₽**

            Применяется, когда:
              - продавец хочет вывезти товары со склада Wildberries
              - на складе обнаружили бракованные товары
              - покупатель возвращает товар, но его нельзя вернуть в продажу
              - подключён автовозврат товаров, продаваемых по схеме Маркетплейс
          example: '129'
        deliveryDumpSupOfficeLiter:
          type: string
          description: "<b>Стоимость возврата, доставка на\_ПВЗ (доп. литр), ₽</b> Стоимость за\_каждый дополнительный литр"
          example: 9,1
        deliveryDumpSupReturnExpr:
          type: string
          description: |
            <b>Стоимость возврата, обратная логистика невостребованного возврата, за единицу товара, ₽</b><br>
            Доставка невостребованного возврата обратно на склад Wildberries.

            Применяется, когда:
              - продавец хочет вывезти товары со склада Wildberries
              - на складе обнаружили бракованные товары
              - покупатель возвращает товар, но его нельзя вернуть в продажу
              - подключён автовозврат товаров, продаваемых по схеме Маркетплейс
          example: '250'
        warehouseName:
          type: string
          description: Название склада
          example: Электросталь
    TariffsBoxResponse:
      type: object
      properties:
        response:
          $ref: '#/components/schemas/models.TariffsBoxResponse'
    TariffsPalletResponse:
      type: object
      properties:
        response:
          $ref: '#/components/schemas/models.TariffsPalletResponse'
    ReturnTariffsResponse:
      type: object
      properties:
        response:
          $ref: '#/components/schemas/models.ReturnTariffsResponse'
    Commission:
      type: object
      properties:
        report:
          type: array
          description: Список комиссий
          items:
            type: object
            properties:
              kgvpBooking:
                type: number
                description: Комиссия по модели «Бронирование», %
              kgvpMarketplace:
                type: number
                description: Комиссия по модели «Маркетплейс» (`FBS`), %
              kgvpPickup:
                type: number
                description: Комиссия по модели «Самовывоз из магазина продавца» (`C&C`), %
              kgvpSupplier:
                type: number
                description: Комиссия по моделям «Витрина» (`DBS`) и «Курьер WB» (`DBW`), %
              kgvpSupplierExpress:
                type: number
                description: Комиссия по модели «Витрина экспресс» (`EDBS`), %
              paidStorageKgvp:
                type: number
                description: Комиссия по модели «Склад WB» (`FBW`), %
              parentID:
                type: integer
                description: ID родительской категории
              parentName:
                type: string
                description: Название родительской категории
              subjectID:
                type: integer
                description: ID предмета
              subjectName:
                type: string
                description: Название предмета
    CommissionChina:
      type: object
      properties:
        report:
          type: array
          description: Список комиссий
          items:
            type: object
            properties:
              kgvpChina:
                type: number
                description: Комиссия для продавцов из Китая, %
              parentID:
                type: integer
                description: ID родительской категории
              parentName:
                type: string
                description: Название родительской категории
              subjectID:
                type: integer
                description: ID предмета
              subjectName:
                type: string
                description: Название предмета
    CommissionTurkey:
      type: object
      properties:
        report:
          type: array
          description: Список комиссий
          items:
            type: object
            properties:
              kgvpTurkey:
                type: number
                description: Комиссия для продавцов из Турции, %
              parentID:
                type: integer
                description: ID родительской категории
              parentName:
                type: string
                description: Название родительской категории
              subjectID:
                type: integer
                description: ID предмета
              subjectName:
                type: string
                description: Название предмета
    CommissionUzbekistan:
      type: object
      properties:
        report:
          type: array
          description: Список комиссий
          items:
            type: object
            properties:
              kgvpUzbekistan:
                type: number
                description: Комиссия для продавцов из Узбекистана, %
              parentID:
                type: integer
                description: ID родительской категории
              parentName:
                type: string
                description: Название родительской категории
              subjectID:
                type: integer
                description: ID предмета
              subjectName:
                type: string
                description: Название предмета
    CommissionUAE:
      type: object
      properties:
        report:
          type: array
          description: Список комиссий
          items:
            type: object
            properties:
              kgvpUAE:
                type: number
                description: Комиссия для продавцов из ОАЭ, %
              parentID:
                type: integer
                description: ID родительской категории
              parentName:
                type: string
                description: Название родительской категории
              subjectID:
                type: integer
                description: ID предмета
              subjectName:
                type: string
                description: Название предмета
    BadRequest:
      type: object
      properties:
        detail:
          type: string
          description: Детали ошибки
        origin:
          type: string
          description: ID внутреннего сервиса WB
        requestId:
          type: string
          description: Уникальный ID запроса
        title:
          type: string
          description: Заголовок ошибки
  responses:
    '401':
      description: Пользователь не авторизован
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Заголовок ошибки
              detail:
                type: string
                description: Детали ошибки
              code:
                type: string
                description: Внутренний код ошибки
              requestId:
                type: string
                description: Уникальный ID запроса
              origin:
                type: string
                description: ID внутреннего сервиса WB
              status:
                type: number
                description: HTTP статус-код
              statusText:
                type: string
                description: Расшифровка HTTP статус-кода
              timestamp:
                type: string
                format: date-time
                description: Дата и время запроса
          example:
            title: unauthorized
            detail: 'token problem; token is malformed: could not base64 decode signature: illegal base64 data at input byte 84'
            code: 07e4668e--a53a3d31f8b0-[UK-oWaVDUqNrKG]; 03bce=277; 84bd353bf-75
            requestId: 7b80742415072fe8b6b7f7761f1d1211
            origin: s2s-api-auth-catalog
            status: 401
            statusText: Unauthorized
            timestamp: '2024-09-30T06:52:38Z'
    '429':
      description: Слишком много запросов
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Заголовок ошибки
              detail:
                type: string
                description: Детали ошибки
              code:
                type: string
                description: Внутренний код ошибки
              requestId:
                type: string
                description: Уникальный ID запроса
              origin:
                type: string
                description: ID внутреннего сервиса WB
              status:
                type: number
                description: HTTP статус-код
              statusText:
                type: string
                description: Расшифровка HTTP статус-кода
              timestamp:
                type: string
                format: date-time
                description: Дата и время запроса
          example:
            title: too many requests
            detail: limited by c122a060-a7fb-4bb4-abb0-32fd4e18d489
            code: 07e4668e-ac2242c5c8c5-[UK-4dx7JUdskGZ]
            requestId: 9d3c02cc698f8b041c661a7c28bed293
            origin: s2s-api-auth-catalog
            status: 429
            statusText: Too Many Requests
            timestamp: '2024-09-30T06:52:38Z'