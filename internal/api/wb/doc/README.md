# Wildberries API 文档

本目录包含 Wildberries Seller API 的完整 OpenAPI 文档。

## 📁 文档结构

### 核心 API 文档

1. **[01-introduction.yaml](01-introduction.yaml)** - API 介绍和认证
   - API 概述
   - 认证方式
   - 速率限制
   - 错误处理

2. **[02-products.yaml](02-products.yaml)** - 商品管理 API
   - 分类、主题和特征管理
   - 商品卡片创建和编辑
   - 媒体文件管理
   - 标签管理
   - 价格和折扣管理
   - 卖家仓库管理
   - 库存管理

3. **[07-promotion.yaml](07-promotion.yaml)** - 推广营销 API
   - 广告活动管理
   - 广告活动创建
   - 广告活动管理
   - 财务数据
   - 广告活动参数
   - 媒体广告
   - 推广日历

4. **[10-analytics.yaml](10-analytics.yaml)** - 分析数据 API
   - 推广统计
   - 销售漏斗分析
   - 搜索查询分析
   - 库存报告
   - 卖家分析 CSV

## 🔗 API 端点概览


### 推广服务端点
- `https://advert-api.wildberries.ru` - 推广 API 基础 URL
- 广告活动管理、竞价设置、统计数据

### 内容服务端点
- `https://content-api.wildberries.ru` - 内容 API 基础 URL
- 商品卡片、媒体文件、分类管理

### 价格服务端点
- `https://discounts-prices-api.wildberries.ru` - 价格 API 基础 URL
- 价格和折扣管理

### 统计服务端点
- `https://statistics-api.wildberries.ru` - 统计 API 基础 URL
- 订单统计、销售数据

### 库存服务端点
- `https://marketplace-api.wildberries.ru` - 市场 API 基础 URL
- 库存管理、仓库信息

### 分析服务端点
- `https://seller-analytics-api.wildberries.ru` - 卖家分析 API 基础 URL
- 高级分析和报告

## 🔑 认证方式

所有 API 都使用 Header API Key 认证：

```yaml
security:
  - HeaderApiKey: []
```

在请求头中添加：
```
Authorization: your-api-key
```

## 📊 速率限制

不同服务有不同的速率限制：

- **推广 API**: 5 请求/秒
- **内容 API**: 10 请求/分钟  
- **价格 API**: 10 请求/分钟
- **统计 API**: 根据具体端点而定

## 🔧 使用说明

1. **查看具体 API**: 打开对应的 YAML 文件查看详细的 API 规范
2. **测试 API**: 可以使用 Swagger UI 或 Postman 导入这些文档进行测试
3. **代码生成**: 可以使用 OpenAPI 代码生成工具基于这些文档生成客户端代码

## 📝 版本信息

- **商品管理 API**: products 版本
- **推广营销 API**: promotion 版本  
- **分析数据 API**: analytics 版本

## 🔄 更新日志

文档会根据 WB 官方 API 的更新进行同步更新。

## 📞 支持

如有文档相关问题，请查看：
1. WB 官方开发者文档
2. 项目 README.md
3. 提交 Issue 获取帮助