openapi: 3.0.1
info:
  title: Product Management
  version: products
  description: Manage content, prices and stocks
  x-file-name: products
security:
  - HeaderApiKey: []
tags:
  - name: Categories, Subjects, and Characteristics
    description: ''
  - name: Creating Product Cards
    description: ''
  - name: Product Cards
    description: ''
  - name: Media Files
    description: ''
  - name: Tags
    description: ''
  - name: Prices and Discounts
    description: ''
  - name: Seller Warehouses
    description: ''
  - name: Inventory
    description: ''
paths:
  /content/v2/object/parent/all:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Products parent categories
      description: |
        Returns the list of all products parent categories

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Categories, Subjects, and Characteristics
      parameters:
        - name: locale
          in: query
          description: Product name language (`ru`, `en`, `zh`)
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    items:
                      type: object
                      properties:
                        name:
                          description: Category name
                          type: string
                          example: Электроника
                        id:
                          type: integer
                          description: Parent category ID
                          example: 479
                        isVisible:
                          description: Visibility on the site
                          type: boolean
                          example: true
                  error:
                    description: Error flag
                    type: boolean
                    example: false
                  errorText:
                    description: Error description
                    type: string
                    example: ''
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: string
                    example: ''
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/object/all:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Subjects list
      description: |
        Returns the list of all available subjects, subjects parent categories and their IDs

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Categories, Subjects, and Characteristics
      parameters:
        - name: locale
          in: query
          schema:
            type: string
            example: en
            description: Response language (ru, en, zh)
        - name: name
          in: query
          schema:
            type: string
            example: Socks
            description: Search by product name (any supported language)
        - name: limit
          in: query
          schema:
            type: integer
            example: 1000
            description: Number of search results, maximum 1,000
            default: 30
        - name: offset
          in: query
          schema:
            type: integer
            example: 5000
            description: How many results to skip
            default: 0
        - name: parentID
          in: query
          schema:
            type: integer
            example: 1000
            description: Parent category ID
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    description: Categories and subjects
                    items:
                      type: object
                      properties:
                        subjectID:
                          type: integer
                          description: Subject ID
                        parentID:
                          type: integer
                          description: Category ID
                        subjectName:
                          type: string
                          description: Subject name
                        parentName:
                          type: string
                          description: Category name
                  error:
                    type: boolean
                    description: Error flag
                  errorText:
                    type: string
                    description: Error text
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: string
              example:
                data:
                  - subjectID: 2560
                    parentID: 479
                    subjectName: 3D очки
                    parentName: Электроника
                  - subjectID: 1152
                    parentID: 858
                    subjectName: 3D-принтеры
                    parentName: Оргтехника
                error: false
                errorText: ''
                additionalErrors: null
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/object/charcs/{subjectId}:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Subject characteristics
      description: |
        Returns list of the subject characteristics by its ID

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Categories, Subjects, and Characteristics
      parameters:
        - name: subjectId
          in: path
          required: true
          description: ID предмета
          schema:
            type: integer
            example: 105
        - name: locale
          in: query
          description: Language of `subjectName` and `name` (ru, en, zh)
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        charcID:
                          type: integer
                          description: Characteristics ID
                        subjectName:
                          type: string
                          description: Subject name
                        subjectID:
                          type: integer
                          description: Subject ID
                        name:
                          type: string
                          description: Characteristic name
                        required:
                          type: boolean
                          description: Characteristic required
                        unitName:
                          type: string
                          description: Unit (sm, gr and others)
                        maxCount:
                          description: |
                            Max count of values that can be assigned to this characteristic.
                            <br>zero means no limit.
                          type: integer
                        popular:
                          description: The characteristic is popular with users
                          type: boolean
                        charcType:
                          description: Characteristic type (1 and 0 - string or list of strings; 4 - number or list of numbers
                          type: integer
                  error:
                    type: boolean
                    description: Error flag
                    example: false
                  errorText:
                    description: Error description
                    type: string
                    example: ''
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                example:
                  data:
                    - charcID: 54337
                      subjectName: Кроссовки
                      subjectID: 105
                      name: Размер
                      required: false
                      unitName: см
                      maxCount: 0
                      popular: false
                      charcType: 4
                  error: false
                  errorText: ''
                  additionalErrors: null
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/directory/colors:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Color
      description: |
        Provides values of color characteristic.

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Categories, Subjects, and Characteristics
      parameters:
        - name: locale
          in: query
          description: |
            Language of `subjectName` and `name` (ru, en, zh) 
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    items:
                      type: object
                      properties:
                        name:
                          description: Color name
                          type: string
                          example: персиковый мелок
                        parentName:
                          description: Parent color name
                          type: string
                          example: оранжевый
                  error:
                    description: Error flag
                    type: boolean
                    example: false
                  errorText:
                    description: Error description
                    type: string
                    example: ''
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: string
                    example: ''
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/directory/kinds:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Gender
      description: |
        Provides values of gender characteristic.

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Categories, Subjects, and Characteristics
      parameters:
        - name: locale
          in: query
          description: Language of `subjectName` and `name` (ru, en, zh)
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    description: List of values for sex characteristic.
                    type: array
                    items:
                      type: string
                      example: Мужской
                  error:
                    description: Error flag
                    type: boolean
                    example: false
                  errorText:
                    description: Error description
                    type: string
                    example: ''
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: string
                    example: ''
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/directory/countries:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Country of origin
      description: |
        Provides value of characteristic country of origin.

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Categories, Subjects, and Characteristics
      parameters:
        - name: locale
          in: query
          description: |
            Language of `subjectName` and `name` (ru, en, zh) 
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    items:
                      type: object
                      properties:
                        name:
                          description: Value of characteristic country of origin
                          type: string
                          example: Афганистан
                        fullName:
                          description: Full country name
                          type: string
                          example: Исламский Эмират Афганистан
                  error:
                    description: Error flag
                    type: boolean
                    example: false
                  errorText:
                    description: Error description
                    type: string
                    example: ''
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: string
                    example: ''
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/directory/seasons:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Season
      description: |
        Provide values of season characteristic

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Categories, Subjects, and Characteristics
      parameters:
        - name: locale
          in: query
          description: Language of `subjectName` and `name` (ru, en, zh)
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    description: Array of values for season characteristic.
                    type: array
                    items:
                      type: string
                      example: демисезон
                  error:
                    description: Error flag
                    type: boolean
                    example: false
                  errorText:
                    description: Error description
                    type: string
                    example: ''
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: string
                    example: ''
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/directory/vat:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: VAT rate
      description: |
        Returns a list of values for the **VAT rate** characteristic

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Categories, Subjects, and Characteristics
      parameters:
        - name: locale
          in: query
          required: true
          description: Language of the `data` elements values (`ru`, `en`, `zh`). Not used in the sandbox
          schema:
            type: string
            example: ru
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: string
                  error:
                    type: boolean
                    description: Error flag
                  errorText:
                    type: string
                    description: Error text
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: string
              example:
                data:
                  - '0'
                  - '10'
                  - '20'
                  - Без НДС
                  - '13'
                error: false
                errorText: ''
                additionalErrors: null
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/directory/tnved:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: HS codes
      description: |
        Method provides list of HS codes by category name and filter by HS code.

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Categories, Subjects, and Characteristics
      parameters:
        - name: subjectID
          in: query
          required: true
          description: Subject ID
          schema:
            type: integer
            example: 105
        - name: search
          in: query
          description: Search by HS code. Works only with the subjectID parameter
          schema:
            type: integer
            example: 6106903000
        - name: locale
          in: query
          description: Language (`ru`, `en`, `zh`) of the `subjectName` and `name` fields values. Not used in a sandbox
          schema:
            type: string
            example: en
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    description: Data
                    type: array
                    items:
                      type: object
                      properties:
                        tnved:
                          type: string
                          description: HS code
                        isKiz:
                          type: boolean
                          description: |
                            - `true` - labeling code is required
                            - `false` - labeling code is not required
                  error:
                    description: Error flag
                    type: boolean
                    example: false
                  errorText:
                    description: Error description
                    type: string
                  additionalErrors:
                    type: string
                    nullable: true
                    description: Additional errors
              example:
                data:
                  - tnved: '6106903000'
                    isKiz: true
                error: false
                errorText: ''
                additionalErrors: null
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/tags:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Tags list
      description: |
        Returns seller's tags list

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Tags
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    properties:
                      id:
                        description: Numeric tag identifier
                        type: integer
                      color:
                        description: Tag color
                        type: string
                      name:
                        description: Tag name
                        type: string
                  error:
                    description: Error flag
                    type: boolean
                    example: false
                  errorText:
                    description: Error description
                    type: string
                    example: ''
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: string
                    example: ''
              example:
                data:
                  - id: 1
                    color: D1CFD7
                    name: Sale
                error: false
                errorText: ''
                additionalErrors: ''
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/tag:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Create a tag
      description: |
        Creates a tag.

        It is possible to create 15 tags.

        The maximum length of a tag is 15 characters

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Tags
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                color:
                  description: |
                    Tag color.
                    <dl>
                    <dt>Available colors:</dt>
                    <dd><code>D1CFD7</code> - grey</dd>
                    <dd><code>FEE0E0</code> - red</dd>
                    <dd><code>ECDAFF</code> - purple</dd>
                    <dd><code>E4EAFF</code> - blue</dd>
                    <dd><code>DEF1DD</code> - green</dd>
                    <dd><code>FFECC7</code> - yellow</dd>
                    </dl>
                  type: string
                  example: D1CFD7
                name:
                  description: Tag name
                  type: string
                  example: Sale
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseContentError6'
              examples:
                responseOK200:
                  $ref: '#/components/examples/responseOK200'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/responseBodyContentError400'
                  - $ref: '#/components/schemas/responseContentError4'
                  - $ref: '#/components/schemas/responseContentError6'
              examples:
                responseBodyContentError400:
                  $ref: '#/components/examples/responseBodyContentError400'
                responseBody400LenName:
                  $ref: '#/components/examples/responseBody400LenName'
                responseBodyLimitTag:
                  $ref: '#/components/examples/responseBodyLimitTag'
                responseBodyNameNotExist:
                  $ref: '#/components/examples/responseBodyNameNotExist'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/tag/{id}:
    servers:
      - url: https://content-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      summary: Update the tag
      description: |
        Changes tag data: name and color

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Tags
      parameters:
        - name: id
          in: path
          schema:
            type: integer
            example: 1
          description: Numeric tag ID
          required: true
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                color:
                  description: Tag color
                  type: string
                  example: D1CFD7
                name:
                  description: Tag name
                  type: string
                  example: Sale
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/responseContentError6'
                  - $ref: '#/components/schemas/responseContentError4'
              examples:
                responseOK200:
                  $ref: '#/components/examples/responseOK200'
                responseNotFound200:
                  $ref: '#/components/examples/responseNotFound200'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/responseBodyContentError400'
                  - $ref: '#/components/schemas/responseContentError4'
              examples:
                responseBodyContentError400:
                  $ref: '#/components/examples/responseBodyContentError400'
                responseBody400LenName:
                  $ref: '#/components/examples/responseBody400LenName'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
    delete:
      security:
        - HeaderApiKey: []
      summary: Delete the tag
      description: |
        Deletes the tag

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Tags
      parameters:
        - name: id
          in: path
          schema:
            type: integer
            example: 1
          description: Numeric tag ID
          required: true
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/responseContentError6'
                  - $ref: '#/components/schemas/responseContentError5'
              examples:
                responseOK200:
                  $ref: '#/components/examples/responseOK200'
                responseNotFound200Del:
                  $ref: '#/components/examples/responseNotFound200Del'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/responseBodyContentError400'
                  - $ref: '#/components/schemas/responseContentError5'
              examples:
                responseBodyContentError400:
                  $ref: '#/components/examples/responseBodyContentError400'
                responseBodyTagNotExist:
                  $ref: '#/components/examples/responseBodyTagNotExist'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/tag/nomenclature/link:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Tag management in the product card
      description: |
        The method allows to add tags to the product card and remove tags from the product card.<br>
        When removing a tag from a product card, the tag itself is not removed.<br>
        It is possible to add 15 tags to a product card.

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Tags
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nmID:
                  description: WB article
                  type: integer
                tagsIDs:
                  description: |
                    An array of numeric tag IDs.<br>  
                    When removing a tag from a product card, the tag itself is not removed.<br>
                    To add tags to existing ones in the product card, you need to specify in the request the new tags and the tags that are already exist in the product card.
                  type: array
                  items:
                    type: integer
                    description: Numeric tag identifier
            example:
              nmID: 179891389
              tagsIDs:
                - 123456
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseContentError6'
              examples:
                responseOK200:
                  $ref: '#/components/examples/responseOK200'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseContentError4'
              examples:
                IncorrectRequest4:
                  $ref: '#/components/examples/IncorrectRequest4'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/get/cards/list:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Product Cards
      summary: Product cards list
      description: |
        <div class="description_auth">
          Method is available with the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">token</a> of the <strong>Promotion</strong> category
        </div>

        Returns the list of created product cards.<br>

        To get **more than 100** product cards, you need to use pagination:

          <ol>
            <li>Make first request (all listed params are required):
              <pre>
                {
                  "settings": {                      
                    "cursor": {
                      "limit": 100
                    },
                    "filter": {
                      "withPhoto": -1
                    }
                  }
                }</pre>                               
            </li>
            <li>From last part of response list, copy 2 strings from `cursor` field response:
              <ul>
                <li><code>"updatedAt": "***"</code></li>
                <li><code>"nmID": ***</code></li>
              </ul>
            <li>Paste the copied strings to the request <code>cursor</code>, repeat the request. </li>
            <li>Repeat <b>2</b> and <b>3</b>, until <code>total</code> will be less than <code>limit</code> in response. This will mean you got all cards.
          </ol>

        <div class="description_important">
          Product cards from the trash are not provided in the method response. You can get these product cards via <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1get~1cards~1trash/post">different method</a>.
        </div>    

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
        <br>
          • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
          • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
          • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      parameters:
        - name: locale
          in: query
          schema:
            type: string
            example: ru
          description: |
            Language for response fields `name`, `value` and `object` translation:  `ru` - Russian, `en` - English, `zh` - Chinese.

            Is not used in the sandbox  
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                settings:
                  type: object
                  description: Settings
                  properties:
                    sort:
                      description: Sort by
                      type: object
                      properties:
                        ascending:
                          type: boolean
                          description: Sort by **updatedAt** field (`false` - descending, `true` - ascending)
                    filter:
                      description: Filters
                      type: object
                      properties:
                        withPhoto:
                          type: integer
                          description: |
                            Photo:
                              * `0` — only cards without photos
                              * `1` — only cards with photos
                              * `-1` — all cards
                        textSearch:
                          type: string
                          description: Seller's article, Wildberries article or barcode search
                        tagIDs:
                          type: array
                          description: Tags ID search
                          items:
                            type: integer
                        allowedCategoriesOnly:
                          description: |
                            Category: true — permitted, false — forbidden
                          type: boolean
                        objectIDs:
                          type: array
                          description: Subject ID search
                          items:
                            type: integer
                        brands:
                          type: array
                          description: Brand search
                          items:
                            type: string
                        imtID:
                          description: |
                            `imtID` search
                          type: integer
                    cursor:
                      description: |
                        Cursor
                      type: object
                      properties:
                        limit:
                          type: integer
                          description: Number of cards in response
            example:
              settings:
                sort:
                  ascending: false
                filter:
                  textSearch: '****************'
                  allowedCategoriesOnly: true
                  tagIDs:
                    - 345
                    - 415
                  objectIDs:
                    - 235
                    - 67
                  brands:
                    - уллу
                    - EkkE
                  imtID: 328632
                  withPhoto: -1
                cursor:
                  updatedAt: '2023-12-06T11:17:00.96577Z'
                  nmID: 370870300
                  limit: 11
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  cards:
                    type: array
                    description: Product cards
                    items:
                      type: object
                      properties:
                        nmID:
                          type: integer
                          description: WB article
                        imtID:
                          type: integer
                          description: |
                            Merged product card ID<br>
                            The same for all WB article of a merged product card<br>
                            The product card has it, even if one is not merged with any other card
                        nmUUID:
                          type: string
                          format: uuid
                          description: Internal system ID of the product card
                        subjectID:
                          type: integer
                          description: Subject ID
                        subjectName:
                          type: string
                          description: Subject name
                        vendorCode:
                          type: string
                          description: Seller's article
                        brand:
                          type: string
                          description: Brand
                        title:
                          type: string
                          description: Product title
                        description:
                          type: string
                          description: Product description
                        needKiz:
                          description: |
                            Is a [labeling code](https://chestnyznak.ru/en) required for the product
                            <br>
                            * `false` — labeling code is not required
                            <br>  
                            * `true` — labeling code is required
                          type: boolean
                        photos:
                          type: array
                          description: Photo array
                          items:
                            type: object
                            properties:
                              big:
                                description: URL of `900х1200` photo
                                type: string
                              c246x328:
                                description: URL of `248х328` photo
                                type: string
                              c516x688:
                                description: URL of `516х688` photo
                                type: string
                              square:
                                description: URL of `600х600` photo
                                type: string
                              tm:
                                description: URL of `75х100` photo
                                type: string
                        video:
                          type: string
                          description: Video URL
                        dimensions:
                          description: Dimensions and weight of packed product in `centimeters` and `kilograms`
                          type: object
                          properties:
                            length:
                              type: integer
                              description: Length, cm
                            width:
                              type: integer
                              description: Width, cm
                            height:
                              type: integer
                              description: Height, cm
                            weightBrutto:
                              type: number
                              description: Weight, kg<br>Number of decimal places <=3
                            isValid:
                              type: boolean
                              description: |
                                Potential dimension difference:

                                - `true` — not detected. `"isValid": true` does not guarantee that the dimensions are correct. In some cases (for example, when creating a new product category), `"isValid": true` will be returned for any values except zero.
                                  
                                - `false` — the specified dimensions significantly differ from the average for the category (subject). it is recommended to double-check whether the product dimensions in the packaging are correctly specified **in centimeters**. The functionality of the product card, including the calculation of logistics and storage, will not be limited. Logistics and storage will continue to be calculated based on the current dimensions. Also, `"isValid": false` is returned when there are missing values or any dimension is zero.
                        characteristics:
                          type: array
                          description: Characteristics
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                description: Characteristic ID
                              name:
                                type: string
                                description: Characteristic name
                              value:
                                description: Characteristic value
                        sizes:
                          type: array
                          description: Product sizes
                          items:
                            type: object
                            properties:
                              chrtID:
                                type: integer
                                description: Size ID
                              techSize:
                                type: string
                                description: Size (for example, S or 42)
                              wbSize:
                                type: string
                                description: Russian size
                              skus:
                                type: array
                                description: Barcode
                                items:
                                  type: string
                                  example: 12345Ejf5
                        tags:
                          description: Tags
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                description: Tag ID
                              name:
                                type: string
                                description: Tag name
                              color:
                                type: string
                                description: |
                                  Color:

                                    * `D1CFD7` - grey
                                    * `FEE0E0` - red
                                    * `ECDAFF` - purple
                                    * `E4EAFF` - blue
                                    * `DEF1DD` - green
                                    * `FFECC7` - yellow                                               
                        createdAt:
                          type: string
                          description: Creation date and time
                        updatedAt:
                          type: string
                          description: Update date and time
                  cursor:
                    type: object
                    description: Pagination
                    properties:
                      updatedAt:
                        type: string
                        description: Date and time for next card page
                      nmID:
                        type: integer
                        description: WB article for next card page
                      total:
                        type: integer
                        description: Number of cards
                example:
                  cards:
                    - nmID: 12345678
                      imtID: 123654789
                      nmUUID: 01bda0b1-5c0b-736c-b2be-d0a6543e9be
                      subjectID: 7771
                      subjectName: AKF системы
                      vendorCode: wb7f6mumjr1
                      brand: Тест
                      title: Тест-система
                      description: Тестовое описание
                      needKiz: false
                      photos:
                        - big: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/big/1.webp
                          c246x328: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/c246x328/1.webp
                          c516x688: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/c516x688/1.webp
                          square: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/square/1.webp
                          tm: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/tm/1.webp
                      video: https://videonme-basket-12.wbbasket.ru/vol137/part22557/225577433/hls/1440p/index.m3u8
                      dimensions:
                        length: 5
                        width: 3
                        height: 8
                        weightBrutto: 0.968
                        isValid: false
                      characteristics:
                        - id: 14177449
                          name: Цвет
                          value:
                            - красно-сиреневый
                      sizes:
                        - chrtID: 316399238
                          techSize: '0'
                          skus:
                            - '987456321654'
                      tags:
                        - id: 592569
                          name: Популярный
                          color: D1CFD7
                      createdAt: '2023-12-06T11:17:00.96577Z'
                      updatedAt: '2023-12-06T11:17:00.96577Z'
                  cursor:
                    updatedAt: '2023-12-06T11:17:00.96577Z'
                    nmID: 123654123
                    total: 1
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
            plain/text:
              example: Request body can not be decoded
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/cards/error/list:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: List of failed product cards with errors
      description: |
        Returns the list of product cards and the list of errors encountered during product card creation<br>

        <div class="description_important">
          To delete product card from the errors list you need to repeat the <a href="/openapi/work-with-products#tag/Creating-Products-Cards/paths/~1content~1v2~1cards~1upload/post">product card creation</a> request with fixed errors.
        </div>

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Product Cards
      parameters:
        - name: locale
          in: query
          schema:
            type: string
            example: en
          description: |
            Language of multi-language parameters (for which the multi-language option is provided).

            Is not used in the sandbox
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        object:
                          description: Category for which the product card was created with the given product card
                          type: string
                          example: Блузки
                        objectID:
                          type: integer
                          description: Product ID
                          example: 1234
                        vendorCode:
                          description: Supplier's article
                          type: string
                          example: '6000000001'
                        updateAt:
                          description: The date and time of creating product card with specified product card request
                          type: string
                          example: '2022-06-15T14:37:13Z'
                        errors:
                          description: |
                            List of errors due to which the request to create a product card with a given product card is not processed
                          type: array
                          items:
                            type: string
                            example: Поля Рос. размер, Размер обязательны для заполнения
                  error:
                    description: Error flag.
                    type: boolean
                    example: false
                  errorText:
                    description: Error description.
                    type: string
                    example: ''
                  additionalErrors:
                    description: Any additional errors.
                    type: string
                    nullable: true
                    example: null
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/cards/update:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      tags:
        - Product Cards
      summary: Update product cards
      description: |
        Edits product cards. If you need some product data, get it using [product cards list](/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1get~1cards~1list/post).

        <div class="description_important">
          The product card is overwritten during an update. Therefore, you need to include <strong>all</strong> parameters of the product card in the request, including those you do not intend to update.
        </div>

        The dimensions of the products can only be specified in `centimeters`, and the weight of packed products must be specified in `kilograms`.

        You can not edit or delete barcodes but you can add new ones. You have not to send `photos`, `video` and `tags` and can not edit them.

        If this method response is Success (200) but product card was not updated, check errors using [list of failed product cards with errors](/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1error~1list/get).

        With one request you can edit maximum product cards (`nmID`). Maximum request size is 10 Mb

        <div class="description_limit">
          Maximum of 10 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account
        </div>
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                required:
                  - nmID
                  - vendorCode
                  - sizes
                properties:
                  nmID:
                    description: WB article
                    type: integer
                  vendorCode:
                    description: Supplier's article
                    type: string
                  brand:
                    type: string
                    description: Brand
                  title:
                    type: string
                    description: Product title
                    maxLength: 60
                  description:
                    type: string
                    description: |
                      Product description<br>
                      The maximum number of characters depends on the product category<br>
                      Standard - 2000, minimum - 1000, maximum - 5000<br>
                      More details about description rules in **Product card filling rules** in [Instructions](https://seller.wildberries.ru/help-center/article/A-113#описание) category of sellers portal
                  dimensions:
                    description: |
                      Dimensions and weight of packed product in `centimeters` and `kilograms`
                    type: object
                    properties:
                      length:
                        type: integer
                        description: Length, cm
                      width:
                        type: integer
                        description: Width, cm
                      height:
                        type: integer
                        description: Height, cm
                      weightBrutto:
                        type: number
                        description: Weight, kg<br>Number of decimal places <=3
                  characteristics:
                    description: Product characteristics
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: Characteristic ID
                        value:
                          description: Characteristic value
                  sizes:
                    type: array
                    description: |
                      Product sizes<br>
                      If product has no sizes, send only barcodes
                    items:
                      type: object
                      properties:
                        chrtID:
                          type: integer
                          description: |
                            WB article of this size<br>
                            Required for existing sizes<br>
                            If you add new size, do not send this ID
                        techSize:
                          type: string
                          description: Size (for example, XL, S, 45)
                        wbSize:
                          type: string
                          description: Russian size
                        skus:
                          type: array
                          items:
                            type: string
                          description: Barcodes
              example:
                - nmID: 11111111
                  vendorCode: wbiz72wmro
                  brand: ''
                  title: Свитер женский оверсайз с горлом
                  description: '12345'
                  dimensions:
                    length: 55
                    width: 40
                    height: 15
                    weightBrutto: 3
                  characteristics:
                    - id: 14177450
                      value:
                        - хлопок 50% акрил 50%
                    - id: 50
                      value:
                        - свободный крой
                  sizes:
                    - chrtID: 12345678
                      techSize: ONE SIZE
                      wbSize: 78-90
                      skus:
                        - '123487653460134'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseCardCreate'
              example:
                data: null
                error: false
                errorText: ''
                additionalErrors: {}
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '413':
          description: The request body size exceeds the given limit
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  code:
                    type: string
                    description: Internal error code
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
                  status:
                    type: number
                    description: HTTP status code
                  statusText:
                    type: string
                    description: Text of the HTTP status code
              example:
                title: request body too long
                detail: https://openapi.wildberries.ru/content/api/ru/
                code: 71d3de1b-001e-488f-bbf5-55c31254fbeb
                requestId: MN8usr6RfrzWHZfucSvNgb
                origin: s2s-api-auth-content
                status: 413
                statusText: Request Entity Too Large
        '429':
          $ref: '#/components/responses/429'
  /content/v2/cards/moveNm:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      tags:
        - Product Cards
      summary: Merging or separating of product cards
      description: |
        The method merges and separates product cards. Product cards are considered merged if they have the same `imtID`.<br><br>

        To merge product cards, make a request **specifying** the `imtID`. You can merge up to 30 product cards at a time.<br>

        To separate product cards, make a request **without specifying** the `imtID`. New `imtID`s will be generated for the separated cards.<br><br>

        If you separate multiple product cards simultaneously, these cards will merge into one and receive a new `imtID`.<br>

        To assign a unique `imtID` to each product card, you need to send one product card per request.<br><br>

        The maximum request size is 10 MB.

        <div class="description_important">
          It is possible to merge product cards containing only the same subjects.
        </div>

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      requestBody:
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/requestMoveNmsImtConn'
                - $ref: '#/components/schemas/requestMoveNmsImtDisconn'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseCardCreate'
              example:
                data: null
                error: false
                errorText: ''
                additionalErrors: {}
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/responseContentError1'
                  - $ref: '#/components/schemas/responseCardCreate'
                  - $ref: '#/components/schemas/responseIncorrectDate'
              examples:
                responseExceededLimit:
                  $ref: '#/components/examples/responseExceededLimit'
                responseCombining:
                  $ref: '#/components/examples/responseCombining'
                responseIncorrectRequestFormat:
                  $ref: '#/components/examples/responseIncorrectRequestFormat'
                responseNonExistentNmId:
                  $ref: '#/components/examples/responseNonExistentNmId'
                responseNonExistentImt:
                  $ref: '#/components/examples/responseNonExistentImt'
                responseCardCreate1:
                  $ref: '#/components/examples/responseCardCreate1'
                responseDuplicateRequests:
                  $ref: '#/components/examples/responseDuplicateRequests'
                responseAllCardsInSameGroup:
                  $ref: '#/components/examples/responseAllCardsInSameGroup'
                responseIncorrectBeginDate:
                  $ref: '#/components/examples/responseIncorrectBeginDate'
                responseIncorrectEndDate:
                  $ref: '#/components/examples/responseIncorrectEndDate'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseCardCreate'
        '413':
          description: The request body size exceeds the given limit
          content:
            application/json:
              schema:
                type: string
              examples:
                BodySizeExceedsTheGivenLimit:
                  $ref: '#/components/examples/BodySizeExceedsTheGivenLimit'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/cards/delete/trash:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Transfer product card to trash
      description: |
        Transfers the product card to trash. In doing so, the product card would not be deleted.

        <div class="description_important">
          When transferring product cards to the trash, this product card is removed from the product card, meaning it is assigned a new <code>imtID</code>.
        </div>

        After 30 days in the trash the product card would be deleted automatically. The trash is cleared every night according to Moscow time.<br>
        The product card can be deleted at any time in [personal account](https://seller.wildberries.ru/new-goods/basket-cards).

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Product Cards
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nmIDs:
                  description: WB article (max. 1000)
                  type: array
                  items:
                    type: integer
            example:
              nmIDs:
                - 123456789
                - 987654321
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    nullable: true
                  error:
                    description: Error flag
                    type: boolean
                  errorText:
                    description: Error description
                    type: string
                  additionalErrors:
                    description: Any additional errors
                    type: object
              example:
                data: null
                error: false
                errorText: ''
                additionalErrors: {}
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/cards/recover:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Recover product card from trash
      description: |
        Returns the product card from trash

        <div class="description_important">
          When restoring the product card from the trash, its <code>imtID</code> doesn't remain the same as it was for the product card in the trash.
        </div>

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Product Cards
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nmIDs:
                  description: WB article (max. 1000)
                  type: array
                  items:
                    type: integer
            example:
              nmIDs:
                - 123456789
                - 987654321
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    nullable: true
                  error:
                    description: Error flag
                    type: boolean
                  errorText:
                    description: Error text
                    type: string
                  additionalErrors:
                    description: Additional errors
                    type: object
              example:
                data: null
                error: false
                errorText: ''
                additionalErrors: {}
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/get/cards/trash:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Product cards in trash list
      description: |
        <div class="description_auth">
          Method is available with the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">token</a> of the <strong>Promotion</strong> category
        </div>

        Returns list of product cards in trash.

        To get full list of product cards, if their number **exceed 100**, user need to use pagination.

          <ol>
            <li>Make the first request: <br>
              <pre>
                {
                  "settings": {                      
                    "cursor": {
                      "limit": 100
                    }
                  }
                }</pre>                               
            </li>
            <li>From last part of response list copy 2 strings from <code>cursor</code> field response:
              <ul>
                <li><code>"trashedAt": "***"</code></li>
                <li><code>"nmID": ***</code></li>
              </ul>
            <li>Paste copied strings in <code>cursor</code> request parameter, repeat the method request. </li>
            <li>Repeat <b>2</b> and <b>3</b>, until <code>total</code> will be less than <code>limit</code> in response. This will mean you got all cards.
          </ol>

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
        <br>
          • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
          • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
          • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Product Cards
      parameters:
        - name: locale
          in: query
          schema:
            type: string
            enum:
              - ru
              - en
              - zh
          description: |
            Language of `name`, `value` and `object` parameters
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                settings:
                  type: object
                  description: Settings
                  properties:
                    sort:
                      description: Sort by
                      type: object
                      properties:
                        ascending:
                          type: boolean
                          description: "`trashedAt`: true — ascending, false\_— descending\n"
                    cursor:
                      description: Cursor
                      type: object
                      properties:
                        limit:
                          type: integer
                          description: Number of cards in response
                    filter:
                      description: Filters
                      type: object
                      properties:
                        textSearch:
                          type: string
                          description: Seller's article, Wildberries article or barcode to search
            example:
              settings:
                sort:
                  ascending: false
                filter:
                  textSearch: '****************'
                cursor:
                  trashedAt: '2023-12-06T11:17:00.96577Z'
                  nmID: 370870300
                  limit: 11
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  cards:
                    description: The list of requested product cards
                    type: array
                    items:
                      type: object
                      properties:
                        nmID:
                          description: WB article
                          type: integer
                        vendorCode:
                          type: string
                          description: Seller's article
                        subjectID:
                          type: integer
                          description: Subject ID
                        subjectName:
                          type: string
                          description: Subject name
                        photos:
                          type: array
                          description: Photo array
                          items:
                            type: object
                            properties:
                              big:
                                description: URL of `900х1200` photo
                                type: string
                              c246x328:
                                description: URL of `248х328` photo
                                type: string
                              c516x688:
                                description: URL of `516х688` photo
                                type: string
                              square:
                                description: URL of `600х600` photo
                                type: string
                              tm:
                                description: URL of `75х100` photo
                                type: string
                        sizes:
                          description: Product sizes
                          type: array
                          items:
                            type: object
                            properties:
                              chrtID:
                                type: integer
                                description: Size ID
                              techSize:
                                description: Product size (eg S, M, L, XL, 42, 42-43)
                                type: string
                              wbSize:
                                description: Russian product size
                                type: string
                              skus:
                                description: Array of barcodes
                                type: array
                                items:
                                  type: string
                                  example: 12345Ejf5
                        video:
                          type: string
                          description: Video URL
                        dimensions:
                          description: Dimensions and weight of packed product in `centimeters` and `kilograms`
                          type: object
                          properties:
                            length:
                              type: integer
                              description: Length, cm
                            width:
                              type: integer
                              description: Width, cm
                            height:
                              type: integer
                              description: Height, cm
                            weightBrutto:
                              type: number
                              description: Weight, kg<br>Number of decimal places <=3
                            isValid:
                              type: boolean
                              description: |
                                Potential dimension difference:

                                - `true` — not detected. `"isValid": true` does not guarantee that the dimensions are correct. In some cases (for example, when creating a new product category), `"isValid": true` will be returned for any values except zero.

                                - `false` — the specified dimensions significantly differ from the average for the category (subject). it is recommended to double-check whether the product dimensions in the packaging are correctly specified **in centimeters**. The functionality of the product card, including the calculation of logistics and storage, will not be limited. Logistics and storage will continue to be calculated based on the current dimensions. Also, `"isValid": false` is returned when there are missing values or any dimension is zero.
                        characteristics:
                          type: array
                          description: Characteristics
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                description: Characteristic ID
                              name:
                                type: string
                                description: Characteristic name
                              value:
                                description: Characteristic value
                          createdAt:
                            type: string
                            description: Creation date and time
                          trashedAt:
                            type: string
                            description: Throwing date and time
                  cursor:
                    type: object
                    description: Pagination
                    properties:
                      trashedAt:
                        type: string
                        description: Date and time for next card page
                      nmID:
                        type: integer
                        description: WB article for next card page
                      total:
                        type: integer
                        description: Number of cards
              example:
                cards:
                  - nmID: 1234567
                    vendorCode: wb5xsy5ftj
                    subjectID: 1436
                    subjectName: Ведра хозяйственные
                    photos:
                      - big: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/big/1.webp
                        c246x328: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/c246x328/1.webp
                        c516x688: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/c516x688/1.webp
                        square: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/square/1.webp
                        tm: https://basket-10.wbbasket.ru/vol1592/part159206/159206280/images/tm/1.webp
                    video: https://videonme-basket-12.wbbasket.ru/vol137/part22557/225577433/hls/1440p/index.m3u8
                    sizes:
                      - chrtID: 111111111
                        techSize: '0'
                        skus:
                          - xxxxxxxxxxxx
                    dimensions:
                      length: 55
                      width: 40
                      height: 15
                      weightBrutto: 3.2
                      isValid: false
                    createdAt: '2023-12-05T14:55:09.323462Z'
                    trashedAt: '2023-12-06T10:57:42.193028Z'
                cursor:
                  trashedAt: '2023-12-06T10:57:42.193028Z'
                  nmID: 194128521
                  total: 1
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
            plain/text:
              example: Request body can not be decoded
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/cards/limits:
    servers:
      - url: https://content-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      summary: Limits for the product cards
      description: |
        The method allows to get separately free and paid vendor limits for creating product cards.<br>
        To calculate the number of cards that can be created, use the formula: (freeLimits + paidLimits) - Number of cards created.<br>
        All cards that can be obtained using the [product cards list](/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1get~1cards~1list/post) and [list of product cards that are in the trash](/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1get~1cards~1trash/post) methods are considered created.

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Creating Product Cards
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      freeLimits:
                        description: Amount of free limits
                        type: integer
                      paidLimits:
                        description: Amount of paid limits
                        type: integer
                  error:
                    description: Error flag
                    type: boolean
                  errorText:
                    description: Error description
                    type: string
                  additionalErrors:
                    description: Additional errors
                    type: string
                    nullable: true
              example:
                data:
                  freeLimits: 1500
                  paidLimits: 10
                error: false
                errorText: ''
                additionalErrors: null
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/barcodes:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Generation of barcodes
      description: |
        Generates array of unique barcodes to create size of the product card

        <div class="description_limit">
          Maximum of 100 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> per one seller's account for all the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Content</a> category methods except:
          <br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post">creating product cards</a><br>
            • <a href="/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post">creating product cards with merge</a><br>
            • <a href="/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post">editing product cards</a>
        </div>
      tags:
        - Creating Product Cards
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                count:
                  description: Number of barcodes to be generated, maximum 5,000
                  type: integer
                  example: 100
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    description: An array of generated barcodes
                    type: array
                    items:
                      type: string
                      example: '5032781145187'
                  error:
                    description: Error flag
                    type: boolean
                    example: false
                  errorText:
                    description: Error description.
                    type: string
                    example: ''
                  additionalErrors:
                    description: Any additional errors
                    nullable: true
                    type: string
                    example: ''
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '429':
          $ref: '#/components/responses/429'
  /content/v2/cards/upload:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      tags:
        - Creating Product Cards
      summary: Create product cards
      description: "Creates products cards. You can specify product description and characteristics.<br>\n\nHow to create a card:\n\n  1. Get [parent categories list](/openapi/work-with-products#tag/Categories-Subjects-and-Characteristics/paths/~1content~1v2~1object~1parent~1all/get)\n  2. Get [the category and get all subjects](/openapi/work-with-products#tag/Categories-Subjects-and-Characteristics/paths/~1content~1v2~1object~1all/get)\n  3. Choose [the subject and get all available characteristics](/openapi/work-with-products#tag/Categories-Subjects-and-Characteristics/paths/~1content~1v2~1object~1charcs~1%7BsubjectId%7D/get). For `Color`, `Gender`, `Country of origin`, `Season`, `VAT rate`, `HS code` characteristics use values from [category](/openapi/work-with-products#tag/Categories-Subjects-and-Characteristics).\n  4. Send the request. If the response is Success (200) but the card was not created, check errors using [list of failed product card with errors](/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1error~1list/get).\n\nThe dimensions of the products can only be specified in `centimeters`, and the weight of packed products must be specified in `kilograms`.<br>\n\nWith one request you can create maximum 100\_merged product cards (`imtID`), 30\_product cards (`nmID`) in\_each. Maximum request size is 10 Mb.<br>\n\nCreating a card is asynchronous, after sending the request is put in a queue for processing.\n\n<div class=\"description_important\">\n  If there were errors during queue processing, the product card is considered invalid.\n</div>\n\n<div class=\"description_limit\">\n  Maximum of 10 requests per <a href=\"/openapi/api-information#tag/Introduction/Rate-Limits\">minute</a> per one seller's account\n</div>\n"
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                required:
                  - subjectID
                  - variants
                properties:
                  subjectID:
                    type: integer
                    description: Subject ID
                  variants:
                    description: Product variants, maximum 30 in one merged card
                    type: array
                    items:
                      type: object
                      required:
                        - vendorCode
                      properties:
                        brand:
                          type: string
                          description: Brand
                        title:
                          type: string
                          description: Product title
                          maxLength: 60
                        description:
                          type: string
                          description: |
                            Product description<br>
                            The maximum number of characters depends on the product category<br>
                            Standard - 2000, minimum - 1000, maximum - 5000<br>
                            More details about description rules in **Product card filling rules** in [Instructions](https://seller.wildberries.ru/help-center/article/A-113#описание) category of sellers portal
                        vendorCode:
                          type: string
                          description: Seller's article
                        dimensions:
                          description: |
                            Dimensions and weight of packed product in `centimeters` and `kilograms`<br>
                            In case of empty `dimensions`, they are generated automatically with zero values
                          type: object
                          properties:
                            length:
                              type: integer
                              description: Length, cm
                            width:
                              type: integer
                              description: Width, cm
                            height:
                              type: integer
                              description: Height, cm
                            weightBrutto:
                              type: number
                              description: Weight, kg<br>Number of decimal places <=3
                        sizes:
                          type: array
                          description: |
                            Product sizes<br>
                            If the product has sizes but you send empty parameter, it is generated automatically with values: `techSize` = "A", `wbSize` = "1" and some random barcode
                          items:
                            type: object
                            properties:
                              techSize:
                                type: string
                                description: Product size (for example, L or 45)
                              wbSize:
                                type: string
                                description: Russian size
                              price:
                                type: integer
                                description: Price, ₽
                              skus:
                                type: array
                                items:
                                  type: string
                                description: Barcode. In case of empty `skus`, the barcode is generated automatically
                        characteristics:
                          type: array
                          description: Product characteristics
                          items:
                            type: object
                            required:
                              - id
                              - value
                            properties:
                              id:
                                type: integer
                                description: Characteristic ID
                              value:
                                description: Characteristic value
            examples:
              creatingOneCard:
                $ref: '#/components/examples/creatingOneCard'
              creatingMergedCards:
                $ref: '#/components/examples/creatingMergedCards'
              creatingGroupOfIndividualCards:
                $ref: '#/components/examples/creatingGroupOfIndividualCards'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseCardCreate'
              example:
                data: null
                error: false
                errorText: ''
                additionalErrors: {}
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
              examples:
                InvalidRequestFormatContent:
                  $ref: '#/components/examples/InvalidRequestFormatContent'
                CardCreatedWithoutVendorCode:
                  $ref: '#/components/examples/CardCreatedWithoutVendorCode'
                CardsVendorCodeUsedInOtherCards:
                  $ref: '#/components/examples/CardsVendorCodeUsedInOtherCards'
                ThisCategoryDoesNotExist:
                  $ref: '#/components/examples/ThisCategoryDoesNotExist'
        '401':
          $ref: '#/components/responses/401'
        '413':
          description: The request body size exceeds the given limit
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  code:
                    type: string
                    description: Internal error code
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
                  status:
                    type: number
                    description: HTTP status code
                  statusText:
                    type: string
                    description: Text of the HTTP status code
              example:
                title: request body too long
                detail: https://openapi.wildberries.ru/content/api/ru/
                code: 71d3de1b-001e-488f-bbf5-55c31254fbeb
                requestId: MN8usr6RfrzWHZfucSvNgb
                origin: s2s-api-auth-content
                status: 413
                statusText: Request Entity Too Large
        '429':
          $ref: '#/components/responses/429'
  /content/v2/cards/upload/add:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      tags:
        - Creating Product Cards
      summary: Create product cards with merge
      description: "The method creates new product cards by merging them with existing ones.\n\nThe dimensions of the products can only be specified in `centimeters`, and the weight of packed products must be specified in `kilograms`.\n\nYou can not edit or delete barcodes but you can add new ones. You have not to send `photos`, `video` and `tags` and can not edit them.\n\nIf this method response is Success (200) but product card was not updated, check errors using [list of failed nomenclature with errors](/openapi/work-with-products#tag/Nomenclatures/paths/~1content~1v2~1cards~1error~1list/get).\n\nWith one request you can edit maximum 3000\_nomenclatures (`nmID`). Maximum request size is 10 Mb\n\n<div class=\"description_limit\">\n  Maximum of 10 requests per <a href=\"/openapi/api-information#tag/Introduction/Rate-Limits\">minute</a> per one seller's account\n</div>\n"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                imtID:
                  type: integer
                  description: |
                    `imtID` of product card, to which the product card added
                cardsToAdd:
                  description: The structure of the added product card
                  type: array
                  items:
                    type: object
                    required:
                      - vendorCode
                    properties:
                      brand:
                        type: string
                        description: Brand
                      vendorCode:
                        description: Supplier's article
                        type: string
                      title:
                        type: string
                        description: Product title
                        maxLength: 60
                      description:
                        type: string
                        description: |
                          Product description<br>
                          The maximum number of characters depends on the product category<br>
                          Standard - 2000, minimum - 1000, maximum - 5000<br>
                          More details about description rules in **Product card filling rules** in [Instructions](https://seller.wildberries.ru/help-center/article/A-113#описание) category of sellers portal
                      dimensions:
                        description: |
                          Dimensions and weight of packed product in `centimeters` and `kilograms`<br>
                          In case of empty `dimensions`, they are generated automatically with zero values
                        type: object
                        properties:
                          length:
                            type: integer
                            description: Length, cm
                          width:
                            type: integer
                            description: Width, cm
                          height:
                            type: integer
                            description: Height, cm
                          weightBrutto:
                            type: number
                            description: Weight, kg<br>Number of decimal places <=3
                      sizes:
                        type: array
                        description: |
                          Product sizes<br>
                          If the product has sizes but you send empty parameter, it is generated automatically with values: `techSize` = "A", `wbSize` = "1" and some random barcode
                        items:
                          type: object
                          properties:
                            techSize:
                              type: string
                              description: Product size (for example, L or 45)
                            wbSize:
                              type: string
                              description: Russian size
                            price:
                              type: integer
                              description: Price, ₽
                            skus:
                              type: array
                              items:
                                type: string
                              description: Barcode. In case of empty `skus`, the barcode is generated automatically
                      characteristics:
                        type: array
                        description: Product characteristics
                        items:
                          type: object
                          required:
                            - id
                            - value
                          properties:
                            id:
                              type: integer
                              description: Characteristic ID
                            value:
                              description: Characteristic value
              example:
                imtID: 987654321
                cardsToAdd:
                  - vendorCode: myVariant1
                    title: Наименование товара
                    description: Описание товара
                    brand: Бренд
                    dimensions:
                      length: 9
                      width: 6
                      height: 3
                      weightBrutto: 0.893
                    characteristics:
                      - id: 12
                        value:
                          - Russian flag
                      - id: 25471
                        value: 1300
                      - id: 14177449
                        value:
                          - blue
                    sizes:
                      - skus:
                          - '12345678'
                  - vendorCode: myVariant2
                    title: Наименование товара
                    description: Описание товаров
                    brand: Бренд
                    dimensions:
                      length: 8
                      width: 8
                      height: 8
                      weightBrutto: 1.04
                    characteristics:
                      - id: 12
                        value:
                          - Russian flag
                      - id: 25471
                        value: 1300
                      - id: 14177449
                        value:
                          - blue
                    sizes:
                      - skus:
                          - '222222222222'
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseCardCreate'
              example:
                data: null
                error: false
                errorText: ''
                additionalErrors: {}
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseBodyContentError403'
        '413':
          description: The request body size exceeds the given limit
          content:
            application/json:
              schema:
                type: object
                properties:
                  title:
                    type: string
                    description: Error title
                  detail:
                    type: string
                    description: Error details
                  code:
                    type: string
                    description: Internal error code
                  requestId:
                    type: string
                    description: Unique request ID
                  origin:
                    type: string
                    description: WB internal service ID
                  status:
                    type: number
                    description: HTTP status code
                  statusText:
                    type: string
                    description: Text of the HTTP status code
              example:
                title: request body too long
                detail: https://openapi.wildberries.ru/content/api/ru/
                code: 71d3de1b-001e-488f-bbf5-55c31254fbeb
                requestId: MN8usr6RfrzWHZfucSvNgb
                origin: s2s-api-auth-content
                status: 413
                statusText: Request Entity Too Large
        '429':
          $ref: '#/components/responses/429'
  /content/v3/media/file:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Upload media file
      description: "Uploads and adds one media file for the product card.\n\nRequirements for images:\n\n  * maximum images for\_each product card \_— 30,\n  * minimal resolution – 700\_×\_900 pixels,\n  * maximum size\_— 32\_МB,\n  * minimal quality\_— 65%,\n  * formats\_— JPG, PNG, BMP, GIF (static), WebP.\n\nRequirements for video:\n\n  * maximum one video for\_each product card\n  * maximum size\_— 50\_MB\n  * formats\_— MOV, MP4\n\n<div class=\"description_limit\">\n  Maximum of 100 requests per <a href=\"/openapi/api-information#tag/Introduction/Rate-Limits\">minute</a> per one seller's account for all the <a href=\"/openapi/api-information#tag/Authorization/How-to-create-a-token\">Content</a> category methods except:\n  <br>\n    • <a href=\"/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post\">creating product cards</a><br>\n    • <a href=\"/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post\">creating product cards with merge</a><br>\n    • <a href=\"/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post\">editing product cards</a>\n</div>\n"
      tags:
        - Media Files
      parameters:
        - name: X-Nm-Id
          in: header
          description: Wildberries article
          schema:
            type: string
            example: '*********'
          required: true
        - name: X-Photo-Number
          in: header
          description: "Number of media file, starting from\_`1`. To add the video set `1`.\n\nTo add the image to the uploaded ones, set file the number more then number of uploaded files.\n"
          schema:
            type: integer
            example: 2
          required: true
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                uploadfile:
                  type: string
                  format: binary
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  error:
                    description: Error flag
                    type: boolean
                  errorText:
                    description: Error description
                    type: string
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: object
              example:
                data: {}
                error: false
                errorText: ''
                additionalErrors: null
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/mediaErrors'
            plain/text:
              example: Invalid 'boundary' for 'multipart/form-data' request
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/mediaErrors'
        '429':
          $ref: '#/components/responses/429'
  /content/v3/media/save:
    servers:
      - url: https://content-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Upload media files via links
      description: "The method uploads a set of media files to a product card by specifying links in the request.\n\n<div class=\"description_important\">\n  New media files (<code>data</code>) replace old ones (<code>mediaFiles</code>). To add new files, set links both to\_new and old files.\n</div>\n\nRequirements for images:\n\n  * maximum images for\_each product card\_— 30,\n  * minimal resolution – 700\_×\_900 pixels,\n  * maximum size\_— 32\_MB,\n  * minimal quality\_— 65%,\n  * formats\_— JPG, PNG, BMP, GIF (static), WebP.\n\n\nRequirements for video:\n\n  * maximum one video for\_each product card,\n  * maximum size\_— 50\_MB,\n  * formats\_— MOV, MP4.\n\n\nIf one or several images or a video do not meet the requirements, no images and a video will be uploaded even if you have the success response\_(200)\n\n<div class=\"description_limit\">\n  Maximum of 100 requests per <a href=\"/openapi/api-information#tag/Introduction/Rate-Limits\">minute</a> per one seller's account for all the <a href=\"/openapi/api-information#tag/Authorization/How-to-create-a-token\">Content</a> category methods except:\n  <br>\n    • <a href=\"/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload/post\">creating product cards</a><br>\n    • <a href=\"/openapi/work-with-products#tag/Creating-Product-Cards/paths/~1content~1v2~1cards~1upload~1add/post\">creating product cards with merge</a><br>\n    • <a href=\"/openapi/work-with-products#tag/Product-Cards/paths/~1content~1v2~1cards~1update/post\">editing product cards</a>\n</div>\n"
      tags:
        - Media Files
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nmId:
                  description: Wildberries article
                  type: integer
                data:
                  description: Links to images in the order that they are on the card, and a video at any position of the array
                  type: array
                  items:
                    type: string
            example:
              nmId: *********
              data:
                - https://basket-stage-02.dasec.ru/vol669/part66964/66964260/images/big/2.jpg
                - https://sample-videos.com/video321/mp4/720/big_buck_bunny_720p_mb.mp4
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  error:
                    description: Error flag
                    type: boolean
                  errorText:
                    description: Error description
                    type: string
                  additionalErrors:
                    description: Additional errors
                    nullable: true
                    type: object
              example:
                data: {}
                error: false
                errorText: ''
                additionalErrors: null
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/mediaErrors'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/mediaErrors'
        '409':
          description: Error saving some of the links
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/mediaErrors'
        '422':
          description: The parameter nmId is missing
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/mediaErrors'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/upload/task:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Set prices and discounts
      description: |
        Sets prices and discounts

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      requestBody:
        $ref: '#/components/requestBodies/SupplierTaskRequest'
      responses:
        '200':
          description: Success
          $ref: '#/components/responses/SuccessTaskResponse'
        '208':
          $ref: '#/components/responses/Responses208'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                UploadLimitExceeded:
                  $ref: '#/components/examples/UploadLimitExceeded'
                DuplicateItemNos:
                  $ref: '#/components/examples/DuplicateItemNos'
                TheSpecifiedPricesAndDiscountsAreAlreadySet:
                  $ref: '#/components/examples/TheSpecifiedPricesAndDiscountsAreAlreadySet'
                InvalidDataFormat:
                  $ref: '#/components/examples/InvalidDataFormat'
                PriceShouldBeAWholeNumber:
                  $ref: '#/components/examples/PriceShouldBeAWholeNumber'
                InvalidPriceValue:
                  $ref: '#/components/examples/InvalidPriceValue'
                InvalidDiscountValue:
                  $ref: '#/components/examples/InvalidDiscountValue'
                InvalidItemNo:
                  $ref: '#/components/examples/InvalidItemNo'
                PriceAndDiscountNotSpecified:
                  $ref: '#/components/examples/PriceAndDiscountNotSpecified'
                EmptyData:
                  $ref: '#/components/examples/EmptyData'
                AllItemNosAreSpecifiedIncorrectlyOrPricesAndDiscounts:
                  $ref: '#/components/examples/AllItemNosAreSpecifiedIncorrectlyOrPricesAndDiscounts'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Unexpected result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                UnexpectedResult:
                  $ref: '#/components/examples/UnexpectedResult'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/upload/task/size:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Sets size prices
      description: |
        Sets different prices for different sizes. 

        Only for products from categories where size price setting is available. For these products `editableSizePrice: true` in [Get product sizes](/openapi/work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1list~1goods~1size~1nm/get) response.

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      requestBody:
        $ref: '#/components/requestBodies/SupplierTaskRequestSize'
      responses:
        '200':
          description: Success
          $ref: '#/components/responses/SuccessTaskResponse'
        '208':
          $ref: '#/components/responses/Responses208'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                UploadLimitExceeded:
                  $ref: '#/components/examples/UploadLimitExceeded'
                DuplicateItemNos:
                  $ref: '#/components/examples/DuplicateItemNos'
                DuplicateSizeIDs:
                  $ref: '#/components/examples/DuplicateSizeIDs'
                TheSpecifiedPricesAreAlreadySet:
                  $ref: '#/components/examples/TheSpecifiedPricesAreAlreadySet'
                InvalidDataFormat:
                  $ref: '#/components/examples/InvalidDataFormat'
                PriceShouldBeAWholeNumber:
                  $ref: '#/components/examples/PriceShouldBeAWholeNumber'
                InvalidPriceValue:
                  $ref: '#/components/examples/InvalidPriceValue'
                InvalidItemNo:
                  $ref: '#/components/examples/InvalidItemNo'
                InvalidSize:
                  $ref: '#/components/examples/InvalidSize'
                PriceNotSpecified:
                  $ref: '#/components/examples/PriceNotSpecified'
                EmptyData:
                  $ref: '#/components/examples/EmptyData'
                AllItemNosAreSpecifiedIncorrectlyOrPrices:
                  $ref: '#/components/examples/AllItemNosAreSpecifiedIncorrectlyOrPrices'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Unexpected result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                UnexpectedResult:
                  $ref: '#/components/examples/UnexpectedResult'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/upload/task/club-discount:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Set WB Club discounts
      description: |
        Sets WB Club subscription discounts

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      requestBody:
        $ref: '#/components/requestBodies/SupplierTaskRequestClubDisc'
      responses:
        '200':
          $ref: '#/components/responses/SuccessTaskResponse'
        '208':
          $ref: '#/components/responses/Responses208'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                CheckTheWBClubDiscount:
                  $ref: '#/components/examples/CheckTheWBClubDiscount'
                DiscountsAreTheSameAsThoseAlreadySet:
                  $ref: '#/components/examples/DiscountsAreTheSameAsThoseAlreadySet'
                AllItemNosAreSpecifiedIncorrectlyOrDiscounts:
                  $ref: '#/components/examples/AllItemNosAreSpecifiedIncorrectlyOrDiscounts'
                InvalidDiscountValue:
                  $ref: '#/components/examples/InvalidDiscountValue'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Unexpected result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                UnexpectedResult:
                  $ref: '#/components/examples/UnexpectedResult'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/history/tasks:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Processed upload state
      description: |
        Returns the processed upload data

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/uploadID'
      responses:
        '200':
          description: Success
          $ref: '#/components/responses/ResponseTaskHistory'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    nullable: true
                  error:
                    type: boolean
                    example: true
                  errorText:
                    type: string
                    example: Invalid request parameters
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/history/goods/task:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Processed upload details
      description: |
        Returns products in processed upload including product errors

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/uploadID'
      responses:
        '200':
          description: Success
          $ref: '#/components/responses/ResponseGoodHistories'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    nullable: true
                  error:
                    type: boolean
                    example: true
                  errorText:
                    type: string
                    example: Invalid request parameters
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/buffer/tasks:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Unprocessed upload state
      description: |
        Returns the processing upload data

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/uploadID'
      responses:
        '200':
          description: Success
          $ref: '#/components/responses/ResponseTaskBuffer'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    nullable: true
                  error:
                    type: boolean
                    example: true
                  errorText:
                    type: string
                    example: Invalid request parameters
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/buffer/goods/task:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Unprocessed upload details
      description: |
        Returns products in processing upload including product errors

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/uploadID'
      responses:
        '200':
          description: Success
          $ref: '#/components/responses/ResponseGoodBufferHistories'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    nullable: true
                  error:
                    type: boolean
                    example: true
                  errorText:
                    type: string
                    example: Invalid request parameters
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/list/goods/filter:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Get products with prices
      description: |
        Returns product data by its article.<br> To get data for all products, do not set the article, set `limit=1000`, and use the `offset` field to set the data offset. The offset should be calculated using the formula: `offset` plus `limit` from the previous request. Repeat the request until you receive a response with an empty array.<br> To get data for the size of the product, use [a separate method](/openapi/work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1list~1goods~1size~1nm/get)

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/filterNmID'
      responses:
        '200':
          description: Success
          $ref: '#/components/responses/ResponseGoodsLists'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                SortError:
                  $ref: '#/components/examples/SortError'
                InvalidRequestParameters:
                  $ref: '#/components/examples/InvalidRequestParameters'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/list/goods/size/nm:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Get product sizes with prices
      description: |
        Returns sizes data for the product. Only for products from categories where size price setting is available. For these products `editableSizePrice: true`

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/nmID'
      responses:
        '200':
          description: Success
          $ref: '#/components/responses/ResponseSizeLists'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                InvalidRequestParameters:
                  $ref: '#/components/examples/InvalidRequestParameters'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /api/v2/quarantine/goods:
    servers:
      - url: https://discounts-prices-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Prices and Discounts
      summary: Get products in quarantine
      description: |
        Returns information about products in quarantine.
        <br><br>
        If the product new price with discount will be minimum 3 times less than the old price, the product will be placed [in quarantine](https://seller.wildberries.ru/discount-and-prices/quarantine) and will be sold at the old price. An error about this will be in [upload states](/openapi/work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1history~1tasks/get) method response.
        <br><br>
        You can change price or discount via API or release product from quarantine [in personal account](https://seller.wildberries.ru/discount-and-prices/quarantine).
        <br><br>
        For products with [size-based pricing](/openapi/work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1upload~1task~1size/post), quarantine does not apply

        <div class="description_limit">
          Maximum of 10 requests per 6 <a href="/openapi/api-information#tag/Introduction/Rate-Limits">seconds</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Prices and Discounts</a> category per one seller's account
        </div>
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          $ref: '#/components/responses/ResponseQuarantineGoods'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                InvalidRequestParameters:
                  $ref: '#/components/examples/InvalidRequestParameters'
        '401':
          $ref: '#/components/responses/401'
        '422':
          description: Unexpected result
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResponseError'
              examples:
                UnexpectedResult:
                  $ref: '#/components/examples/UnexpectedResult'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/stocks/{warehouseId}:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Inventory
      summary: Get inventory
      description: |
        Returns product inventory

        <div class="description_limit">
          Maximum of 300 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Marketplace</a> category per one seller's account.
          <br><br>
          One request with a response code of <code>409</code> is counted as 5 requests.
        </div>
      parameters:
        - $ref: '#/components/parameters/Warehouse'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              nullable: false
              properties:
                skus:
                  type: array
                  description: SKUs array
                  minItems: 1
                  maxItems: 1000
                  items:
                    type: string
                    example: BarcodeTest123
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  stocks:
                    type: array
                    nullable: false
                    items:
                      type: object
                      properties:
                        sku:
                          type: string
                          nullable: false
                          description: SKU
                          example: SkuTest123
                        amount:
                          type: integer
                          nullable: false
                          description: Amount
                          example: 10
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Inventory
      summary: Update inventory
      description: |
        Updates product inventory

        <div class="description_important">
          The names of the query parameters are not validated. If invalid names are sent, the response will be successful(204), but the remaining amounts will not be updated.
        </div>

        <div class="description_limit">
          Maximum of 300 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Marketplace</a> category per one seller's account.
          <br><br>
          One request with a response code of <code>409</code> is counted as 5 requests.
        </div>
      parameters:
        - $ref: '#/components/parameters/Warehouse'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - stocks
              properties:
                stocks:
                  type: array
                  minItems: 1
                  maxItems: 1000
                  description: SKU array
                  items:
                    type: object
                    properties:
                      sku:
                        type: string
                        description: SKU
                        example: SkuTest123
                      amount:
                        type: integer
                        maximum: 100000
                        description: Amount
                        example: 10
      responses:
        '204':
          description: Updated
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StocksWarehouseError'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '406':
          $ref: '#/components/responses/StatusNotAcceptable'
        '409':
          description: Error updating inventory
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                SubjectDBSRestriction:
                  $ref: '#/components/examples/SubjectDBSRestriction'
                SubjectFBSRestriction:
                  $ref: '#/components/examples/SubjectFBSRestriction'
                UploadDataLimit:
                  $ref: '#/components/examples/UploadDataLimit'
                CargoWarehouseRestrictionMGT:
                  $ref: '#/components/examples/CargoWarehouseRestrictionMGT'
                CargoWarehouseRestrictionSGT:
                  $ref: '#/components/examples/CargoWarehouseRestrictionSGT'
                CargoWarehouseRestrictionSGTKGTPlus:
                  $ref: '#/components/examples/CargoWarehouseRestrictionSGTKGTPlus'
                CargoWarehouseRestrictionKGTPlus:
                  $ref: '#/components/examples/CargoWarehouseRestrictionKGTPlus'
                NotFound:
                  $ref: '#/components/examples/NotFound'
                StoreIsProcessing:
                  $ref: '#/components/examples/StoreIsProcessing'
        '429':
          $ref: '#/components/responses/429'
    delete:
      security:
        - HeaderApiKey: []
      tags:
        - Inventory
      summary: Delete inventory
      description: |
        Deletes product inventory

        <div class="description_important">
          <strong>This action is irreversible</strong>. Deleted stock will require re-uploading to continue sales
        </div>

        <div class="description_limit">
          Maximum of 300 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Marketplace</a> category per one seller's account.
          <br><br>
          One request with a response code of <code>409</code> is counted as 5 requests.
        </div>
      parameters:
        - $ref: '#/components/parameters/Warehouse'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                skus:
                  type: array
                  nullable: false
                  minItems: 1
                  maxItems: 1000
                  description: SKUs array
                  items:
                    type: string
                    nullable: false
                    example: BarcodeTest123
      responses:
        '204':
          description: Deleted
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          description: Access denied
          $ref: '#/components/responses/AccessDenied'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                code: NotFound
                message: Not Found
                data:
                  skuTest1: null
                  skuTest2: null
        '409':
          description: Error deleting inventory
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                StoreIsProcessing:
                  $ref: '#/components/examples/StoreIsProcessing'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/offices:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Seller Warehouses
      summary: Get offices
      description: |
        Returns a list of all offices to link to seller warehouse.

        <div class="description_limit">
          Maximum of 300 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Marketplace</a> category per one seller's account.
          <br><br>
          One request with a response code of <code>409</code> is counted as 5 requests.
        </div>
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                nullable: false
                description: Offices list
                items:
                  $ref: '#/components/schemas/Office'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/warehouses:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Seller Warehouses
      summary: Get warehouses
      description: |
        Returns a list of all seller's warehouses

        <div class="description_limit">
          Maximum of 300 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Marketplace</a> category per one seller's account.
          <br><br>
          One request with a response code of <code>409</code> is counted as 5 requests.
        </div>
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                nullable: false
                description: Warehouses list
                items:
                  $ref: '#/components/schemas/Warehouse'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Seller Warehouses
      summary: Create warehouse
      description: |
        Creates a seller's warehouse. You cannot link an office that is already in use.

        <div class="description_limit">
          Maximum of 300 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Marketplace</a> category per one seller's account.
          <br><br>
          One request with a response code of <code>409</code> is counted as 5 requests.
        </div>
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  nullable: false
                  minLength: 1
                  maxLength: 200
                  description: Supplier's warehouse name
                  example: Koledino 2
                officeId:
                  type: integer
                  nullable: false
                  minimum: 1
                  description: Office ID
                  example: 15
              required:
                - name
                - officeId
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    nullable: false
                    description: Warehouse ID
                    example: 2
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                WarehouseNameInvalid:
                  $ref: '#/components/examples/WarehouseNameInvalid'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Error creating a new warehouse
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/warehouses/{warehouseId}:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Seller Warehouses
      summary: Update warehouse
      description: |
        Updates the seller's warehouse details. Changing the linked office is allowed once per day. You cannot link an office that is already in use

        <div class="description_limit">
          Maximum of 300 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Marketplace</a> category per one seller's account.
          <br><br>
          One request with a response code of <code>409</code> is counted as 5 requests.
        </div>
      parameters:
        - $ref: '#/components/parameters/Warehouse'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  nullable: false
                  minLength: 1
                  maxLength: 200
                  description: Supplier's warehouse name
                  example: Koledino
                officeId:
                  type: integer
                  nullable: false
                  minimum: 1
                  description: Office ID
                  example: 15
              required:
                - name
                - officeId
      responses:
        '204':
          description: Updated
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                WarehouseNameInvalid:
                  $ref: '#/components/examples/WarehouseNameInvalid'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Error updating a warehouse
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '429':
          $ref: '#/components/responses/429'
    delete:
      security:
        - HeaderApiKey: []
      tags:
        - Seller Warehouses
      summary: Delete warehouse
      description: |
        Deletes the supplier's warehouse.

        <div class="description_limit">
          Maximum of 300 requests per <a href="/openapi/api-information#tag/Introduction/Rate-Limits">minute</a> for all methods in the <a href="/openapi/api-information#tag/Authorization/How-to-create-a-token">Marketplace</a> category per one seller's account.
          <br><br>
          One request with a response code of <code>409</code> is counted as 5 requests.
        </div>
      parameters:
        - $ref: '#/components/parameters/Warehouse'
      responses:
        '204':
          description: Deleted
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
components:
  schemas:
    responseCardCreate:
      type: object
      properties:
        data:
          type: object
          nullable: true
          example: null
        error:
          description: Error flag
          type: boolean
        errorText:
          description: Error description
          type: string
        additionalErrors:
          description: Any additional errors
          oneOf:
            - type: object
              properties:
                string:
                  type: string
              additionalProperties: false
            - type: string
    responseContentError1:
      type: object
      properties:
        data:
          type: object
          nullable: true
          example: null
        error:
          description: Error flag
          type: boolean
        errorText:
          description: Error description
          type: string
        additionalErrors:
          description: Any additional errors
          type: object
          properties:
            error:
              type: string
          additionalProperties: false
    responseIncorrectDate:
      type: object
      properties:
        error:
          type: string
      additionalProperties: false
    responseContentError4:
      type: object
      properties:
        data:
          type: object
          nullable: true
        error:
          description: Error flag
          type: boolean
        errorText:
          description: Error description
          type: string
        additionalErrors:
          description: Additional errors
          nullable: true
          type: object
          properties:
            description:
              type: string
    responseContentError5:
      type: object
      properties:
        data:
          type: object
          nullable: true
        error:
          description: Error flag
          type: boolean
        errorText:
          description: Error description
          type: string
        additionalErrors:
          description: Additional errors
          type: object
          properties:
            tagID:
              type: string
    responseContentError6:
      type: object
      properties:
        data:
          type: object
          nullable: true
        error:
          description: Error flag
          type: boolean
        errorText:
          description: Error description
          type: string
        additionalErrors:
          description: Additional errors
          type: string
          nullable: true
      example:
        data: null
        error: false
        errorText: ''
        additionalErrors: null
    responseBodyContentError400:
      type: object
      properties:
        data:
          type: object
          nullable: true
        error:
          description: Error flag
          type: boolean
        errorText:
          description: Error description
          type: string
        additionalErrors:
          description: Additional errors
          nullable: true
          type: object
          properties:
            MoveNmsToTrash:
              type: string
      example:
        data: null
        error: true
        errorText: Error text
        additionalErrors:
          MoveNmsToTrash: Bad request
    responseBodyContentError403:
      type: object
      properties:
        data:
          type: object
          nullable: true
        error:
          description: Error flag
          type: boolean
        errorText:
          description: Error description
          type: string
        additionalErrors:
          description: Additional errors
          nullable: true
          type: string
      example:
        data: null
        error: true
        errorText: Access denied
        additionalErrors: Access denied
    mediaErrors:
      type: object
      properties:
        additionalErrors:
          description: Additional errors
          nullable: true
          type: object
        data:
          type: object
        error:
          description: Error flag
          type: boolean
        errorText:
          description: Error description
          type: string
      example:
        additionalErrors: null
        data: null
        error: true
        errorText: Error text
    requestMoveNmsImtConn:
      type: object
      required:
        - targetIMT
        - nmIDs
      properties:
        targetIMT:
          description: Seller's existing `imtID`, in which product cards have to be merged
          type: integer
          example: 123
        nmIDs:
          description: |
            `nmID` to be merged, maximum 30
          type: array
          items:
            type: integer
          example:
            - 837459235
            - 828572090
    requestMoveNmsImtDisconn:
      type: object
      required:
        - nmIDs
      properties:
        nmIDs:
          description: |
            `nmID` to be separated, maximum 30
          type: array
          items:
            type: integer
          example:
            - 837459235
            - 828572090
      additionalProperties: false
    ResponseError:
      type: object
      properties:
        data:
          type: object
          nullable: true
        error:
          type: boolean
        errorText:
          type: string
    RequestAlreadyExistsError:
      type: object
      properties:
        data:
          type: object
          properties:
            id:
              description: Upload ID
              type: integer
            alreadyExists:
              description: "Upload duplication: `true`\_— upload already exists\n"
              type: boolean
        error:
          description: Error flag
          type: boolean
        errorText:
          description: Error text
          type: string
    StocksWarehouseError:
      type: array
      items:
        type: object
        properties:
          code:
            description: Error code
            type: string
          data:
            description: Additional data enriching the error
            nullable: true
            type: object
          message:
            description: Error description
            type: string
    TaskCreated:
      type: object
      properties:
        data:
          type: object
          properties:
            id:
              type: integer
              description: Upload ID
            alreadyExists:
              type: boolean
              description: "Upload duplication: `true`\_— upload already exists\n"
              example: false
        error:
          description: Error flag
          type: boolean
          example: false
        errorText:
          description: Error text
          type: string
          example: ''
    Goods:
      type: array
      description: "Products, prices and discounts. Maximum 1,000\_products. Both price and discount can not be empty \n<br><br>\nIf the new price with discount is at least 3 times less than the previous one, the price will go [into price quarantine](https://seller.wildberries.ru/discount-and-prices/quarantine) and will not change. You will get the error in the response of upload states methods.\n<br><br>\nYou can edit price/discount using API or remove the price out of quarantine [in\_your account](https://seller.wildberries.ru/discount-and-prices/quarantine)\n"
      items:
        $ref: '#/components/schemas/Good'
    Good:
      type: object
      required:
        - nmID
      properties:
        nmID:
          type: integer
          example: 123
          description: WB article
        price:
          type: integer
          example: 999
          description: |
            Price. You can get the currency with [Get products](./work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1list~1goods~1filter/get)
            method, field `currencyIsoCode4217`
        discount:
          type: integer
          example: 30
          description: "Discount,\_%"
    SizeGoodsBody:
      description: "Sizes and prices. Maximum 1,000 sizes \n<br><br>\nIf the new price with discount is at least 3 times less than the previous one, the price will go [into price quarantine](https://seller.wildberries.ru/discount-and-prices/quarantine) and will not change. You will get the error in the response of [Upload states](./work-with-products#tag/Prices-and-Discounts) methods.\n<br><br>\nYou can edit price/discount using API or remove the price out of quarantine [in\_your account](https://seller.wildberries.ru/discount-and-prices/quarantine)\n"
      type: array
      items:
        $ref: '#/components/schemas/SizeGoodReq'
    SizeGoodReq:
      type: object
      required:
        - nmID
        - sizeID
        - price
      properties:
        nmID:
          type: integer
          example: 123
          description: WB article
        sizeID:
          type: integer
          example: ********
          description: Size ID. You can get the ID with [Get products](./work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1list~1goods~1filter/get) method, field `sizeID`. In content methods this is `chrtID` field
        price:
          type: integer
          example: 999
          description: Price. You can get the currency with [Get products](./work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1list~1goods~1filter/get) method, field `currencyIsoCode4217`
    ClubDisc:
      type: array
      description: "Products and WB Club discounts. Maximum 1,000\_products.\n"
      items:
        $ref: '#/components/schemas/ClubDiscReq'
    ClubDiscReq:
      type: object
      required:
        - nmID
        - clubDiscount
      properties:
        nmID:
          type: integer
          example: 123
          description: WB article
        clubDiscount:
          type: integer
          example: 5
          description: "WB Club discount,\_%"
    GoodsList:
      type: object
      description: Product sizes
      properties:
        nmID:
          type: integer
          description: WB article
          example: 98486
        vendorCode:
          type: string
          description: Seller's article
          example: '07326060'
        sizes:
          type: array
          description: Size
          items:
            type: object
            properties:
              sizeID:
                type: integer
                description: Size ID. In content methods this is `chrtID` field
                format: int64
                example: 3123515574
              price:
                type: integer
                description: Price
                example: 500
              discountedPrice:
                type: integer
                description: Price with discount
              clubDiscountedPrice:
                type: number
                description: Price with discount including WB Club discount
                example: 332.5
              techSizeName:
                description: Product size
                type: string
                example: 42
        currencyIsoCode4217:
          type: string
          description: Currency, according to ISO 4217
          example: RUB
        discount:
          type: integer
          description: "Discount,\_%"
          example: 30
        clubDiscount:
          type: integer
          example: 5
          description: "WB Club discount,\_%"
        editableSizePrice:
          description: "Setting of size prices: `true`\_— available, `false`\_— unavailable. Depends on product category\n"
          type: boolean
          example: true
    SizeGood:
      type: object
      description: Size information
      properties:
        nmID:
          type: integer
          example: 123
          description: WB article
        sizeID:
          description: Size ID. You can get this ID with [Get products](./work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1list~1goods~1filter/get) method, field `sizeID`. In content methods this is `chrtID` field
          type: integer
          example: ********
        vendorCode:
          type: string
          example: '34552332'
          description: Seller's article
        price:
          type: integer
          example: 1000
          description: Price
        currencyIsoCode4217:
          type: string
          description: Currency, according to ISO 4217
          example: RUB
        discountedPrice:
          type: integer
          example: 700
          description: Price with discount
        clubDiscountedPrice:
          type: number
          description: Price with discount including WB Club discount
          example: 665
        discount:
          description: "Discount,\_%"
          type: integer
          example: 30
        clubDiscount:
          type: integer
          example: 5
          description: "WB Club discount,\_%"
        techSizeName:
          description: Size
          type: string
          example: 42
        editableSizePrice:
          description: "Setting of size prices: `true`\_— available, `false`\_— unavailable. Depends on product category\n"
          type: boolean
          example: true
    GoodBufferHistory:
      type: object
      properties:
        nmID:
          type: integer
          example: 544833232
          description: WB article
        vendorCode:
          type: string
          example: '34552332'
          description: Seller's article
        sizeID:
          type: integer
          example: 54483342
          description: Size ID. In content methods this is `chrtID` field
        techSizeName:
          type: string
          example: XXL
          description: Size
        price:
          type: integer
          example: 1500
          description: Price
        currencyIsoCode4217:
          type: string
          example: RUB
          description: Currency, according to ISO 4217
        discount:
          type: integer
          example: 25
          description: "Discount,\_%"
        clubDiscount:
          type: integer
          example: 5
          description: "WB Club discount,\_%"
        status:
          $ref: '#/components/schemas/GoodStatusBuffer'
        errorText:
          type: string
          example: ''
          description: Error text
    GoodHistory:
      type: object
      properties:
        nmID:
          type: integer
          example: 544833232
          description: WB article
        vendorCode:
          type: string
          example: '34552332'
          description: Seller's article
        sizeID:
          type: integer
          example: 54483342
          description: Size ID. In content methods this is `chrtID` field
        techSizeName:
          type: string
          example: 42
          description: Size
        price:
          type: integer
          example: 1500
          description: Price
        currencyIsoCode4217:
          type: string
          example: RUB
          description: Currency, according to ISO 4217
        discount:
          type: integer
          example: 25
          description: "Discount,\_%"
        clubDiscount:
          type: integer
          example: 5
          description: "WB Club discount,\_%"
          nullable: true
        status:
          $ref: '#/components/schemas/GoodStatus'
        errorText:
          type: string
          example: The new price is several times lower than the current price. Item has been moved to Price Quarantine
          description: "Error text\n\n<div class=\"description_important\">\n  <p class=\"descr\"><code>The new price is several times lower than the current price. Item has been moved to Price Quarantine</code> error means that new price with discount is at least 3 times less than the previous one. You can edit price/discount using API or remove the price out of quarantine <a href=\"https://seller.wildberries.ru/discount-and-prices/quarantine\">in\_your account</a>\n</div>\n"
    SupplierTaskMetadata:
      nullable: true
      type: object
      properties:
        uploadID:
          type: integer
          example: *********
          description: Upload ID
        status:
          $ref: '#/components/schemas/TaskStatus'
        uploadDate:
          $ref: '#/components/schemas/Date'
        activationDate:
          $ref: '#/components/schemas/Date1'
        overAllGoodsNumber:
          description: "Total number of\_products"
          type: integer
        successGoodsNumber:
          description: Number products without errors
          type: integer
    SupplierTaskMetadataBuffer:
      nullable: true
      type: object
      properties:
        uploadID:
          type: integer
          example: *********
          description: Upload ID
        status:
          $ref: '#/components/schemas/TaskStatusBuffer'
        uploadDate:
          $ref: '#/components/schemas/Date'
        activationDate:
          $ref: '#/components/schemas/Date1'
        overAllGoodsNumber:
          description: "Total number of\_products"
          type: integer
          example: 100
        successGoodsNumber:
          description: Number products without errors (`0` because the upload is processing)
          type: integer
          example: 0
    Date:
      type: string
      example: '2022-08-21T22:00:13+02:00'
      format: date-time
      description: Date and time of the upload creation
    Date1:
      type: string
      example: '2022-08-21T22:00:13+02:00'
      format: date-time
      description: Date and time when the upload processing was started
    TaskStatus:
      description: "Upload status: \n  * `3`\_— processed, no errors in products, prices and discounts were updated\n  * `4`\_— canceled\n  * `5`\_— processed, but some product have errors. For other products prices and discounts were updated. You can get errors with [Processed upload details](./work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1history~1goods~1task/get) method\n  * `6`\_— processed, but all product have errors. You can get them with [Processed upload details](./work-with-products#tag/Prices-and-Discounts/paths/~1api~1v2~1history~1goods~1task/get) method\n"
      type: integer
      example: 3
    TaskStatusBuffer:
      description: "Upload status: `1`\_— processing\n"
      type: integer
      example: 1
    GoodStatus:
      description: "Product status: \n  * `2`\_— no errors, price and/or discount were updated\n  * `3`\_— product has errors, data were not updated\n"
      type: integer
      example: 1
    GoodStatusBuffer:
      description: "Product status: `1`\_— processing\n"
      type: integer
      example: 1
    Error:
      type: object
      nullable: false
      properties:
        code:
          type: string
          description: Error code
          nullable: false
        message:
          type: string
          description: Error description
          nullable: false
        data:
          type: object
          description: Additional data enriching the error
          nullable: true
    QuarantineGoods:
      type: object
      properties:
        nmID:
          type: integer
          example: 206025152
          description: WB article
        sizeID:
          description: Not used
          type: integer
          example: null
        techSizeName:
          description: Not used
          type: string
          example: ''
        currencyIsoCode4217:
          type: string
          description: Currency, according to ISO 4217
          example: RUB
        newPrice:
          type: number
          format: float
          example: 134
          description: New seller's price before discount
        oldPrice:
          type: number
          format: float
          example: 4000
          description: Current seller's price before discount
        newDiscount:
          type: integer
          example: 25
          description: New seller's discount, %
        oldDiscount:
          type: integer
          example: 25
          description: Current seller's discount, %
        priceDiff:
          type: number
          format: float
          description: 'Difference: `newPrice` * (1 - `newDiscount` / 100) - `oldPrice` * (1 - `oldDiscount` / 100)'
          example: -2899.5
    Office:
      type: object
      description: Office details
      properties:
        address:
          type: string
          nullable: false
          description: Address
          example: Kosmonavtov 10А
        name:
          type: string
          nullable: false
          description: Name
          example: Koledino
        city:
          type: string
          nullable: false
          description: City
          example: Vyborg
        id:
          type: integer
          format: int64
          nullable: false
          description: ID
          example: 15
        longitude:
          type: number
          format: float64
          nullable: false
          description: Longitude
          example: 55.386871
        latitude:
          type: number
          format: float64
          nullable: false
          description: Latitude
          example: 37.588898
        cargoType:
          type: integer
          nullable: false
          description: |
            <dl> <dt>The type of goods a warehouse can accept:</dt>  <dd>1 - small-sized goods</dd> <dd>2 - ODC (Over dimensional cargo)</dd> <dd>3 - CD+ (Dimensional cargo+)</dd> </dl>
          enum:
            - 1
            - 2
            - 3
          example: 1
        deliveryType:
          type: integer
          nullable: false
          description: |
            The type of deliveries:
              - `1` — Fulfillment By Wildberries (FBS)
              - `2` — Delivery By Supplier (DBS)
              - `3` — Delivery by WB courier (DBW)
              - `5` — In-Store Pickup (C&C)
              - `6` — Express Delivery By Supplier (EDBS)
          enum:
            - 1
            - 2
            - 3
            - 5
            - 6
          example: 1
        selected:
          type: boolean
          nullable: false
          description: The flag indicating that the office has already been selected by the supplier
    Warehouse:
      type: object
      description: Supplier's warehouse details
      properties:
        name:
          type: string
          nullable: false
          description: Name
          example: Kosmonavtov 14
        officeId:
          type: integer
          format: int64
          nullable: false
          description: Office ID
          example: 15
        id:
          type: integer
          format: int64
          nullable: false
          description: ID
          example: 1
        cargoType:
          type: integer
          nullable: false
          description: |
            <dl> <dt>The type of goods:</dt>  <dd>1 - small-sized goods</dd> <dd>2 - ODC (Over dimensional cargo)</dd> <dd>3 - CD+ (Dimensional cargo+)</dd> </dl>
          enum:
            - 1
            - 2
            - 3
          example: 1
        deliveryType:
          type: integer
          nullable: false
          description: |
            The type of deliveries:
              - `1` — Fulfillment By Wildberries (FBS)
              - `2` — Delivery By Supplier (DBS)
              - `3` — Delivery by WB courier (DBW)
              - `5` — In-Store Pickup (C&C)
              - `6` — Express Delivery By Supplier (EDBS)
          enum:
            - 1
            - 2
            - 3
            - 5
            - 6
          example: 1
  parameters:
    Warehouse:
      name: warehouseId
      in: path
      required: true
      description: The supplier's warehouse ID
      schema:
        type: integer
        format: int64
        example: 2
    uploadID:
      in: query
      name: uploadID
      description: Download ID
      required: true
      schema:
        type: integer
        example: 146567
    limit:
      in: query
      name: limit
      description: Number of elements per page (pagination). Maximum 1,000 elements
      schema:
        type: integer
        format: uint
        example: 10
      required: true
    offset:
      in: query
      name: offset
      description: From which element to start outputting data
      schema:
        type: integer
        format: uint
        minimum: 0
        example: 0
    nmID:
      in: query
      name: nmID
      description: WB article
      schema:
        type: integer
        example: 1
      required: true
    filterNmID:
      in: query
      description: WB article to search
      name: filterNmID
      schema:
        type: integer
      example: 44589768676
  requestBodies:
    SupplierTaskRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                $ref: '#/components/schemas/Goods'
    SupplierTaskRequestSize:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                $ref: '#/components/schemas/SizeGoodsBody'
    SupplierTaskRequestClubDisc:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                $ref: '#/components/schemas/ClubDisc'
  responses:
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Error title
              detail:
                type: string
                description: Error details
              code:
                type: string
                description: Internal error code
              requestId:
                type: string
                description: Unique request ID
              origin:
                type: string
                description: WB internal service ID
              status:
                type: number
                description: HTTP status code
              statusText:
                type: string
                description: Text of the HTTP status code
              timestamp:
                type: string
                format: date-time
                description: Request date and time
          example:
            title: unauthorized
            detail: 'token problem; token is malformed: could not base64 decode signature: illegal base64 data at input byte 84'
            code: 07e4668e--a53a3d31f8b0-[UK-oWaVDUqNrKG]; 03bce=277; 84bd353bf-75
            requestId: 7b80742415072fe8b6b7f7761f1d1211
            origin: s2s-api-auth-catalog
            status: 401
            statusText: Unauthorized
            timestamp: '2024-09-30T06:52:38Z'
    '429':
      description: Too many requests
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Error title
              detail:
                type: string
                description: Error details
              code:
                type: string
                description: Internal error code
              requestId:
                type: string
                description: Unique request ID
              origin:
                type: string
                description: WB internal service ID
              status:
                type: number
                description: HTTP status code
              statusText:
                type: string
                description: Text of the HTTP status code
              timestamp:
                type: string
                format: date-time
                description: Request date and time
          example:
            title: too many requests
            detail: limited by c122a060-a7fb-4bb4-abb0-32fd4e18d489
            code: 07e4668e-ac2242c5c8c5-[UK-4dx7JUdskGZ]
            requestId: 9d3c02cc698f8b041c661a7c28bed293
            origin: s2s-api-auth-catalog
            status: 429
            statusText: Too Many Requests
            timestamp: '2024-09-30T06:52:38Z'
    NotFound:
      description: Not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: NotFound
            message: Не найдено
    Unauthorized:
      description: User not authorized
      content:
        application/json:
          schema:
            type: string
          examples:
            TokenMissing:
              $ref: '#/components/examples/TokenMissing'
            TokenInvalid:
              $ref: '#/components/examples/TokenInvalid'
            TokenNotFound:
              $ref: '#/components/examples/TokenNotFound'
    AccessDenied:
      description: Access denied
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: AccessDenied
            message: Доступ запрещён
    ResponseGoodsLists:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  listGoods:
                    description: Product data
                    type: array
                    items:
                      $ref: '#/components/schemas/GoodsList'
    ResponseGoodHistories:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  uploadID:
                    type: integer
                    description: Upload ID
                    example: 3235236546
                  historyGoods:
                    type: array
                    description: Data of products in the upload
                    items:
                      $ref: '#/components/schemas/GoodHistory'
    ResponseGoodBufferHistories:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  uploadID:
                    type: integer
                    description: Upload ID
                    example: 3235236546
                    nullable: true
                  bufferGoods:
                    type: array
                    description: Data of products in the upload
                    items:
                      $ref: '#/components/schemas/GoodBufferHistory'
              error:
                type: boolean
                description: Error flag
                example: false
              errorText:
                type: string
                description: Error text
                example: ''
    ResponseTaskHistory:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                $ref: '#/components/schemas/SupplierTaskMetadata'
              error:
                type: boolean
                description: Error flag
                example: false
              errorText:
                description: "Error text\n\n<div class=\"description_important\">\n  <code>The new price is several times lower than the current price. Item has been moved to Price Quarantine</code> error means that new price with discount is at least 3 times less than the previous one. You can edit price/discount using API or remove the price out of quarantine <a href=\"https://seller.wildberries.ru/discount-and-prices/quarantine\">in\_your account</a>\n</div>\n"
                type: string
                example: The new price is several times lower than the current price. Item has been moved to Price Quarantine
    ResponseTaskBuffer:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                $ref: '#/components/schemas/SupplierTaskMetadataBuffer'
              error:
                type: boolean
                description: Error flag
                example: false
              errorText:
                type: string
                description: Error text
                example: ''
    ResponseSizeLists:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                properties:
                  listGoods:
                    description: Product sizes
                    type: array
                    items:
                      $ref: '#/components/schemas/SizeGood'
              error:
                description: Error flag
                type: boolean
                example: false
              errorText:
                description: Error text
                type: string
    SuccessTaskResponse:
      description: Success
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TaskCreated'
    ResponseQuarantineGoods:
      description: Success
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                nullable: true
                type: object
                properties:
                  quarantineGoods:
                    description: Information about products in quarantine
                    type: array
                    items:
                      $ref: '#/components/schemas/QuarantineGoods'
              error:
                description: Error flag
                type: boolean
                example: false
              errorText:
                description: Error text
                type: string
                example: ''
    Responses208:
      description: Upload already exists
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RequestAlreadyExistsError'
          examples:
            This task already exists:
              description: Upload already exists
              value:
                data:
                  id: 1111111
                  alreadyExists: true
                error: false
                errorText: This task already exists
    StatusNotAcceptable:
      description: Update of the inventory is blocked
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: StatusNotAcceptable
            message: Обновление остатков заблокировано в связи с баном поставщика
  examples:
    IncorrectParameter:
      value:
        code: IncorrectParameter
        message: An incorrect parameter was passed
    SubjectDBSRestriction:
      value:
        code: SubjectDBSRestriction
        message: The product category cannot be sold with 'Fulfillment By Wildberries'.
        data:
          - sku: skuTest1
            stock: 0
    SubjectFBSRestriction:
      value:
        code: SubjectFBSRestriction
        message: The product category cannot be sold with 'Delivery By Supplier'.
        data:
          - sku: skuTest2
            stock: 1
    UploadDataLimit:
      value:
        code: UploadDataLimit
        message: Upload limit exceeded.
        data:
          - sku: skuTest2
            stock: 100001
    CargoWarehouseRestrictionMGT:
      value:
        code: CargoWarehouseRestrictionMGT
        message: The selected warehouse is not suitable for goods with the type "LCL (Less than Container Load)". Upload the balances to the warehouse without the ODC or CD+ mark
        data:
          - sku: skuTest3
            stock: 10
    CargoWarehouseRestrictionSGT:
      value:
        code: CargoWarehouseRestrictionSGT
        message: The selected warehouse is not suitable for goods with the type "ODC". Upload the balances to the warehouse marked - ODC
        data:
          - sku: skuTest3
            stock: 10
    CargoWarehouseRestrictionSGTKGTPlus:
      value:
        code: CargoWarehouseRestrictionSGTKGTPlus
        message: The selected warehouse is not suitable for goods with the type "ODC/CD+". Upload the balances to the warehouse with the label - ODC or CD+
        data:
          - sku: skuTest3
            stock: 10
    CargoWarehouseRestrictionKGTPlus:
      value:
        code: CargoWarehouseRestrictionKGTPlus
        message: The selected warehouse is not suitable for goods with the type "CD+". Upload the balances to the warehouse marked - CD+
        data:
          - sku: skuTest3
            stock: 10
    NotFound:
      value:
        code: NotFound
        message: Not found.
        data:
          - sku: skuTest4
            stock: 10
    RateLimit:
      description: The number of requests per unit of time has been exceeded
      value: Too many requests
    StoreIsProcessing:
      description: The warehouse is in the process of being updated or deleted. Try again after a few seconds.
      value:
        code: StoreIsProcessing
        message: The store is processing
    creatingOneCard:
      description: ''
      value:
        - subjectID: 105
          variants:
            - vendorCode: АртикулПродавца
              title: Наименование товара
              description: Описание товара
              brand: Бренд
              dimensions:
                length: 11
                width: 2
                height: 8
                weightBrutto: 1.624
              characteristics:
                - id: 12
                  value:
                    - Turkish flag
                - id: 25471
                  value: 1200
                - id: 14177449
                  value:
                    - red
              sizes:
                - techSize: S
                  wbSize: '42'
                  price: 5000
                  skus:
                    - '88005553535'
    creatingMergedCards:
      description: ''
      value:
        - subjectID: 3091
          variants:
            - vendorCode: АртикулПродавца11
              title: Наименование товара 1
              description: Описание товара 1
              brand: Бренд
              dimensions:
                length: 55
                width: 40
                height: 15
                weightBrutto: 6.24
              characteristics:
                - id: 12
                  value:
                    - Turkish flag
                - id: 25471
                  value: 1200
                - id: 14177449
                  value:
                    - red
              sizes:
                - skus:
                    - '111111111133111'
            - vendorCode: АртикулПродавца22
              title: Наименование товара 2
              description: Описание товара 2
              brand: БрендДругой
              dimensions:
                length: 55
                width: 40
                height: 15
                weightBrutto: 6.241
              characteristics:
                - id: 12
                  value:
                    - Turkish flag
                - id: 25471
                  value: 1200
                - id: 14177449
                  value:
                    - red
              sizes:
                - skus:
                    - '111111111441111'
    creatingGroupOfIndividualCards:
      description: ''
      value:
        - subjectID: 3091
          variants:
            - vendorCode: АртикулПродавца1
              title: Наименование товара 1
              description: Описание товара 1
              brand: Бренд
              dimensions:
                length: 55
                width: 40
                height: 15
                weightBrutto: 6.3
              characteristics:
                - id: 12
                  value:
                    - Turkish flag
                - id: 25471
                  value: 1200
                - id: 14177449
                  value:
                    - red
              sizes:
                - skus:
                    - '1111111111111'
        - subjectID: 105
          variants:
            - vendorCode: Артикул продавца 2
              title: Наименование товара 2
              description: Описание товара 2
              brand: Бренд
              dimensions:
                length: 3
                width: 8
                height: 4
                weightBrutto: 0.83
              characteristics:
                - id: 12
                  value:
                    - Turkish flag
                - id: 25471
                  value: 1200
                - id: 14177449
                  value:
                    - red
              sizes:
                - techSize: S
                  wbSize: '42'
                  price: 5000
                  skus:
                    - '2222222222222'
    responseIncorrectBeginDate:
      description: Incorrect start date
      value:
        error: Incorrect start date
    responseIncorrectEndDate:
      description: Incorrect end date
      value:
        error: Incorrect end date
    responseExceededLimit:
      description: ''
      value:
        data: null
        error: true
        errorText: Maximum 30 cards
        additionalErrors:
          error: Maximum 30 cards
    responseCombining:
      description: ''
      value:
        data: null
        error: true
        errorText: You can not merge products with different subjects
        additionalErrors:
          error: You can not merge products with different subjects
    responseIncorrectRequestFormat:
      description: ''
      value:
        data: null
        error: true
        errorText: Incorrect request
        additionalErrors:
          error: Incorrect request
    responseNonExistentNmId:
      description: ''
      value:
        data: null
        error: true
        errorText: nmID not found
        additionalErrors:
          error: nmID not found
    responseNonExistentImt:
      description: ''
      value:
        data: null
        error: true
        errorText: imt not found
        additionalErrors:
          error: imt not found
    responseDuplicateRequests:
      description: ''
      value:
        data: null
        error: true
        errorText: Duplicate requests
        additionalErrors:
          error: Duplicate requests
    responseAllCardsInSameGroup:
      description: ''
      value:
        data: null
        error: true
        errorText: All cards are in the same group
        additionalErrors:
          error: All cards are in the same group
    responseCardCreate1:
      description: ''
      value:
        data: null
        error: true
        errorText: string
        additionalErrors:
          string: string
    InvalidRequestFormatContent:
      description: Incorrect request
      value:
        data: null
        error: true
        errorText: Incorrect request
        additionalErrors: {}
    CardCreatedWithoutVendorCode:
      description: Seller's article is required
      value:
        data: null
        error: true
        errorText: vendorCode is required
        additionalErrors: {}
    CardsVendorCodeUsedInOtherCards:
      description: Seller's article used in another card
      value:
        data: null
        error: true
        errorText: Seller's article used in another card
        additionalErrors: {}
    ThisCategoryDoesNotExist:
      description: Subject not found
      value:
        data: null
        error: true
        errorText: Subject not found
        additionalErrors:
          id: '342342'
    responseBodyContentError400:
      description: Bad request
      value:
        data: null
        error: true
        errorText: Error text
        additionalErrors:
          MoveNmsToTrash: Bad request
    responseOK200:
      description: Success
      value:
        data: null
        error: false
        errorText: ''
        additionalErrors: null
    responseBody400LenName:
      description: Bad request
      value:
        data: null
        error: true
        errorText: Invalid tag name value
        additionalErrors:
          description: Tag name too long. No more than 15 characters allowed
    responseBodyLimitTag:
      description: ''
      value:
        data: null
        error: true
        errorText: Tag creation limit reached
        additionalErrors: null
    responseBodyNameNotExist:
      description: ''
      value:
        data: null
        error: true
        errorText: A tag with this name already exists
        additionalErrors: null
    responseNotFound200:
      description: ''
      value:
        data: null
        error: true
        errorText: Incorrect request
        additionalErrors:
          description: Such a tag does not exist for the seller
    responseNotFound200Del:
      description: ''
      value:
        data: null
        error: true
        errorText: Such a tag does not exist
        additionalErrors:
          tagID: '1234'
    IncorrectRequest4:
      description: ''
      value:
        data: null
        error: true
        errorText: Bad request
        additionalErrors:
          description: Non-existent tags specified
    responseBodyTagNotExist:
      description: ''
      value:
        data: null
        error: true
        errorText: Such a tag does not exist
        additionalErrors:
          tagID: '1234'
    TokenMissing:
      description: The token is missing
      value: 'proxy: unauthorized'
    TokenInvalid:
      description: Token invalid
      value: 'proxy: invalid token'
    TokenNotFound:
      description: Token deleted
      value: 'proxy: not found'
    BodySizeExceedsTheGivenLimit:
      description: The request body size exceeds the given limit
      value: body size exceeds the given limit
    IncorrectRequestBody:
      value:
        code: IncorrectRequestBody
        message: Incorrect request body
    WarehouseNameInvalid:
      value:
        code: WarehouseNameInvalid
        message: Incorrect warehouse name
    IncorrectRequest:
      value:
        code: IncorrectRequest
        message: Incorrect request parameters
    InvalidRequestParameters:
      description: Incorrect request parameters
      value:
        data: null
        error: true
        errorText: Invalid request parameters
    UploadLimitExceeded:
      description: Too many products (it should be read `1000 items`)
      value:
        data: null
        error: true
        errorText: 'Upload limit exceeded: You can upload a maximum of 10 000 items'
    DuplicateItemNos:
      description: Duplicated `nmID` in the request
      value:
        data: null
        error: true
        errorText: Duplicate item Nos.
    DuplicateSizeIDs:
      description: Duplicated `sizeID` in the request
      value:
        data: null
        error: true
        errorText: Duplicate size IDs
    TheSpecifiedPricesAndDiscountsAreAlreadySet:
      description: No price or discount changes
      value:
        data: null
        error: true
        errorText: The specified prices and discounts are already set
    TheSpecifiedPricesAreAlreadySet:
      description: No price changes
      value:
        data: null
        error: true
        errorText: The specified prices and discounts are already set
    InvalidDataFormat:
      description: Data can not be processed, check the request
      value:
        data: null
        error: true
        errorText: Invalid data format
    PriceShouldBeAWholeNumber:
      description: Price has decimal value, but it should be integer
      value:
        data: null
        error: true
        errorText: Price should be a whole number
    InvalidPriceValue:
      description: Incorrect price
      value:
        data: null
        error: true
        errorText: Invalid price value
    InvalidDiscountValue:
      description: Incorrect discount
      value:
        data: null
        error: true
        errorText: Invalid discount value
    InvalidItemNo:
      description: Incorrect `nmID`
      value:
        data: null
        error: true
        errorText: Invalid item No.
    InvalidSize:
      description: Incorrect `sizeID`
      value:
        data: null
        error: true
        errorText: Invalid size
    PriceAndDiscountNotSpecified:
      description: Both price and discount are not specified
      value:
        data: null
        error: true
        errorText: Price and discount not specified
    PriceNotSpecified:
      description: Price is not specified
      value:
        data: null
        error: true
        errorText: Price and discount not specified
    EmptyData:
      description: Empty data
      value:
        data: null
        error: true
        errorText: Empty data
    AllItemNosAreSpecifiedIncorrectlyOrPricesAndDiscounts:
      description: No products (for example, they were deleted), or no price or discount changes
      value:
        data: null
        error: true
        errorText: All item Nos. are specified incorrectly, or the specified prices and discounts are already set
    AllItemNosAreSpecifiedIncorrectlyOrPrices:
      description: No products or sizes (for example, they were deleted), or no price changes
      value:
        data: null
        error: true
        errorText: All item Nos. are specified incorrectly, or the specified prices and discounts are already set
    SortError:
      description: You should choose sorting by price or by discount, not both values
      value:
        data: null
        error: true
        errorText: You can't sort items by price and discount at the same time
    CheckTheWBClubDiscount:
      description: WB Club discounts can only take specified values
      value:
        data: null
        error: true
        errorText: 'Check the field values in the WB Club Discount column. Field format: a whole number between {{.WbClubMinDiscount}} and {{.WbClubMaxDiscount}}, without dots or commas'
    DiscountsAreTheSameAsThoseAlreadySet:
      description: Some of the discounts in the request are already set
      value:
        data: null
        error: true
        errorText: Discounts in the file are the same as those already set. To change discounts, edit the file and upload it again
    AllItemNosAreSpecifiedIncorrectlyOrDiscounts:
      description: No products (for example, they were deleted), or no discount changes
      value:
        data: null
        error: true
        errorText: All item Nos. are specified incorrectly, or the specified discounts are already set
    UnexpectedResult:
      description: Unexpected result, create a dialogue in Seller Support, <code>API</code> category
      value:
        data: null
        error: true
        errorText: Unexpected result
  securitySchemes:
    HeaderApiKey:
      type: apiKey
      name: Authorization
      in: header
