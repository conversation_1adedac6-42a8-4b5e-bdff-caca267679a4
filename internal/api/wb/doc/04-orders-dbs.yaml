openapi: 3.0.1
info:
  title: Заказы DBS
  version: order
  description: |
    <div class="description_important">
        Узнать больше о заказах DBS можно в <a href="https://seller.wildberries.ru/instructions/category/6572e024-7428-4db1-86a8-a4c7dbebbfcf?goBackOption=prevRoute&categoryId=5a8e1202-0865-45b7-acae-5d0afc7add56">справочном центре</a>
    </div>

    Управление [сборочными заданиями](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS) и [метаданными](/openapi/orders-dbs#tag/Metadannye-DBS) заказов DBS (Delivery by Seller).
  x-file-name: orders-dbs
security:
  - HeaderApiKey: []
tags:
  - name: Сборочные задания DBS
    description: ''
  - name: Метаданные DBS
    description: ''
paths:
  /api/v3/dbs/orders/new:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания DBS
      summary: Получить список новых сборочных заданий
      description: |
        Метод предоставляет список всех новых [сборочных заданий](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS), которые есть у продавца на момент запроса.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  orders:
                    type: array
                    nullable: false
                    description: Список новых сборочных заданий
                    items:
                      $ref: '#/components/schemas/OrderNewDBS'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания DBS
      summary: Получить информацию о завершённых сборочных заданиях
      description: |
        Метод предоставляет информацию о завершённых [сборочных заданиях](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS) после продажи или отмены заказа.
        <br><br>
        Можно получить данные за заданный период, максимум 30 календарных дней одним запросом.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Next'
        - name: dateFrom
          in: query
          required: true
          schema:
            type: integer
          description: |
            Дата начала периода в формате Unix timestamp
        - name: dateTo
          in: query
          required: true
          schema:
            type: integer
          description: Дата конца периода в формате Unix timestamp
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  next:
                    $ref: '#/components/schemas/Next'
                  orders:
                    type: array
                    nullable: false
                    items:
                      $ref: '#/components/schemas/OrderDBS'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/groups/info:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      description: |
        Метод предоставляет информацию о платной доставке сборочных заданий, которые поступили на один склад (`warehouseId`) в рамках одной транзакции покупателя (`orderUid`).

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      tags:
        - Сборочные задания DBS
      summary: Получить информацию о платной доставке
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/api.OrderGroupsRequest'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/api.OrderGroup'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody1'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/client:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Информация о покупателе
      description: |
        Метод предоставляет информацию о покупателе по ID сборочных заданий.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      tags:
        - Сборочные задания DBS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrdersRequestAPI'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DbsOnlyClientInfoResp'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/delivery-date:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Дата и время доставки
      description: |
        Метод предоставляет информацию о выбранных покупателем дате и времени доставки сборочных заданий.
        <br>

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      tags:
        - Сборочные задания DBS
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeliveryDatesRequest'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryDatesInfoResp'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest1'
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/status:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания DBS
      summary: Получить статусы сборочных заданий
      description: |
        Метод предоставляет статусы сборочных заданий по их ID.
        <br><br>
        `supplierStatus` — статус сборочного задания. Триггер его изменения — сам продавец.

        Возможные значения `supplierStatus`:
        | Статус   | Описание            | Как перевести сборочное задание в данный статус |
        | -------  | ---------           | --------------------------------------|
        | `new`      | **Новое сборочное задание** | |
        | `confirm`  | **На сборке**      |  <a href="/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1{orderId}~1confirm/patch">Перевести сборочное задание на сборку</a>
        | `deliver`  | **В доставке**    | <a href="/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1{orderId}~1deliver/patch">Перевести сборочное задание в доставку</a>
        | `receive`  | **Получено покупателем**       | <a href="/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1{orderId}~1receive/patch">Сообщить, что заказ принят покупателем</a> <br>
        | `reject`   | **Отказ покупателя при получении**           |  <a href="/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1{orderId}~1reject/patch">Сообщить, что покупатель отказался от заказа</a>
        | `cancel`   | **Отменено продавцом**   |  <a href="/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1{orderId}~1cancel/patch">Отменить сборочное задание</a>
        | `cancel_missed_call` | **Отмена заказа по причине недозвона** | Статус меняется автоматически |

        <br><br>
        `wbStatus` — статус сборочного задания в системе WB.

        Возможные значения `wbStatus`:
        - `waiting` - сборочное задание в работе
        - `sold` - сборочное задание получено покупателем
        - `canceled` - отмена сборочного задания
        - `canceled_by_client` - покупатель отменил заказ при получении
        - `declined_by_client` - покупатель отменил заказ в первый чаc
        <br> Отмена доступна покупателю в первый час с момента заказа, если заказ не переведён на сборку
        - `defect` - отмена сборочного задания по причине брака
        - `ready_for_pickup` - сборочное задание прибыло на ПВЗ
        - `canceled_by_missed_call` - отмена заказа по причине недозвона

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - orders
              properties:
                orders:
                  type: array
                  minItems: 1
                  maxItems: 1000
                  description: Список ID сборочных заданий
                  items:
                    type: integer
                    format: int64
                    example: 5632423
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    nullable: false
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          nullable: false
                          description: ID сборочного задания
                          example: 5632423
                        supplierStatus:
                          type: string
                          nullable: false
                          description: Статус сборочного задания продавца (устанавливается продавцом)
                          example: new
                        wbStatus:
                          type: string
                          nullable: false
                          description: Статус сборочного задания в системе Wildberries
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/cancel:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания DBS
      summary: Отменить сборочное задание
      description: |
        Метод отменяет [сборочное задание](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS) и переводит в [статус](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1status/post) `cancel` — отменено продавцом.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      responses:
        '204':
          description: Отменено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка обновления статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                StatusMismatch:
                  $ref: '#/components/examples/StatusMismatch'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/confirm:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания DBS
      summary: Перевести на сборку
      description: |
        Метод переводит [сборочное задание](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS) в [статус](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1status/post) `confirm` — на сборке.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/OrderDBS'
      responses:
        '204':
          description: Подтверждено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка обновления статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                StatusMismatch:
                  $ref: '#/components/examples/StatusMismatch'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/deliver:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания DBS
      summary: Перевести в доставку
      description: |
        Метод переводит [сборочное задание](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS) в [статус](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1status/post) `deliver` — в доставке.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/OrderDBS'
      responses:
        '204':
          description: Подтверждено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка обновления статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                StatusMismatch:
                  $ref: '#/components/examples/StatusMismatch'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/receive:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания DBS
      summary: Сообщить, что заказ принят покупателем
      description: |
        Метод переводит [сборочное задание](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS) в [статус](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1status/post) `receive` — получено покупателем.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Code'
      parameters:
        - $ref: '#/components/parameters/OrderDBS'
      responses:
        '204':
          description: Подтверждено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка обновления статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                StatusMismatch:
                  $ref: '#/components/examples/StatusMismatch'
                InvalidWBCode:
                  $ref: '#/components/examples/InvalidWBCode'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/reject:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания DBS
      summary: Сообщить, что покупатель отказался от заказа
      description: |
        Метод переводит [сборочное задание](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS) в [статус](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1status/post) `reject` — отказ при получении.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Code'
      parameters:
        - $ref: '#/components/parameters/OrderDBS'
      responses:
        '204':
          description: Подтверждено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка обновления статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                StatusMismatch:
                  $ref: '#/components/examples/StatusMismatch'
                InvalidWBCode:
                  $ref: '#/components/examples/InvalidWBCode'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/meta:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные DBS
      summary: Получить метаданные сборочного задания
      description: |
        Метод предоставляет метаданные заказа, доступные для [сборочного задания](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1orders/get).
        <br><br>
        Возможные метаданные:
          - `imei` — [IMEI](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta~1imei/put)
          - `uin` — [УИН](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta~1uin/put)
          - `gtin` — [GTIN](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta~1gtin/put)
          - `sgtin` — [код маркировки](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta~1sgtin/put)

        Если ответ вернулся с пустой структурой `meta`, значит у сборочного задания нет метаданных и добавить их нельзя.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  meta:
                    $ref: '#/components/schemas/Meta'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
    delete:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные DBS
      summary: Удалить метаданные сборочного задания
      description: |
        Метод удаляет значение [метаданных сборочного задания](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta/get) для переданного ключа.
        <br><br>
        Возможные метаданные:
          - `imei` — [IMEI](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta~1imei/put)
          - `uin` — [УИН](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta~1uin/put)
          - `gtin` — [GTIN](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta~1gtin/put)
          - `sgtin` — [код маркировки](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta~1sgtin/put)

        Можно передать только один ключ.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
        - name: key
          in: query
          schema:
            type: string
          description: Название метаданных для удаления (**imei**, **uin**, **gtin**, **sgtin**). Передается только одно значение.
      responses:
        '204':
          description: Удалено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '409':
          description: Ошибка удаления метаданных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/meta/sgtin:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные DBS
      summary: Закрепить за сборочным заданием код маркировки товара
      description: |
        Метод позволяет закрепить за сборочным заданием код маркировки [Честный знак](https://честныйзнак.рф).
        <br><br>
        Закрепить код маркировки можно только если в [метаданных сборочного задания](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta/get) есть поле `sgtin`, а сборочное задание находится в [статусе](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1status/post) `confirm`.
        <br><br>
        Получить загруженные маркировки можно в [метаданных сборочного задания](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta/get).

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов <strong>закрепления метаданных</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                sgtins:
                  type: array
                  description: Массив кодов маркировки. Допускается от 16 до 135 символов для кода одной маркировки.
                  minItems: 1
                  maxItems: 24
                  items:
                    type: string
                    description: Код маркировки на упаковке. От 16 до 135 символов.
                    example: '1234567890123456'
      responses:
        '204':
          description: Отправлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка добавления маркировки.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/meta/uin:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные DBS
      summary: Закрепить за сборочным заданием УИН (уникальный идентификационный номер)
      description: |
        Метод обновляет УИН в [метаданных сборочного задания](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta/get) — уникальный идентификационный номер.
        <br><br>
        У одного сборочного задания может быть только один УИН.

        Добавлять маркировку можно только для заказов, которые доставляются WB и находятся в [статусе](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1status/post) `confirm`.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов <strong>закрепления метаданных</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                uin:
                  type: string
                  minLength: 16
                  maxLength: 16
                  description: УИН
                  example: '1234567890123456'
              required:
                - uin
      responses:
        '204':
          description: Обновлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка добавления маркировки.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/meta/imei:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные DBS
      summary: Закрепить за сборочным заданием IMEI
      description: |
        Метод обновляет IMEI в [метаданных сборочного задания](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta/get).
        <br><br>
        У одного сборочного задания может быть только один IMEI.

        Добавлять маркировку можно только для заказов, которые доставляются WB и находятся в [статусе](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1status/post) `confirm`.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов <strong>закрепления метаданных</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                imei:
                  type: string
                  minLength: 15
                  maxLength: 15
                  description: IMEI
                  example: '123456789012345'
              required:
                - imei
      responses:
        '204':
          description: Обновлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка добавления маркировки.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/dbs/orders/{orderId}/meta/gtin:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные DBS
      summary: Закрепить за сборочным заданием GTIN
      description: |
        Метод обновляет GTIN в [метаданных сборочного задания](/openapi/orders-dbs#tag/Metadannye-DBS/paths/~1api~1v3~1dbs~1orders~1%7BorderId%7D~1meta/get) — уникальный ID товара в Беларуси.
        <br><br>
        У одного сборочного задания может быть только один GTIN.

        Добавлять маркировку можно только для заказов, которые доставляются WB и находятся в [статусе](/openapi/orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1status/post) `confirm`.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов <strong>закрепления метаданных</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                gtin:
                  type: string
                  minLength: 13
                  maxLength: 13
                  description: GTIN
                  example: '1234567890123'
              required:
                - gtin
      responses:
        '204':
          description: Обновлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка добавления маркировки.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
components:
  securitySchemes:
    HeaderApiKey:
      type: apiKey
      name: Authorization
      in: header
  parameters:
    OrderDBS:
      name: orderId
      in: path
      required: true
      description: ID сборочного задания
      schema:
        type: integer
        format: int64
        example: 5632423
    Order:
      name: orderId
      in: path
      required: true
      description: ID сборочного задания
      schema:
        type: integer
        format: int64
        example: 5632423
    Next:
      name: next
      in: query
      required: true
      schema:
        type: integer
        format: int64
      description: Параметр пагинации. Устанавливает значение, с которого надо получить следующий пакет данных. Для получения полного списка данных должен быть равен 0 в первом запросе. Для следующих запросов необходимо брать значения из одноимённого поля в ответе.
    Limit:
      name: limit
      in: query
      required: true
      schema:
        type: integer
        minimum: 1
        maximum: 1000
      description: Параметр пагинации. Устанавливает предельное количество возвращаемых данных.
  examples:
    IncorrectRequestBody:
      description: Некорректное тело запроса
      value:
        code: IncorrectRequestBody
        message: Некорректное тело запроса
    IncorrectRequestBody1:
      value:
        code: IncorrectRequestBody
        message: ''
    IncorrectRequest:
      value:
        code: IncorrectRequest
        message: Переданы некорректные данные
    IncorrectRequest1:
      description: Некорректный запрос
      value:
        code: IncorrectRequest
        message: ''
    IncorrectParameter:
      value:
        code: IncorrectParameter
        message: Передан некорректный параметр
    FailedToUpdateMeta:
      value:
        code: FailedToUpdateMeta
        message: Не удалось обновить метаданные сборочного задания. Убедитесь, что сборочное задание удовлетворяет всем необходимым требованиям (сборочное задание существует, находится в статусе "На сборке", сборочному заданию доступны метаданные см. описание метода **Получить метаданные сборочного задания**)
    StatusMismatch:
      value:
        code: StatusMismatch
        message: Несоответствие статусов, проверьте их правильность
    InvalidWBCode:
      description: Код недействителен
      value:
        code: InvalidWBCode
        message: ''
  responses:
    '401':
      description: Пользователь не авторизован
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Заголовок ошибки
              detail:
                type: string
                description: Детали ошибки
              code:
                type: string
                description: Внутренний код ошибки
              requestId:
                type: string
                description: Уникальный ID запроса
              origin:
                type: string
                description: ID внутреннего сервиса WB
              status:
                type: number
                description: HTTP статус-код
              statusText:
                type: string
                description: Расшифровка HTTP статус-кода
              timestamp:
                type: string
                format: date-time
                description: Дата и время запроса
          example:
            title: unauthorized
            detail: 'token problem; token is malformed: could not base64 decode signature: illegal base64 data at input byte 84'
            code: 07e4668e--a53a3d31f8b0-[UK-oWaVDUqNrKG]; 03bce=277; 84bd353bf-75
            requestId: 7b80742415072fe8b6b7f7761f1d1211
            origin: s2s-api-auth-catalog
            status: 401
            statusText: Unauthorized
            timestamp: '2024-09-30T06:52:38Z'
    '429':
      description: Слишком много запросов
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Заголовок ошибки
              detail:
                type: string
                description: Детали ошибки
              code:
                type: string
                description: Внутренний код ошибки
              requestId:
                type: string
                description: Уникальный ID запроса
              origin:
                type: string
                description: ID внутреннего сервиса WB
              status:
                type: number
                description: HTTP статус-код
              statusText:
                type: string
                description: Расшифровка HTTP статус-кода
              timestamp:
                type: string
                format: date-time
                description: Дата и время запроса
          example:
            title: too many requests
            detail: limited by c122a060-a7fb-4bb4-abb0-32fd4e18d489
            code: 07e4668e-ac2242c5c8c5-[UK-4dx7JUdskGZ]
            requestId: 9d3c02cc698f8b041c661a7c28bed293
            origin: s2s-api-auth-catalog
            status: 429
            statusText: Too Many Requests
            timestamp: '2024-09-30T06:52:38Z'
    NotFound:
      description: Не найдено
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: NotFound
            message: Not Found
    AccessDenied:
      description: Доступ запрещён
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: AccessDenied
            message: Доступ запрещён
  schemas:
    api.OrderGroupsRequest:
      type: object
      properties:
        groups:
          type: array
          maxItems: 1000
          description: Список значений `groupId`. Можно получить из [новых](./orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders~1new/get) и [завершённых](./orders-dbs#tag/Sborochnye-zadaniya-DBS/paths/~1api~1v3~1dbs~1orders/get) сборочных заданий
          items:
            type: string
      example:
        groups:
          - 7a2c8810-1db2-4011-9682-5c7fa33afd83
          - 5a8f4523-4cf2-5691-9682-5a7fd33afd73
    Error:
      type: object
      nullable: false
      properties:
        code:
          type: string
          description: Код ошибки
          nullable: false
        message:
          type: string
          description: Описание ошибки
          nullable: false
        data:
          type: object
          description: Дополнительные данные, обогащающие ошибку
          nullable: true
    OrdersRequestAPI:
      properties:
        orders:
          description: Список ID сборочных заданий
          items:
            type: integer
          type: array
      type: object
      example:
        orders:
          - 987654321
          - 123456789
    api.OrderGroup:
      type: array
      items:
        type: object
        properties:
          groupID:
            description: ID группы сборочных заданий
            type: string
            format: UUID
            example: 0596a30a-d11c-4210-a231-ee1c39d61fe4
          deliveryCost:
            description: Стоимость платной доставки в валюте продажи, умноженная на 100
            type: integer
            example: 1108
          convertedDeliveryCost:
            description: Стоимость платной доставки в валюте страны продавца, умноженная на 100. Предоставляется в информационных целях.
            type: integer
            example: 29803
          currencyCode:
            description: Код валюты продажи
            format: ISO 4217
            type: integer
            example: 933
          convertedCurrencyCode:
            description: Код валюты страны продавца
            type: integer
            example: 643
    DeliveryDatesRequest:
      properties:
        orders:
          description: Список ID сборочных заданий
          items:
            type: integer
            minItems: 1
            maxItems: 1000
          type: array
      type: object
      example:
        orders:
          - 1234567890
    Code:
      properties:
        code:
          type: string
          description: Код подтверждения. <br> Отображается у покупателя на сайте и в приложении Wildberries
      type: object
      example:
        code: '123456'
    Next:
      type: integer
      format: int64
      nullable: false
      description: Параметр пагинации. Содержит значение, которое необходимо указать в запросе для получения следующего пакета данных
      example: 13833711
    OrderNewDBS:
      type: object
      properties:
        salePrice:
          description: |
            Цена в валюте продажи с учётом скидки продавца, без учёта скидки WB Клуба, умноженная на 100. Предоставляется в информационных целях
          type: integer
          nullable: true
          example: 504658
        requiredMeta:
          description: Перечень метаданных, которые необходимо добавить в сборочное задание
          type: array
          nullable: true
          items:
            type: string
          example:
            - uin
        comment:
          type: string
          nullable: false
          description: Комментарий покупателя
          maxLength: 300
          example: Упакуйте в плёнку, пожалуйста
        options:
          type: object
          description: Опции заказа
          properties:
            isB2b:
              type: boolean
              description: |
                Признак B2B-продажи:
                  - <code>false</code> — не B2B-продажа
                  - <code>true</code> — B2B-продажа
        address:
          type: object
          nullable: false
          description: Адрес покупателя для доставки
          properties:
            fullAddress:
              description: Адрес доставки
              type: string
              example: Челябинская область, г. Челябинск, 51-я улица Арабкира, д. 10А, кв. 42
            longitude:
              type: number
              format: float64
              nullable: false
              description: Долгота
              example: 44.519068
            latitude:
              type: number
              format: float64
              nullable: false
              description: Широта
              example: 40.20192
        orderUid:
          type: string
          description: ID транзакции для группировки сборочных заданий. Сборочные задания в одной корзине покупателя будут иметь одинаковый orderUID
          example: 165918930_629fbc924b984618a44354475ca58675
        groupId:
          description: ID группы сборочных заданий. <br> Объединяет сборочные задания, поступившие на один склад (`warehouseId`) в рамках одной транзакции покупателя (`orderUid`)
          type: string
          format: UUID
          example: 7a2c8810-1db2-4011-9682-5c7fa33afd83
        article:
          type: string
          nullable: false
          description: Артикул продавца
          example: one-ring-7548
        colorCode:
          description: Код цвета (только для колеруемых товаров)
          type: string
          nullable: false
          example: RAL 3017
        rid:
          type: string
          nullable: false
          description: ID сборочного задания
          example: f884001e44e511edb8780242ac120002
        createdAt:
          type: string
          format: RFC3339
          nullable: false
          description: Дата создания сборочного задания
          example: '2022-05-04T07:56:29Z'
        deliveryType:
          type: string
          nullable: false
          description: |-
            <dl>
            <dt>Тип доставки:</dt>
            <dd><code>dbs</code> - доставка силами продавца</dd>
            <dd><code>edbs</code> - экспресс-доставка силами продавца</dd>
            </dl>
          enum:
            - dbs
            - edbs
        skus:
          type: array
          nullable: false
          description: Массив баркодов товара
          items:
            type: string
            example: '6665956397512'
        id:
          type: integer
          format: int64
          nullable: false
          description: ID сборочного задания
          example: 13833711
        warehouseId:
          type: integer
          nullable: false
          description: ID склада продавца, на который поступило сборочное задание
          example: 658434
        nmId:
          type: integer
          nullable: false
          description: Артикул WB
          example: 123456789
        chrtId:
          type: integer
          nullable: false
          description: ID размера WB
          example: 987654321
        price:
          type: integer
          nullable: false
          description: Цена в валюте продажи с учетом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Код валюты продажи указан в поле `currencyCode`. Предоставляется в информационных целях
          example: 1014
        finalPrice:
          description: Цена в валюте продажи с учетом всех скидок (к взиманию с покупателя), умноженная на 100. Код валюты продажи указан в поле `currencyCode`. Предоставляется в информационных целях
          type: integer
          example: 1014
        convertedFinalPrice:
          description: Цена в валюте страны продавца с учетом всех скидок (к взиманию с покупателя), умноженная на 100. Предоставляется в информационных целях
          type: integer
          example: 1014
        convertedPrice:
          type: integer
          nullable: false
          description: Цена в валюте страны продавца с учетом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Предоставляется в информационных целях
          example: 1014
        currencyCode:
          type: integer
          nullable: false
          format: ISO 4217
          description: Код валюты продажи
          example: 643
        convertedCurrencyCode:
          type: integer
          nullable: false
          format: ISO 4217
          description: Код валюты страны продавца
          example: 643
        cargoType:
          type: integer
          nullable: false
          description: <dl> <dt>Тип товара:</dt> <dd><code>1</code> - МГТ (малогабаритный, то есть обычный товар)</dd> <dd><code>2</code> - СГТ (Сверхгабаритный товар)</dd> <dd><code>3</code> - КГТ+ (Крупногабаритный товар)</dd> </dl>
          enum:
            - 1
            - 2
            - 3
        isZeroOrder:
          description: Признак заказа, сделанного на нулевой остаток товара. (<code>false</code> - заказ сделан на товар с ненулевым остатком, <code>true</code> - заказ сделан на товар с остатком равным нулю. Такой заказ можно отменить без штрафа за отмену)
          example: false
    OrderDBS:
      type: object
      properties:
        address:
          type: object
          nullable: true
          description: Адрес покупателя для доставки
          properties:
            fullAddress:
              description: Адрес доставки
              type: string
              example: Челябинская область, г. Челябинск, 51-я улица Арабкира, д. 10А, кв. 42
            longitude:
              type: number
              format: float64
              nullable: false
              description: Долгота
              example: 44.519068
            latitude:
              type: number
              format: float64
              nullable: false
              description: Широта
              example: 40.20192
        deliveryType:
          type: string
          nullable: false
          description: |-
            <dl>
            <dt>Тип доставки:</dt>
            <dd><code>dbs</code> - доставка силами продавца</dd>
            <dd><code>edbs</code> - экспресс-доставка силами продавца</dd>
            </dl>
        options:
          type: object
          description: Опции заказа
          properties:
            isB2b:
              type: boolean
              description: |
                Признак B2B-продажи:
                  - <code>false</code> — не B2B-продажа
                  - <code>true</code> — B2B-продажа
        orderUid:
          type: string
          description: ID транзакции для группировки сборочных заданий. Сборочные задания в одной корзине покупателя будут иметь одинаковый `orderUID`
          example: 165918930_629fbc924b984618a44354475ca58675
        groupId:
          description: ID группы сборочных заданий. <br> Объединяет сборочные задания, поступившие на один склад (`warehouseId`) в рамках одной транзакции покупателя (`orderUid`)
          type: string
          format: UUID
          example: 7a2c8810-1db2-4011-9682-5c7fa33afd83
        article:
          type: string
          nullable: false
          description: Артикул продавца
          example: one-ring-7548
        colorCode:
          type: string
          nullable: false
          description: Код цвета (только для колеруемых товаров)
          example: RAL 3017
        rid:
          type: string
          nullable: false
          description: ID сборочного задания
          example: f884001e44e511edb8780242ac120002
        createdAt:
          type: string
          format: RFC3339
          nullable: false
          description: Дата создания сборочного задания
          example: '2022-05-04T07:56:29Z'
        skus:
          type: array
          nullable: false
          description: Массив баркодов товара
          items:
            type: string
            example: '6665956397512'
        id:
          type: integer
          format: int64
          nullable: false
          description: ID сборочного задания
          example: 13833711
        warehouseId:
          type: integer
          nullable: false
          description: ID склада продавца, на который поступило сборочное задание
          example: 658434
        nmId:
          type: integer
          nullable: false
          description: Артикул WB
          example: 12345678
        chrtId:
          type: integer
          nullable: false
          description: ID размера WB
          example: 987654321
        price:
          type: integer
          nullable: false
          description: Цена в валюте продажи с учетом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Код валюты продажи указан в поле `currencyCode`. Предоставляется в информационных целях
          example: 1014
        convertedPrice:
          type: integer
          nullable: false
          description: Цена в валюте страны продавца с учетом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Предоставляется в информационных целях
          example: 1014
        currencyCode:
          type: integer
          nullable: false
          format: ISO 4217
          description: Код валюты продажи
          example: 643
        convertedCurrencyCode:
          type: integer
          nullable: false
          format: ISO 4217
          description: Код валюты страны продавца
          example: 643
        convertedFinalPrice:
          description: Цена в валюте страны продавца с учетом всех скидок (к взиманию с покупателя), умноженная на 100. Предоставляется в информационных целях
          type: integer
          example: 1014
        finalPrice:
          description: Цена в валюте продажи с учетом всех скидок (к взиманию с покупателя), умноженная на 100. Код валюты продажи указан в поле `currencyCode`. Предоставляется в информационных целях
          type: integer
          example: 1014
        cargoType:
          type: integer
          nullable: false
          description: <dl> <dt>Тип товара:</dt> <dd><code>1</code> - МГТ (малогабаритный, то есть обычный товар)</dd> <dd><code>2</code> - СГТ (Сверхгабаритный товар)</dd> <dd><code>3</code> - КГТ+ (Крупногабаритный товар)</dd> </dl>
          enum:
            - 1
            - 2
            - 3
        comment:
          type: string
          nullable: false
          description: Комментарий покупателя
          maxLength: 300
          example: Упакуйте в плёнку, пожалуйста
        isZeroOrder:
          description: Признак заказа, сделанного на нулевой остаток товара. (<code>false</code> - заказ сделан на товар с ненулевым остатком, <code>true</code> - заказ сделан на товар с остатком равным нулю. Такой заказ можно отменить без штрафа за отмену)
          example: false
    DbsOnlyClientInfo:
      properties:
        replacementPhone:
          type: string
          description: Основной номер телефона для связи с покупателем. <br> Не является прямым номером покупателя
          example: '79871234567'
          nullable: false
        firstName:
          type: string
          description: Имя покупателя
          nullable: false
        fullName:
          type: string
          description: Полное имя, используется для оформления документов. Например, документы на автомобиль.
          example: Иван Иван Иванович
          nullable: false
        orderID:
          type: integer
          description: ID сборочного задания
          example: 134567
        phone:
          type: string
          description: Резервный номер телефона для связи с покупателем. Использовать, если недоступен основной номер из `replacementPhone`. <br> Чтобы связаться с покупателем, наберите этот номер и введите добавочный код из `phoneCode`. <br> Не является прямым номером покупателя.
          example: '+79871234567'
          nullable: false
        phoneCode:
          type: integer
          description: Добавочный код
          example: 1234567
          nullable: false
        additionalPhoneCodes:
          nullable: false
          type: array
          items:
            type: string
          description: Дополнительные добавочные коды покупателя. Использовать, если не получилось дозвониться по добавочному коду из `phoneCode`
          example:
            - '12345'
            - '65498'
      type: object
    DbsOnlyClientInfoResp:
      properties:
        orders:
          description: Информация о покупателе
          items:
            $ref: '#/components/schemas/DbsOnlyClientInfo'
          type: array
      type: object
    DeliveryDatesInfoResp:
      type: object
      properties:
        orders:
          type: array
          items:
            type: object
            properties:
              dTimeFrom:
                type: string
                nullable: true
                description: Актуальное время доставки "с"
                example: '11:11'
              dTimeTo:
                type: string
                nullable: true
                description: Актуальное время доставки "по"
                example: '22:22'
              dTimeFromOld:
                type: string
                nullable: true
                description: Прежнее время доставки "с". Будет доступно только первые сутки после изменения
                example: '12:30'
              dTimeToOld:
                type: string
                nullable: true
                description: Прежнее время доставки "по". Будет доступно только первые сутки после изменения
                example: '22:30'
              dDateOld:
                type: string
                nullable: true
                description: Прежняя дата доставки. Будет доступна только первые сутки после изменения
                example: '2025-01-28'
              dDate:
                type: string
                nullable: false
                description: Актуальная дата доставки
                example: '2025-02-20'
              id:
                type: integer
                description: ID сборочного задания
                example: 1234567890
    Meta:
      type: object
      description: Метаданные заказа
      properties:
        imei:
          type: object
          properties:
            value:
              nullable: true
              type: string
              example: '123456789012345'
          description: IMEI
        uin:
          type: object
          properties:
            value:
              nullable: true
              type: string
              example: '123456789012345'
          description: УИН
        gtin:
          type: object
          properties:
            value:
              nullable: true
              type: string
              example: '123456789012345'
          description: GTIN
        sgtin:
          type: object
          properties:
            value:
              type: array
              items:
                type: string
              example:
                - '123456789012345'
              nullable: true
          description: Код маркировки Честного знака