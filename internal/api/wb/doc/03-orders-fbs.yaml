openapi: 3.0.1
info:
  title: Заказы FBS
  version: order
  description: |
    <div class="description_important">
        Узнать больше о заказах FBS можно в <a href="https://seller.wildberries.ru/instructions/category/b3e60238-fd4c-49ce-8668-ff688725a12d?goBackOption=prevRoute&categoryId=5a8e1202-0865-45b7-acae-5d0afc7add56">справочном центре</a>
    </div>

    В разделе заказов FBS (Fulfillment by Seller) вам доступны методы:
      1. Управления [сборочными заданиями](/openapi/orders-fbs#tag/Sborochnye-zadaniya): информация о сборочных заданиях, метаданные, стикеры и так далее.
      2. Управления [поставками заказов](/openapi/orders-fbs#tag/Postavki-FBS) продавца на склады WB.
      3. Заказа [пропусков](/openapi/orders-fbs#tag/Propuska) на склады WB.
      4. Доставки заказов [курьером WB](/openapi/orders-fbs#tag/Dostavka-kurerom-WB-(DBW)) (DBW).
  x-file-name: orders-fbs
security:
  - HeaderApiKey: []
tags:
  - name: Сборочные задания
    description: ''
  - name: Метаданные
    description: ''
  - name: Поставки FBS
    description: ''
  - name: Пропуска
    description: ''
  - name: Доставка курьером WB (DBW)
    description: ''
paths:
  /api/v3/passes/offices:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Пропуска
      summary: Получить список складов, для которых требуется пропуск
      description: |
        Метод предоставляет список складов для привязки к [пропуску продавца](/openapi/orders-fbs#tag/Propuska/paths/~1api~1v3~1passes/get).

        <div class="description_important">
          Данные, которые возвращает метод, могут меняться. Рекомендуем периодически синхронизировать список
        </div>

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: array
                nullable: false
                description: Список складов
                items:
                  $ref: '#/components/schemas/PassOffice'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/passes:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Пропуска
      summary: Получить список пропусков
      description: |
        Метод предоставляет список всех [созданных](/openapi/orders-fbs#tag/Propuska/paths/~1api~1v3~1passes/post) пропусков продавца.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: array
                nullable: false
                description: Список пропусков продавца
                items:
                  $ref: '#/components/schemas/Pass'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Пропуска
      summary: Создать пропуск
      description: |
        Метод создаёт [пропуск продавца](/openapi/orders-fbs#tag/Propuska/paths/~1api~1v3~1passes/get) с привязкой к складу WB.

        Пропуск действует 48 часов со времени создания.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 10 минут | 1 запрос | 10 минут | 1 запрос |
        </div>
      requestBody:
        required: true
        description: Общая длина ФИО ограничена от 6 до 100 символов. В номере машины могут быть только буквы и цифры.
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                  nullable: false
                  minLength: 1
                  description: Имя водителя
                  example: Александр
                lastName:
                  type: string
                  nullable: false
                  minLength: 1
                  description: Фамилия водителя
                  example: Петров
                carModel:
                  type: string
                  nullable: false
                  minLength: 1
                  maxLength: 100
                  description: Марка машины
                  example: Lamborghini
                carNumber:
                  type: string
                  nullable: false
                  minLength: 6
                  maxLength: 9
                  description: Номер машины
                  example: A456BC123
                officeId:
                  type: integer
                  format: int64
                  nullable: false
                  minimum: 1
                  description: ID склада
                  example: 15
              required:
                - firstName
                - lastName
                - carModel
                - carNumber
                - officeId
      responses:
        '201':
          description: Создано
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    nullable: false
                    description: ID пропуска продавца
                    example: 2
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/passes/{passId}:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Пропуска
      summary: Обновить пропуск
      description: |
        Метод обновляет данные [пропуска продавца](/openapi/orders-fbs#tag/Propuska/paths/~1api~1v3~1passes/get). В том числе, можно обновить данные привязанного склада WB.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Pass'
      requestBody:
        required: true
        description: Общая длина ФИО ограничена от 6 до 100 символов. В номере машины могут быть только буквы и цифры.
        content:
          application/json:
            schema:
              type: object
              properties:
                firstName:
                  type: string
                  nullable: false
                  minLength: 1
                  description: Имя водителя
                  example: Александр
                lastName:
                  type: string
                  nullable: false
                  minLength: 6
                  description: Фамилия водителя
                  example: Петров
                carModel:
                  type: string
                  nullable: false
                  minLength: 1
                  maxLength: 100
                  description: Марка машины
                  example: Lamborghini
                carNumber:
                  type: string
                  nullable: false
                  minLength: 6
                  maxLength: 9
                  description: Номер машины
                  example: A456BC123
                officeId:
                  type: integer
                  format: int64
                  nullable: false
                  minimum: 1
                  description: ID склада
                  example: 15
              required:
                - firstName
                - lastName
                - carModel
                - carNumber
                - officeId
      responses:
        '204':
          description: Обновлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                WarehouseNameInvalid:
                  $ref: '#/components/examples/WarehouseNameInvalid'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
    delete:
      security:
        - HeaderApiKey: []
      tags:
        - Пропуска
      summary: Удалить пропуск
      description: |
        Метод удаляет пропуск продавца [из списка](/openapi/orders-fbs#tag/Propuska/paths/~1api~1v3~1passes/get).

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Pass'
      responses:
        '204':
          description: Удалено
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/new:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания
      summary: Получить список новых сборочных заданий
      description: |
        Метод предоставляет список всех новых [сборочных заданий](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get), которые есть у продавца на момент запроса.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  orders:
                    type: array
                    nullable: false
                    description: Список новых сборочных заданий
                    items:
                      $ref: '#/components/schemas/OrderNew'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания
      summary: Получить информацию о сборочных заданиях
      description: |
        Метод предоставляет информацию о сборочных заданиях без их актуального [статуса](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post).

        Можно получить данные за заданный период, максимум 30 календарных дней одним запросом.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Next'
        - name: dateFrom
          in: query
          schema:
            type: integer
          description: |
            Дата начала периода в формате Unix timestamp. По умолчанию — дата за 30 дней до запроса
        - name: dateTo
          in: query
          schema:
            type: integer
          description: Дата конца периода в формате Unix timestamp
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  next:
                    $ref: '#/components/schemas/Next'
                  orders:
                    type: array
                    nullable: false
                    items:
                      $ref: '#/components/schemas/Order'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/status:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания
      summary: Получить статусы сборочных заданий
      description: |
        Метод предоставляет статусы [сборочных заданий](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) по их ID.
        <br><br>
        `supplierStatus` — статус сборочного задания. Триггер его изменения — сам продавец.

        Возможные значения `supplierStatus`:

        | Статус   | Описание            | Как перевести сборочное задание в данный статус |
        |-------|----------------------|--------------------------------------|
        | `new`      | **Новое сборочное задание** |  |
        | `confirm`  | **На сборке** <br>Для доставки силами Wildberries `fbs` |[Добавить сборочное задание к поставке](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D~1orders~1%7BorderId%7D/patch)
        | `complete` | **В доставке** <br> Для доставки силами Wildberries `fbs` и курьером WB `wbgo`  | Для `fbs` — [передать поставку в доставку](/openapi/orders-fbs#tag/Postavki/paths/~1api~1v3~1supplies~1%7BsupplyId%7D~1deliver/patch) <br> Для `wbgo` — [перевести сборочное задание в доставку](/openapi/orders-fbs#tag/Dostavka-kurerom-WB-(DBW)/paths/~1api~1v3~1orders~1%7BorderId%7D~1assemble/patch) |
        | `cancel`   | **Отменено продавцом**   | [Отменить сборочное задание](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1%7BorderId%7D~1cancel/patch)
        | `receive`  | **Получено клиентом**<br> Для доставки курьером WB `wbgo`   | Статус меняется автоматически
        | `reject`   | **Отказ клиента при получении**<br>  Для доставки курьером WB `wbgo`  |  Статус меняется автоматически

        <br><br>
        `wbStatus` — статус сборочного задания в системе WB.

        Возможные значения `wbStatus`:
        - `waiting` — сборочное задание в работе
        - `sorted` — сборочное задание отсортировано
        - `sold` — сборочное задание получено покупателем
        - `canceled` — отмена сборочного задания
        - `canceled_by_client` — покупатель отменил заказ при получении
        - `declined_by_client` — покупатель отменил заказ. Отмена доступна покупателю в первый час с момента заказа, если заказ не переведён на сборку
        - `defect` — отмена сборочного задания по причине брака
        - `ready_for_pickup` — сборочное задание прибыло на пункт выдачи заказов (ПВЗ)
        - `postponed_delivery` — курьерская доставка отложена

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - orders
              properties:
                orders:
                  type: array
                  minItems: 1
                  maxItems: 1000
                  description: Список ID сборочных заданий
                  items:
                    type: integer
                    format: int64
                    example: 5632423
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    nullable: false
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          nullable: false
                          description: ID сборочного задания
                          example: 5632423
                        supplierStatus:
                          type: string
                          nullable: false
                          description: Статус сборочного задания продавца (устанавливается продавцом)
                          enum:
                            - new
                            - confirm
                            - complete
                            - cancel
                          example: new
                        wbStatus:
                          type: string
                          nullable: false
                          description: Статус сборочного задания в системе WB
                          enum:
                            - waiting
                            - sorted
                            - sold
                            - canceled
                            - canceled_by_client
                            - declined_by_client
                            - defect
                            - ready_for_pickup
                            - postponed_delivery
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/orders/reshipment:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания
      summary: Получить все сборочные задания для повторной отгрузки
      description: |
        Метод предоставляет все [сборочные задания](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get), требующие повторной отгрузки.
        <br><br>
        Повторная отгрузка требуется, если поставка была отсканирована в пункте приёмки, но при этом в ней всё ещё есть неотсканированные товары. Спустя определённое время необходимо доставить эти товары заново. Данные сборочные задания можно перевести в [другую активную поставку](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D~1orders~1%7BorderId%7D/patch).

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    description: Список заказов
                    type: array
                    items:
                      type: object
                      properties:
                        supplyID:
                          description: ID поставки
                        orderID:
                          description: ID сборочного задания
              example:
                orders:
                  - supplyID: WB-GI-1234567
                    orderID: 5632423
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/{orderId}/cancel:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания
      summary: Отменить сборочное задание
      description: |
        Метод отменяет [сборочное задание](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) и переводит в [статус](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `cancel` — отменено продавцом.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      responses:
        '204':
          description: Отменено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка обновления статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                StatusMismatch:
                  $ref: '#/components/examples/StatusMismatch'
                StatusChangeNotAllowed:
                  $ref: '#/components/examples/StatusChangeNotAllowed'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/stickers:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания
      summary: Получить стикеры сборочных заданий
      description: |
        Метод предоставляет список стикеров для [сборочных заданий](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get).

        Можно получить стикер в форматах:
          - SVG
          - ZPLV (вертикальный)
          - ZPLH (горизонтальный)
          - PNG

        Ограничения:
          - За один запрос можно получить максимум 100 стикеров.
          - Можно получить стикеры только для сборочных заданий, находящихся на сборке — [статус](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `confirm`.

        Доступны размеры:
          - 580x400 px при параметрах `"width": 58`, `"height": 40`
          - 400x300 px при параметрах `"width": 40`, `"height": 30`

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - name: type
          in: query
          required: true
          description: Тип стикера
          schema:
            type: string
            enum:
              - svg
              - zplv
              - zplh
              - png
        - name: width
          in: query
          required: true
          description: Ширина стикера
          schema:
            type: integer
            enum:
              - 58
              - 40
        - name: height
          in: query
          required: true
          description: Высота стикера
          schema:
            type: integer
            enum:
              - 40
              - 30
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                orders:
                  type: array
                  minItems: 1
                  maxItems: 100
                  description: Массив ID сборочных заданий
                  items:
                    type: integer
                    format: int64
                    example: 5346346
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                properties:
                  stickers:
                    type: array
                    nullable: false
                    items:
                      type: object
                      properties:
                        orderId:
                          type: integer
                          format: int64
                          nullable: false
                          description: ID сборочного задания
                          example: 5346346
                        partA:
                          type: string
                          nullable: false
                          description: Первая часть ID стикера (для печати подписи)
                          example: '231648'
                        partB:
                          type: string
                          nullable: false
                          description: Вторая часть ID стикера
                          example: '9753'
                        barcode:
                          type: string
                          nullable: false
                          description: Закодированное значение стикера
                          example: '!uKEtQZVx'
                        file:
                          type: string
                          format: byte
                          nullable: false
                          description: Полное представление стикера в заданном формате. (кодировка base64)
                          example: PD94bWwgdmVyc2lvbj0iMS4wIj8+CjwhLS0gR2VuZXJhdGVkIGJ5IFNWR28gLS0+Cjxzdmcgd2lkdGg9IjQwMCIgaGVpZ2h0PSIzMDAiCiAgICAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogICAgIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjQwMCIgaGVpZQiIGhlaWdodD0iMTcwIiBzdHlsZT0iZmlsbDpibGFjayIgLz4KPHJlY3QgeD0iMzE4IiB5PSIyMCIgd2lkdGg9IjYiIGhlaWdodD0iMTcwIiBzdHlsZT0iZmlsbDpibGFjayIgLz4KPHJlY3QgeD0iMzI2IiB5PSIyMCIgd2lkdGg9IjIiIGhlaWdodD0iMTcwIiBzdHlsZT0iZmlsbDpibGFjayIgLz4KPHJlY3QgeD0iMzMwIiB5PSIyMCIgd2lkdGg9IjQiIGhlaWdodD0iMTcwIiBzdHlsZT0iZmlsbDpibGFjayIgLz4KPHJlY3QgeD0iMjAiIHk9IjIwMCIgd2lkdGg9IjM1MCIgaGVpZ2h0PSI5MCIgc3R5bGU9ImZpbGw6YmxhY2siIC8+Cjx0ZXh0IHg9IjMwIiB5PSIyNDAiIHN0eWxlPSJmaWxsOndoaXRlO2ZvbnQtc2l6ZTozMHB0O3RleHQtYW5jaG9yOnN0YXJ0IiA+MjMxNjQ4PC90ZXh0Pgo8dGV4dCB4PSIzNTAiIHk9IjI3MCIgc3R5bGU9ImZpbGw6d2hpdGU7Zm9udC1zaXplOjUwcHQ7dGV4dC1hbmNob3I6ZW5kIiA+OTc1MzwvdGV4dD4KPC9zdmc+Cg==
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/{orderId}/meta:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные
      summary: Получить метаданные сборочного задания
      description: |
        Метод предоставляет метаданные заказа, доступные для [сборочного задания](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get).
        <br><br>
        Возможные метаданные:
          - `imei` — [IMEI](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta~1imei/put)
          - `uin` — [УИН](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta~1uin/put)
          - `gtin` — [GTIN](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta~1gtin/put)
          - `sgtin` — [код маркировки](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta~1sgtin/put)
          - `expiration` — [срок годности товара](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta~1expiration/put)

        Если в ответе не вернулись какие-либо из объектов метаданных, значит, у сборочного задания не может быть таких метаданных — и добавить их нельзя.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  meta:
                    $ref: '#/components/schemas/Meta'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
    delete:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные
      summary: Удалить метаданные сборочного задания
      description: |
        Метод удаляет значение [метаданных сборочного задания](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta/get) для переданного ключа.
        <br><br>
        Возможные метаданные:
          - `imei` — [IMEI](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta~1imei/put)
          - `uin` — [УИН](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta~1uin/put)
          - `gtin` — [GTIN](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta~1gtin/put)
          - `sgtin` — [код маркировки](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta~1sgtin/put)

        Можно передать только один ключ.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
        - name: key
          in: query
          schema:
            type: string
          description: Название метаданных для удаления (`imei`, `uin`, `gtin`, `sgtin`). Передается только одно значение.
      responses:
        '204':
          description: Удалено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '409':
          description: Ошибка удаления метаданных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/{orderId}/meta/sgtin:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные
      summary: Закрепить за сборочным заданием код маркировки товара
      description: |
        Метод позволяет закрепить за [сборочным заданием](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) код маркировки [Честный знак](https://честныйзнак.рф).
        <br><br>
        Закрепить код маркировки можно только если в [метаданных сборочного задания](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta/get) есть поле `sgtin`, а сборочное задание находится в [статусе](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `confirm`.
        <br><br>
        Получить загруженные маркировки можно в [метаданных сборочного задания](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta/get).

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов <strong>закрепления метаданных</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                sgtins:
                  type: array
                  description: Массив кодов маркировки. Допускается от 16 до 135 символов для кода одной маркировки.
                  minItems: 1
                  maxItems: 24
                  items:
                    type: string
                    description: Код маркировки на упаковке. От 16 до 135 символов.
                    example: '1234567890123456'
      responses:
        '204':
          description: Отправлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка добавления маркировки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/{orderId}/meta/uin:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные
      summary: Закрепить за сборочным заданием УИН
      description: |
        Метод обновляет УИН в [метаданных сборочного задания](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta/get) — уникальный идентификационный номер.
        <br><br>
        У одного сборочного задания может быть только один УИН.

        Добавлять маркировку можно только для заказов, которые доставляются WB и находятся в [статусе](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `confirm`.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов <strong>закрепления метаданных</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                uin:
                  type: string
                  minLength: 16
                  maxLength: 16
                  description: УИН
                  example: '1234567890123456'
              required:
                - uin
      responses:
        '204':
          description: Обновлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '409':
          description: Ошибка обновления метаданных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/{orderId}/meta/imei:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные
      summary: Закрепить за сборочным заданием IMEI
      description: |
        Метод обновляет IMEI в [метаданных сборочного задания](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta/get).
        <br><br>
        У одного сборочного задания может быть только один IMEI. Если у устройства два IMEI — <strong>IMEI и IMEI2</strong> или <strong>IMEI1 и IMEI2</strong> — укажите только <strong>IMEI</strong> или <strong>IMEI1</strong>. <strong>IMEI2</strong> указывать не нужно.
        <br><br>
        Добавлять маркировку можно только для заказов, которые доставляются WB и находятся в [статусе](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `confirm`.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов <strong>закрепления метаданных</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                imei:
                  type: string
                  minLength: 15
                  maxLength: 15
                  description: IMEI
                  example: '123456789012345'
              required:
                - imei
      responses:
        '204':
          description: Обновлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '409':
          description: Ошибка обновления метаданных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/{orderId}/meta/gtin:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные
      summary: Закрепить за сборочным заданием GTIN
      description: |
        Метод обновляет GTIN в [метаданных сборочного задания](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta/get) — уникальный ID товара в Беларуси.
        <br><br>
        У одного сборочного задания может быть только один GTIN.

        Добавлять маркировку можно только для заказов, которые доставляются WB и находятся в [статусе](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `confirm`.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов <strong>закрепления метаданных</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                gtin:
                  type: string
                  minLength: 13
                  maxLength: 13
                  description: GTIN
                  example: '1234567890123'
              required:
                - gtin
      responses:
        '204':
          description: Обновлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '409':
          description: Ошибка обновления метаданных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/{orderId}/meta/expiration:
    servers:
      - url: https://marketplace-api.wildberries.ru
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Метаданные
      summary: Закрепить за сборочным заданием срок годности товара
      description: |
        Метод закрепляет за [сборочным заданием](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) срок годности товара. Товар годен до указанной даты.
        <br>
        Добавить срок годности можно только для заказов, которые доставляются WB и находятся в [статусе](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `confirm`.
        <br>
        <br>
        Получить загруженные данные можно в [метаданных сборочного задания](/openapi/orders-fbs#tag/Metadannye/paths/~1api~1v3~1orders~1%7BorderId%7D~1meta/get).
        Чтобы изменить срок годности, отправьте запрос с новой датой.
        Удалить срок годности из метаданных сборочного задания невозможно.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов <strong>закрепления метаданных</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                expiration:
                  type: string
                  description: Дата, до которой годен товар. Не менее 30 дней с текущей даты
                  format: date (dd.mm.yyyy)
                  example: 12.09.2030
      responses:
        '204':
          description: Отправлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                LowExpirationDate:
                  $ref: '#/components/examples/LowExpirationDate'
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка добавления срока годности
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToUpdateMeta:
                  $ref: '#/components/examples/FailedToUpdateMeta'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/files/orders/external-stickers:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания
      summary: Получить список ссылок на стикеры сборочных заданий, которые требуются при кроссбордере
      description: |
        Метод предоставляет список ссылок на стикеры [сборочных заданий](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get), которые требуются при кроссбордере.

        Ограничения:
          - За один запрос можно получить максимум 100 стикеров.
          - Можно получить стикеры только для сборочных заданий, находящихся в доставке — [статус](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `complete`.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                orders:
                  type: array
                  minItems: 1
                  maxItems: 100
                  description: Массив ID сборочных заданий
                  items:
                    type: integer
                    format: int64
                    example: 5346346
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                properties:
                  stickers:
                    type: array
                    nullable: false
                    items:
                      type: object
                      properties:
                        orderID:
                          type: integer
                          format: int64
                          nullable: false
                          description: ID сборочного задания
                          example: 5346346
                        url:
                          type: string
                          nullable: false
                          description: Ссылка, по которой можно получить стикер для сборочного задания
                          example: https://.../some-sticker
                        parcelID:
                          type: string
                          nullable: false
                          description: Трек-номер в стикере для отслеживания сборочного задания
                          example: WB0000000001
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/status/history:
    post:
      servers:
        - url: https://marketplace-api.wildberries.ru
      security:
        - HeaderApiKey: []
      tags:
        - Сборочные задания
      summary: История статусов для сборочных заданий кроссбордера
      description: |
        Метод предоставляет историю [статусов](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) для [сборочных заданий](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) кроссбордера.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                orders:
                  description: ID сборочных заданий
                  type: array
                  items:
                    type: integer
                  maxItems: 100
                  minItems: 1
                  example:
                    - 123456789
                    - 987654321
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    description: Сборочные задания
                    items:
                      type: object
                      properties:
                        deliveryDate:
                          type: string
                          description: Планируемая дата доставки, [RFC3339](https://datatracker.ietf.org/doc/html/rfc3339)
                        statuses:
                          description: Статусы
                          type: array
                          items:
                            type: object
                            properties:
                              date:
                                type: string
                                description: Дата присвоения статуса
                                example: null
                              code:
                                type: string
                                description: |
                                  Статус-код сборочного задания/заказа:
                                    - `accepted_by_carrier` — Товар принят перевозчиком
                                    - `assembling` — Отправлен на сборку
                                    - `assembled` — Собран на складе
                                    - `sorted` — Отсортирован
                                    - `replaced_at_warehouse` — Замена на складе
                                    - `prepared_for_shipment` — Подготовлен к отгрузке
                                    - `in_search` — На поиске
                                    - `arrived_at_dct` — Поступил в распределительный центр-транзит
                                    - `arrived_at_sct` — Поступил в сортировочный центр-транзит
                                    - `arrived_at_dc` — Поступил в распределительный центр
                                    - `arrived_at_sc` — Поступил в сортировочный центр
                                    - `prepared_for_shipment_at_sc` — Подготовлен к отгрузке в сортировочном центре
                                    - `shipped_at_sc` — Отгружено сортировочным центром
                                    - `shipped_at_dct` — Отгружен распределительным центром — транзит
                                    - `shipped_at_dc` — Отгружен распределительным центром
                                    - `delivered_at_sc` — Доставлен в сортировочный/распределительный центр (СЦ/РЦ)
                                    - `on_way_to_sc` — В пути в сортировочный центр
                                    - `on_way_to_dc` — В пути в распределительный центр
                                    - `on_way_to_pp` — В пути на пункт выдачи
                                    - `arrived_at_pp` — Прибыл на пункт выдачи
                                    - `accepted_at_pp` — Приёмка в пункте выдачи
                                    - `ready_for_pick_up` — Готов к выдаче
                                    - `arrived_at_pp` — Поступил на пункт выдачи заказов (ПВЗ)
                                    - `received_by_client` — Получен клиентом
                                    - `delivered_at_parcel_locker` — Доставлен в постамат
                                    - `canceled_by_client` — Покупатель отменил заказ при получении
                                    - `cancel_after_expiration` — Отмена по сроку хранения
                                    - `at_courier` — Передан курьеру
                                    - `moved_to_return_box` — Перемещен в возвратную коробку
                                    - `accepted_for_return` — Принят к возврату на ПВЗ
                                    - `created` — Оформлен
                                    - `canceled_by_seller` — Отменен продавцом
                                    - `dispatched_by_seller` — Отгружено по данным продавца
                                    - `delivered` — Заказ выдан
                                    - `on_way` — В пути
                                    - `arrived_at_wb_wh` — Поступил на склад WB
                                    - `dispatched_from_wh` — Отправлен со склада
                                    - `customs_clearance` — Таможенное оформление
                                    - `customs_clearance_completed` — Выпущен таможней
                                    - `departed_from_origin_country` — Отправлен из страны продавца
                                    - `arrived_in_destination_country` — Прибытие в страну назначения
                                    - `on_way_to_wb_sc` — Отправлен до сортировочного центра (СЦ) WB
                                    - `accepted_at_wb_sc` — Принят СЦ WB
                                    - `canceled` — Отмена
                                    - `failed_to_reach_client` — Не дозвонились до клиента
                                example: SORTED
                        orderID:
                          description: ID сборочного задания
                          type: integer
                          example: 123456789
        '400':
          description: Неправильный запрос
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/client:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      summary: Заказы с информацией по клиенту
      description: |
        Метод позволяет получать информацию о покупателе по ID сборочного задания.

        Только для кроссбордера из **Турции**.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      tags:
        - Сборочные задания
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrdersRequestAPI'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CrossborderTurkeyClientInfoResp'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Создать новую поставку
      description: |
        Метод создаёт новую [поставку](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get).

        Ограничения:
        - Только для [сборочных заданий](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) по схеме FBS.
        - При добавлении в поставку все передаваемые сборочные задания в [статусе](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `new` будут автоматически переведены в статус `confirm` — на сборке.
        - Если вы переведёте сборочное задание в статус `cancel` — отмена продавцом, прикрепленное сборочное задание автоматически удалится из поставки.
        - Поставку можно собрать только из сборочных заданий (заказов) одного габаритного типа `cargoType`. Новая поставка не обладает габаритным признаком, она приобретает габаритный признак первого заказа, добавленного в поставку.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  minLength: 1
                  maxLength: 128
                  description: Наименование поставки
                  example: Тестовая поставка
      responses:
        '201':
          description: Создано
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    nullable: false
                    description: ID поставки
                    example: WB-GI-1234567
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Получить список поставок
      description: |
        Метод предоставляет список [поставок](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get).

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Next'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  next:
                    $ref: '#/components/schemas/Next'
                  supplies:
                    type: array
                    nullable: false
                    description: Список поставок
                    items:
                      $ref: '#/components/schemas/Supply'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/{supplyId}/orders/{orderId}:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Добавить сборочное задание к поставке
      description: |
        Метод добавляет [сборочное задание](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) к поставке и переводит его в [статус](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `confirm` — на сборке.

        Может перемещать сборочное задание:
          - между активными поставками.
          - из закрытой поставки в активную, если сборочное задание требует [повторной отгрузки](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1supplies~1orders~1reshipment/get).

        <div class="description_important">
          В пустую поставку можно добавить сборочное задание любого габаритного типа. Поставка приобретает габаритный тип первого добавленного сборочного задания <a href ="./orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get">из поля</a> <code>cargoType</code>.<br>После этого в поставку можно добавить сборочные задания только того же габаритного типа, что и у поставки.
        </div>

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 1000 запросов | 60 миллисекунд | 20 запросов |
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
        - $ref: '#/components/parameters/Order'
      responses:
        '204':
          description: Задание закреплено за поставкой
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка добавления сборочного задания к поставке
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                FailedToAddSupplyOrder:
                  $ref: '#/components/examples/FailedToAddSupplyOrder'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/{supplyId}:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Получить информацию о поставке
      description: |
        Метод предоставляет подробную информацию о поставке.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supply'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
    delete:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Удалить поставку
      description: |
        Метод удаляет [поставку](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get), если она активна и за ней не закреплено ни одно [сборочное задание](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get).

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
      responses:
        '204':
          description: Удалено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: За поставкой закреплены сборочные задания
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                SupplyHasOrders:
                  $ref: '#/components/examples/SupplyHasOrders'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/{supplyId}/orders:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Получить сборочные задания в поставке
      description: |
        Метод предоставляет [сборочные задания](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get), закреплённые за [поставкой](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get).

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  orders:
                    type: array
                    nullable: false
                    items:
                      $ref: '#/components/schemas/SupplyOrder'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/{supplyId}/deliver:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Передать поставку в доставку
      description: |
        Метод закрывает [поставку](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get) и переводит все [сборочные задания](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) в ней в [статус](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `complete` — в доставке. После закрытия поставки добавить новые сборочные задания к ней нельзя.
        <br><br>
        Если поставка не была передана в доставку, то при сканировании её QR-кода или приёмке первого товара поставка автоматически закроется.
        <br><br>
        Передать поставку в доставку можно только если в ней:
          - есть хотя бы одно сборочное задания
          - отсутствуют пустые короба

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
      responses:
        '204':
          description: Передано в доставку
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка закрытия поставки
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                SupplyHasZeroOrders:
                  $ref: '#/components/examples/SupplyHasZeroOrders'
                UinIsNotFilled:
                  $ref: '#/components/examples/UinIsNotFilled'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/{supplyId}/barcode:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Получить QR-код поставки
      description: |
        Метод предоставляет QR-код [поставки](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get) в форматах:
          - SVG
          - ZPLV (вертикальный)
          - ZPLH (горизонтальный)
          - PNG

        QR-код поставки можно получить только если поставка [передана в доставку](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D~1deliver/patch).
        <br><br>
        Размер — 580x400 px.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
        - name: type
          in: query
          required: true
          description: Тип стикера
          schema:
            type: string
            enum:
              - svg
              - zplv
              - zplh
              - png
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  barcode:
                    type: string
                    nullable: false
                    description: Закодированное значение стикера (ID поставки)
                    example: WB-***********
                  file:
                    type: string
                    format: byte
                    nullable: false
                    description: Полное представление стикера в заданном формате. (кодировка base64)
                    example: U3dhZ2dlciByb2Nrcw==
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка запроса данных
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                SupplyNotClosed:
                  $ref: '#/components/examples/SupplyNotClosed'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/{supplyId}/trbx:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Получить список коробов поставки
      description: |
        Метод предоставляет список коробов и ID [заказов](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get), входящих в эти короба.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                properties:
                  trbxes:
                    type: array
                    items:
                      $ref: '#/components/schemas/SupplyTrbx'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Добавить короба к поставке
      description: |
        Метод добавляет требуемое количество [коробов](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D~1trbx/get) в [поставку](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get).
        <br>
        <br>
        Короба необходимо добавлять только в поставки, отгружаемые на ПВЗ.
        <br>
        Можно добавить только пока поставка на сборке.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - amount
              properties:
                amount:
                  type: integer
                  minimum: 1
                  maximum: 1000
                  description: Количество коробов, которые необходимо добавить к поставке.
                  example: 4
      responses:
        '201':
          description: Создано
          content:
            application/json:
              schema:
                type: object
                properties:
                  trbxIds:
                    type: array
                    description: Список ID коробов, которые были созданы.
                    minItems: 1
                    items:
                      type: string
                      example: WB-TRBX-1234567
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
    delete:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Удалить короба из поставки
      description: |
        Метод убирает заказы из перечисленных [коробов](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D~1trbx/get) [поставки](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get) и удаляет короба.
        <br><br>
        Можно удалить только пока поставка на сборке.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - trbxIds
              properties:
                trbxIds:
                  type: array
                  description: Список ID коробов, которые необходимо удалить.
                  items:
                    type: string
                    example: WB-TRBX-1234567
      responses:
        '204':
          description: Удалено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/{supplyId}/trbx/{trbxId}:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Добавить заказы в короб
      description: |
        Метод добавляет заказы в [короб](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D~1trbx/get) для выбранной [поставки](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get).
        <br><br>
        Можно добавить только пока поставка на сборке.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
        - $ref: '#/components/parameters/Trbx'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - orderIds
              properties:
                orderIds:
                  type: array
                  description: Список заказов, которые необходимо добавить в короб.
                  items:
                    type: integer
                    example: 1234567
      responses:
        '204':
          description: Добавлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/{supplyId}/trbx/{trbxId}/orders/{orderId}:
    servers:
      - url: https://marketplace-api.wildberries.ru
    delete:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Удалить заказ из короба
      description: |
        Метод удаляет заказ из [короба](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D~1trbx/get) выбранной [поставки](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D/get).
        <br><br>
        Можно удалить только пока поставка на сборке.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
        - $ref: '#/components/parameters/Trbx'
        - $ref: '#/components/parameters/Order'
      responses:
        '204':
          description: Удалено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/supplies/{supplyId}/trbx/stickers:
    servers:
      - url: https://marketplace-api.wildberries.ru
    post:
      security:
        - HeaderApiKey: []
      tags:
        - Поставки FBS
      summary: Получить стикеры коробов поставки
      description: |
        Метод предоставляет QR-стикеры в форматах:
          - SVG
          - ZPLV (вертикальный)
          - ZPLH (горизонтальный)
          - PNG

        Можно получить только если в [коробе](/openapi/orders-fbs#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1%7BsupplyId%7D~1trbx/get) есть заказы.
        <br><br>
        Размер стикеров — 580x400 px.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Supply'
        - name: type
          in: query
          required: true
          description: Тип стикера
          schema:
            type: string
            enum:
              - svg
              - zplv
              - zplh
              - png
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - trbxIds
              properties:
                trbxIds:
                  type: array
                  description: Список ID коробов, по которым необходимо вернуть стикеры.
                  items:
                    type: string
                    example: WB-TRBX-1234567
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: object
                nullable: false
                properties:
                  stickers:
                    type: array
                    minItems: 1
                    items:
                      $ref: '#/components/schemas/TrbxStickers'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/{orderId}/confirm:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Доставка курьером WB (DBW)
      summary: Перевести на сборку
      description: |
        Метод переводит [сборочное задание](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) в [статус](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `confirm` — на сборке.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/OrderID'
      responses:
        '204':
          description: Подтверждено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка обновления статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                StatusMismatch:
                  $ref: '#/components/examples/StatusMismatch'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/orders/{orderId}/assemble:
    servers:
      - url: https://marketplace-api.wildberries.ru
    patch:
      security:
        - HeaderApiKey: []
      tags:
        - Доставка курьером WB (DBW)
      summary: Перевести в доставку
      description: |
        Метод переводит [сборочное задание](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders/get) в [статус](/openapi/orders-fbs#tag/Sborochnye-zadaniya/paths/~1api~1v3~1orders~1status/post) `complete` — в доставке.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Order'
      responses:
        '204':
          description: Подтверждено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Ошибка обновления статуса
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                StatusMismatch:
                  $ref: '#/components/examples/StatusMismatch'
        '429':
          $ref: '#/components/responses/429'
  /api/v3/warehouses/{warehouseId}/contacts:
    servers:
      - url: https://marketplace-api.wildberries.ru
    get:
      security:
        - HeaderApiKey: []
      tags:
        - Доставка курьером WB (DBW)
      summary: Список контактов
      description: |
        Метод предоставляет список контактов, привязанных к [складу продавца](/openapi/work-with-products#tag/Sklady-prodavca/paths/~1api~1v3~1warehouses/get).

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      parameters:
        - $ref: '#/components/parameters/Warehouse'
      responses:
        '200':
          description: Успешно
          content:
            application/json:
              schema:
                type: array
                nullable: false
                description: Список контактов склада продавца
                items:
                  $ref: '#/components/schemas/StoreContactResponseBody'
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
    put:
      security:
        - HeaderApiKey: []
      tags:
        - Доставка курьером WB (DBW)
      summary: Обновить список контактов
      parameters:
        - $ref: '#/components/parameters/Warehouse'
      description: |
        Метод обновляет список контактов [склада продавца](/openapi/work-with-products#tag/Sklady-prodavca/paths/~1api~1v3~1warehouses/get).

        <div class="description_important">
          Список контактов перезаписывается при обновлении. Поэтому в запросе нужно передать <strong>все</strong> параметры списка контактов, в том числе те, которые вы не собираетесь обновлять.
        </div>

        Только для складов с типом доставки `3` — курьером WB.
        <br><br>
        К складу можно добавить максимум 5 контактов. Чтобы удалить контакты, отправьте пустой массив `contacts`.

        <div class="description_limit">
        <a href="/openapi/api-information#tag/Vvedenie/Limity-zaprosov">Лимит запросов</a> на один аккаунт продавца для всех методов категории <strong>Маркетплейс</strong>:

        | Период | Лимит | Интервал | Всплеск |
        | --- | --- | --- | --- |
        | 1 минута | 300 запросов | 200 миллисекунд | 20 запросов |

        Один запрос с кодом ответа <code>409</code> учитывается как 5 запросов
        </div>
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StoreContactRequestBody'
      responses:
        '204':
          description: Обновлено
        '400':
          description: Неправильный запрос
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                IncorrectParameter:
                  $ref: '#/components/examples/IncorrectParameter'
                IncorrectRequestBody:
                  $ref: '#/components/examples/IncorrectRequestBody'
                UploadDataLimit:
                  $ref: '#/components/examples/UploadDataLimit'
                IncorrectRequest:
                  $ref: '#/components/examples/IncorrectRequest'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/AccessDenied'
        '429':
          $ref: '#/components/responses/429'
components:
  securitySchemes:
    HeaderApiKey:
      type: apiKey
      name: Authorization
      in: header
  parameters:
    OrderID:
      name: orderId
      in: path
      required: true
      description: ID сборочного задания
      schema:
        type: integer
        format: int64
        example: 5632423
    Warehouse:
      name: warehouseId
      in: path
      required: true
      description: ID склада продавца
      schema:
        type: integer
        format: int64
        example: 1
    Supply:
      name: supplyId
      in: path
      required: true
      description: ID поставки
      schema:
        type: string
        example: WB-GI-1234567
    Trbx:
      name: trbxId
      in: path
      required: true
      description: ID короба
      schema:
        type: string
        example: WB-TRBX-1234567
    Order:
      name: orderId
      in: path
      required: true
      description: ID сборочного задания
      schema:
        type: integer
        format: int64
        example: 5632423
    Pass:
      name: passId
      in: path
      required: true
      description: ID пропуска
      schema:
        type: integer
        format: int64
        example: 45
    Next:
      name: next
      in: query
      required: true
      schema:
        type: integer
        format: int64
      description: Параметр пагинации. Устанавливает значение, с которого надо получить следующий пакет данных. Для получения полного списка данных должен быть равен 0 в первом запросе. Для следующих запросов необходимо брать значения из одноимённого поля в ответе.
    Limit:
      name: limit
      in: query
      required: true
      schema:
        type: integer
        minimum: 1
        maximum: 1000
      description: Параметр пагинации. Устанавливает предельное количество возвращаемых данных.
  examples:
    LowExpirationDate:
      description: Указан срок меньше допустимого
      value:
        code: LowExpirationDate
        message: Не удалось обновить срок годности. Указан срок меньше допустимого
    UploadDataLimit:
      value:
        code: UploadDataLimit
        message: Превышен лимит загружаемых данных
    IncorrectRequestBody:
      value:
        code: IncorrectRequestBody
        message: Некорректное тело запроса
    WarehouseNameInvalid:
      value:
        code: WarehouseNameInvalid
        message: Некорректное имя склада
    IncorrectRequest:
      value:
        code: IncorrectRequest
        message: Переданы некорректные данные
    IncorrectParameter:
      value:
        code: IncorrectParameter
        message: Передан некорректный параметр
    FailedToUpdateMeta:
      value:
        code: FailedToUpdateMeta
        message: Failed to update order metadata. Make sure the order meets all the necessary requirements.
    SupplyHasOrders:
      value:
        code: SupplyHasOrders
        message: Не удалось обработать поставку. Убедитесь, что за ней не осталось закреплённых сборочных заданий.
    SupplyHasZeroOrders:
      value:
        code: SupplyHasZeroOrders
        message: Не удалось обработать поставку. Убедитесь, что за ней закреплён хотя бы одно сборочное задание.
    UinIsNotFilled:
      value:
        code: UinIsNotFilled
        message: Перевод в статус "в доставке" невозможен. Введите УИНы для всех сборочных заданий, где это необходимо.
    SupplyNotClosed:
      value:
        code: SupplyNotClosed
        message: Поставка не передана в доставку
    FailedToAddSupplyOrder:
      value:
        code: FailedToAddSupplyOrder
        message: Не удалось закрепить сборочное задание за поставкой. Убедитесь, что сборочное задание и поставка удовлетворяют всем необходимым требованиям.
    StatusMismatch:
      value:
        code: StatusMismatch
        message: Несоответствие статусов, проверьте их правильность
    StatusChangeNotAllowed:
      value:
        code: StatusChangeNotAllowed
        message: Задание перейдёт в завершенные после сканирования на пунте выдачи заказов (ПВЗ)
  responses:
    '401':
      description: Пользователь не авторизован
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Заголовок ошибки
              detail:
                type: string
                description: Детали ошибки
              code:
                type: string
                description: Внутренний код ошибки
              requestId:
                type: string
                description: Уникальный ID запроса
              origin:
                type: string
                description: ID внутреннего сервиса WB
              status:
                type: number
                description: HTTP статус-код
              statusText:
                type: string
                description: Расшифровка HTTP статус-кода
              timestamp:
                type: string
                format: date-time
                description: Дата и время запроса
          example:
            title: unauthorized
            detail: 'token problem; token is malformed: could not base64 decode signature: illegal base64 data at input byte 84'
            code: 07e4668e--a53a3d31f8b0-[UK-oWaVDUqNrKG]; 03bce=277; 84bd353bf-75
            requestId: 7b80742415072fe8b6b7f7761f1d1211
            origin: s2s-api-auth-catalog
            status: 401
            statusText: Unauthorized
            timestamp: '2024-09-30T06:52:38Z'
    '429':
      description: Слишком много запросов
      content:
        application/json:
          schema:
            type: object
            properties:
              title:
                type: string
                description: Заголовок ошибки
              detail:
                type: string
                description: Детали ошибки
              code:
                type: string
                description: Внутренний код ошибки
              requestId:
                type: string
                description: Уникальный ID запроса
              origin:
                type: string
                description: ID внутреннего сервиса WB
              status:
                type: number
                description: HTTP статус-код
              statusText:
                type: string
                description: Расшифровка HTTP статус-кода
              timestamp:
                type: string
                format: date-time
                description: Дата и время запроса
          example:
            title: too many requests
            detail: limited by c122a060-a7fb-4bb4-abb0-32fd4e18d489
            code: 07e4668e-ac2242c5c8c5-[UK-4dx7JUdskGZ]
            requestId: 9d3c02cc698f8b041c661a7c28bed293
            origin: s2s-api-auth-catalog
            status: 429
            statusText: Too Many Requests
            timestamp: '2024-09-30T06:52:38Z'
    NotFound:
      description: Не найдено
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: NotFound
            message: Не найдено
    AccessDenied:
      description: Доступ запрещён
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: AccessDenied
            message: Доступ запрещён
  schemas:
    PassOffice:
      type: object
      description: Данные о складе, для которого требуется пропуск
      properties:
        name:
          type: string
          nullable: false
          description: Название
          example: Коледино
        address:
          type: string
          nullable: false
          description: Адрес
          example: г. Подольск, д. Коледино, ул. Троицкая
        id:
          type: integer
          format: int64
          nullable: false
          description: ID
          example: 1
    Error:
      type: object
      nullable: false
      properties:
        code:
          type: string
          description: Код ошибки
          nullable: false
        message:
          type: string
          description: Описание ошибки
          nullable: false
        data:
          type: object
          description: Дополнительные данные, обогащающие ошибку
          nullable: true
    Next:
      type: integer
      format: int64
      nullable: false
      description: Параметр пагинации. Содержит значение, которое необходимо указать в запросе для получения следующего пакета данных
      example: 13833711
    Order:
      type: object
      properties:
        address:
          type: object
          nullable: true
          description: Детализованный адрес покупателя для доставки (если применимо). Некоторые из полей могут прийти пустыми из-за специфики адреса
          properties:
            fullAddress:
              description: Адрес доставки.
              type: string
              example: Челябинская область, г. Челябинск, 51-я улица Арабкира, д. 10А, кв. 42
            longitude:
              type: number
              format: float64
              nullable: false
              description: Координата долготы
              example: 44.519068
            latitude:
              type: number
              format: float64
              nullable: false
              description: Координаты широты
              example: 40.20192
        scanPrice:
          type: number
          format: uint32
          description: Цена приёмки в копейках. Появляется после фактической приёмки заказа
          example: 1500
        deliveryType:
          type: string
          nullable: false
          description: |
            <dl>
            <dt>Тип доставки:</dt>
            <dd>fbs - доставка на склад Wildberries (FBS)</dd>
            <dd>wbgo - доставка курьером WB (DBW)</dd>
            </dl>
          enum:
            - fbs
            - wbgo
        supplyId:
          type: string
          description: ID поставки. Возвращается, если заказ закреплён за поставкой
          example: WB-***********
        orderUid:
          type: string
          description: ID транзакции для группировки сборочных заданий. Сборочные задания в одной корзине покупателя будут иметь одинаковый `orderUid`
          example: 165918930_629fbc924b984618a44354475ca58675
        article:
          type: string
          nullable: false
          description: Артикул продавца
          example: one-ring-7548
        colorCode:
          type: string
          nullable: false
          description: Код цвета (только для колеруемых товаров)
          example: RAL 3017
        rid:
          type: string
          nullable: false
          description: ID сборочного задания в системе Wildberries
          example: f884001e44e511edb8780242ac120002
        createdAt:
          type: string
          format: date-time
          nullable: false
          description: Дата создания сборочного задания (RFC3339)
          example: '2022-05-04T07:56:29Z'
        offices:
          type: array
          nullable: true
          description: Список офисов, куда следует привезти товар
          items:
            type: string
            example: Калуга
        skus:
          type: array
          nullable: false
          description: Массив баркодов товара
          items:
            type: string
            example: '6665956397512'
        id:
          type: integer
          format: int64
          nullable: false
          description: ID сборочного задания в Маркетплейсе
          example: 13833711
        warehouseId:
          type: integer
          nullable: false
          description: ID склада продавца, на который поступило сборочное задание
          example: 658434
        officeId:
          type: integer
          format: int64
          nullable: false
          description: ID склада WB, к которому привязан склад продавца
          example: 123
        nmId:
          type: integer
          nullable: false
          description: Артикул WB
          example: 12345678
        chrtId:
          type: integer
          nullable: false
          description: ID размера товара в системе Wildberries
          example: 987654321
        price:
          type: integer
          nullable: false
          description: |
            Цена в валюте продажи с учётом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Код валюты продажи — в поле `currencyCode`. Предоставляется в информационных целях
          example: 1014
        convertedPrice:
          type: integer
          nullable: false
          description: Цена в валюте страны продавца с учетом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Предоставляется в информационных целях
          example: 28322
        currencyCode:
          type: integer
          nullable: false
          description: Код валюты продажи
          format: ISO 4217
          example: 933
        convertedCurrencyCode:
          type: integer
          nullable: false
          description: Код валюты страны продавца
          format: ISO 4217
          example: 643
        cargoType:
          type: integer
          nullable: false
          description: |
            <dl> <dt>Тип товара:</dt> <dd>1 - МГТ (малогабаритный, то есть обычный товар)</dd> <dd>2 - СГТ (Сверхгабаритный товар)</dd> <dd>3 - КГТ+ (Крупногабаритный товар)</dd> </dl>
          enum:
            - 1
            - 2
            - 3
        comment:
          description: Комментарий покупателя
          type: string
          nullable: false
          maxLength: 300
          example: Упакуйте в плёнку, пожалуйста
        isZeroOrder:
          description: Признак заказа сделанного на нулевой остаток товара. (<code>false</code> - заказ сделан на товар с ненулевым остатком, <code>true</code> - заказ сделан на товар с остатком равным нулю. Такой заказ можно отменить без штрафа за отмену)
          example: false
        options:
          type: object
          description: Опции заказа
          properties:
            isB2b:
              type: boolean
              description: |
                Признак B2B-продажи:
                  - <code>false</code> — не B2B-продажа
                  - <code>true</code> — B2B-продажа
    Supply:
      type: object
      properties:
        id:
          type: string
          nullable: false
          description: ID поставки
          example: WB-GI-1234567
        done:
          type: boolean
          nullable: false
          description: Флаг закрытия поставки
        createdAt:
          type: string
          format: date-time
          nullable: false
          description: Дата создания поставки (RFC3339)
          example: '2022-05-04T07:56:29Z'
        closedAt:
          type: string
          format: date-time
          nullable: true
          description: Дата закрытия поставки (RFC3339)
          example: '2022-05-04T07:56:29Z'
        scanDt:
          type: string
          nullable: true
          format: date-time
          description: Дата скана поставки (RFC3339)
          example: '2022-05-04T07:56:29Z'
        name:
          type: string
          nullable: false
          description: Наименование поставки
          example: Тестовая поставка
        cargoType:
          type: integer
          nullable: false
          description: |
            <dl> <dt>Тип поставки:</dt> <dd>0 - признак отсутствует</dd> <dd>1 - МГТ (Содержит малогабаритные товары)</dd> <dd>2 - СГТ (Содержит сверхгабаритные товары)</dd> <dd>3 - КГТ+ (Содержит крупногабаритные товары)</dd> </dl>
          enum:
            - 0
            - 1
            - 2
            - 3
        destinationOfficeId:
          type: integer
          format: int64
          nullable: true
          description: ID склада назначения поставки. Если `null`, склад назначения не указан
          example: 123
    OrderNew:
      type: object
      properties:
        address:
          type: object
          nullable: true
          description: Детализованный адрес покупателя для доставки (если применимо). Некоторые из полей могут прийти пустыми из-за специфики адреса
          properties:
            fullAddress:
              description: Адрес доставки.
              type: string
              example: Челябинская область, г. Челябинск, 51-я улица Арабкира, д. 10А, кв. 42
            longitude:
              type: number
              format: float64
              nullable: false
              description: Координата долготы
              example: 44.519068
            latitude:
              type: number
              format: float64
              nullable: false
              description: Координаты широты
              example: 40.20192
        ddate:
          type: string
          description: |
            Планируемая дата доставки.<br>
            Поле отображается для схем:
              - `wbgo` — доставка курьером WB (DBW)
              - `СГТ` — заказы сверхгабаритных товаров (<code>cargoType: 2</code>) для схемы FBS — доставка на склад Wildberries.
          nullable: false
          example: 17.05.2024
        salePrice:
          description: |
            Цена продавца в валюте продажи с учётом скидки продавца, без учёта скидки WB Клуба, умноженная на 100. Предоставляется в информационных целях
          type: integer
          nullable: true
          example: 504600
        requiredMeta:
          description: |
            Перечень метаданных, которые необходимо добавить в сборочное задание.  <br> На данный момент обязательными к добавлению являются UIN и IMEI при их наличии в перечне
          type: array
          nullable: true
          items:
            type: string
          example:
            - uin
        deliveryType:
          type: string
          nullable: false
          description: |
            <dl>
            <dt>Тип доставки:</dt>
            <dd>fbs - доставка на склад Wildberries (FBS)</dd>
            <dd>wbgo - доставка курьером WB (DBW)</dd>
            </dl>
          enum:
            - fbs
            - wbgo
        comment:
          description: Комментарий покупателя
          type: string
          nullable: false
          maxLength: 300
          example: Упакуйте в плёнку, пожалуйста
        scanPrice:
          description: Цена приёмки в копейках. Появляется после фактической приёмки заказа. Для данного метода всегда будет возвращаться null. Предоставляется в информационных целях
          type: number
          format: uint32
          nullable: true
          example: null
        orderUid:
          type: string
          description: ID транзакции для группировки сборочных заданий. Сборочные задания в одной корзине покупателя будут иметь одинаковый `orderUid`
          example: 165918930_629fbc924b984618a44354475ca58675
        article:
          type: string
          nullable: false
          description: Артикул продавца
          example: one-ring-7548
        colorCode:
          description: Код цвета (только для колеруемых товаров)
          type: string
          nullable: false
          example: RAL 3017
        rid:
          type: string
          nullable: false
          description: ID сборочного задания в системе Wildberries
          example: f884001e44e511edb8780242ac120002
        createdAt:
          type: string
          format: date-time
          nullable: false
          description: Дата создания сборочного задания (RFC3339)
          example: '2022-05-04T07:56:29Z'
        offices:
          type: array
          nullable: true
          description: Список офисов, куда следует привезти товар
          items:
            type: string
            example: Калуга
        skus:
          type: array
          nullable: false
          description: Массив баркодов товара
          items:
            type: string
            example: '6665956397512'
        id:
          type: integer
          format: int64
          nullable: false
          description: ID сборочного задания в Маркетплейсе
          example: 13833711
        warehouseId:
          type: integer
          nullable: false
          description: ID склада продавца, на который поступило сборочное задание
          example: 658434
        officeId:
          type: integer
          format: int64
          nullable: false
          description: ID склада WB, к которому привязан склад продавца
          example: 123
        nmId:
          type: integer
          nullable: false
          description: Артикул WB
          example: 123456789
        chrtId:
          type: integer
          nullable: false
          description: ID размера товара в системе Wildberries
          example: 987654321
        price:
          type: integer
          nullable: false
          description: |
            Цена в валюте продажи с учётом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Код валюты продажи — в поле `currencyCode`. Предоставляется в информационных целях
          example: 1014
        convertedPrice:
          type: integer
          nullable: false
          description: Цена в валюте страны продавца с учетом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Предоставляется в информационных целях
          example: 28322
        currencyCode:
          type: integer
          nullable: false
          description: Код валюты продажи
          format: ISO 4217
          example: 933
        convertedCurrencyCode:
          type: integer
          nullable: false
          description: Код валюты страны продавца
          format: ISO 4217
          example: 643
        cargoType:
          type: integer
          nullable: false
          description: |
            <dl> <dt>Тип товара:</dt> <dd>1 - МГТ (малогабаритный, то есть обычный товар)</dd> <dd>2 - СГТ (Сверхгабаритный товар)</dd> <dd>3 - КГТ+ (Крупногабаритный товар)</dd> </dl>
          enum:
            - 1
            - 2
            - 3
        isZeroOrder:
          description: Признак заказа сделанного на нулевой остаток товара. (<code>false</code> - заказ сделан на товар с ненулевым остатком, <code>true</code> - заказ сделан на товар с остатком равным нулю. Такой заказ можно отменить без штрафа за отмену)
          example: false
        options:
          type: object
          description: Опции заказа
          properties:
            isB2b:
              type: boolean
              description: |
                Признак B2B-продажи:
                  - <code>false</code> — не B2B-продажа
                  - <code>true</code> — B2B-продажа
    SupplyOrder:
      type: object
      properties:
        scanPrice:
          description: Цена приёмки в копейках. Появляется после фактической приёмки заказа. Для данного метода всегда будет возвращаться null. Предоставляется в информационных целях
          type: number
          format: uint32
          nullable: true
          example: null
        orderUid:
          type: string
          description: ID транзакции для группировки сборочных заданий. Сборочные задания в одной корзине покупателя будут иметь одинаковый `orderUid`
          example: 165918930_629fbc924b984618a44354475ca58675
        article:
          type: string
          nullable: false
          description: Артикул продавца
          example: one-ring-7548
        colorCode:
          description: Код цвета (только для колеруемых товаров)
          type: string
          nullable: false
          example: RAL 3017
        rid:
          type: string
          nullable: false
          description: ID сборочного задания в системе Wildberries
          example: f884001e44e511edb8780242ac120002
        createdAt:
          type: string
          format: date-time
          nullable: false
          description: Дата создания сборочного задания (RFC3339)
          example: '2022-05-04T07:56:29Z'
        offices:
          type: array
          nullable: true
          description: Список офисов, куда следует привезти товар
          items:
            type: string
            example: Калуга
        skus:
          type: array
          nullable: false
          description: Массив баркодов товара
          items:
            type: string
            example: '6665956397512'
        id:
          type: integer
          format: int64
          nullable: false
          description: ID сборочного задания в Маркетплейсе
          example: 13833711
        warehouseId:
          type: integer
          nullable: false
          description: ID склада продавца, на который поступило сборочное задание
          example: 658434
        nmId:
          type: integer
          nullable: false
          description: Артикул WB
          example: 123456789
        chrtId:
          type: integer
          nullable: false
          description: ID размера товара в системе Wildberries
          example: 987654321
        price:
          type: integer
          nullable: false
          description: |
            Цена в валюте продажи с учётом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Код валюты продажи — в поле `currencyCode`. Предоставляется в информационных целях
          example: 1014
        convertedPrice:
          type: integer
          nullable: false
          description: Цена в валюте страны продавца с учетом всех скидок, кроме скидки по WB Кошельку, умноженная на 100. Предоставляется в информационных целях
          example: 28322
        currencyCode:
          type: integer
          nullable: false
          description: Код валюты продажи
          format: ISO 4217
          example: 933
        convertedCurrencyCode:
          type: integer
          nullable: false
          description: Код валюты страны продавца
          format: ISO 4217
          example: 643
        cargoType:
          type: integer
          nullable: false
          description: |
            <dl> <dt>Тип товара:</dt> <dd>1 - МГТ (малогабаритный, то есть обычный товар)</dd> <dd>2 - СГТ (Сверхгабаритный товар)</dd> <dd>3 - КГТ+ (Крупногабаритный товар)</dd> </dl>
          enum:
            - 1
            - 2
            - 3
        isZeroOrder:
          description: Признак заказа сделанного на нулевой остаток товара. (<code>false</code> - заказ сделан на товар с ненулевым остатком, <code>true</code> - заказ сделан на товар с остатком равным нулю. Такой заказ можно отменить без штрафа за отмену)
          example: false
    SupplyTrbx:
      type: object
      properties:
        id:
          description: ID короба.
          type: string
          example: WB-TRBX-1234567
        orders:
          type: array
          nullable: false
          description: Массив ID сборочных заданий.
          items:
            type: integer
            example: 1234567
    TrbxStickers:
      type: object
      properties:
        barcode:
          type: string
          minLength: 1
          description: Закодированное значение стикера.
          example: $WBMP:1:123:1234567
        file:
          type: string
          format: byte
          minLength: 1
          description: Полное представление стикера в заданном формате. (кодировка base64)
          example: U3dhZ2dlciByb2Nrcw==
    Meta:
      type: object
      description: Метаданные заказа
      properties:
        imei:
          type: object
          properties:
            value:
              nullable: true
              type: string
              example: '123456789012345'
          description: IMEI
        uin:
          type: object
          properties:
            value:
              nullable: true
              type: string
              example: '123456789012345'
          description: УИН
        gtin:
          type: object
          properties:
            value:
              nullable: true
              type: string
              example: '123456789012345'
          description: GTIN
        sgtin:
          type: object
          properties:
            value:
              type: array
              items:
                type: string
              example:
                - '123456789012345'
              nullable: true
          description: Код маркировки Честного знака
        expiration:
          type: object
          properties:
            value:
              type: string
              example: 12.09.2030
              nullable: true
          description: Срок годности товара
          format: date (dd.mm.yyyy)
    Pass:
      type: object
      description: Данные о пропуске продавца
      properties:
        firstName:
          type: string
          nullable: false
          description: Имя водителя
          example: Александр
        dateEnd:
          type: string
          nullable: false
          description: Дата окончания действия пропуска
          example: '2022-07-31 17:53:13+00:00'
        lastName:
          type: string
          nullable: false
          description: Фамилия водителя
          example: Петров
        carModel:
          type: string
          nullable: false
          description: Марка машины
          example: Lamborghini
        carNumber:
          type: string
          nullable: false
          description: Номер машины
          example: A456BC123
        officeName:
          type: string
          nullable: false
          description: Название склада
          example: Коледино
        officeAddress:
          type: string
          nullable: false
          description: Адрес склада
          example: г. Подольск, д. Коледино, ул. Троицкая
        officeId:
          type: integer
          format: int64
          nullable: false
          description: ID склада
          example: 15
        id:
          type: integer
          format: int64
          nullable: false
          description: ID пропуска
          example: 1
    StoreContactResponseBody:
      type: object
      description: Контакты склада продавца
      properties:
        contacts:
          type: array
          items:
            type: object
            properties:
              comment:
                type: string
                nullable: false
                description: Комментарий
                example: Иванов Иван Иванович. Звонить с 10 до 21 часа.
                maxLength: 1000
              phone:
                type: string
                nullable: false
                description: Номер телефона
                example: '+79998887766'
    StoreContactRequestBody:
      type: object
      description: Контакты склада продавца
      properties:
        contacts:
          type: array
          maxItems: 5
          items:
            type: object
            properties:
              comment:
                type: string
                nullable: false
                description: Комментарий
                example: Иванов Иван Иванович. Звонить с 10 до 21 часа.
                maxLength: 1000
              phone:
                type: string
                nullable: false
                description: |
                  Номер телефона.<br>Поддерживаются коды стран:
                    - `+7` — Россия, Казахстан
                    - `+374` — Армения
                    - `+375` — Белорусь
                    - `+996` — Кыргызстан
                example: '+79998887766'
    CrossborderTurkeyClientInfo:
      properties:
        firstName:
          type: string
          description: Имя клиента
          example: Иван
        fullName:
          type: string
          description: Фамилия, Имя, Отчество
          example: Андреев Иван Васильевич
        lastName:
          type: string
          description: Фамилия клиента
          example: Андреев
        middleName:
          type: string
          description: Отчество клиента
          example: Васильевич
        orderID:
          type: integer
          description: Номер заказа
          example: 134567
        phone:
          type: string
          description: Телефон для связи с клиентом
          example: 79871234567
        phoneCode:
          type: string
          description: Не используется
          example: 0
    CrossborderTurkeyClientInfoResp:
      properties:
        orders:
          description: Информация по клиенту для кроссбордер-заказа из Турции
          items:
            $ref: '#/components/schemas/CrossborderTurkeyClientInfo'
          type: array
      type: object
    OrdersRequestAPI:
      properties:
        orders:
          description: Список заказов
          items:
            type: integer
          type: array
      type: object
      example:
        orders:
          - 987654321
          - 123456789