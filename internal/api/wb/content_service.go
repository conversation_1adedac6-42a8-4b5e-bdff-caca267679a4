package wbapi

import (
	"context"
	"fmt"

	"github.com/go-resty/resty/v2"
)

// 使用 constants.go 中定义的 contentAPIURL

// ContentService 内容服务
type ContentService struct {
	client      *resty.Client
	rateLimiter *RateLimiter
}

// newContentService 创建新的内容服务
func newContentService(c *Client) *ContentService {
	httpClient := resty.New()

	if c.httpClient != nil {
		httpClient.SetTimeout(c.httpClient.GetClient().Timeout)
		for k, v := range c.httpClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &ContentService{
		client:      httpClient,
		rateLimiter: NewRateLimiter(),
	}
}

// GetCategories 获取分类列表
func (s *ContentService) GetCategories() ([]Category, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []Category
	resp, err := s.client.R().
		SetResult(&result).
		Get(contentAPIURL + "/content/v1/categories")

	if err != nil {
		return nil, fmt.Errorf("获取分类失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取分类失败: %s", resp.String())
	}

	return result, nil
}

// GetSubjects 获取主题列表
func (s *ContentService) GetSubjects(categoryID int) ([]Subject, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []Subject
	resp, err := s.client.R().
		SetQueryParam("categoryId", fmt.Sprintf("%d", categoryID)).
		SetResult(&result).
		Get(contentAPIURL + "/content/v1/subjects")

	if err != nil {
		return nil, fmt.Errorf("获取主题失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取主题失败: %s", resp.String())
	}

	return result, nil
}

// GetParentCategories 获取所有父分类
func (s *ContentService) GetParentCategories(locale string) (*ParentCategoriesResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ParentCategoriesResponse
	req := s.client.R().SetResult(&result)

	if locale != "" {
		req.SetQueryParam("locale", locale)
	}

	resp, err := req.Get(contentAPIURL + "/content/v2/object/parent/all")

	if err != nil {
		return nil, fmt.Errorf("获取父分类失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取父分类失败: %s", resp.String())
	}

	return &result, nil
}

// GetObjectCharacteristics 获取分类特征
func (s *ContentService) GetObjectCharacteristics(objectID int, locale string) (*CharacteristicsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CharacteristicsResponse
	req := s.client.R().SetResult(&result)

	if locale != "" {
		req.SetQueryParam("locale", locale)
	}

	resp, err := req.Get(contentAPIURL + fmt.Sprintf("/content/v2/object/characteristics/%d", objectID))

	if err != nil {
		return nil, fmt.Errorf("获取分类特征失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取分类特征失败: %s", resp.String())
	}

	return &result, nil
}

// CreateProductCard 创建商品卡片
func (s *ContentService) CreateProductCard(card *ProductCardRequest) (*CreateCardResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CreateCardResponse
	resp, err := s.client.R().
		SetBody(card).
		SetResult(&result).
		Post(contentAPIURL + "/content/v2/cards/upload")

	if err != nil {
		return nil, fmt.Errorf("创建商品卡片失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("创建商品卡片失败: %s", resp.String())
	}

	return &result, nil
}

// UpdateProductCard 更新商品卡片
func (s *ContentService) UpdateProductCard(card *ProductCardUpdateRequest) (*UpdateCardResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result UpdateCardResponse
	resp, err := s.client.R().
		SetBody(card).
		SetResult(&result).
		Post(contentAPIURL + "/content/v2/cards/update")

	if err != nil {
		return nil, fmt.Errorf("更新商品卡片失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("更新商品卡片失败: %s", resp.String())
	}

	return &result, nil
}

// GetTags 获取标签列表
func (s *ContentService) GetTags() (*TagsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result TagsResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(contentAPIURL + "/content/v2/tags")

	if err != nil {
		return nil, fmt.Errorf("获取标签列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取标签列表失败: %s", resp.String())
	}

	return &result, nil
}

// CreateTag 创建标签
func (s *ContentService) CreateTag(tag *CreateTagRequest) (*CreateTagResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CreateTagResponse
	resp, err := s.client.R().
		SetBody(tag).
		SetResult(&result).
		Post(contentAPIURL + "/content/v2/tags")

	if err != nil {
		return nil, fmt.Errorf("创建标签失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("创建标签失败: %s", resp.String())
	}

	return &result, nil
}