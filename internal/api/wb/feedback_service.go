package wbapi

import (
	"context"
	"fmt"

	"github.com/go-resty/resty/v2"
)

// FeedbackService 反馈服务
type FeedbackService struct {
	client      *resty.Client
	rateLimiter *RateLimiter
}

// newFeedbackService 创建新的反馈服务
func newFeedbackService(c *Client) *FeedbackService {
	httpClient := resty.New()

	if c.httpClient != nil {
		httpClient.SetTimeout(c.httpClient.GetClient().Timeout)
		for k, v := range c.httpClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &FeedbackService{
		client:      httpClient,
		rateLimiter: NewRateLimiter(),
	}
}

// CheckNewFeedbacksQuestions 检查是否有新的评价和问题
func (s *FeedbackService) CheckNewFeedbacksQuestions() (*NewFeedbacksQuestionsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result NewFeedbacksQuestionsResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(feedbacksAPIURL + "/api/v1/new-feedbacks-questions")

	if err != nil {
		return nil, fmt.Errorf("检查新评价和问题失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("检查新评价和问题失败: %s", resp.String())
	}

	return &result, nil
}

// GetUnansweredQuestionsCount 获取未回复问题数量
func (s *FeedbackService) GetUnansweredQuestionsCount() (*UnansweredCountResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result UnansweredCountResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(feedbacksAPIURL + "/api/v1/questions/count-unanswered")

	if err != nil {
		return nil, fmt.Errorf("获取未回复问题数量失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取未回复问题数量失败: %s", resp.String())
	}

	return &result, nil
}

// GetFeedbacks 获取商品评价列表
func (s *FeedbackService) GetFeedbacks(nmID int, isAnswered *bool, take, skip int) (*FeedbacksResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	req := s.client.R()
	req.SetQueryParam("nmId", fmt.Sprintf("%d", nmID))
	req.SetQueryParam("take", fmt.Sprintf("%d", take))
	req.SetQueryParam("skip", fmt.Sprintf("%d", skip))

	if isAnswered != nil {
		req.SetQueryParam("isAnswered", fmt.Sprintf("%t", *isAnswered))
	}

	var result FeedbacksResponse
	resp, err := req.
		SetResult(&result).
		Get(feedbacksAPIURL + "/api/v1/feedbacks")

	if err != nil {
		return nil, fmt.Errorf("获取评价列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取评价列表失败: %s", resp.String())
	}

	return &result, nil
}

// GetQuestions 获取商品问答列表
func (s *FeedbackService) GetQuestions(nmID int, isAnswered *bool, take, skip int) (*QuestionsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	req := s.client.R()
	req.SetQueryParam("nmId", fmt.Sprintf("%d", nmID))
	req.SetQueryParam("take", fmt.Sprintf("%d", take))
	req.SetQueryParam("skip", fmt.Sprintf("%d", skip))
	
	if isAnswered != nil {
		req.SetQueryParam("isAnswered", fmt.Sprintf("%t", *isAnswered))
	}

	var result QuestionsResponse
	resp, err := req.
		SetResult(&result).
		Get(feedbacksAPIURL + "/api/v1/questions")

	if err != nil {
		return nil, fmt.Errorf("获取问答列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取问答列表失败: %s", resp.String())
	}

	return &result, nil
}

// AnswerFeedback 回复评价
func (s *FeedbackService) AnswerFeedback(feedbackID string, text string) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"id":   feedbackID,
			"text": text,
		}).
		Patch(feedbacksAPIURL + "/api/v1/feedbacks")

	if err != nil {
		return fmt.Errorf("回复评价失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("回复评价失败: %s", resp.String())
	}

	return nil
}

// AnswerQuestion 回复问答
func (s *FeedbackService) AnswerQuestion(questionID string, text string) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"id":   questionID,
			"text": text,
		}).
		Patch(feedbacksAPIURL + "/api/v1/questions")

	if err != nil {
		return fmt.Errorf("回复问答失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("回复问答失败: %s", resp.String())
	}

	return nil
}

// GetTemplates 获取回复模板列表
func (s *FeedbackService) GetTemplates() (*TemplatesResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result TemplatesResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(feedbacksAPIURL + "/api/v1/templates")

	if err != nil {
		return nil, fmt.Errorf("获取模板列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取模板列表失败: %s", resp.String())
	}

	return &result, nil
}

// CreateTemplate 创建回复模板
func (s *FeedbackService) CreateTemplate(template *CreateTemplateRequest) (*CreateTemplateResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CreateTemplateResponse
	resp, err := s.client.R().
		SetBody(template).
		SetResult(&result).
		Post(feedbacksAPIURL + "/api/v1/templates")

	if err != nil {
		return nil, fmt.Errorf("创建模板失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("创建模板失败: %s", resp.String())
	}

	return &result, nil
}
