package wbapi

import "time"

// OrderInfo 订单信息
type OrderInfo struct {
	Date             time.Time `json:"date"`             // 日期
	LastChangeDate   time.Time `json:"lastChangeDate"`   // 最后变更日期
	SupplierArticle  string    `json:"supplierArticle"`  // 供应商文章
	TechSize         string    `json:"techSize"`         // 技术尺寸
	Barcode          string    `json:"barcode"`          // 条形码
	TotalPrice       float64   `json:"totalPrice"`       // 总价
	DiscountPercent  int       `json:"discountPercent"`  // 折扣百分比
	WarehouseName    string    `json:"warehouseName"`    // 仓库名称
	Oblast           string    `json:"oblast"`           // 地区
	IncomeID         int       `json:"incomeID"`         // 收入ID
	NMID             int       `json:"nmId"`             // 商品ID
	Subject          string    `json:"subject"`          // 主题
	Category         string    `json:"category"`         // 分类
	Brand            string    `json:"brand"`            // 品牌
	IsCancel         bool      `json:"isCancel"`         // 是否取消
	CancelDate       *time.Time `json:"cancelDate"`      // 取消日期
	OrderType        string    `json:"orderType"`        // 订单类型
	Sticker          string    `json:"sticker"`          // 贴纸
	GNumber          string    `json:"gNumber"`          // G号码
	SrID             string    `json:"srid"`             // SR ID
}

// SaleInfo 销售信息
type SaleInfo struct {
	Date             time.Time `json:"date"`             // 日期
	LastChangeDate   time.Time `json:"lastChangeDate"`   // 最后变更日期
	SupplierArticle  string    `json:"supplierArticle"`  // 供应商文章
	TechSize         string    `json:"techSize"`         // 技术尺寸
	Barcode          string    `json:"barcode"`          // 条形码
	TotalPrice       float64   `json:"totalPrice"`       // 总价
	DiscountPercent  int       `json:"discountPercent"`  // 折扣百分比
	IsSupply         bool      `json:"isSupply"`         // 是否供应
	IsRealization    bool      `json:"isRealization"`    // 是否实现
	PromoCodeDiscount float64  `json:"promoCodeDiscount"` // 促销码折扣
	WarehouseName    string    `json:"warehouseName"`    // 仓库名称
	CountryName      string    `json:"countryName"`      // 国家名称
	DistrictName     string    `json:"districtName"`     // 地区名称
	RegionName       string    `json:"regionName"`       // 区域名称
	IncomeID         int       `json:"incomeID"`         // 收入ID
	SaleID           string    `json:"saleID"`           // 销售ID
	NMID             int       `json:"nmId"`             // 商品ID
	Subject          string    `json:"subject"`          // 主题
	Category         string    `json:"category"`         // 分类
	Brand            string    `json:"brand"`            // 品牌
	IsStorno         int       `json:"isStorno"`         // 是否冲销
	Sticker          string    `json:"sticker"`          // 贴纸
	GNumber          string    `json:"gNumber"`          // G号码
	SrID             string    `json:"srid"`             // SR ID
	ForPay           float64   `json:"forPay"`           // 应付金额
	FinishedPrice    float64   `json:"finishedPrice"`    // 完成价格
	PriceWithDisc    float64   `json:"priceWithDisc"`    // 折扣价格
	OrderType        string    `json:"orderType"`        // 订单类型
}

// ReportDetailByPeriod 按周期详细报告
type ReportDetailByPeriod struct {
	RealizationReportID int       `json:"realizationreport_id"` // 实现报告ID
	DateFrom            time.Time `json:"date_from"`            // 开始日期
	DateTo              time.Time `json:"date_to"`              // 结束日期
	CreateDT            time.Time `json:"create_dt"`            // 创建日期
	SupplierContractCode string   `json:"suppliercontract_code"` // 供应商合同代码
	RrdID               int       `json:"rrd_id"`               // RRD ID
	GiID                int       `json:"gi_id"`                // GI ID
	SubjectName         string    `json:"subject_name"`         // 主题名称
	NMID                int       `json:"nm_id"`                // 商品ID
	BrandName           string    `json:"brand_name"`           // 品牌名称
	VendorCode          string    `json:"vendor_code"`          // 供应商代码
	Size                string    `json:"size"`                 // 尺寸
	Barcode             string    `json:"barcode"`              // 条形码
	DocTypeName         string    `json:"doc_type_name"`        // 文档类型名称
	Quantity            int       `json:"quantity"`             // 数量
	RetailPrice         float64   `json:"retail_price"`         // 零售价
	RetailAmount        float64   `json:"retail_amount"`        // 零售金额
	SalePercent         int       `json:"sale_percent"`         // 销售百分比
	CommissionPercent   float64   `json:"commission_percent"`   // 佣金百分比
	OfficeName          string    `json:"office_name"`          // 办公室名称
	SupplierOperName    string    `json:"supplier_oper_name"`   // 供应商操作名称
	OrderDT             time.Time `json:"order_dt"`             // 订单日期
	SaleDT              time.Time `json:"sale_dt"`              // 销售日期
	RrDT                time.Time `json:"rr_dt"`                // RR日期
	ShkID               int       `json:"shk_id"`               // SHK ID
	RetailPriceWithdiscRub float64 `json:"retail_price_withdisc_rub"` // 折扣零售价
	DeliveryAmount      int       `json:"delivery_amount"`      // 配送金额
	ReturnAmount        int       `json:"return_amount"`        // 退货金额
	DeliveryRub         float64   `json:"delivery_rub"`         // 配送费用
	GiBoxTypeName       string    `json:"gi_box_type_name"`     // GI盒子类型名称
	ProductDiscountForReport float64 `json:"product_discount_for_report"` // 产品报告折扣
	SupplierPromo       float64   `json:"supplier_promo"`       // 供应商促销
	RID                 int       `json:"rid"`                  // RID
	PPVZSppPrc          float64   `json:"ppvz_spp_prc"`         // PPVZ SPP百分比
	PPVZKvwPrcBase      float64   `json:"ppvz_kvw_prc_base"`    // PPVZ KVW基础百分比
	PPVZKvwPrc          float64   `json:"ppvz_kvw_prc"`         // PPVZ KVW百分比
	SupplierID          int       `json:"supplier_id"`          // 供应商ID
	PPVZRewardBase      float64   `json:"ppvz_reward_base"`     // PPVZ基础奖励
	PPVZReward          float64   `json:"ppvz_reward"`          // PPVZ奖励
	AcquiringFee        float64   `json:"acquiring_fee"`        // 收单费
	AcquiringBank       string    `json:"acquiring_bank"`       // 收单银行
	PPVZVw              float64   `json:"ppvz_vw"`              // PPVZ VW
	PPVZVwNds           float64   `json:"ppvz_vw_nds"`          // PPVZ VW NDS
	PPVZOfficeID        int       `json:"ppvz_office_id"`       // PPVZ办公室ID
	PPVZOfficeName      string    `json:"ppvz_office_name"`     // PPVZ办公室名称
	PPVZSupplierID      int       `json:"ppvz_supplier_id"`     // PPVZ供应商ID
	PPVZSupplierName    string    `json:"ppvz_supplier_name"`   // PPVZ供应商名称
	PPVZInn             string    `json:"ppvz_inn"`             // PPVZ INN
	DeclarationNumber   string    `json:"declaration_number"`   // 申报号
	BonusTypeName       string    `json:"bonus_type_name"`      // 奖金类型名称
	StickerID           string    `json:"sticker_id"`           // 贴纸ID
	SiteCountry         string    `json:"site_country"`         // 网站国家
	PenaltyAmount       float64   `json:"penalty"`              // 罚款金额
	AdditionalPayment   float64   `json:"additional_payment"`   // 附加付款
	RebillLogisticCost  float64   `json:"rebill_logistic_cost"` // 重新计费物流成本
	RebillLogisticOrg   string    `json:"rebill_logistic_org"`  // 重新计费物流组织
	Kiz                 string    `json:"kiz"`                  // KIZ
	StorageFee          float64   `json:"storage_fee"`          // 存储费
	Deduction           float64   `json:"deduction"`            // 扣除
	Acceptance          float64   `json:"acceptance"`           // 验收
	SrID                string    `json:"srid"`                 // SR ID
}

// ===== 新订单相关模型 =====

// NewOrdersResponse 新订单响应
type NewOrdersResponse struct {
	Orders []NewOrder `json:"orders"` // 新订单列表
}

// NewOrder 新订单信息
type NewOrder struct {
	ID              int       `json:"id"`              // 订单ID
	RID             string    `json:"rid"`             // 订单唯一标识
	CreatedAt       time.Time `json:"createdAt"`       // 创建时间
	OfficesNames    []string  `json:"officesNames"`    // 仓库名称列表
	SupplierArticle string    `json:"supplierArticle"` // 供应商商品编码
	TechSize        string    `json:"techSize"`        // 技术尺寸
	Barcode         string    `json:"barcode"`         // 条形码
	TotalPrice      int       `json:"totalPrice"`      // 总价格（копейки）
	IsLarge         bool      `json:"isLarge"`         // 是否大件
	WarehouseID     int       `json:"warehouseId"`     // 仓库ID
	NMID            int       `json:"nmId"`            // 商品ID
	ChrtID          int       `json:"chrtId"`          // 特征ID
	Price           int       `json:"price"`           // 价格（копейки）
	ConvertedPrice  int       `json:"convertedPrice"`  // 转换价格（копейки）
	CurrencyCode    int       `json:"currencyCode"`    // 货币代码
	ConvertedCurrencyCode int `json:"convertedCurrencyCode"` // 转换货币代码
	CargoType       int       `json:"cargoType"`       // 货物类型
	Status          string    `json:"status"`          // 订单状态
}

// OrdersStatusResponse 订单状态响应
type OrdersStatusResponse struct {
	Orders []OrderStatus `json:"orders"` // 订单状态列表
}

// OrderStatus 订单状态
type OrderStatus struct {
	ID              int       `json:"id"`              // 订单ID
	SupplierArticle string    `json:"supplierArticle"` // 供应商商品编码
	TechSize        string    `json:"techSize"`        // 技术尺寸
	Barcode         string    `json:"barcode"`         // 条形码
	TotalPrice      int       `json:"totalPrice"`      // 总价格（копейки）
	WarehouseID     int       `json:"warehouseId"`     // 仓库ID
	NMID            int       `json:"nmId"`            // 商品ID
	ChrtID          int       `json:"chrtId"`          // 特征ID
	Price           int       `json:"price"`           // 价格（копейки）
	ConvertedPrice  int       `json:"convertedPrice"`  // 转换价格（копейки）
	CurrencyCode    int       `json:"currencyCode"`    // 货币代码
	ConvertedCurrencyCode int `json:"convertedCurrencyCode"` // 转换货币代码
	CargoType       int       `json:"cargoType"`       // 货物类型
	Status          string    `json:"status"`          // 订单状态
	UserInfo        struct {
		FIO   string `json:"fio"`   // 用户姓名
		Phone string `json:"phone"` // 用户电话
	} `json:"userInfo"` // 用户信息
	DeliveryAddress struct {
		FullAddress string `json:"fullAddress"` // 完整地址
		Longitude   string `json:"longitude"`   // 经度
		Latitude    string `json:"latitude"`    // 纬度
	} `json:"deliveryAddress"` // 配送地址
}
