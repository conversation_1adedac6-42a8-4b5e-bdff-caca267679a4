package wbapi

import "time"

// ===== 推广统计相关 =====

// FullStatsRequest 完整统计请求
type FullStatsRequest struct {
	ID       int      `json:"id"`              // 广告活动ID
	Dates    []string `json:"dates,omitempty"` // 指定具体日期列表，格式：YYYY-MM-DD
	Interval *struct {
		Begin string `json:"begin"` // 开始日期，格式：YYYY-MM-DD
		End   string `json:"end"`   // 结束日期，格式：YYYY-MM-DD
	} `json:"interval,omitempty"` // 时间区间
}

// FullStatsResponse 完整统计响应
type FullStatsResponse struct {
	Views    int                `json:"views"`     // 展示量
	Clicks   int                `json:"clicks"`    // 点击量
	CTR      float64            `json:"ctr"`       // 点击率
	CPC      float64            `json:"cpc"`       // 点击成本
	Sum      float64            `json:"sum"`       // 花费
	Orders   int                `json:"orders"`    // 订单量
	CR       float64            `json:"cr"`        // 转化率
	Shks     int                `json:"shks"`      // 购买量
	SumPrice int                `json:"sum_price"` // 订单金额
	Dates    []string           `json:"dates"`     // 日期列表
	Days     []FullStatsDayData `json:"days"`      // 每日数据
	AdvertID int                `json:"advertId"`  // 广告活动ID
}

// FullStatsDayData 每日统计数据
type FullStatsDayData struct {
	Date     time.Time          `json:"date"`      // 日期
	Views    int                `json:"views"`     // 展示量
	Clicks   int                `json:"clicks"`    // 点击量
	CTR      float64            `json:"ctr"`       // 点击率
	CPC      float64            `json:"cpc"`       // 点击成本
	Sum      float64            `json:"sum"`       // 花费
	Orders   int                `json:"orders"`    // 订单量
	CR       float64            `json:"cr"`        // 转化率
	Shks     int                `json:"shks"`      // 购买量
	SumPrice int                `json:"sum_price"` // 订单金额
	Apps     []FullStatsAppData `json:"apps"`      // 应用数据
}

// FullStatsAppData 应用统计数据
type FullStatsAppData struct {
	Views    int               `json:"views"`     // 展示量
	Clicks   int               `json:"clicks"`    // 点击量
	CTR      float64           `json:"ctr"`       // 点击率
	CPC      float64           `json:"cpc"`       // 点击成本
	Sum      float64           `json:"sum"`       // 花费
	Orders   int               `json:"orders"`    // 订单量
	CR       float64           `json:"cr"`        // 转化率
	Shks     int               `json:"shks"`      // 购买量
	SumPrice int               `json:"sum_price"` // 订单金额
	NM       []FullStatsNMData `json:"nm"`        // 商品数据
	AppType  int               `json:"appType"`   // 应用类型
}

// FullStatsNMData 商品统计数据
type FullStatsNMData struct {
	Views    int     `json:"views"`     // 展示量
	Clicks   int     `json:"clicks"`    // 点击量
	CTR      float64 `json:"ctr"`       // 点击率
	CPC      float64 `json:"cpc"`       // 点击成本
	Sum      float64 `json:"sum"`       // 花费
	Orders   int     `json:"orders"`    // 订单量
	CR       float64 `json:"cr"`        // 转化率
	Shks     int     `json:"shks"`      // 购买量
	SumPrice int     `json:"sum_price"` // 订单金额
	Name     string  `json:"name"`      // 商品名称
	NMID     int     `json:"nmId"`      // 商品ID
}

// ===== 自动广告词组统计 =====

// AutoStatWordsResponse 自动广告词组统计响应
type AutoStatWordsResponse struct {
	Excluded []string `json:"excluded"` // 排除词列表
	Clusters []struct {
		Cluster  string   `json:"cluster"`  // 词组
		Count    int      `json:"count"`    // 展示次数
		Keywords []string `json:"keywords"` // 关键词列表
	} `json:"clusters"` // 词组统计
}

// ===== 搜索广告词组统计 =====

// StatWordsResponse 搜索广告词组统计响应
type StatWordsResponse struct {
	Words struct {
		Phrase   []string `json:"phrase"`   // 词组匹配
		Strong   []string `json:"strong"`   // 强匹配
		Excluded []string `json:"excluded"` // 排除词
		Pluse    []string `json:"pluse"`    // 固定词组
		Keywords []struct {
			Keyword string `json:"keyword"` // 关键词
			Count   int    `json:"count"`   // 展示次数
		} `json:"keywords"` // 关键词统计
		Fixed bool `json:"fixed"` // 是否固定关键词
	} `json:"words"` // 关键词信息
	Stat []StatWordsData `json:"stat"` // 统计数据
}

// StatWordsData 关键词统计数据
type StatWordsData struct {
	AdvertID     int       `json:"advertId"`     // 广告ID
	Keyword      string    `json:"keyword"`      // 关键词
	AdvertName   string    `json:"advertName"`   // 广告名称（已禁用）
	CampaignName string    `json:"campaignName"` // 活动名称
	Begin        time.Time `json:"begin"`        // 开始时间
	End          time.Time `json:"end"`          // 结束时间
	Views        int       `json:"views"`        // 展示量
	Clicks       int       `json:"clicks"`       // 点击量
	Frequency    float64   `json:"frq"`          // 频率
	CTR          float64   `json:"ctr"`          // 点击率
	CPC          float64   `json:"cpc"`          // 点击成本
	Duration     int       `json:"duration"`     // 持续时间
	Sum          float64   `json:"sum"`          // 花费
}

// ===== 关键词统计 =====

// KeywordsStatsResponse 关键词统计响应
type KeywordsStatsResponse struct {
	Keywords []KeywordsStatsData `json:"keywords"` // 关键词统计
}

// KeywordsStatsData 关键词统计数据
type KeywordsStatsData struct {
	Date  string                 `json:"date"`  // 日期
	Stats []KeywordsStatsDayData `json:"stats"` // 统计数据
}

// KeywordsStatsDayData 关键词每日统计数据
type KeywordsStatsDayData struct {
	Clicks  int     `json:"clicks"`  // 点击量
	CTR     float64 `json:"ctr"`     // 点击率
	CPC     float64 `json:"cpc"`     // 点击成本
	Orders  int     `json:"orders"`  // 订单量
	CR      float64 `json:"cr"`      // 转化率
	Keyword string  `json:"keyword"` // 关键词
	Views   int     `json:"views"`   // 展示量
	Sum     float64 `json:"sum"`     // 花费
}

// ===== 销售漏斗相关 =====

// NMReportDetailRequest 商品卡片详细报告请求
type NMReportDetailRequest struct {
	Page       int      `json:"page"`                 // 页码
	IsNextPage bool     `json:"isNextPage"`           // 是否有下一页
	Period     Period   `json:"period"`               // 时间周期
	OrderBy    OrderBy  `json:"orderBy"`              // 排序
	BrandNames []string `json:"brandNames,omitempty"` // 品牌名称列表
	ObjectIDs  []int    `json:"objectIDs,omitempty"`  // 商品类目ID列表
	TagIDs     []int    `json:"tagIDs,omitempty"`     // 标签ID列表
	NMIDs      []int    `json:"nmIDs,omitempty"`      // 商品ID列表
}

// Period 时间周期
type Period struct {
	Begin string `json:"begin"` // 开始时间，格式：YYYY-MM-DD HH:MM:SS
	End   string `json:"end"`   // 结束时间，格式：YYYY-MM-DD HH:MM:SS
}

// OrderBy 排序
type OrderBy struct {
	Field string `json:"field"` // 排序字段
	Mode  string `json:"mode"`  // 排序方向：asc/desc
}

// NMReportDetailResponse 商品卡片详细报告响应
type NMReportDetailResponse struct {
	Data             NMReportDetailData `json:"data"`      // 数据
	Error            bool               `json:"error"`     // 错误标志
	ErrorText        string             `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// NMReportDetailData 商品卡片详细数据
type NMReportDetailData struct {
	Page       int                  `json:"page"`       // 页码
	IsNextPage bool                 `json:"isNextPage"` // 是否有下一页
	Cards      []NMReportDetailCard `json:"cards"`      // 商品卡片列表
}

// NMReportDetailCard 商品卡片详细信息
type NMReportDetailCard struct {
	NMID       int                    `json:"nmID"`       // 商品ID
	VendorCode string                 `json:"vendorCode"` // 商家编码
	BrandName  string                 `json:"brandName"`  // 品牌名称
	Tags       []NMReportTag          `json:"tags"`       // 标签列表
	Object     NMReportObject         `json:"object"`     // 商品类目
	Statistics NMReportCardStatistics `json:"statistics"` // 统计数据
	Stocks     NMReportStocks         `json:"stocks"`     // 库存信息
}

// NMReportTag 标签信息
type NMReportTag struct {
	ID   int    `json:"id"`   // 标签ID
	Name string `json:"name"` // 标签名称
}

// NMReportObject 商品类目信息
type NMReportObject struct {
	ID   int    `json:"id"`   // 类目ID
	Name string `json:"name"` // 类目名称
}

// NMReportCardStatistics 商品卡片统计数据
type NMReportCardStatistics struct {
	SelectedPeriod   NMReportPeriodStats `json:"selectedPeriod"`   // 选定周期统计
	PreviousPeriod   NMReportPeriodStats `json:"previousPeriod"`   // 上一周期统计
	PeriodComparison NMReportComparison  `json:"periodComparison"` // 周期对比
}

// NMReportPeriodStats 周期统计数据
type NMReportPeriodStats struct {
	Begin                string              `json:"begin"`                // 开始时间
	End                  string              `json:"end"`                  // 结束时间
	OpenCardCount        int                 `json:"openCardCount"`        // 商品页面浏览量
	AddToCartCount       int                 `json:"addToCartCount"`       // 加购数量
	OrdersCount          int                 `json:"ordersCount"`          // 订单数量
	OrdersSumRub         int                 `json:"ordersSumRub"`         // 订单金额
	BuyoutsCount         int                 `json:"buyoutsCount"`         // 购买数量
	BuyoutsSumRub        int                 `json:"buyoutsSumRub"`        // 购买金额
	CancelCount          int                 `json:"cancelCount"`          // 取消数量
	CancelSumRub         int                 `json:"cancelSumRub"`         // 取消金额
	AvgPriceRub          int                 `json:"avgPriceRub"`          // 平均价格
	AvgOrdersCountPerDay float64             `json:"avgOrdersCountPerDay"` // 日均订单数
	Conversions          NMReportConversions `json:"conversions"`          // 转化率
}

// NMReportConversions 转化率数据
type NMReportConversions struct {
	AddToCartPercent   float64 `json:"addToCartPercent"`   // 加购转化率
	CartToOrderPercent float64 `json:"cartToOrderPercent"` // 下单转化率
	BuyoutsPercent     float64 `json:"buyoutsPercent"`     // 购买转化率
}

// NMReportComparison 周期对比数据
type NMReportComparison struct {
	OpenCardDynamics             float64             `json:"openCardDynamics"`             // 浏览量变化
	AddToCartDynamics            float64             `json:"addToCartDynamics"`            // 加购变化
	OrdersCountDynamics          float64             `json:"ordersCountDynamics"`          // 订单数变化
	OrdersSumRubDynamics         float64             `json:"ordersSumRubDynamics"`         // 订单金额变化
	BuyoutsCountDynamics         float64             `json:"buyoutsCountDynamics"`         // 购买数变化
	BuyoutsSumRubDynamics        float64             `json:"buyoutsSumRubDynamics"`        // 购买金额变化
	CancelCountDynamics          float64             `json:"cancelCountDynamics"`          // 取消数变化
	CancelSumRubDynamics         float64             `json:"cancelSumRubDynamics"`         // 取消金额变化
	AvgOrdersCountPerDayDynamics float64             `json:"avgOrdersCountPerDayDynamics"` // 日均订单数变化
	AvgPriceRubDynamics          float64             `json:"avgPriceRubDynamics"`          // 平均价格变化
	Conversions                  NMReportConversions `json:"conversions"`                  // 转化率变化
}

// NMReportStocks 库存信息
type NMReportStocks struct {
	StocksMP int `json:"stocksMp"` // MP库存
	StocksWB int `json:"stocksWb"` // WB库存
}

// ===== 媒体广告统计相关模型 =====

// MediaStatsRequest 媒体广告统计请求
type MediaStatsRequest struct {
	CampaignIDs []int  `json:"campaignIds"` // 活动ID列表
	Period      Period `json:"period"`      // 时间周期
}

// MediaStatsResponse 媒体广告统计响应
type MediaStatsResponse struct {
	Data []MediaStatsData `json:"data"` // 统计数据列表
}

// MediaStatsData 媒体广告统计数据
type MediaStatsData struct {
	CampaignID   int     `json:"campaignId"`   // 活动ID
	CampaignName string  `json:"campaignName"` // 活动名称
	Impressions  int     `json:"impressions"`  // 展示次数
	Clicks       int     `json:"clicks"`       // 点击次数
	CTR          float64 `json:"ctr"`          // 点击率
	CPC          float64 `json:"cpc"`          // 平均点击成本
	Spend        int     `json:"spend"`        // 花费（копейки）
	Conversions  int     `json:"conversions"`  // 转化次数
	CVR          float64 `json:"cvr"`          // 转化率
	CPA          float64 `json:"cpa"`          // 平均转化成本
	Revenue      int     `json:"revenue"`      // 收入（копейки）
	ROAS         float64 `json:"roas"`         // 广告支出回报率
}

// ===== 销售漏斗历史数据相关模型 =====

// NMReportDetailHistoryRequest 商品卡片历史详细报告请求
type NMReportDetailHistoryRequest struct {
	Page        int      `json:"page"`                 // 页码
	IsNextPage  bool     `json:"isNextPage"`           // 是否有下一页
	Period      Period   `json:"period"`               // 时间周期
	OrderBy     OrderBy  `json:"orderBy"`              // 排序
	BrandNames  []string `json:"brandNames,omitempty"` // 品牌名称列表
	ObjectIDs   []int    `json:"objectIDs,omitempty"`  // 商品类目ID列表
	TagIDs      []int    `json:"tagIDs,omitempty"`     // 标签ID列表
	NMIDs       []int    `json:"nmIDs,omitempty"`      // 商品ID列表
	Aggregation string   `json:"aggregation"`          // 聚合方式：day, week, month
}

// NMReportDetailHistoryResponse 商品卡片历史详细报告响应
type NMReportDetailHistoryResponse struct {
	Data             []NMReportDetailHistoryCard `json:"data"`             // 数据数组
	Error            bool                        `json:"error"`            // 错误标志
	ErrorText        string                      `json:"errorText"`        // 错误信息
	AdditionalErrors interface{}                 `json:"additionalErrors"` // 附加错误（可能为null）
}

// NMReportDetailHistoryCard 商品卡片历史详细信息
type NMReportDetailHistoryCard struct {
	NMID       int                           `json:"nmID"`       // 商品ID
	ImtName    string                        `json:"imtName"`    // 商品名称
	VendorCode string                        `json:"vendorCode"` // 商家编码
	History    []NMReportDetailHistoryPeriod `json:"history"`    // 历史周期数据
}

// NMReportDetailHistoryPeriod 历史周期数据
type NMReportDetailHistoryPeriod struct {
	Dt                     string  `json:"dt"`                     // 日期 (YYYY-MM-DD)
	OpenCardCount          int     `json:"openCardCount"`          // 商品页面浏览量
	AddToCartCount         int     `json:"addToCartCount"`         // 加购数量
	OrdersCount            int     `json:"ordersCount"`            // 订单数量
	OrdersSumRub           int     `json:"ordersSumRub"`           // 订单金额（卢布）
	BuyoutsCount           int     `json:"buyoutsCount"`           // 赎回数量
	BuyoutsSumRub          int     `json:"buyoutsSumRub"`          // 赎回金额（卢布）
	BuyoutPercent          int     `json:"buyoutPercent"`          // 赎回百分比
	AddToCartConversion    int     `json:"addToCartConversion"`    // 加购转化率
	CartToOrderConversion  int     `json:"cartToOrderConversion"`  // 购物车到订单转化率
}

// NMReportGroupedHistoryRequest 分组商品卡片历史报告请求
type NMReportGroupedHistoryRequest struct {
	Period      Period   `json:"period"`               // 时间周期
	GroupBy     string   `json:"groupBy"`              // 分组方式：brand, object, tag
	BrandNames  []string `json:"brandNames,omitempty"` // 品牌名称列表
	ObjectIDs   []int    `json:"objectIDs,omitempty"`  // 商品类目ID列表
	TagIDs      []int    `json:"tagIDs,omitempty"`     // 标签ID列表
	NMIDs       []int    `json:"nmIDs,omitempty"`      // 商品ID列表
	Aggregation string   `json:"aggregation"`          // 聚合方式：day, week, month
}

// NMReportGroupedHistoryResponse 分组商品卡片历史报告响应
type NMReportGroupedHistoryResponse struct {
	Data             NMReportGroupedHistoryData `json:"data"`      // 数据
	Error            bool                       `json:"error"`     // 错误标志
	ErrorText        string                     `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// NMReportGroupedHistoryData 分组商品卡片历史数据
type NMReportGroupedHistoryData struct {
	Groups []NMReportGroupedHistoryGroup `json:"groups"` // 分组数据列表
}

// NMReportGroupedHistoryGroup 分组历史数据
type NMReportGroupedHistoryGroup struct {
	GroupKey  string                           `json:"groupKey"`  // 分组键
	GroupName string                           `json:"groupName"` // 分组名称
	History   []NMReportDetailHistoryPeriod   `json:"history"`   // 历史周期数据
}

// ===== 搜索报告相关模型 =====

// SearchReportRequest 搜索报告请求
type SearchReportRequest struct {
	Period     Period   `json:"period"`               // 时间周期
	BrandNames []string `json:"brandNames,omitempty"` // 品牌名称列表
	ObjectIDs  []int    `json:"objectIDs,omitempty"`  // 商品类目ID列表
	TagIDs     []int    `json:"tagIDs,omitempty"`     // 标签ID列表
	NMIDs      []int    `json:"nmIDs,omitempty"`      // 商品ID列表
}

// SearchReportResponse 搜索报告响应
type SearchReportResponse struct {
	Data             SearchReportData `json:"data"`      // 数据
	Error            bool             `json:"error"`     // 错误标志
	ErrorText        string           `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// SearchReportData 搜索报告数据
type SearchReportData struct {
	Summary SearchReportSummary `json:"summary"` // 汇总数据
	Charts  SearchReportCharts  `json:"charts"`  // 图表数据
}

// SearchReportSummary 搜索报告汇总
type SearchReportSummary struct {
	TotalSearches    int     `json:"totalSearches"` // 总搜索次数
	TotalClicks      int     `json:"totalClicks"`   // 总点击次数
	TotalOrders      int     `json:"totalOrders"`   // 总订单数
	TotalRevenue     int     `json:"totalRevenue"`  // 总收入（копейки）
	AvgCTR           float64 `json:"avgCtr"`        // 平均点击率
	AvgConversion    float64 `json:"avgConversion"` // 平均转化率
	TopSearchQueries []struct {
		Query  string `json:"query"`  // 搜索词
		Count  int    `json:"count"`  // 搜索次数
		Clicks int    `json:"clicks"` // 点击次数
		Orders int    `json:"orders"` // 订单数
	} `json:"topSearchQueries"` // 热门搜索词
}

// SearchReportCharts 搜索报告图表数据
type SearchReportCharts struct {
	SearchesByDay []struct {
		Date     string `json:"date"`     // 日期
		Searches int    `json:"searches"` // 搜索次数
		Clicks   int    `json:"clicks"`   // 点击次数
		Orders   int    `json:"orders"`   // 订单数
	} `json:"searchesByDay"` // 按日搜索数据
	SearchesByCategory []struct {
		CategoryID   int    `json:"categoryId"`   // 分类ID
		CategoryName string `json:"categoryName"` // 分类名称
		Searches     int    `json:"searches"`     // 搜索次数
		Clicks       int    `json:"clicks"`       // 点击次数
		Orders       int    `json:"orders"`       // 订单数
	} `json:"searchesByCategory"` // 按分类搜索数据
}

// SearchReportTableGroupsRequest 搜索报告分组数据请求
type SearchReportTableGroupsRequest struct {
	Period     Period   `json:"period"`               // 时间周期
	GroupBy    string   `json:"groupBy"`              // 分组方式：brand, object, tag, query
	BrandNames []string `json:"brandNames,omitempty"` // 品牌名称列表
	ObjectIDs  []int    `json:"objectIDs,omitempty"`  // 商品类目ID列表
	TagIDs     []int    `json:"tagIDs,omitempty"`     // 标签ID列表
	NMIDs      []int    `json:"nmIDs,omitempty"`      // 商品ID列表
	Page       int      `json:"page"`                 // 页码
	Limit      int      `json:"limit"`                // 每页数量
}

// SearchReportTableGroupsResponse 搜索报告分组数据响应
type SearchReportTableGroupsResponse struct {
	Data             SearchReportTableGroupsData `json:"data"`      // 数据
	Error            bool                        `json:"error"`     // 错误标志
	ErrorText        string                      `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// SearchReportTableGroupsData 搜索报告分组数据
type SearchReportTableGroupsData struct {
	Page       int                      `json:"page"`       // 页码
	IsNextPage bool                     `json:"isNextPage"` // 是否有下一页
	Groups     []SearchReportTableGroup `json:"groups"`     // 分组数据列表
}

// SearchReportTableGroup 搜索报告分组
type SearchReportTableGroup struct {
	GroupKey    string  `json:"groupKey"`    // 分组键
	GroupName   string  `json:"groupName"`   // 分组名称
	Searches    int     `json:"searches"`    // 搜索次数
	Clicks      int     `json:"clicks"`      // 点击次数
	CTR         float64 `json:"ctr"`         // 点击率
	Orders      int     `json:"orders"`      // 订单数
	Conversion  float64 `json:"conversion"`  // 转化率
	Revenue     int     `json:"revenue"`     // 收入（копейки）
	AvgPosition float64 `json:"avgPosition"` // 平均位置
}

// SearchReportTableDetailsRequest 搜索报告详细数据请求
type SearchReportTableDetailsRequest struct {
	Period     Period   `json:"period"`               // 时间周期
	GroupKey   string   `json:"groupKey"`             // 分组键
	BrandNames []string `json:"brandNames,omitempty"` // 品牌名称列表
	ObjectIDs  []int    `json:"objectIDs,omitempty"`  // 商品类目ID列表
	TagIDs     []int    `json:"tagIDs,omitempty"`     // 标签ID列表
	NMIDs      []int    `json:"nmIDs,omitempty"`      // 商品ID列表
	Page       int      `json:"page"`                 // 页码
	Limit      int      `json:"limit"`                // 每页数量
}

// SearchReportTableDetailsResponse 搜索报告详细数据响应
type SearchReportTableDetailsResponse struct {
	Data             SearchReportTableDetailsData `json:"data"`      // 数据
	Error            bool                         `json:"error"`     // 错误标志
	ErrorText        string                       `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// SearchReportTableDetailsData 搜索报告详细数据
type SearchReportTableDetailsData struct {
	Page       int                       `json:"page"`       // 页码
	IsNextPage bool                      `json:"isNextPage"` // 是否有下一页
	Details    []SearchReportTableDetail `json:"details"`    // 详细数据列表
}

// SearchReportTableDetail 搜索报告详细项
type SearchReportTableDetail struct {
	NMID        int     `json:"nmId"`        // 商品ID
	VendorCode  string  `json:"vendorCode"`  // 商家编码
	BrandName   string  `json:"brandName"`   // 品牌名称
	ProductName string  `json:"productName"` // 商品名称
	SearchQuery string  `json:"searchQuery"` // 搜索词
	Searches    int     `json:"searches"`    // 搜索次数
	Clicks      int     `json:"clicks"`      // 点击次数
	CTR         float64 `json:"ctr"`         // 点击率
	Orders      int     `json:"orders"`      // 订单数
	Conversion  float64 `json:"conversion"`  // 转化率
	Revenue     int     `json:"revenue"`     // 收入（копейки）
	AvgPosition float64 `json:"avgPosition"` // 平均位置
}

// ProductSearchTextsRequest 商品搜索文本请求
type ProductSearchTextsRequest struct {
	NMID   int    `json:"nmId"`   // 商品ID
	Period Period `json:"period"` // 时间周期
}

// ProductSearchTextsResponse 商品搜索文本响应
type ProductSearchTextsResponse struct {
	Data             ProductSearchTextsData `json:"data"`      // 数据
	Error            bool                   `json:"error"`     // 错误标志
	ErrorText        string                 `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// ProductSearchTextsData 商品搜索文本数据
type ProductSearchTextsData struct {
	NMID        int                 `json:"nmId"`        // 商品ID
	SearchTexts []ProductSearchText `json:"searchTexts"` // 搜索文本列表
}

// ProductSearchText 商品搜索文本
type ProductSearchText struct {
	SearchQuery string  `json:"searchQuery"` // 搜索词
	Searches    int     `json:"searches"`    // 搜索次数
	Clicks      int     `json:"clicks"`      // 点击次数
	CTR         float64 `json:"ctr"`         // 点击率
	Orders      int     `json:"orders"`      // 订单数
	Conversion  float64 `json:"conversion"`  // 转化率
	AvgPosition float64 `json:"avgPosition"` // 平均位置
}

// ProductOrdersRequest 商品订单数据请求
type ProductOrdersRequest struct {
	NMID   int    `json:"nmId"`   // 商品ID
	Period Period `json:"period"` // 时间周期
}

// ProductOrdersResponse 商品订单数据响应
type ProductOrdersResponse struct {
	Data             ProductOrdersData `json:"data"`      // 数据
	Error            bool              `json:"error"`     // 错误标志
	ErrorText        string            `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// ProductOrdersData 商品订单数据
type ProductOrdersData struct {
	NMID   int                `json:"nmId"`   // 商品ID
	Orders []ProductOrderInfo `json:"orders"` // 订单信息列表
}

// ProductOrderInfo 商品订单信息
type ProductOrderInfo struct {
	Date        string `json:"date"`        // 日期
	Orders      int    `json:"orders"`      // 订单数
	Revenue     int    `json:"revenue"`     // 收入（копейки）
	SearchQuery string `json:"searchQuery"` // 搜索词
}

// ===== 库存报告相关模型 =====

// StockReportRequest 库存报告请求
type StockReportRequest struct {
	Period       Period   `json:"period"`                 // 时间周期
	BrandNames   []string `json:"brandNames,omitempty"`   // 品牌名称列表
	ObjectIDs    []int    `json:"objectIDs,omitempty"`    // 商品类目ID列表
	TagIDs       []int    `json:"tagIDs,omitempty"`       // 标签ID列表
	NMIDs        []int    `json:"nmIDs,omitempty"`        // 商品ID列表
	WarehouseIDs []int    `json:"warehouseIds,omitempty"` // 仓库ID列表
}

// StockReportResponse 库存报告响应
type StockReportResponse struct {
	Data             StockReportData `json:"data"`      // 数据
	Error            bool            `json:"error"`     // 错误标志
	ErrorText        string          `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// StockReportData 库存报告数据
type StockReportData struct {
	Summary StockReportSummary `json:"summary"` // 汇总数据
	Charts  StockReportCharts  `json:"charts"`  // 图表数据
}

// StockReportSummary 库存报告汇总
type StockReportSummary struct {
	TotalProducts      int     `json:"totalProducts"`      // 总商品数
	TotalStockValue    int     `json:"totalStockValue"`    // 总库存价值（копейки）
	AvgStockDays       float64 `json:"avgStockDays"`       // 平均库存天数
	LowStockProducts   int     `json:"lowStockProducts"`   // 低库存商品数
	OutOfStockProducts int     `json:"outOfStockProducts"` // 缺货商品数
	TopStockProducts   []struct {
		NMID        int    `json:"nmId"`        // 商品ID
		ProductName string `json:"productName"` // 商品名称
		StockValue  int    `json:"stockValue"`  // 库存价值（копейки）
		StockDays   int    `json:"stockDays"`   // 库存天数
	} `json:"topStockProducts"` // 高库存商品
}

// StockReportCharts 库存报告图表数据
type StockReportCharts struct {
	StockByDay []struct {
		Date       string `json:"date"`       // 日期
		StockValue int    `json:"stockValue"` // 库存价值（копейки）
		StockCount int    `json:"stockCount"` // 库存数量
	} `json:"stockByDay"` // 按日库存数据
	StockByWarehouse []struct {
		WarehouseID   int    `json:"warehouseId"`   // 仓库ID
		WarehouseName string `json:"warehouseName"` // 仓库名称
		StockValue    int    `json:"stockValue"`    // 库存价值（копейки）
		StockCount    int    `json:"stockCount"`    // 库存数量
	} `json:"stockByWarehouse"` // 按仓库库存数据
}

// StockReportTableGroupsRequest 库存报告分组数据请求
type StockReportTableGroupsRequest struct {
	Period       Period   `json:"period"`                 // 时间周期
	GroupBy      string   `json:"groupBy"`                // 分组方式：brand, object, tag, warehouse
	BrandNames   []string `json:"brandNames,omitempty"`   // 品牌名称列表
	ObjectIDs    []int    `json:"objectIDs,omitempty"`    // 商品类目ID列表
	TagIDs       []int    `json:"tagIDs,omitempty"`       // 标签ID列表
	NMIDs        []int    `json:"nmIDs,omitempty"`        // 商品ID列表
	WarehouseIDs []int    `json:"warehouseIds,omitempty"` // 仓库ID列表
	Page         int      `json:"page"`                   // 页码
	Limit        int      `json:"limit"`                  // 每页数量
}

// StockReportTableGroupsResponse 库存报告分组数据响应
type StockReportTableGroupsResponse struct {
	Data             StockReportTableGroupsData `json:"data"`      // 数据
	Error            bool                       `json:"error"`     // 错误标志
	ErrorText        string                     `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// StockReportTableGroupsData 库存报告分组数据
type StockReportTableGroupsData struct {
	Page       int                     `json:"page"`       // 页码
	IsNextPage bool                    `json:"isNextPage"` // 是否有下一页
	Groups     []StockReportTableGroup `json:"groups"`     // 分组数据列表
}

// StockReportTableGroup 库存报告分组
type StockReportTableGroup struct {
	GroupKey     string  `json:"groupKey"`     // 分组键
	GroupName    string  `json:"groupName"`    // 分组名称
	StockValue   int     `json:"stockValue"`   // 库存价值（копейки）
	StockCount   int     `json:"stockCount"`   // 库存数量
	AvgStockDays float64 `json:"avgStockDays"` // 平均库存天数
	TurnoverRate float64 `json:"turnoverRate"` // 周转率
}

// StockReportTableDetailsRequest 库存报告详细数据请求
type StockReportTableDetailsRequest struct {
	Period       Period   `json:"period"`                 // 时间周期
	GroupKey     string   `json:"groupKey"`               // 分组键
	BrandNames   []string `json:"brandNames,omitempty"`   // 品牌名称列表
	ObjectIDs    []int    `json:"objectIDs,omitempty"`    // 商品类目ID列表
	TagIDs       []int    `json:"tagIDs,omitempty"`       // 标签ID列表
	NMIDs        []int    `json:"nmIDs,omitempty"`        // 商品ID列表
	WarehouseIDs []int    `json:"warehouseIds,omitempty"` // 仓库ID列表
	Page         int      `json:"page"`                   // 页码
	Limit        int      `json:"limit"`                  // 每页数量
}

// StockReportTableDetailsResponse 库存报告详细数据响应
type StockReportTableDetailsResponse struct {
	Data             StockReportTableDetailsData `json:"data"`      // 数据
	Error            bool                        `json:"error"`     // 错误标志
	ErrorText        string                      `json:"errorText"` // 错误信息
	AdditionalErrors []struct {
		Field       string `json:"field"`       // 错误字段
		Description string `json:"description"` // 错误描述
	} `json:"additionalErrors"` // 附加错误
}

// StockReportTableDetailsData 库存报告详细数据
type StockReportTableDetailsData struct {
	Page       int                      `json:"page"`       // 页码
	IsNextPage bool                     `json:"isNextPage"` // 是否有下一页
	Details    []StockReportTableDetail `json:"details"`    // 详细数据列表
}

// StockReportTableDetail 库存报告详细项
type StockReportTableDetail struct {
	NMID          int     `json:"nmId"`          // 商品ID
	VendorCode    string  `json:"vendorCode"`    // 商家编码
	BrandName     string  `json:"brandName"`     // 品牌名称
	ProductName   string  `json:"productName"`   // 商品名称
	WarehouseID   int     `json:"warehouseId"`   // 仓库ID
	WarehouseName string  `json:"warehouseName"` // 仓库名称
	StockValue    int     `json:"stockValue"`    // 库存价值（копейки）
	StockCount    int     `json:"stockCount"`    // 库存数量
	StockDays     int     `json:"stockDays"`     // 库存天数
	TurnoverRate  float64 `json:"turnoverRate"`  // 周转率
	LastSaleDate  string  `json:"lastSaleDate"`  // 最后销售日期
}
