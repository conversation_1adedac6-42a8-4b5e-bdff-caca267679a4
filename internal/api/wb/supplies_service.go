package wbapi

import (
	"context"
	"fmt"

	"github.com/go-resty/resty/v2"
)

// SuppliesService 供应服务
type SuppliesService struct {
	client      *resty.Client
	rateLimiter *RateLimiter
}

// newSuppliesService 创建新的供应服务
func newSuppliesService(c *Client) *SuppliesService {
	httpClient := resty.New()

	if c.httpClient != nil {
		httpClient.SetTimeout(c.httpClient.GetClient().Timeout)
		for k, v := range c.httpClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &SuppliesService{
		client:      httpClient,
		rateLimiter: NewRateLimiter(),
	}
}

// GetSupplies 获取供货单列表
func (s *SuppliesService) GetSupplies(limit, next int) (*SuppliesResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result SuppliesResponse
	resp, err := s.client.R().
		SetQueryParam("limit", fmt.Sprintf("%d", limit)).
		SetQueryParam("next", fmt.Sprintf("%d", next)).
		SetResult(&result).
		Get(suppliesAPIURL + "/api/v3/supplies")

	if err != nil {
		return nil, fmt.Errorf("获取供货单列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取供货单列表失败: %s", resp.String())
	}

	return &result, nil
}

// CreateSupply 创建供货单
func (s *SuppliesService) CreateSupply(name string) (*CreateSupplyResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CreateSupplyResponse
	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"name": name,
		}).
		SetResult(&result).
		Post(suppliesAPIURL + "/api/v3/supplies")

	if err != nil {
		return nil, fmt.Errorf("创建供货单失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("创建供货单失败: %s", resp.String())
	}

	return &result, nil
}

// GetSupplyDetails 获取供货单详情
func (s *SuppliesService) GetSupplyDetails(supplyID string) (*SupplyDetails, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result SupplyDetails
	resp, err := s.client.R().
		SetResult(&result).
		Get(suppliesAPIURL + "/api/v3/supplies/" + supplyID)

	if err != nil {
		return nil, fmt.Errorf("获取供货单详情失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取供货单详情失败: %s", resp.String())
	}

	return &result, nil
}

// AddOrdersToSupply 向供货单添加订单
func (s *SuppliesService) AddOrdersToSupply(supplyID string, orders []int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"orders": orders,
		}).
		Put(suppliesAPIURL + "/api/v3/supplies/" + supplyID)

	if err != nil {
		return fmt.Errorf("添加订单到供货单失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("添加订单到供货单失败: %s", resp.String())
	}

	return nil
}

// CloseSupply 关闭供货单
func (s *SuppliesService) CloseSupply(supplyID string) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		Patch(suppliesAPIURL + "/api/v3/supplies/" + supplyID + "/close")

	if err != nil {
		return fmt.Errorf("关闭供货单失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("关闭供货单失败: %s", resp.String())
	}

	return nil
}

// GetSupplyOrders 获取供货单中的订单
func (s *SuppliesService) GetSupplyOrders(supplyID string) (*SupplyOrdersResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result SupplyOrdersResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(suppliesAPIURL + "/api/v3/supplies/" + supplyID + "/orders")

	if err != nil {
		return nil, fmt.Errorf("获取供货单订单失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取供货单订单失败: %s", resp.String())
	}

	return &result, nil
}

// DeleteSupply 删除供货单
func (s *SuppliesService) DeleteSupply(supplyID string) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		Delete(suppliesAPIURL + "/api/v3/supplies/" + supplyID)

	if err != nil {
		return fmt.Errorf("删除供货单失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("删除供货单失败: %s", resp.String())
	}

	return nil
}
