package wbapi

import (
	"context"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

// FinanceService 财务服务
type FinanceService struct {
	client      *resty.Client
	rateLimiter *RateLimiter
}

// newFinanceService 创建新的财务服务
func newFinanceService(c *Client) *FinanceService {
	httpClient := resty.New()

	if c.httpClient != nil {
		httpClient.SetTimeout(c.httpClient.GetClient().Timeout)
		for k, v := range c.httpClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &FinanceService{
		client:      httpClient,
		rateLimiter: NewRateLimiter(),
	}
}

// GetReportDetailByPeriod 获取按周期详细报告
func (s *FinanceService) GetReportDetailByPeriod(dateFrom, dateTo time.Time, limit, rrdID int) (*ReportDetailResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ReportDetailResponse
	resp, err := s.client.R().
		SetQueryParam("dateFrom", dateFrom.Format("2006-01-02")).
		SetQueryParam("dateTo", dateTo.Format("2006-01-02")).
		SetQueryParam("limit", fmt.Sprintf("%d", limit)).
		SetQueryParam("rrdid", fmt.Sprintf("%d", rrdID)).
		SetResult(&result).
		Get(financeAPIURL + "/api/v5/supplier/reportDetailByPeriod")

	if err != nil {
		return nil, fmt.Errorf("获取详细报告失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取详细报告失败: %s", resp.String())
	}

	return &result, nil
}

// GetExciseReport 获取消费税报告
func (s *FinanceService) GetExciseReport(dateFrom, dateTo time.Time) (*ExciseReportResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ExciseReportResponse
	resp, err := s.client.R().
		SetQueryParam("dateFrom", dateFrom.Format("2006-01-02")).
		SetQueryParam("dateTo", dateTo.Format("2006-01-02")).
		SetResult(&result).
		Get(financeAPIURL + "/api/v1/supplier/excise-report")

	if err != nil {
		return nil, fmt.Errorf("获取消费税报告失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取消费税报告失败: %s", resp.String())
	}

	return &result, nil
}

// GetRealizationReport 获取实现报告
func (s *FinanceService) GetRealizationReport(dateFrom, dateTo time.Time, limit, rrdID int) (*RealizationReportResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result RealizationReportResponse
	resp, err := s.client.R().
		SetQueryParam("dateFrom", dateFrom.Format("2006-01-02")).
		SetQueryParam("dateTo", dateTo.Format("2006-01-02")).
		SetQueryParam("limit", fmt.Sprintf("%d", limit)).
		SetQueryParam("rrdid", fmt.Sprintf("%d", rrdID)).
		SetResult(&result).
		Get(financeAPIURL + "/api/v5/supplier/reportDetailByPeriod")

	if err != nil {
		return nil, fmt.Errorf("获取实现报告失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取实现报告失败: %s", resp.String())
	}

	return &result, nil
}

// GetCommissionReport 获取佣金报告
func (s *FinanceService) GetCommissionReport(limit, rrdID int) (*CommissionReportResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CommissionReportResponse
	resp, err := s.client.R().
		SetQueryParam("limit", fmt.Sprintf("%d", limit)).
		SetQueryParam("rrdid", fmt.Sprintf("%d", rrdID)).
		SetResult(&result).
		Get(financeAPIURL + "/api/v1/supplier/commission-report")

	if err != nil {
		return nil, fmt.Errorf("获取佣金报告失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取佣金报告失败: %s", resp.String())
	}

	return &result, nil
}

// GetBalance 获取账户余额
func (s *FinanceService) GetBalance() (*BalanceResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result BalanceResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(financeAPIURL + "/api/v1/supplier/balance")

	if err != nil {
		return nil, fmt.Errorf("获取账户余额失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取账户余额失败: %s", resp.String())
	}

	return &result, nil
}
