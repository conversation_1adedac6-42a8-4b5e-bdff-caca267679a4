package wbapi

import "time"

// ===== 详细报告相关模型 =====

// ReportDetailResponse 详细报告响应
type ReportDetailResponse struct {
	Data []ReportDetailItem `json:"data"` // 报告数据
}

// ReportDetailItem 报告详细项目
type ReportDetailItem struct {
	RealizationReportID      int       `json:"realizationreport_id"`      // 实现报告ID
	DateFrom                 time.Time `json:"date_from"`                 // 开始日期
	DateTo                   time.Time `json:"date_to"`                   // 结束日期
	CreateDT                 time.Time `json:"create_dt"`                 // 创建日期
	SupplierContractCode     string    `json:"suppliercontract_code"`     // 供应商合同代码
	RRDID                    int       `json:"rrd_id"`                    // RRD ID
	GIID                     int       `json:"gi_id"`                     // GI ID
	SubjectName              string    `json:"subject_name"`              // 主题名称
	NMID                     int       `json:"nm_id"`                     // 商品ID
	BrandName                string    `json:"brand_name"`                // 品牌名称
	VendorCode               string    `json:"vendor_code"`               // 供应商代码
	Size                     string    `json:"size"`                      // 尺寸
	Barcode                  string    `json:"barcode"`                   // 条形码
	DocTypeName              string    `json:"doc_type_name"`             // 文档类型名称
	Quantity                 int       `json:"quantity"`                  // 数量
	RetailPrice              float64   `json:"retail_price"`              // 零售价
	RetailAmount             float64   `json:"retail_amount"`             // 零售金额
	SalePercent              int       `json:"sale_percent"`              // 销售百分比
	CommissionPercent        float64   `json:"commission_percent"`        // 佣金百分比
	OfficeName               string    `json:"office_name"`               // 办公室名称
	SupplierOperName         string    `json:"supplier_oper_name"`        // 供应商操作名称
	OrderDT                  time.Time `json:"order_dt"`                  // 订单日期
	SaleDT                   time.Time `json:"sale_dt"`                   // 销售日期
	RRDT                     time.Time `json:"rr_dt"`                     // RR日期
	SHKID                    int       `json:"shk_id"`                    // SHK ID
	RetailPriceWithDiscRub   float64   `json:"retail_price_withdisc_rub"` // 折扣零售价
	DeliveryAmount           int       `json:"delivery_amount"`           // 配送金额
	ReturnAmount             int       `json:"return_amount"`             // 退货金额
	DeliveryRub              float64   `json:"delivery_rub"`              // 配送费用
	GIBoxTypeName            string    `json:"gi_box_type_name"`          // GI盒子类型名称
	ProductDiscountForReport float64   `json:"product_discount_for_report"` // 产品报告折扣
	SupplierPromo            float64   `json:"supplier_promo"`            // 供应商促销
	RID                      int       `json:"rid"`                       // RID
	PPVZSppPrc               float64   `json:"ppvz_spp_prc"`              // PPVZ SPP百分比
	PPVZKvwPrcBase           float64   `json:"ppvz_kvw_prc_base"`         // PPVZ KVW基础百分比
	PPVZKvwPrc               float64   `json:"ppvz_kvw_prc"`              // PPVZ KVW百分比
	SupplierID               int       `json:"supplier_id"`               // 供应商ID
	PPVZRewardBase           float64   `json:"ppvz_reward_base"`          // PPVZ基础奖励
	PPVZReward               float64   `json:"ppvz_reward"`               // PPVZ奖励
	AcquiringFee             float64   `json:"acquiring_fee"`             // 收单费
	AcquiringBank            string    `json:"acquiring_bank"`            // 收单银行
	PPVZVW                   float64   `json:"ppvz_vw"`                   // PPVZ VW
	PPVZVWNds                float64   `json:"ppvz_vw_nds"`               // PPVZ VW NDS
	PPVZOfficeID             int       `json:"ppvz_office_id"`            // PPVZ办公室ID
	PPVZOfficeName           string    `json:"ppvz_office_name"`          // PPVZ办公室名称
	PPVZSupplierID           int       `json:"ppvz_supplier_id"`          // PPVZ供应商ID
	PPVZSupplierName         string    `json:"ppvz_supplier_name"`        // PPVZ供应商名称
	PPVZInn                  string    `json:"ppvz_inn"`                  // PPVZ INN
	DeclarationNumber        string    `json:"declaration_number"`        // 申报号
	BonusTypeName            string    `json:"bonus_type_name"`           // 奖金类型名称
	StickerID                string    `json:"sticker_id"`                // 贴纸ID
	SiteCountry              string    `json:"site_country"`              // 网站国家
	Penalty                  float64   `json:"penalty"`                   // 罚款金额
	AdditionalPayment        float64   `json:"additional_payment"`        // 附加付款
	RebillLogisticCost       float64   `json:"rebill_logistic_cost"`      // 重新计费物流成本
	RebillLogisticOrg        string    `json:"rebill_logistic_org"`       // 重新计费物流组织
	Kiz                      string    `json:"kiz"`                       // KIZ
	StorageFee               float64   `json:"storage_fee"`               // 存储费
	Deduction                float64   `json:"deduction"`                 // 扣除
	Acceptance               float64   `json:"acceptance"`                // 验收
	SRID                     string    `json:"srid"`                      // SR ID
}

// ===== 消费税报告相关模型 =====

// ExciseReportResponse 消费税报告响应
type ExciseReportResponse struct {
	Data []ExciseReportItem `json:"data"` // 消费税报告数据
}

// ExciseReportItem 消费税报告项目
type ExciseReportItem struct {
	FinishedPrice float64   `json:"finishedPrice"` // 完成价格
	OperationTypeID int     `json:"operationTypeId"` // 操作类型ID
	FiscalDT      time.Time `json:"fiscalDt"`      // 财政日期
	DocNumber     string    `json:"docNumber"`     // 文档号
	FNUMBER       string    `json:"fnumber"`       // F号码
	RegNumber     string    `json:"regNumber"`     // 注册号
	ExciseBase    float64   `json:"exciseBase"`    // 消费税基数
	ExcisePercent float64   `json:"excisePercent"` // 消费税百分比
	ExciseAmount  float64   `json:"exciseAmount"`  // 消费税金额
	InvoiceNumber string    `json:"invoiceNumber"` // 发票号
	BrandName     string    `json:"brandName"`     // 品牌名称
	VendorCode    string    `json:"vendorCode"`    // 供应商代码
}

// ===== 实现报告相关模型 =====

// RealizationReportResponse 实现报告响应
type RealizationReportResponse struct {
	Data []RealizationReportItem `json:"data"` // 实现报告数据
}

// RealizationReportItem 实现报告项目
type RealizationReportItem struct {
	RRDID                    int       `json:"rrd_id"`                    // RRD ID
	GIID                     int       `json:"gi_id"`                     // GI ID
	SubjectName              string    `json:"subject_name"`              // 主题名称
	NMID                     int       `json:"nm_id"`                     // 商品ID
	BrandName                string    `json:"brand_name"`                // 品牌名称
	VendorCode               string    `json:"vendor_code"`               // 供应商代码
	Size                     string    `json:"size"`                      // 尺寸
	Barcode                  string    `json:"barcode"`                   // 条形码
	DocTypeName              string    `json:"doc_type_name"`             // 文档类型名称
	Quantity                 int       `json:"quantity"`                  // 数量
	RetailPrice              float64   `json:"retail_price"`              // 零售价
	RetailAmount             float64   `json:"retail_amount"`             // 零售金额
	SalePercent              int       `json:"sale_percent"`              // 销售百分比
	CommissionPercent        float64   `json:"commission_percent"`        // 佣金百分比
	OfficeName               string    `json:"office_name"`               // 办公室名称
	SupplierOperName         string    `json:"supplier_oper_name"`        // 供应商操作名称
	OrderDT                  time.Time `json:"order_dt"`                  // 订单日期
	SaleDT                   time.Time `json:"sale_dt"`                   // 销售日期
	RRDT                     time.Time `json:"rr_dt"`                     // RR日期
	SHKID                    int       `json:"shk_id"`                    // SHK ID
	RetailPriceWithDiscRub   float64   `json:"retail_price_withdisc_rub"` // 折扣零售价
	ForPay                   float64   `json:"for_pay"`                   // 应付金额
	ForPayNds                float64   `json:"for_pay_nds"`               // 应付NDS
}

// ===== 佣金报告相关模型 =====

// CommissionReportResponse 佣金报告响应
type CommissionReportResponse struct {
	Data []CommissionReportItem `json:"data"` // 佣金报告数据
}

// CommissionReportItem 佣金报告项目
type CommissionReportItem struct {
	NMID              int     `json:"nm_id"`              // 商品ID
	SubjectName       string  `json:"subject_name"`       // 主题名称
	BrandName         string  `json:"brand_name"`         // 品牌名称
	VendorCode        string  `json:"vendor_code"`        // 供应商代码
	CommissionPercent float64 `json:"commission_percent"` // 佣金百分比
	FBS               float64 `json:"fbs"`                // FBS佣金
	FBO               float64 `json:"fbo"`                // FBO佣金
}

// ===== 余额相关模型 =====

// BalanceResponse 余额响应
type BalanceResponse struct {
	Balance float64 `json:"balance"` // 账户余额
}
