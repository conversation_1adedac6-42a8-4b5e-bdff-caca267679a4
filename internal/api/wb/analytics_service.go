package wbapi

import (
	"context"
	"fmt"
	"strconv"

	"github.com/go-resty/resty/v2"
)

// 使用 constants.go 中定义的 URL 常量

// AnalyticsService 分析服务
type AnalyticsService struct {
	client      *resty.Client
	rateLimiter *RateLimiter
}

// newAnalyticsService 创建新的分析服务
func newAnalyticsService(c *Client) *AnalyticsService {
	// 使用主客户端，不设置 BaseURL，通过完整 URL 访问不同端点
	httpClient := resty.New()
	if c.httpClient != nil {
		httpClient.SetTimeout(c.httpClient.GetClient().Timeout)
		for k, v := range c.httpClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &AnalyticsService{
		client:      httpClient,
		rateLimiter: NewRateLimiter(),
	}
}

// ===== 推广统计方法 =====

// GetFullStats 获取广告活动完整统计
func (s *AnalyticsService) GetFullStats(req *FullStatsRequest) (*FullStatsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result FullStatsResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(advertAPIURL + "/adv/v2/fullstats")

	if err != nil {
		return nil, fmt.Errorf("获取完整统计失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取完整统计失败: %s", resp.String())
	}

	return &result, nil
}

// GetAutoStatWords 获取自动广告词组统计
func (s *AnalyticsService) GetAutoStatWords(campaignID int) (*AutoStatWordsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result AutoStatWordsResponse
	resp, err := s.client.R().
		SetQueryParam("id", strconv.Itoa(campaignID)).
		SetResult(&result).
		Get(advertAPIURL + "/adv/v2/auto/stat-words")

	if err != nil {
		return nil, fmt.Errorf("获取自动广告词组统计失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取自动广告词组统计失败: %s", resp.String())
	}

	return &result, nil
}

// GetStatWords 获取搜索广告词组统计
func (s *AnalyticsService) GetStatWords(campaignID int) (*StatWordsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result StatWordsResponse
	resp, err := s.client.R().
		SetQueryParam("id", strconv.Itoa(campaignID)).
		SetResult(&result).
		Get(advertAPIURL + "/adv/v1/stat/words")

	if err != nil {
		return nil, fmt.Errorf("获取搜索广告词组统计失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取搜索广告词组统计失败: %s", resp.String())
	}

	return &result, nil
}

// GetKeywordsStats 获取关键词统计
func (s *AnalyticsService) GetKeywordsStats(advertID int, from, to string) (*KeywordsStatsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result KeywordsStatsResponse
	resp, err := s.client.R().
		SetQueryParam("advert_id", strconv.Itoa(advertID)).
		SetQueryParam("from", from).
		SetQueryParam("to", to).
		SetResult(&result).
		Get(advertAPIURL + "/adv/v0/stats/keywords")

	if err != nil {
		return nil, fmt.Errorf("获取关键词统计失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取关键词统计失败: %s", resp.String())
	}

	return &result, nil
}

// ===== 销售漏斗方法 =====

// GetNMReportDetail 获取商品卡片详细报告
func (s *AnalyticsService) GetNMReportDetail(req *NMReportDetailRequest) (*NMReportDetailResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result NMReportDetailResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/nm-report/detail")

	if err != nil {
		return nil, fmt.Errorf("获取商品卡片详细报告失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取商品卡片详细报告失败: %s", resp.String())
	}

	return &result, nil
}

// ===== 媒体广告统计方法 =====

// GetMediaStats 获取媒体广告统计
func (s *AnalyticsService) GetMediaStats(req *MediaStatsRequest) (*MediaStatsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result MediaStatsResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post("https://advert-media-api.wildberries.ru/adv/v1/stats")

	if err != nil {
		return nil, fmt.Errorf("获取媒体广告统计失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取媒体广告统计失败: %s", resp.String())
	}

	return &result, nil
}

// ===== 销售漏斗历史数据方法 =====

// GetNMReportDetailHistory 获取商品卡片历史详细报告
func (s *AnalyticsService) GetNMReportDetailHistory(req *NMReportDetailHistoryRequest) (*NMReportDetailHistoryResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result NMReportDetailHistoryResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/nm-report/detail/history")

	if err != nil {
		return nil, fmt.Errorf("获取商品卡片历史详细报告失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取商品卡片历史详细报告失败: %s", resp.String())
	}

	return &result, nil
}

// GetNMReportGroupedHistory 获取分组商品卡片历史报告
func (s *AnalyticsService) GetNMReportGroupedHistory(req *NMReportGroupedHistoryRequest) (*NMReportGroupedHistoryResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result NMReportGroupedHistoryResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/nm-report/grouped/history")

	if err != nil {
		return nil, fmt.Errorf("获取分组商品卡片历史报告失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取分组商品卡片历史报告失败: %s", resp.String())
	}

	return &result, nil
}

// ===== 搜索报告方法 =====

// GetSearchReport 获取搜索报告主页数据
func (s *AnalyticsService) GetSearchReport(req *SearchReportRequest) (*SearchReportResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result SearchReportResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/search-report/report")

	if err != nil {
		return nil, fmt.Errorf("获取搜索报告主页数据失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取搜索报告主页数据失败: %s", resp.String())
	}

	return &result, nil
}

// GetSearchReportTableGroups 获取搜索报告分组数据
func (s *AnalyticsService) GetSearchReportTableGroups(req *SearchReportTableGroupsRequest) (*SearchReportTableGroupsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result SearchReportTableGroupsResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/search-report/table/groups")

	if err != nil {
		return nil, fmt.Errorf("获取搜索报告分组数据失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取搜索报告分组数据失败: %s", resp.String())
	}

	return &result, nil
}

// GetSearchReportTableDetails 获取搜索报告详细数据
func (s *AnalyticsService) GetSearchReportTableDetails(req *SearchReportTableDetailsRequest) (*SearchReportTableDetailsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result SearchReportTableDetailsResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/search-report/table/details")

	if err != nil {
		return nil, fmt.Errorf("获取搜索报告详细数据失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取搜索报告详细数据失败: %s", resp.String())
	}

	return &result, nil
}

// GetProductSearchTexts 获取商品搜索文本
func (s *AnalyticsService) GetProductSearchTexts(req *ProductSearchTextsRequest) (*ProductSearchTextsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ProductSearchTextsResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/search-report/product/search-texts")

	if err != nil {
		return nil, fmt.Errorf("获取商品搜索文本失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取商品搜索文本失败: %s", resp.String())
	}

	return &result, nil
}

// GetProductOrders 获取商品订单数据
func (s *AnalyticsService) GetProductOrders(req *ProductOrdersRequest) (*ProductOrdersResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ProductOrdersResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/search-report/product/orders")

	if err != nil {
		return nil, fmt.Errorf("获取商品订单数据失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取商品订单数据失败: %s", resp.String())
	}

	return &result, nil
}

// ===== 库存报告方法 =====

// GetStockReport 获取库存报告
func (s *AnalyticsService) GetStockReport(req *StockReportRequest) (*StockReportResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result StockReportResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/stock-report/report")

	if err != nil {
		return nil, fmt.Errorf("获取库存报告失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取库存报告失败: %s", resp.String())
	}

	return &result, nil
}

// GetStockReportTableGroups 获取库存报告分组数据
func (s *AnalyticsService) GetStockReportTableGroups(req *StockReportTableGroupsRequest) (*StockReportTableGroupsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result StockReportTableGroupsResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/stock-report/table/groups")

	if err != nil {
		return nil, fmt.Errorf("获取库存报告分组数据失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取库存报告分组数据失败: %s", resp.String())
	}

	return &result, nil
}

// GetStockReportTableDetails 获取库存报告详细数据
func (s *AnalyticsService) GetStockReportTableDetails(req *StockReportTableDetailsRequest) (*StockReportTableDetailsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result StockReportTableDetailsResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(sellerAnalyticsAPIURL + "/api/v2/stock-report/table/details")

	if err != nil {
		return nil, fmt.Errorf("获取库存报告详细数据失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取库存报告详细数据失败: %s", resp.String())
	}

	return &result, nil
}

// ===== CSV 报告下载方法 =====

// DownloadNMReportDetailCSV 下载商品卡片详细报告 CSV
func (s *AnalyticsService) DownloadNMReportDetailCSV(req *NMReportDetailRequest) ([]byte, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(req).
		Post(sellerAnalyticsAPIURL + "/api/v2/nm-report/detail/csv")

	if err != nil {
		return nil, fmt.Errorf("下载商品卡片详细报告CSV失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("下载商品卡片详细报告CSV失败: %s", resp.String())
	}

	return resp.Body(), nil
}

// DownloadSearchReportCSV 下载搜索报告 CSV
func (s *AnalyticsService) DownloadSearchReportCSV(req *SearchReportRequest) ([]byte, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(req).
		Post(sellerAnalyticsAPIURL + "/api/v2/search-report/csv")

	if err != nil {
		return nil, fmt.Errorf("下载搜索报告CSV失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("下载搜索报告CSV失败: %s", resp.String())
	}

	return resp.Body(), nil
}

// DownloadStockReportCSV 下载库存报告 CSV
func (s *AnalyticsService) DownloadStockReportCSV(req *StockReportRequest) ([]byte, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(req).
		Post(sellerAnalyticsAPIURL + "/api/v2/stock-report/csv")

	if err != nil {
		return nil, fmt.Errorf("下载库存报告CSV失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("下载库存报告CSV失败: %s", resp.String())
	}

	return resp.Body(), nil
}
