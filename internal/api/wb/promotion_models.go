package wbapi

import "time"

// Campaign 广告活动
type Campaign struct {
	AdvertID      int       `json:"advertId"`      // 广告活动ID
	Type          int       `json:"type"`          // 广告活动类型
	Status        int       `json:"status"`        // 广告活动状态
	Name          string    `json:"name"`          // 广告活动名称
	CreateTime    time.Time `json:"createTime"`    // 创建时间
	ChangeTime    time.Time `json:"changeTime"`    // 修改时间
	StartTime     time.Time `json:"startTime"`     // 开始时间
	EndTime       time.Time `json:"endTime"`       // 结束时间
	DailyBudget   int       `json:"dailyBudget"`   // 日预算
	SearchPlusBid int       `json:"searchPlusBid"` // 搜索加价
}

// CampaignList 广告活动列表响应
type CampaignList struct {
	Adverts []Campaign `json:"adverts"`
}

// BidUpdate 竞价更新
type BidUpdate struct {
	CampaignID int `json:"campaignId"` // 广告活动ID
	ProductID  int `json:"productId"`  // 商品ID
	Bid        int `json:"bid"`        // 竞价
}

// StatisticsRequest 统计数据请求
type StatisticsRequest struct {
	CampaignIDs []int  `json:"campaignIds"` // 广告活动ID列表
	DateFrom    string `json:"dateFrom"`    // 开始日期
	DateTo      string `json:"dateTo"`      // 结束日期
}

// CampaignStatistics 广告活动统计
type CampaignStatistics struct {
	CampaignID int     `json:"campaignId"` // 广告活动ID
	Views      int     `json:"views"`      // 展示次数
	Clicks     int     `json:"clicks"`     // 点击次数
	CTR        float64 `json:"ctr"`        // 点击率
	CPC        float64 `json:"cpc"`        // 平均点击成本
	Sum        float64 `json:"sum"`        // 花费
	Orders     int     `json:"orders"`     // 订单数
	CR         float64 `json:"cr"`         // 转化率
	Shks       int     `json:"shks"`       // 订购商品数
	SumPrice   float64 `json:"sumPrice"`   // 订单金额
}

// Balance 余额信息
type Balance struct {
	Wallet int `json:"wallet"` // 钱包余额
	Bonus  int `json:"bonus"`  // 奖励余额
}

// MinusPhraseRequest 否定关键词请求
type MinusPhraseRequest struct {
	CampaignID int      `json:"campaignId"` // 广告活动ID
	Phrases    []string `json:"phrases"`    // 否定关键词列表
}

// AutoCampaignExcluded 自动广告活动排除词
type AutoCampaignExcluded struct {
	CampaignID int      `json:"campaignId"` // 广告活动ID
	Excluded   []string `json:"excluded"`   // 排除词列表
}

// ===== 基础配置和响应结构 =====

// CampaignCount 活动列表响应
type CampaignCount struct {
	Adverts []struct {
		Type       int `json:"type"`
		Status     int `json:"status"`
		Count      int `json:"count"`
		AdvertList []struct {
			AdvertID   int       `json:"advertId"`
			ChangeTime time.Time `json:"changeTime"`
		} `json:"advert_list"`
	} `json:"adverts"`
	All int `json:"all"`
}

// Config 推广配置响应
type Config struct {
	Categories []struct {
		ID     int    `json:"id"`
		Name   string `json:"name"`
		CPMMin int    `json:"cpm_min"`
	} `json:"categories"`
	Config []struct {
		Description string `json:"description"`
		Name        string `json:"name"`
		Value       string `json:"value"`
	} `json:"config"`
}

// NomenclatureItem 商品信息
type NomenclatureItem struct {
	Title     string `json:"title"`     // 商品名称
	NM        int    `json:"nm"`        // WB商品编号
	SubjectID int    `json:"subjectId"` // 类目ID
}

// ===== 广告活动创建请求 =====

// AutoCampaignRequest 自动广告活动创建请求
type AutoCampaignRequest struct {
	Type      int    `json:"type"`      // 活动类型，固定为 8
	Name      string `json:"name"`      // 活动名称（最大128字符）
	SubjectID int    `json:"subjectId"` // 创建活动的商品ID
	Sum       int    `json:"sum"`       // 充值金额
	BType     int    `json:"btype"`     // 扣费类型
	OnPause   bool   `json:"on_pause"`  // 创建后是否暂停
	NMS       []int  `json:"nms"`       // WB商品编号数组，最多100个
	CPM       int    `json:"cpm"`       // 出价
}

// AuctionCampaignRequest 竞价广告活动创建请求
type AuctionCampaignRequest struct {
	Name          string `json:"name"`          // 活动名称
	SubjectID     int    `json:"subjectId"`     // 商品ID
	Sum           int    `json:"sum"`           // 充值金额
	BType         int    `json:"btype"`         // 扣费类型
	AutoParams    bool   `json:"autoParams"`    // 是否自动设置参数
	SearchPromot  bool   `json:"searchPromot"`  // 是否在搜索结果中推广
	CatalogPromot bool   `json:"catalogPromot"` // 是否在目录中推广
	CardsPromot   bool   `json:"cardsPromot"`   // 是否在商品卡片中推广
	NMS           []int  `json:"nms"`           // WB商品编号数组
	CPM           int    `json:"cpm"`           // 出价
}

// ===== 出价和竞价相关 =====

// BidItem 出价信息
type BidItem struct {
	AdvertID int               `json:"advert_id"` // 广告活动ID
	NmBids   []AuctionMultibid `json:"nm_bids"`   // 商品出价列表
}

// AuctionMultibid 竞价出价信息
type AuctionMultibid struct {
	NM  int `json:"nm"`  // 商品ID
	Bid int `json:"bid"` // 商品出价（每千次展现费用）
}

// ===== 查询参数和过滤器 =====

// CampaignQueryParams 活动查询参数
type CampaignQueryParams struct {
	Status    *int    `url:"status,omitempty"`    // 活动状态
	Type      *int    `url:"type,omitempty"`      // 活动类型
	Order     *string `url:"order,omitempty"`     // 排序字段：create(创建时间)、change(修改时间)、id(活动ID)
	Direction *string `url:"direction,omitempty"` // 排序方向：desc(降序)、asc(升序)
}

// CampaignSubject 活动类目信息
type CampaignSubject struct {
	ID          int    `json:"id"`          // 类目ID
	Name        string `json:"name"`        // 类目名称
	Status      int    `json:"status"`      // 状态
	SubjectID   int    `json:"subjectId"`   // 商品类目ID
	SubjectName string `json:"subjectName"` // 商品类目名称
}

// ===== 广告参数配置 =====

// AutoParams 自动广告参数
type AutoParams struct {
	CPM     int `json:"cpm"` // 基础出价（每千次展现费用）
	Subject struct {
		Name string `json:"name"` // 类目名称
		ID   int    `json:"id"`   // 类目ID
	} `json:"subject"` // 投放类目信息
	NMS    []int `json:"nms"` // 参与推广的商品ID列表
	Active struct {
		Carousel bool `json:"carousel"` // 是否开启轮播推广
		Recom    bool `json:"recom"`    // 是否开启相似推荐
		Booster  bool `json:"booster"`  // 是否开启推广加速
	} `json:"active"` // 推广位置设置
	NMCPM []struct {
		NM  int `json:"nm"`  // 商品ID
		CPM int `json:"cpm"` // 该商品的独立出价
	} `json:"nmCPM"` // 商品单独出价列表
}

// UnitedParam 竞价广告参数
type UnitedParam struct {
	CatalogCPM int `json:"catalogCPM"` // 目录页展示出价
	SearchCPM  int `json:"searchCPM"`  // 搜索页展示出价
	Subject    struct {
		ID   int    `json:"id"`   // 类目ID
		Name string `json:"name"` // 类目名称
	} `json:"subject"` // 投放类目信息
	NMS []int `json:"nms"` // 参与推广的商品ID列表
}

// ===== 活动详细信息 =====

// CampaignInfo 活动详细信息
type CampaignInfo struct {
	AdvertID         int                `json:"advertId"`          // 活动ID
	Name             string             `json:"name"`              // 活动名称
	Status           int                `json:"status"`            // 活动状态
	Type             int                `json:"type"`              // 活动类型
	StartTime        *time.Time         `json:"startTime"`         // 开始时间
	CreateTime       time.Time          `json:"createTime"`        // 创建时间
	ChangeTime       time.Time          `json:"changeTime"`        // 修改时间
	EndTime          *time.Time         `json:"endTime"`           // 结束时间
	Budget           float64            `json:"budget"`            // 预算
	Balance          float64            `json:"balance"`           // 余额
	Subjects         []CampaignSubject  `json:"subjects"`          // 活动类目信息
	Statistics       CampaignStatistics `json:"statistics"`        // 活动统计信息
	DailyBudget      *float64           `json:"dailyBudget"`       // 日预算
	AutoParams       *AutoParams        `json:"autoParams"`        // 自动广告参数
	SearchPromot     *bool              `json:"searchPromot"`      // 是否搜索推广
	CatalogPromot    *bool              `json:"catalogPromot"`     // 是否目录推广
	CardsPromot      *bool              `json:"cardsPromot"`       // 是否卡片推广
	UnitedParams     []UnitedParam      `json:"unitedParams"`      // 竞价广告参数
	AuctionMultibids []AuctionMultibid  `json:"auction_multibids"` // 竞价出价信息
}

// ===== 关键词管理 =====

// SearchKeywordRequest 搜索关键词请求
type SearchKeywordRequest struct {
	AdvertID int      `json:"advertId"` // 广告活动ID
	Pluse    []string `json:"pluse"`    // 加词列表（最多100个）
	Phrase   []string `json:"phrase"`   // 词组列表（最多1000个）
	Strong   []string `json:"strong"`   // 强匹配词列表（最多1000个）
	Excluded []string `json:"excluded"` // 排除词列表（最多1000个）
}

// AutoExcludeRequest 自动广告排除请求
type AutoExcludeRequest struct {
	AdvertID int   `json:"advertId"` // 广告活动ID
	NMS      []int `json:"nms"`      // 要排除的商品ID列表
}

// AutoUpdateNmRequest 更新自动广告商品请求
type AutoUpdateNmRequest struct {
	AdvertID int   `json:"advertId"` // 广告活动ID
	AddNms   []int `json:"addNms"`   // 要添加的商品ID列表
	DelNms   []int `json:"delNms"`   // 要删除的商品ID列表
}

// ===== 统计数据结构 =====

// StatsRequest 统计请求参数
type StatsRequest struct {
	AdvertID int      `json:"id"`              // 广告活动ID
	Dates    []string `json:"dates,omitempty"` // 指定具体日期列表，格式：YYYY-MM-DD
	Interval *struct {
		Begin string `json:"begin"` // 开始日期，格式：YYYY-MM-DD
		End   string `json:"end"`   // 结束日期，格式：YYYY-MM-DD
	} `json:"interval,omitempty"` // 时间区间
}

// StatsResponse 统计响应
type StatsResponse struct {
	Views    int        `json:"views"`
	Clicks   int        `json:"clicks"`
	Ctr      float64    `json:"ctr"`
	Cpc      float64    `json:"cpc"`
	Sum      float64    `json:"sum"`
	Orders   int        `json:"orders"`
	Cr       float64    `json:"cr"`
	Shks     int        `json:"shks"`
	SumPrice int        `json:"sum_price"`
	Dates    []string   `json:"dates"`
	Days     []StatsDay `json:"days"`
	AdvertId int        `json:"advertId"`
}

// StatsDay 日统计数据
type StatsDay struct {
	Date     time.Time  `json:"date"`
	Views    int        `json:"views"`
	Clicks   int        `json:"clicks"`
	Ctr      float64    `json:"ctr"`
	Cpc      float64    `json:"cpc"`
	Sum      float64    `json:"sum"`
	Orders   int        `json:"orders"`
	Cr       float64    `json:"cr"`
	Shks     int        `json:"shks"`
	SumPrice int        `json:"sum_price"`
	Apps     []StatsApp `json:"apps"`
}

// StatsApp 应用统计数据
type StatsApp struct {
	Views    int       `json:"views"`
	Clicks   int       `json:"clicks"`
	Ctr      float64   `json:"ctr"`
	Cpc      float64   `json:"cpc"`
	Sum      float64   `json:"sum"`
	Orders   int       `json:"orders"`
	Cr       float64   `json:"cr"`
	Shks     int       `json:"shks"`
	SumPrice int       `json:"sum_price"`
	Nm       []StatsNm `json:"nm"`
	AppType  int       `json:"appType"`
}

// StatsNm 商品统计数据
type StatsNm struct {
	Views    int     `json:"views"`
	Clicks   int     `json:"clicks"`
	Ctr      float64 `json:"ctr"`
	Cpc      float64 `json:"cpc"`
	Sum      float64 `json:"sum"`
	Orders   int     `json:"orders"`
	Cr       float64 `json:"cr"`
	Shks     int     `json:"shks"`
	SumPrice int     `json:"sum_price"`
	Name     string  `json:"name"`
	NmId     int     `json:"nmId"`
}

// ===== 财务管理相关模型 =====

// PromotionBalanceResponse 推广余额响应
type PromotionBalanceResponse struct {
	Wallet int `json:"wallet"` // 钱包余额（копейки）
	Bonus  int `json:"bonus"`  // 奖励余额（копейки）
}

// CampaignBudgetResponse 活动预算响应
type CampaignBudgetResponse struct {
	Budget  int `json:"budget"`  // 活动预算（копейки）
	Balance int `json:"balance"` // 活动余额（копейки）
}

// ExpenseHistoryResponse 支出历史响应
type ExpenseHistoryResponse struct {
	Data []ExpenseRecord `json:"data"` // 支出记录列表
}

// ExpenseRecord 支出记录
type ExpenseRecord struct {
	Date     time.Time `json:"date"` // 支出日期
	Sum      int       `json:"sum"`  // 支出金额（копейки）
	Type     string    `json:"type"` // 支出类型
	Campaign struct {
		ID   int    `json:"id"`   // 活动ID
		Name string `json:"name"` // 活动名称
	} `json:"campaign"` // 活动信息
}

// PaymentHistoryResponse 充值历史响应
type PaymentHistoryResponse struct {
	Data []PaymentRecord `json:"data"` // 充值记录列表
}

// PaymentRecord 充值记录
type PaymentRecord struct {
	Date   time.Time `json:"date"`   // 充值日期
	Sum    int       `json:"sum"`    // 充值金额（копейки）
	Status string    `json:"status"` // 充值状态
	Method string    `json:"method"` // 充值方式
}

// ===== 媒体广告相关模型 =====

// MediaCampaignQueryParams 媒体广告查询参数
type MediaCampaignQueryParams struct {
	Type   int `url:"type,omitempty"`   // 活动类型
	Status int `url:"status,omitempty"` // 活动状态
	Limit  int `url:"limit,omitempty"`  // 限制数量
	Offset int `url:"offset,omitempty"` // 偏移量
}

// MediaCampaignsResponse 媒体广告活动列表响应
type MediaCampaignsResponse struct {
	Data []MediaCampaign `json:"data"` // 媒体广告活动列表
}

// MediaCampaign 媒体广告活动
type MediaCampaign struct {
	ID         int       `json:"id"`         // 活动ID
	Name       string    `json:"name"`       // 活动名称
	Type       int       `json:"type"`       // 活动类型
	Status     int       `json:"status"`     // 活动状态
	CreateTime time.Time `json:"createTime"` // 创建时间
	UpdateTime time.Time `json:"updateTime"` // 更新时间
	Budget     int       `json:"budget"`     // 预算（копейки）
	Spent      int       `json:"spent"`      // 已花费（копейки）
}

// CreateMediaCampaignRequest 创建媒体广告活动请求
type CreateMediaCampaignRequest struct {
	Name           string `json:"name"`   // 活动名称
	Type           int    `json:"type"`   // 活动类型
	Budget         int    `json:"budget"` // 预算（копейки）
	TargetAudience struct {
		Gender   []int `json:"gender"` // 性别：1-男，2-女
		AgeRange struct {
			Min int `json:"min"` // 最小年龄
			Max int `json:"max"` // 最大年龄
		} `json:"ageRange"` // 年龄范围
		Regions []int `json:"regions"` // 地区ID列表
	} `json:"targetAudience"` // 目标受众
	Creative struct {
		Title       string   `json:"title"`       // 创意标题
		Description string   `json:"description"` // 创意描述
		Images      []string `json:"images"`      // 图片URL列表
		Video       string   `json:"video"`       // 视频URL
	} `json:"creative"` // 创意内容
}

// CreateMediaCampaignResponse 创建媒体广告活动响应
type CreateMediaCampaignResponse struct {
	ID int `json:"id"` // 新创建的活动ID
}

// UpdateMediaCampaignRequest 更新媒体广告活动请求
type UpdateMediaCampaignRequest struct {
	Name   string `json:"name"`   // 活动名称
	Budget int    `json:"budget"` // 预算（копейки）
	Status int    `json:"status"` // 活动状态
}

// MediaCampaignDetails 媒体广告活动详情
type MediaCampaignDetails struct {
	ID         int       `json:"id"`         // 活动ID
	Name       string    `json:"name"`       // 活动名称
	Type       int       `json:"type"`       // 活动类型
	Status     int       `json:"status"`     // 活动状态
	CreateTime time.Time `json:"createTime"` // 创建时间
	UpdateTime time.Time `json:"updateTime"` // 更新时间
	Budget     int       `json:"budget"`     // 预算（копейки）
	Spent      int       `json:"spent"`      // 已花费（копейки）
	Statistics struct {
		Impressions int     `json:"impressions"` // 展示次数
		Clicks      int     `json:"clicks"`      // 点击次数
		CTR         float64 `json:"ctr"`         // 点击率
		CPC         float64 `json:"cpc"`         // 平均点击成本
		Conversions int     `json:"conversions"` // 转化次数
		CVR         float64 `json:"cvr"`         // 转化率
	} `json:"statistics"` // 统计数据
}

// ===== 日历促销相关模型 =====

// PromotionCalendarResponse 促销日历响应
type PromotionCalendarResponse struct {
	Data []PromotionEvent `json:"data"` // 促销活动列表
}

// PromotionEvent 促销活动事件
type PromotionEvent struct {
	ID          int       `json:"id"`          // 促销活动ID
	Name        string    `json:"name"`        // 促销活动名称
	Description string    `json:"description"` // 促销活动描述
	StartDate   time.Time `json:"startDate"`   // 开始日期
	EndDate     time.Time `json:"endDate"`     // 结束日期
	Type        string    `json:"type"`        // 促销类型
	Status      string    `json:"status"`      // 状态
	Categories  []int     `json:"categories"`  // 适用分类ID列表
	Discount    struct {
		Type  string  `json:"type"`  // 折扣类型：percentage, fixed
		Value float64 `json:"value"` // 折扣值
	} `json:"discount"` // 折扣信息
}

// PromotionDetailsResponse 促销活动详情响应
type PromotionDetailsResponse struct {
	Data PromotionDetails `json:"data"` // 促销活动详情
}

// PromotionDetails 促销活动详情
type PromotionDetails struct {
	ID          int       `json:"id"`          // 促销活动ID
	Name        string    `json:"name"`        // 促销活动名称
	Description string    `json:"description"` // 促销活动描述
	StartDate   time.Time `json:"startDate"`   // 开始日期
	EndDate     time.Time `json:"endDate"`     // 结束日期
	Type        string    `json:"type"`        // 促销类型
	Status      string    `json:"status"`      // 状态
	Rules       struct {
		MinOrderAmount int     `json:"minOrderAmount"` // 最小订单金额
		MaxDiscount    int     `json:"maxDiscount"`    // 最大折扣金额
		DiscountRate   float64 `json:"discountRate"`   // 折扣率
	} `json:"rules"` // 促销规则
	Categories []struct {
		ID   int    `json:"id"`   // 分类ID
		Name string `json:"name"` // 分类名称
	} `json:"categories"` // 适用分类
	Participation struct {
		IsParticipating bool      `json:"isParticipating"` // 是否参与
		JoinedAt        time.Time `json:"joinedAt"`        // 参与时间
		ProductCount    int       `json:"productCount"`    // 参与商品数量
	} `json:"participation"` // 参与状态
}

// ParticipatePromotionRequest 参与促销活动请求
type ParticipatePromotionRequest struct {
	PromotionID int   `json:"promotionId"` // 促销活动ID
	ProductIDs  []int `json:"productIds"`  // 参与的商品ID列表
}

// PromotionParticipationResponse 促销参与状态响应
type PromotionParticipationResponse struct {
	Data struct {
		IsParticipating bool      `json:"isParticipating"` // 是否参与
		JoinedAt        time.Time `json:"joinedAt"`        // 参与时间
		Products        []struct {
			ID       int    `json:"id"`     // 商品ID
			Name     string `json:"name"`   // 商品名称
			Status   string `json:"status"` // 参与状态
			Discount struct {
				Current  float64 `json:"current"`  // 当前折扣
				Proposed float64 `json:"proposed"` // 建议折扣
			} `json:"discount"` // 折扣信息
		} `json:"products"` // 参与商品列表
	} `json:"data"`
}

// ===== 固定短语和关键词相关模型 =====

// FixedPhrasesResponse 固定短语响应
type FixedPhrasesResponse struct {
	Data []FixedPhraseItem `json:"data"` // 固定短语列表
}

// FixedPhraseItem 固定短语项目
type FixedPhraseItem struct {
	Keyword string `json:"keyword"` // 关键词
	Rate    int    `json:"rate"`    // 出价（копейки）
}

// CampaignStatisticsResponse 活动统计概览响应
type CampaignStatisticsResponse struct {
	Data CampaignStatisticsSummary `json:"data"` // 统计概览数据
}

// CampaignStatisticsSummary 活动统计概览
type CampaignStatisticsSummary struct {
	Period struct {
		From time.Time `json:"from"` // 统计开始时间
		To   time.Time `json:"to"`   // 统计结束时间
	} `json:"period"` // 统计周期
	Summary struct {
		Impressions int     `json:"impressions"` // 总展示次数
		Clicks      int     `json:"clicks"`      // 总点击次数
		CTR         float64 `json:"ctr"`         // 平均点击率
		CPC         float64 `json:"cpc"`         // 平均点击成本
		Spend       int     `json:"spend"`       // 总花费（копейки）
		Orders      int     `json:"orders"`      // 总订单数
		Sales       int     `json:"sales"`       // 总销售额（копейки）
		ROAS        float64 `json:"roas"`        // 广告支出回报率
	} `json:"summary"` // 汇总数据
	Daily []struct {
		Date        time.Time `json:"date"`        // 日期
		Impressions int       `json:"impressions"` // 展示次数
		Clicks      int       `json:"clicks"`      // 点击次数
		Spend       int       `json:"spend"`       // 花费（копейки）
		Orders      int       `json:"orders"`      // 订单数
		Sales       int       `json:"sales"`       // 销售额（копейки）
	} `json:"daily"` // 每日数据
}

// CampaignKeywordsResponse 活动关键词响应
type CampaignKeywordsResponse struct {
	Data []KeywordItem `json:"data"` // 关键词列表
}

// KeywordItem 关键词项目
type KeywordItem struct {
	Keyword string `json:"keyword"` // 关键词
	Rate    int    `json:"rate"`    // 出价（копейки）
	Status  string `json:"status"`  // 状态：active, paused
}
