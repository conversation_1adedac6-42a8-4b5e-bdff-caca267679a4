package wbapi

import "time"

// CardsListRequest 商品卡片列表请求
type CardsListRequest struct {
    Settings CardsListSettings `json:"settings"`
}

// CardsListSettings 商品卡片列表设置
type CardsListSettings struct {
    Sort   CardsSort   `json:"sort"`
    Filter CardsFilter `json:"filter"`
    Cursor CardsCursor `json:"cursor"`
}

// CardsSort 排序设置
type CardsSort struct {
    Ascending bool `json:"ascending"` // 按更新时间升序排序
}

// CardsFilter 过滤条件
type CardsFilter struct {
    WithPhoto              int      `json:"withPhoto"`              // 照片过滤：0-无照片，1-有照片，-1-全部
    TextSearch             string   `json:"textSearch"`             // 文本搜索
    TagIDs                 []int    `json:"tagIDs"`                 // 标签ID搜索
    AllowedCategoriesOnly  bool     `json:"allowedCategoriesOnly"`  // 仅允许的分类
    ObjectIDs              []int    `json:"objectIDs"`              // 主题ID搜索
    Brands                 []string `json:"brands"`                 // 品牌搜索
    ImtID                  int      `json:"imtID"`                  // 合并商品卡片ID搜索
}

// CardsCursor 分页游标
type CardsCursor struct {
    UpdatedAt time.Time `json:"updatedAt"` // 更新时间
    NMID      int       `json:"nmID"`      // WB商品ID
    Limit     int       `json:"limit"`     // 限制数量
}

// CardsListResponse 商品卡片列表响应
type CardsListResponse struct {
    Cards  []ProductCard     `json:"cards"`
    Cursor CardsListCursor   `json:"cursor"`
}

// CardsListCursor 列表分页游标
type CardsListCursor struct {
    UpdatedAt time.Time `json:"updatedAt"` // 下一页更新时间
    NMID      int       `json:"nmID"`      // 下一页WB商品ID
    Total     int       `json:"total"`     // 总数量
}

// ProductCard 商品卡片
type ProductCard struct {
    NMID          int                    `json:"nmID"`          // WB商品ID
    ImtID         int                    `json:"imtID"`         // 合并商品卡片ID
    NMUUID        string                 `json:"nmUUID"`        // 内部系统ID
    SubjectID     int                    `json:"subjectID"`     // 主题ID
    SubjectName   string                 `json:"subjectName"`   // 主题名称
    VendorCode    string                 `json:"vendorCode"`    // 卖家商品代码
    Brand         string                 `json:"brand"`         // 品牌
    Title         string                 `json:"title"`         // 商品标题
    Description   string                 `json:"description"`   // 商品描述
    NeedKiz       bool                   `json:"needKiz"`       // 是否需要标识码
    Photos        []ProductPhoto         `json:"photos"`        // 照片
    Video         string                 `json:"video"`         // 视频
    Dimensions    ProductDimensions      `json:"dimensions"`    // 尺寸
    Characteristics []ProductCharacteristic `json:"characteristics"` // 特征
    Sizes         []ProductSize          `json:"sizes"`         // 尺码
    Tags          []ProductTag           `json:"tags"`          // 标签
    CreatedAt     time.Time              `json:"createdAt"`     // 创建时间
    UpdatedAt     time.Time              `json:"updatedAt"`     // 更新时间
}

// ProductPhoto 商品照片
type ProductPhoto struct {
    Big      string `json:"big"`      // 大图
    C246x328 string `json:"c246x328"` // 246x328尺寸
    C516x688 string `json:"c516x688"` // 516x688尺寸
    Square   string `json:"square"`   // 正方形
    Tm       string `json:"tm"`       // 缩略图
}

// ProductDimensions 商品尺寸
type ProductDimensions struct {
    Length       int `json:"length"`       // 长度(厘米)
    Width        int `json:"width"`        // 宽度(厘米)
    Height       int `json:"height"`       // 高度(厘米)
    WeightBrutto int `json:"weightBrutto"` // 毛重(千克)
}

// ProductCharacteristic 商品特征
type ProductCharacteristic struct {
    ID    int      `json:"id"`    // 特征ID
    Value []string `json:"value"` // 特征值
}

// ProductSize 商品尺码
type ProductSize struct {
    ChrtID   int      `json:"chrtID"`   // WB尺码ID
    TechSize string   `json:"techSize"` // 技术尺码
    WbSize   string   `json:"wbSize"`   // WB尺码
    Skus     []string `json:"skus"`     // 条形码
}

// ProductTag 商品标签
type ProductTag struct {
    ID    int    `json:"id"`    // 标签ID
    Name  string `json:"name"`  // 标签名称
    Color string `json:"color"` // 标签颜色
}

// CardUpdate 商品卡片更新
type CardUpdate struct {
    NMID            int                     `json:"nmID"`            // WB商品ID
    VendorCode      string                  `json:"vendorCode"`      // 卖家商品代码
    Brand           string                  `json:"brand"`           // 品牌
    Title           string                  `json:"title"`           // 商品标题
    Description     string                  `json:"description"`     // 商品描述
    Dimensions      ProductDimensions       `json:"dimensions"`      // 尺寸
    Characteristics []ProductCharacteristic `json:"characteristics"` // 特征
    Sizes           []ProductSize           `json:"sizes"`           // 尺码
}

// MediaUpdateRequest 媒体更新请求
type MediaUpdateRequest struct {
    NMID int      `json:"nmId"` // WB商品ID
    Data []string `json:"data"` // 媒体链接列表
}

// MediaUpdateResponse 媒体更新响应
type MediaUpdateResponse struct {
    Data             interface{} `json:"data"`
    Error            bool        `json:"error"`
    ErrorText        string      `json:"errorText"`
    AdditionalErrors interface{} `json:"additionalErrors"`
}

// ===== 目录和分类相关模型 =====

// AllObjectsResponse 所有分类响应
type AllObjectsResponse struct {
	Data             []ObjectInfo `json:"data"`             // 分类数据
	Error            bool         `json:"error"`            // 错误标志
	ErrorText        string       `json:"errorText"`        // 错误信息
	AdditionalErrors *string      `json:"additionalErrors"` // 附加错误
}

// ObjectInfo 分类信息
type ObjectInfo struct {
	ObjectID   int    `json:"objectId"`   // 分类ID
	ObjectName string `json:"objectName"` // 分类名称
	ParentID   int    `json:"parentId"`   // 父分类ID
	ParentName string `json:"parentName"` // 父分类名称
	IsVisible  bool   `json:"isVisible"`  // 是否可见
}

// ObjectCharacteristicsResponse 分类特征响应
type ObjectCharacteristicsResponse struct {
	Data             []CharacteristicInfo `json:"data"`             // 特征数据
	Error            bool                 `json:"error"`            // 错误标志
	ErrorText        string               `json:"errorText"`        // 错误信息
	AdditionalErrors *string              `json:"additionalErrors"` // 附加错误
}

// CharacteristicInfo 特征信息
type CharacteristicInfo struct {
	CharcID      int                    `json:"charcId"`      // 特征ID
	CharcName    string                 `json:"charcName"`    // 特征名称
	Required     bool                   `json:"required"`     // 是否必填
	CharcType    int                    `json:"charcType"`    // 特征类型
	PopularValue string                 `json:"popularValue"` // 热门值
	Dictionary   []CharacteristicOption `json:"dictionary"`   // 字典选项
}

// 使用 content_models.go 中定义的 CharacteristicOption

// ColorsResponse 颜色目录响应
type ColorsResponse struct {
	Data             []ColorInfo `json:"data"`             // 颜色数据
	Error            bool        `json:"error"`            // 错误标志
	ErrorText        string      `json:"errorText"`        // 错误信息
	AdditionalErrors *string     `json:"additionalErrors"` // 附加错误
}

// ColorInfo 颜色信息
type ColorInfo struct {
	ID   int    `json:"id"`   // 颜色ID
	Name string `json:"name"` // 颜色名称
}

// KindsResponse 种类目录响应
type KindsResponse struct {
	Data             []KindInfo `json:"data"`             // 种类数据
	Error            bool       `json:"error"`            // 错误标志
	ErrorText        string     `json:"errorText"`        // 错误信息
	AdditionalErrors *string    `json:"additionalErrors"` // 附加错误
}

// KindInfo 种类信息
type KindInfo struct {
	ID   int    `json:"id"`   // 种类ID
	Name string `json:"name"` // 种类名称
}

// CountriesResponse 国家目录响应
type CountriesResponse struct {
	Data             []CountryInfo `json:"data"`             // 国家数据
	Error            bool          `json:"error"`            // 错误标志
	ErrorText        string        `json:"errorText"`        // 错误信息
	AdditionalErrors *string       `json:"additionalErrors"` // 附加错误
}

// CountryInfo 国家信息
type CountryInfo struct {
	ID   int    `json:"id"`   // 国家ID
	Name string `json:"name"` // 国家名称
}

// SeasonsResponse 季节目录响应
type SeasonsResponse struct {
	Data             []SeasonInfo `json:"data"`             // 季节数据
	Error            bool         `json:"error"`            // 错误标志
	ErrorText        string       `json:"errorText"`        // 错误信息
	AdditionalErrors *string      `json:"additionalErrors"` // 附加错误
}

// SeasonInfo 季节信息
type SeasonInfo struct {
	ID   int    `json:"id"`   // 季节ID
	Name string `json:"name"` // 季节名称
}

// ===== 商品卡片管理相关模型 =====

// 使用 content_models.go 中定义的商品卡片相关类型：
// - ProductCardRequest (作为 CreateCardRequest)
// - ProductVariant
// - ProductDim
// - ProductSizeInfo
// - ProductCharacteristicReq
// - CreateCardResponse
// - CreatedCard
// - ProductCardUpdateRequest (作为 UpdateCardRequest)
// - UpdateCardResponse

// CardResponse 商品卡片响应
type CardResponse struct {
	Data struct {
		Card ProductCard `json:"card"` // 商品卡片
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// 使用已存在的 ProductCard 定义

// ===== 价格相关模型 =====

// PriceInfo 价格信息
type PriceInfo struct {
	NMID     int `json:"nmId"`     // 商品ID
	Price    int `json:"price"`    // 价格(копейки)
	Discount int `json:"discount"` // 折扣百分比
	PromoCode int `json:"promoCode"` // 促销码折扣
}

// PriceUpdate 价格更新
type PriceUpdate struct {
	NMID  int `json:"nmId"`  // 商品ID
	Price int `json:"price"` // 新价格(копейки)
}

// PricesResponse 价格响应
type PricesResponse struct {
	Data []PriceInfo `json:"data"` // 价格数据
}

// PricesBySizeResponse 按尺码价格响应
type PricesBySizeResponse struct {
	Data []PriceBySizeInfo `json:"data"` // 按尺码价格数据
}

// PriceBySizeInfo 按尺码价格信息
type PriceBySizeInfo struct {
	NMID     int    `json:"nmId"`     // 商品ID
	Size     string `json:"size"`     // 尺码
	Price    int    `json:"price"`    // 价格(копейки)
	Discount int    `json:"discount"` // 折扣百分比
}

// DiscountsResponse 折扣响应
type DiscountsResponse struct {
	Data []DiscountInfo `json:"data"` // 折扣数据
}

// DiscountInfo 折扣信息
type DiscountInfo struct {
	NMID     int `json:"nmId"`     // 商品ID
	Discount int `json:"discount"` // 折扣百分比
}

// DiscountUpdate 折扣更新
type DiscountUpdate struct {
	NMID     int `json:"nmId"`     // 商品ID
	Discount int `json:"discount"` // 新折扣百分比
}

// ===== 库存相关模型 =====

// StockInfo 库存信息
type StockInfo struct {
	LastChangeDate      time.Time `json:"lastChangeDate"`      // 最后更改日期
	SupplierArticle     string    `json:"supplierArticle"`     // 供应商商品编码
	TechSize            string    `json:"techSize"`            // 技术尺寸
	Barcode             string    `json:"barcode"`             // 条形码
	Quantity            int       `json:"quantity"`            // 数量
	IsSupply            bool      `json:"isSupply"`            // 是否供应
	IsRealization       bool      `json:"isRealization"`       // 是否实现
	QuantityFull        int       `json:"quantityFull"`        // 完整数量
	QuantityNotInOrders int       `json:"quantityNotInOrders"` // 非订单数量
	Warehouse           string    `json:"warehouse"`           // 仓库名称
	WarehouseID         int       `json:"warehouseId"`         // 仓库ID
	NMID                int       `json:"nmId"`                // 商品ID
	Subject             string    `json:"subject"`             // 主题
	Category            string    `json:"category"`            // 分类
	DaysOnSite          int       `json:"daysOnSite"`          // 在网站天数
	Brand               string    `json:"brand"`               // 品牌
	SCCode              string    `json:"SCCode"`              // SC代码
	Price               int       `json:"price"`               // 价格
	Discount            int       `json:"discount"`            // 折扣
}

// StockUpdate 库存更新
type StockUpdate struct {
	Barcode     string `json:"barcode"`     // 条形码
	Stock       int    `json:"stock"`       // 库存数量
	WarehouseID int    `json:"warehouseId"` // 仓库ID
}

// StocksResponse 库存响应
type StocksResponse struct {
	Stocks []StockInfo `json:"stocks"` // 库存数据
}

// ===== 仓库相关模型 =====
// 使用 supplies_models.go 中定义的类型：
// - WarehousesResponse
// - WarehouseInfo (在 stock_models.go 中定义)

// WarehouseUpdate 仓库更新
type WarehouseUpdate struct {
	Name         string `json:"name"`         // 仓库名称
	Address      string `json:"address"`      // 仓库地址
	WorkTime     string `json:"workTime"`     // 工作时间
	DeliveryType int    `json:"deliveryType"` // 配送类型
	IsLarge      bool   `json:"isLarge"`      // 是否支持大件
}

// ===== 媒体相关模型 =====

// MediaUploadRequest 媒体上传请求
type MediaUploadRequest struct {
	VendorCode string   `json:"vendorCode"` // 商家编码
	Data       []string `json:"data"`       // 媒体数据(base64编码)
}

// MediaUploadResponse 媒体上传响应
type MediaUploadResponse struct {
	Data struct {
		VendorCode string   `json:"vendorCode"` // 商家编码
		Photos     []string `json:"photos"`     // 照片链接列表
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// ===== 标签相关模型 =====
// 使用 content_models.go 中定义的类型：
// - TagsResponse
// - ContentTag (作为 TagInfo)
// - CreateTagRequest
// - CreateTagResponse

// UpdateTagRequest 更新标签请求
type UpdateTagRequest struct {
	Name  string `json:"name"`  // 标签名称
	Color string `json:"color"` // 标签颜色
}