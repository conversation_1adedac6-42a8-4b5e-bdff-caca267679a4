package wbapi

import (
	"context"
	"fmt"

	"github.com/go-resty/resty/v2"
)

// 使用 analytics_service.go 中定义的 statisticsAPIURL

// OrderService 订单服务
type OrderService struct {
	client      *resty.Client
	rateLimiter *RateLimiter
}

// newOrderService 创建新的订单服务
func newOrderService(c *Client) *OrderService {
	httpClient := resty.New()

	if c.httpClient != nil {
		httpClient.SetTimeout(c.httpClient.GetClient().Timeout)
		for k, v := range c.httpClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &OrderService{
		client:      httpClient,
		rateLimiter: NewRateLimiter(),
	}
}

// GetOrders 获取订单列表
func (s *OrderService) GetOrders(dateFrom, dateTo string, flag int) ([]OrderInfo, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []OrderInfo
	resp, err := s.client.R().
		SetQueryParam("dateFrom", dateFrom).
		SetQueryParam("dateTo", dateTo).
		SetQueryParam("flag", fmt.Sprintf("%d", flag)).
		SetResult(&result).
		Get(statisticsAPIURL + "/api/v1/supplier/orders")

	if err != nil {
		return nil, fmt.Errorf("获取订单列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取订单列表失败: %s", resp.String())
	}

	return result, nil
}

// GetSales 获取销售数据
func (s *OrderService) GetSales(dateFrom, dateTo string, flag int) ([]SaleInfo, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []SaleInfo
	resp, err := s.client.R().
		SetQueryParam("dateFrom", dateFrom).
		SetQueryParam("dateTo", dateTo).
		SetQueryParam("flag", fmt.Sprintf("%d", flag)).
		SetResult(&result).
		Get(statisticsAPIURL + "/api/v1/supplier/sales")

	if err != nil {
		return nil, fmt.Errorf("获取销售数据失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取销售数据失败: %s", resp.String())
	}

	return result, nil
}

// GetNewOrders 获取新订单
func (s *OrderService) GetNewOrders() (*NewOrdersResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result NewOrdersResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(marketplaceAPIURL + "/api/v3/orders/new")

	if err != nil {
		return nil, fmt.Errorf("获取新订单失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取新订单失败: %s", resp.String())
	}

	return &result, nil
}

// ConfirmOrder 确认订单
func (s *OrderService) ConfirmOrder(orderID int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"orders": []int{orderID},
		}).
		Put(marketplaceAPIURL + "/api/v3/orders/confirm")

	if err != nil {
		return fmt.Errorf("确认订单失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("确认订单失败: %s", resp.String())
	}

	return nil
}

// CancelOrder 取消订单
func (s *OrderService) CancelOrder(orderID int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"orders": []int{orderID},
		}).
		Put(marketplaceAPIURL + "/api/v3/orders/cancel")

	if err != nil {
		return fmt.Errorf("取消订单失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("取消订单失败: %s", resp.String())
	}

	return nil
}

// GetOrdersStatus 获取订单状态
func (s *OrderService) GetOrdersStatus(orders []int) (*OrdersStatusResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result OrdersStatusResponse
	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"orders": orders,
		}).
		SetResult(&result).
		Post(marketplaceAPIURL + "/api/v3/orders/status")

	if err != nil {
		return nil, fmt.Errorf("获取订单状态失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取订单状态失败: %s", resp.String())
	}

	return &result, nil
}