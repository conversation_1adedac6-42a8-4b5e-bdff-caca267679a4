package wbapi

// WB API 端点 URL 常量
const (
	// 核心业务 API 端点
	statisticsAPIURL      = "https://statistics-api.wildberries.ru"       // 统计API - 订单、销售数据
	advertAPIURL          = "https://advert-api.wildberries.ru"           // 广告API - 推广活动管理
	sellerAnalyticsAPIURL = "https://seller-analytics-api.wildberries.ru" // 卖家分析API - 销售漏斗分析
	contentAPIURL         = "https://content-api.wildberries.ru"          // 内容API - 商品卡片、分类
	priceAPIURL           = "https://discounts-prices-api.wildberries.ru" // 价格API - 价格和折扣管理
	marketplaceAPIURL     = "https://marketplace-api.wildberries.ru"      // 市场API - 库存管理

	// 客户服务 API 端点
	feedbacksAPIURL = "https://feedbacks-api.wildberries.ru"  // 反馈API - 评价和问答
	buyerChatAPIURL = "https://buyer-chat-api.wildberries.ru" // 买家聊天API - 客服对话

	// 物流和供应链 API 端点
	suppliesAPIURL = "https://supplies-api.wildberries.ru" // 供应API - 供货和入库
	returnsAPIURL  = "https://returns-api.wildberries.ru"  // 退货API - 退货处理

	// 财务和文档 API 端点
	documentsAPIURL = "https://documents-api.wildberries.ru" // 文档API - 报告和文档
	financeAPIURL   = "https://finance-api.wildberries.ru"   // 财务API - 财务数据

	// 通用 API 端点
	commonAPIURL = "https://common-api.wildberries.ru" // 通用API - 公共服务

	// 别名常量（向后兼容）
	promotionAPIURL = advertAPIURL  // 推广API（别名）
	productsAPIURL  = contentAPIURL // 商品API（别名）
)
