# Wildberries API 客户端

这是一个完整的 Wildberries Seller API Go 客户端，基于官方 OpenAPI 文档构建，提供了对 WB 平台各种服务的完整访问。

## 🎯 版本 2.0.0 - 完整服务实现

基于官方 OpenAPI 文档，实现了所有主要 API 接口，分为 7 个完整的服务模块。

### ✨ 服务模块

1. **推广服务 (PromotionService)** - `promotion_service.go`
   - 广告活动管理
   - 竞价设置
   - 财务数据统计
   - 自动和媒体广告设置

2. **商品服务 (ProductsService)** - `products_service.go`
   - 商品卡片创建和编辑
   - 媒体文件管理
   - 标签管理
   - 商品信息维护

3. **内容服务 (ContentService)** - `content_service.go`
   - 分类、主题和特征管理
   - 商品内容管理
   - 媒体内容处理

4. **订单服务 (OrderService)** - `order_service.go`
   - 订单管理
   - 销售统计
   - 订单报告

5. **分析服务 (AnalyticsService)** - `analytics_service.go`
   - 推广统计
   - 销售漏斗分析
   - 搜索查询分析
   - 卖家分析报告

### 🔧 主要特性

- **完整 API 覆盖** - 覆盖 WB 平台所有主要功能
- **模块化设计** - 7 个独立服务模块，功能清晰分离
- **多版本支持** - 支持 v1-v2 版本 API
- **精确数据模型** - 根据 OpenAPI schema 精确定义
- **完整类型安全** - 编译时错误检查
- **速率限制** - 内置速率限制器，符合官方限制
- **标准化实现** - 符合官方规范

## 📖 使用示例

### 基础用法

```go
package main

import (
    "fmt"
    "time"
    
    "your-project/internal/api/wb"
)

func main() {
    // 创建客户端
    client := wbapi.NewClient("your-api-key", 
        wbapi.WithTimeout(30*time.Second),
    )
    
    // 使用推广服务
    campaigns, err := client.Promotion.GetCampaigns()
    if err != nil {
        panic(err)
    }
    
    fmt.Printf("找到 %d 个广告活动\n", len(campaigns))
}
```

### 推广服务示例

```go
// 获取广告活动列表
campaigns, err := client.Promotion.GetCampaigns()

// 更新竞价
err = client.Promotion.UpdateBids([]wbapi.BidUpdate{
    {
        CampaignID: 12345,
        ProductID:  67890,
        Bid:        100,
    },
})

// 获取统计数据
stats, err := client.Promotion.GetStatistics(wbapi.StatisticsRequest{
    CampaignIDs: []int{12345},
    DateFrom:    "2024-01-01",
    DateTo:      "2024-01-31",
})
```

### 商品服务示例

```go
// 获取商品卡片列表
cards, err := client.Products.GetCardsList(wbapi.CardsListRequest{
    Settings: wbapi.CardsListSettings{
        Filter: wbapi.CardsFilter{
            TextSearch: "商品名称",
            WithPhoto:  1,
        },
        Cursor: wbapi.CardsCursor{
            Limit: 100,
        },
    },
})

// 更新商品卡片
err = client.Products.UpdateCards([]wbapi.CardUpdate{
    {
        NMID:       12345678,
        VendorCode: "SKU-001",
        Title:      "更新的商品标题",
        // ... 其他字段
    },
})
```



## 🔑 认证配置

```go
// 基础认证
client := wbapi.NewClient("your-api-key")

// 带超时配置
client := wbapi.NewClient("your-api-key", 
    wbapi.WithTimeout(30*time.Second),
)
```

## 📊 速率限制

客户端内置了速率限制器，自动遵循 WB API 的限制：

- 推广 API: 5 请求/秒
- 内容 API: 10 请求/分钟
- 价格 API: 10 请求/分钟

## 🔧 配置选项

```go
// 自定义超时
client := wbapi.NewClient("api-key", wbapi.WithTimeout(60*time.Second))

// 获取底层 HTTP 客户端
httpClient := client.GetHTTPClient()

// 创建自定义请求
resp, err := client.R().
    SetBody(requestData).
    Post("/custom/endpoint")
```

## 📁 项目结构

```
internal/api/wb/
├── README.md                    # 项目文档
├── client.go                    # 主客户端
├── promotion_service.go         # 推广服务
├── promotion_models.go          # 推广数据模型
├── products_service.go          # 商品服务
├── products_models.go           # 商品数据模型
├── content_service.go           # 内容服务
├── content_models.go            # 内容数据模型
├── order_service.go             # 订单服务
├── order_models.go              # 订单数据模型
├── analytics_service.go         # 分析服务
├── analytics_models.go          # 分析数据模型
├── rate_limiter.go              # 速率限制器
├── common_models.go             # 通用数据模型
└── doc/                         # API 文档
    ├── 01-introduction.yaml     # API 介绍
    ├── 02-products.yaml         # 商品管理
    ├── 07-promotion.yaml        # 推广营销
    └── 10-analytics.yaml        # 分析数据
```

## 🚀 版本历史

### v2.0.0 (当前版本)
- 完整重构客户端架构
- 添加 7 个完整服务模块
- 统一代码风格和结构
- 添加完整的类型定义
- 改进错误处理和速率限制

### v1.0.0
- 基础推广和商品卡片功能
- 简单的客户端实现

## 📝 许可证

本项目遵循 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 Issue 或联系维护团队。

---

## 🆕 新增功能 (v2.7.0)

### 🎯 基于 OpenAPI 规范的分析服务完整实现
根据 `10-analytics.yaml` OpenAPI 规范，全面实现了分析服务的所有端点：

### 📊 分析服务完整实现
- ✅ **媒体广告统计**
  - 获取媒体广告统计 (`GetMediaStats`)

- ✅ **销售漏斗历史数据**
  - 获取商品卡片历史详细报告 (`GetNMReportDetailHistory`)
  - 获取分组商品卡片历史报告 (`GetNMReportGroupedHistory`)

- ✅ **搜索报告分析**
  - 获取搜索报告主页数据 (`GetSearchReport`)
  - 获取搜索报告分组数据 (`GetSearchReportTableGroups`)
  - 获取搜索报告详细数据 (`GetSearchReportTableDetails`)
  - 获取商品搜索文本 (`GetProductSearchTexts`)
  - 获取商品订单数据 (`GetProductOrders`)

- ✅ **库存报告分析**
  - 获取库存报告 (`GetStockReport`)
  - 获取库存报告分组数据 (`GetStockReportTableGroups`)
  - 获取库存报告详细数据 (`GetStockReportTableDetails`)

- ✅ **CSV 报告下载**
  - 下载商品卡片详细报告 CSV (`DownloadNMReportDetailCSV`)
  - 下载搜索报告 CSV (`DownloadSearchReportCSV`)
  - 下载库存报告 CSV (`DownloadStockReportCSV`)

## 🆕 新增功能 (v2.6.0)

### 🎯 基于 OpenAPI 规范的推广服务完整实现
根据 `07-promotion.yaml` OpenAPI 规范，全面实现了推广服务的所有端点：

### 🚀 推广服务完整实现
- ✅ **财务管理**
  - 获取推广账户余额 (`GetBalance`)
  - 获取活动预算 (`GetCampaignBudget`)
  - 为活动充值预算 (`DepositCampaignBudget`)
  - 获取支出历史 (`GetExpenseHistory`)
  - 获取充值历史 (`GetPaymentHistory`)

- ✅ **媒体广告管理**
  - 获取媒体广告活动列表 (`GetMediaCampaigns`)
  - 创建媒体广告活动 (`CreateMediaCampaign`)
  - 更新媒体广告活动 (`UpdateMediaCampaign`)
  - 获取媒体广告活动详情 (`GetMediaCampaignDetails`)
  - 暂停媒体广告活动 (`PauseMediaCampaign`)
  - 启动媒体广告活动 (`StartMediaCampaign`)

- ✅ **日历促销管理**
  - 获取促销日历 (`GetPromotionCalendar`)
  - 获取促销活动详情 (`GetPromotionDetails`)
  - 参与促销活动 (`ParticipateInPromotion`)
  - 获取促销参与状态 (`GetPromotionParticipation`)

- ✅ **关键词和短语管理**
  - 获取固定短语列表 (`GetFixedPhrases`)
  - 设置固定短语 (`SetFixedPhrases`)
  - 获取活动关键词列表 (`GetCampaignKeywords`)
  - 更新活动关键词 (`UpdateCampaignKeywords`)

- ✅ **统计分析**
  - 获取活动统计概览 (`GetCampaignStatistics`)

## 🆕 新增功能 (v2.5.0)

### 🎯 基于 OpenAPI 规范的完整实现
根据 `02-products.yaml` OpenAPI 规范，全面实现了商品服务的所有端点：

### 📦 商品服务完整实现
- ✅ **目录和分类管理**
  - 获取所有分类 (`GetAllObjects`)
  - 获取分类特征 (`GetObjectCharacteristics`)
  - 获取颜色目录 (`GetColors`)
  - 获取种类目录 (`GetKinds`)
  - 获取国家目录 (`GetCountries`)
  - 获取季节目录 (`GetSeasons`)

- ✅ **商品卡片管理**
  - 创建商品卡片 (`CreateCard`)
  - 更新商品卡片 (`UpdateCard`)
  - 根据NMID获取卡片 (`GetCardByNMID`)
  - 删除商品卡片 (`DeleteCard`)
  - 移动到回收站 (`MoveCard`)
  - 从回收站恢复 (`RecoverCard`)

- ✅ **价格管理**
  - 获取商品价格 (`GetPrices`)
  - 按尺码获取价格 (`GetPricesBySize`)
  - 更新商品价格 (`UpdatePrices`)
  - 获取折扣信息 (`GetDiscounts`)
  - 更新折扣 (`UpdateDiscounts`)

- ✅ **库存管理**
  - 获取库存信息 (`GetStocks`)
  - 更新库存 (`UpdateStocks`)

- ✅ **仓库管理**
  - 获取仓库列表 (`GetWarehouses`)
  - 更新仓库信息 (`UpdateWarehouse`)

- ✅ **媒体管理**
  - 上传媒体文件 (`UploadMedia`)
  - 删除媒体文件 (`DeleteMedia`)

- ✅ **标签管理**
  - 获取标签列表 (`GetTags`)
  - 创建标签 (`CreateTag`)
  - 更新标签 (`UpdateTag`)
  - 删除标签 (`DeleteTag`)

## 🆕 新增功能 (v2.4.0)

### 🎯 基于官方文档的完整实现
根据最新的 WB API 官方文档，全面完善了所有服务功能：

### 📦 内容服务增强
- ✅ **分类特征管理** - 获取分类的详细特征信息
- ✅ **商品卡片创建** - 完整的商品卡片创建和更新功能
- ✅ **标签管理** - 创建、获取和管理商品标签

### 💬 新增聊天服务 (ChatService)
- ✅ **聊天管理** - 获取买家聊天列表
- ✅ **消息处理** - 发送和接收聊天消息
- ✅ **状态管理** - 标记已读、获取未读数量

### 🔄 反馈服务增强
- ✅ **模板管理** - 创建、更新、删除回复模板
- ✅ **状态检查** - 检查新评价和问题
- ✅ **批量处理** - 批量处理评价和问答

### 📋 订单服务增强
- ✅ **新订单管理** - 获取和处理新订单
- ✅ **订单状态** - 确认、取消订单操作
- ✅ **状态查询** - 批量查询订单状态

## 🆕 新增功能 (v2.3.0)

### 🚀 新增服务
- ✅ **反馈服务 (FeedbackService)** - 评价和问答管理
  - 获取商品评价列表
  - 获取商品问答列表
  - 回复评价和问答
- ✅ **供应服务 (SuppliesService)** - 供货单和物流管理
  - 创建和管理供货单
  - 添加订单到供货单
  - 获取供货单详情和订单列表
- ✅ **财务服务 (FinanceService)** - 财务报告和余额查询
  - 获取详细财务报告
  - 消费税报告
  - 佣金报告
  - 账户余额查询

### 📡 完整 API 端点支持
更新了所有官方 WB API 端点：
- `https://seller-analytics-api.wildberries.ru` - 卖家分析
- `https://discounts-prices-api.wildberries.ru` - 价格折扣
- `https://marketplace-api.wildberries.ru` - 市场服务
- `https://statistics-api.wildberries.ru` - 统计数据
- `https://advert-api.wildberries.ru` - 广告推广
- `https://feedbacks-api.wildberries.ru` - 反馈评价 ✨
- `https://buyer-chat-api.wildberries.ru` - 买家聊天 ✨
- `https://supplies-api.wildberries.ru` - 供应物流 ✨
- `https://returns-api.wildberries.ru` - 退货处理 ✨
- `https://documents-api.wildberries.ru` - 文档管理 ✨
- `https://finance-api.wildberries.ru` - 财务服务 ✨
- `https://common-api.wildberries.ru` - 通用服务 ✨

## 🆕 新增功能 (v2.2.0)

### 🏗️ 架构优化
- ✅ **单一客户端架构** - 每个服务只使用一个 `resty.Client` 实例
- ✅ **URL 拼接方式** - 通过完整 URL 访问不同 API 端点，避免多个 BaseURL 设置
- ✅ **统一常量管理** - 所有 API 端点 URL 集中在 `constants.go` 中管理
- ✅ **简化的速率限制器** - 重命名为更通用的 `RateLimiter`

### 🔧 技术改进
- ✅ **减少内存占用** - 每个服务不再创建多个 HTTP 客户端实例
- ✅ **更好的可维护性** - URL 常量集中管理，易于更新和维护
- ✅ **一致的命名** - 统一的命名规范，提高代码可读性

## 🆕 新增功能 (v2.1.0)

### 📈 完整分析服务实现
- ✅ **推广统计** - 获取广告活动完整统计数据
- ✅ **词组分析** - 自动和搜索广告词组统计
- ✅ **关键词统计** - 详细的关键词表现数据
- ✅ **销售漏斗** - 商品卡片详细报告和转化分析

### 📦 增强的服务功能
- ✅ **内容服务** - 新增父分类获取功能
- ✅ **库存服务** - 完整的库存管理和报告
- ✅ **订单服务** - 订单和销售数据获取
- ✅ **价格服务** - 增强的价格管理功能

### 🔧 技术改进
- ✅ **多客户端架构** - 分析服务支持多个API端点
- ✅ **完整错误处理** - 新增认证和验证错误类型
- ✅ **示例代码** - 提供完整的使用示例 (`example_usage.go`)
- ✅ **代码重构** - 数据模型与业务逻辑完全分离

## 📊 API 覆盖状态

| 服务 | 基础功能 | 高级功能 | 统计分析 | 状态 |
|------|----------|----------|----------|------|
| **推广服务** | ✅ | ✅ | ✅ | **🎯 完全实现** |
| **分析服务** | ✅ | ✅ | ✅ | **🎯 完全实现** |
| **商品服务** | ✅ | ✅ | ✅ | **🎯 完全实现** |
| 内容服务 | ✅ | ✅ | N/A | **完成** |
| 订单服务 | ✅ | ✅ | ✅ | **完成** |
| 反馈服务 | ✅ | ✅ | N/A | **完成** |
| 供应服务 | ✅ | ✅ | N/A | 完成 |
| 财务服务 | ✅ | ✅ | ✅ | 完成 |
| **聊天服务** | ✅ | ✅ | N/A | **完成** |

## 📚 更新的文档

- [OpenAPI 规范](./doc/) - 基于官方 API 文档
- [速率限制说明](./rate_limit.go) - 详细的限制规则
- [错误处理指南](./errors.go) - 错误类型和处理方式
- [使用示例](./example_usage.go) - 完整的功能演示