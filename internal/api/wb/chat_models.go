package wbapi

import "time"

// ===== 聊天相关模型 =====

// ChatsResponse 聊天列表响应
type ChatsResponse struct {
	Data struct {
		Chats []Chat `json:"chats"` // 聊天列表
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// Chat 聊天信息
type Chat struct {
	ID               string    `json:"id"`               // 聊天ID
	UserID           string    `json:"userId"`           // 用户ID
	UserName         string    `json:"userName"`         // 用户名称
	LastMessage      string    `json:"lastMessage"`      // 最后一条消息
	LastMessageTime  time.Time `json:"lastMessageTime"`  // 最后消息时间
	UnreadCount      int       `json:"unreadCount"`      // 未读消息数
	Status           string    `json:"status"`           // 聊天状态
	ProductInfo      *struct {
		NMID         int    `json:"nmId"`         // 商品ID
		ProductName  string `json:"productName"`  // 商品名称
		ProductImage string `json:"productImage"` // 商品图片
	} `json:"productInfo"` // 商品信息
}

// ChatMessagesResponse 聊天消息响应
type ChatMessagesResponse struct {
	Data struct {
		Messages []ChatMessage `json:"messages"` // 消息列表
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// ChatMessage 聊天消息
type ChatMessage struct {
	ID        string    `json:"id"`        // 消息ID
	ChatID    string    `json:"chatId"`    // 聊天ID
	Text      string    `json:"text"`      // 消息内容
	Timestamp time.Time `json:"timestamp"` // 发送时间
	IsFromSeller bool   `json:"isFromSeller"` // 是否来自卖家
	MessageType  string `json:"messageType"`  // 消息类型
	Attachments  []struct {
		Type string `json:"type"` // 附件类型
		URL  string `json:"url"`  // 附件URL
	} `json:"attachments"` // 附件列表
}

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	Text        string `json:"text"`        // 消息内容
	MessageType string `json:"messageType"` // 消息类型
}

// UnreadChatsCountResponse 未读聊天数量响应
type UnreadChatsCountResponse struct {
	Data struct {
		UnreadCount int `json:"unreadCount"` // 未读聊天数量
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// ===== 聊天设置相关模型 =====

// ChatSettingsResponse 聊天设置响应
type ChatSettingsResponse struct {
	Data struct {
		AutoReply struct {
			Enabled bool   `json:"enabled"` // 是否启用自动回复
			Message string `json:"message"` // 自动回复消息
		} `json:"autoReply"` // 自动回复设置
		WorkingHours struct {
			Enabled   bool   `json:"enabled"`   // 是否启用工作时间
			StartTime string `json:"startTime"` // 开始时间
			EndTime   string `json:"endTime"`   // 结束时间
			TimeZone  string `json:"timeZone"`  // 时区
		} `json:"workingHours"` // 工作时间设置
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// UpdateChatSettingsRequest 更新聊天设置请求
type UpdateChatSettingsRequest struct {
	AutoReply struct {
		Enabled bool   `json:"enabled"` // 是否启用自动回复
		Message string `json:"message"` // 自动回复消息
	} `json:"autoReply"` // 自动回复设置
	WorkingHours struct {
		Enabled   bool   `json:"enabled"`   // 是否启用工作时间
		StartTime string `json:"startTime"` // 开始时间
		EndTime   string `json:"endTime"`   // 结束时间
		TimeZone  string `json:"timeZone"`  // 时区
	} `json:"workingHours"` // 工作时间设置
}
