package wbapi

import (
	"context"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

// 使用 constants.go 中定义的 promotionAPIURL

// Campaign 广告活动状态
const (
	CampaignStatusDeleting = -1 // 正在删除中
	CampaignStatusReady    = 4  // 准备启动
	CampaignStatusComplete = 7  // 活动完成
	CampaignStatusDeclined = 8  // 已拒绝
	CampaignStatusRunning  = 9  // 正在展示
	CampaignStatusPaused   = 11 // 已暂停
)

// Campaign 广告活动类型
const (
	CampaignTypeCatalog        = 4 // 目录广告（已废弃）
	CampaignTypeContent        = 5 // 内容广告（已废弃）
	CampaignTypeSearch         = 6 // 搜索广告（已废弃）
	CampaignTypeRecommendation = 7 // 主页推荐广告（已废弃）
	CampaignTypeAutomatic      = 8 // 自动广告
	CampaignTypeAuction        = 9 // 竞价广告
)

// WriteOffType 扣费类型
const (
	WriteOffTypeAccount = 0 // 账户
	WriteOffTypeBalance = 1 // 余额
	WriteOffTypeBonuses = 3 // 奖金
)

// PromotionService 推广服务
type PromotionService struct {
	client      *resty.Client // 独立的HTTP客户端
	rateLimiter *RateLimiter
}

// newPromotionService 创建新的推广服务
func newPromotionService(c *Client) *PromotionService {
	// 创建独立的HTTP客户端
	httpClient := resty.New()
	// 复制原始客户端的通用设置
	if c.httpClient != nil {
		httpClient.SetTimeout(c.httpClient.GetClient().Timeout)
		// 复制认证头
		for k, v := range c.httpClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &PromotionService{
		client:      httpClient,
		rateLimiter: NewRateLimiter(),
	}
}

// GetCampaignCount 获取活动列表
func (s *PromotionService) GetCampaignCount() (*CampaignCount, error) {
	// 等待限流器许可
	if err := s.rateLimiter.campaignCountLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp := &CampaignCount{}
	_, err := s.client.R().
		SetResult(resp).
		Get(promotionAPIURL + "/adv/v1/promotion/count")

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// GetConfig 获取推广配置值
func (s *PromotionService) GetConfig() (*Config, error) {
	// 等待限流器许可
	if err := s.rateLimiter.configLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp := &Config{}
	_, err := s.client.R().
		SetResult(resp).
		Get(promotionAPIURL + "/adv/v0/config")

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// GetSubjects 获取可用于广告活动的商品类目
func (s *PromotionService) GetSubjects() ([]Subject, error) {
	// 等待限流器许可
	if err := s.rateLimiter.subjectsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []Subject
	_, err := s.client.R().
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/supplier/subjects")

	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetNomenclatures 获取可用于广告活动的商品列表
func (s *PromotionService) GetNomenclatures(subjectIDs []int) ([]NomenclatureItem, error) {
	// 等待限流器许可
	if err := s.rateLimiter.nomenclaturesLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []NomenclatureItem
	_, err := s.client.R().
		SetBody(subjectIDs).
		SetResult(&result).
		Post(promotionAPIURL + "/adv/v2/supplier/nms")

	if err != nil {
		return nil, err
	}

	return result, nil
}

// CreateAutoCampaign 创建自动广告活动
func (s *PromotionService) CreateAutoCampaign(req *AutoCampaignRequest) (string, error) {
	// 等待限流器许可
	if err := s.rateLimiter.autoCreateLimiter.Wait(context.Background()); err != nil {
		return "", fmt.Errorf("rate limit exceeded: %w", err)
	}

	req.Type = CampaignTypeAutomatic

	var result string
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(promotionAPIURL + "/adv/v1/save-ad")

	if err != nil {
		return "", err
	}

	if resp.IsError() {
		return "", fmt.Errorf("create auto campaign failed: %s", resp.String())
	}

	return result, nil
}

// CreateAuctionCampaign 创建竞价广告活动
func (s *PromotionService) CreateAuctionCampaign(req *AuctionCampaignRequest) (string, error) {
	// 等待限流器许可
	if err := s.rateLimiter.auctionCreateLimiter.Wait(context.Background()); err != nil {
		return "", fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result string
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(promotionAPIURL + "/adv/v2/seacat/save-ad")

	if err != nil {
		return "", err
	}

	if resp.IsError() {
		return "", fmt.Errorf("create auction campaign failed: %s", resp.String())
	}

	return result, nil
}

// UpdateBids 批量更新商品出价
func (s *PromotionService) UpdateBids(bids []BidItem) error {
	// 等待限流器许可
	if err := s.rateLimiter.bidsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"bids": bids,
		}).
		Patch(promotionAPIURL + "/adv/v0/bids")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("update bids failed: %s", resp.String())
	}

	return nil
}

// DeleteCampaign 删除广告活动
func (s *PromotionService) DeleteCampaign(campaignID int) error {
	// 等待限流器许可
	if err := s.rateLimiter.deleteLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		Get(promotionAPIURL + "/adv/v0/delete")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("delete campaign failed: %s", resp.String())
	}

	return nil
}

// RenameCampaign 重命名广告活动
func (s *PromotionService) RenameCampaign(campaignID int, newName string) error {
	// 等待限流器许可
	if err := s.rateLimiter.renameLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"advertId": campaignID,
			"name":     newName,
		}).
		Post(promotionAPIURL + "/adv/v0/rename")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("rename campaign failed: %s", resp.String())
	}

	return nil
}

// PauseCampaign 暂停广告活动
func (s *PromotionService) PauseCampaign(campaignID int) error {
	// 等待限流器许可
	if err := s.rateLimiter.pauseLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		Get(promotionAPIURL + "/adv/v0/pause")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("pause campaign failed: %s", resp.String())
	}

	return nil
}

// StopCampaign 停止广告活动
func (s *PromotionService) StopCampaign(campaignID int) error {
	// 等待限流器许可
	if err := s.rateLimiter.stopLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		Get(promotionAPIURL + "/adv/v0/stop")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("stop campaign failed: %s", resp.String())
	}

	return nil
}

// StartCampaign 启动广告活动
func (s *PromotionService) StartCampaign(campaignID int) error {
	// 等待限流器许可
	if err := s.rateLimiter.startLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		Get(promotionAPIURL + "/adv/v0/start")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("start campaign failed: %s", resp.String())
	}

	return nil
}

// GetCampaignsByParams 通过查询参数获取活动信息
func (s *PromotionService) GetCampaignsByParams(params *CampaignQueryParams) ([]CampaignInfo, error) {
	// 等待限流器许可
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []CampaignInfo
	req := s.client.R()

	if params != nil {
		if params.Status != nil {
			req.SetQueryParam("status", fmt.Sprintf("%d", *params.Status))
		}
		if params.Type != nil {
			req.SetQueryParam("type", fmt.Sprintf("%d", *params.Type))
		}
		if params.Order != nil {
			req.SetQueryParam("order", *params.Order)
		}
		if params.Direction != nil {
			req.SetQueryParam("direction", *params.Direction)
		}
	}

	// 更新为v2版本的API路径
	resp, err := req.SetResult(&result).Post(promotionAPIURL + "/adv/v1/promotion/adverts")
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, fmt.Errorf("get campaigns failed: %s", resp.String())
	}

	return result, nil
}

// GetCampaignsByIDs 通过活动ID列表获取活动信息
func (s *PromotionService) GetCampaignsByIDs(ids []int) ([]CampaignInfo, error) {
	// 使用与 GetCampaignsByParams 相同的限流器
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []CampaignInfo
	resp, err := s.client.R().
		SetBody(ids).
		SetResult(&result).
		Post(promotionAPIURL + "/adv/v1/promotion/adverts")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get campaigns failed: %s", resp.String())
	}

	return result, nil
}

// SetPlusKeywords 设置加词
func (s *PromotionService) SetPlusKeywords(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.plusKeywordLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"pluse": keywords,
		}).
		Post(promotionAPIURL + "/adv/v1/search/set-plus")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set plus keywords failed: %s", resp.String())
	}

	return nil
}

// SetPhraseKeywords 设置词组
func (s *PromotionService) SetPhraseKeywords(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.phraseKeywordLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"phrase": keywords,
		}).
		Post(promotionAPIURL + "/adv/v1/search/set-phrase")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set phrase keywords failed: %s", resp.String())
	}

	return nil
}

// SetStrongKeywords 设置强匹配词
func (s *PromotionService) SetStrongKeywords(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.strongKeywordLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"strong": keywords,
		}).
		Post(promotionAPIURL + "/adv/v1/search/set-strong")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set strong keywords failed: %s", resp.String())
	}

	return nil
}

// SetExcludedKeywords 设置搜索广告排除词
func (s *PromotionService) SetExcludedKeywords(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.excludedKeywordLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"excluded": keywords,
		}).
		Post(promotionAPIURL + "/adv/v1/search/set-excluded")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set excluded keywords failed: %s", resp.String())
	}

	return nil
}

// SetAutoExcluded 设置自动广告排除商品
func (s *PromotionService) SetAutoExcluded(advertID int, keywords []string) error {
	// 等待限流器许可
	if err := s.rateLimiter.autoExcludeLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetBody(map[string]interface{}{
			"excluded": keywords,
		}).
		Post(promotionAPIURL + "/adv/v1/auto/set-excluded")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("set auto excluded failed: %s", resp.String())
	}

	return nil
}

// GetAutoNmsToAdd 获取可添加到自动广告的商品列表
func (s *PromotionService) GetAutoNmsToAdd(advertID int) ([]int, error) {
	// 等待限流器许可
	if err := s.rateLimiter.autoNmsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []int
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/auto/getnmtoadd")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get auto nms to add failed: %s", resp.String())
	}

	return result, nil
}

// UpdateAutoNms 更新自动广告商品列表
func (s *PromotionService) UpdateAutoNms(req *AutoUpdateNmRequest) error {
	// 等待限流器许可
	if err := s.rateLimiter.autoUpdateLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(req).
		Post(promotionAPIURL + "/adv/v1/auto/updatenm")

	if err != nil {
		return err
	}

	if resp.IsError() {
		return fmt.Errorf("update auto nms failed: %s", resp.String())
	}

	return nil
}

// GetFullStats 获取广告活动完整统计数据
func (s *PromotionService) GetFullStats(reqs []*StatsRequest) ([]StatsResponse, error) {
	// 等待限流器许可（每分钟1次请求）
	if err := s.rateLimiter.fullStatsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}
	var result []StatsResponse
	resp, err := s.client.R().
		SetBody(reqs).
		SetResult(&result).
		Post(promotionAPIURL + "/adv/v2/fullstats")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get full stats failed: %s", resp.String())
	}

	return result, nil
}

// GetAutoStatWords 获取自动广告词组统计数据
func (s *PromotionService) GetAutoStatWords(advertID int) (*AutoStatWordsResponse, error) {
	// 等待限流器许可（每秒4次请求）
	if err := s.rateLimiter.autoStatWordsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	result := &AutoStatWordsResponse{}
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetResult(result).
		Get(promotionAPIURL + "/adv/v2/auto/stat-words")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get auto stat words failed: %s", resp.String())
	}

	return result, nil
}

// GetStatWords 获取搜索广告词组统计数据
func (s *PromotionService) GetStatWords(advertID int) (*StatWordsResponse, error) {
	// 等待限流器许可（每秒4次请求）
	if err := s.rateLimiter.statWordsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	result := &StatWordsResponse{}
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetResult(result).
		Get(promotionAPIURL + "/adv/v1/stat/words")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get stat words failed: %s", resp.String())
	}

	return result, nil
}

// GetKeywordsStats 获取关键词统计数据
func (s *PromotionService) GetKeywordsStats(advertID int, from, to time.Time) (*KeywordsStatsResponse, error) {
	// 等待限流器许可（每秒4次请求）
	if err := s.rateLimiter.keywordsStatsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	result := &KeywordsStatsResponse{}
	resp, err := s.client.R().
		SetQueryParams(map[string]string{
			"advert_id": fmt.Sprintf("%d", advertID),
			"from":      from.Format("2006-01-02"),
			"to":        to.Format("2006-01-02"),
		}).
		SetResult(result).
		Get(promotionAPIURL + "/adv/v0/stats/keywords")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get keywords stats failed: %s", resp.String())
	}

	return result, nil
}

// GetMediaStats 获取媒体广告统计数据
func (s *PromotionService) GetMediaStats(req *StatsRequest) ([]StatsResponse, error) {
	// 等待限流器许可（每秒60次请求）
	if err := s.rateLimiter.mediaStatsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result []StatsResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(promotionAPIURL + "/adv/v1/stats")

	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, fmt.Errorf("get media stats failed: %s", resp.String())
	}

	return result, nil
}

// ===== 财务管理方法 =====

// GetBalance 获取推广账户余额
func (s *PromotionService) GetBalance() (*PromotionBalanceResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result PromotionBalanceResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/balance")

	if err != nil {
		return nil, fmt.Errorf("获取推广账户余额失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取推广账户余额失败: %s", resp.String())
	}

	return &result, nil
}

// GetCampaignBudget 获取活动预算
func (s *PromotionService) GetCampaignBudget(campaignID int) (*CampaignBudgetResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CampaignBudgetResponse
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/budget")

	if err != nil {
		return nil, fmt.Errorf("获取活动预算失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取活动预算失败: %s", resp.String())
	}

	return &result, nil
}

// DepositCampaignBudget 为活动充值预算
func (s *PromotionService) DepositCampaignBudget(campaignID int, sum int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"id":  campaignID,
			"sum": sum,
		}).
		Post(promotionAPIURL + "/adv/v1/budget/deposit")

	if err != nil {
		return fmt.Errorf("为活动充值预算失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("为活动充值预算失败: %s", resp.String())
	}

	return nil
}

// GetExpenseHistory 获取支出历史
func (s *PromotionService) GetExpenseHistory(from, to time.Time) (*ExpenseHistoryResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ExpenseHistoryResponse
	resp, err := s.client.R().
		SetQueryParam("from", from.Format("2006-01-02")).
		SetQueryParam("to", to.Format("2006-01-02")).
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/upd")

	if err != nil {
		return nil, fmt.Errorf("获取支出历史失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取支出历史失败: %s", resp.String())
	}

	return &result, nil
}

// GetPaymentHistory 获取充值历史
func (s *PromotionService) GetPaymentHistory(from, to time.Time) (*PaymentHistoryResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result PaymentHistoryResponse
	resp, err := s.client.R().
		SetQueryParam("from", from.Format("2006-01-02")).
		SetQueryParam("to", to.Format("2006-01-02")).
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/payments")

	if err != nil {
		return nil, fmt.Errorf("获取充值历史失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取充值历史失败: %s", resp.String())
	}

	return &result, nil
}

// ===== 媒体广告管理方法 =====

// GetMediaCampaigns 获取媒体广告活动列表
func (s *PromotionService) GetMediaCampaigns(params *MediaCampaignQueryParams) (*MediaCampaignsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result MediaCampaignsResponse
	req := s.client.R().SetResult(&result)

	if params != nil {
		if params.Type != 0 {
			req.SetQueryParam("type", fmt.Sprintf("%d", params.Type))
		}
		if params.Status != 0 {
			req.SetQueryParam("status", fmt.Sprintf("%d", params.Status))
		}
		if params.Limit > 0 {
			req.SetQueryParam("limit", fmt.Sprintf("%d", params.Limit))
		}
		if params.Offset > 0 {
			req.SetQueryParam("offset", fmt.Sprintf("%d", params.Offset))
		}
	}

	resp, err := req.Get(promotionAPIURL + "/adv/v1/media/campaigns")

	if err != nil {
		return nil, fmt.Errorf("获取媒体广告活动列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取媒体广告活动列表失败: %s", resp.String())
	}

	return &result, nil
}

// CreateMediaCampaign 创建媒体广告活动
func (s *PromotionService) CreateMediaCampaign(req *CreateMediaCampaignRequest) (*CreateMediaCampaignResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CreateMediaCampaignResponse
	resp, err := s.client.R().
		SetBody(req).
		SetResult(&result).
		Post(promotionAPIURL + "/adv/v1/media/campaigns")

	if err != nil {
		return nil, fmt.Errorf("创建媒体广告活动失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("创建媒体广告活动失败: %s", resp.String())
	}

	return &result, nil
}

// UpdateMediaCampaign 更新媒体广告活动
func (s *PromotionService) UpdateMediaCampaign(campaignID int, req *UpdateMediaCampaignRequest) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(req).
		Put(promotionAPIURL + fmt.Sprintf("/adv/v1/media/campaigns/%d", campaignID))

	if err != nil {
		return fmt.Errorf("更新媒体广告活动失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("更新媒体广告活动失败: %s", resp.String())
	}

	return nil
}

// GetMediaCampaignDetails 获取媒体广告活动详情
func (s *PromotionService) GetMediaCampaignDetails(campaignID int) (*MediaCampaignDetails, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result MediaCampaignDetails
	resp, err := s.client.R().
		SetResult(&result).
		Get(promotionAPIURL + fmt.Sprintf("/adv/v1/media/campaigns/%d", campaignID))

	if err != nil {
		return nil, fmt.Errorf("获取媒体广告活动详情失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取媒体广告活动详情失败: %s", resp.String())
	}

	return &result, nil
}

// PauseMediaCampaign 暂停媒体广告活动
func (s *PromotionService) PauseMediaCampaign(campaignID int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		Post(promotionAPIURL + fmt.Sprintf("/adv/v1/media/campaigns/%d/pause", campaignID))

	if err != nil {
		return fmt.Errorf("暂停媒体广告活动失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("暂停媒体广告活动失败: %s", resp.String())
	}

	return nil
}

// StartMediaCampaign 启动媒体广告活动
func (s *PromotionService) StartMediaCampaign(campaignID int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		Post(promotionAPIURL + fmt.Sprintf("/adv/v1/media/campaigns/%d/start", campaignID))

	if err != nil {
		return fmt.Errorf("启动媒体广告活动失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("启动媒体广告活动失败: %s", resp.String())
	}

	return nil
}

// ===== 日历促销管理方法 =====

// GetPromotionCalendar 获取促销日历
func (s *PromotionService) GetPromotionCalendar() (*PromotionCalendarResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result PromotionCalendarResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/promotion/calendar")

	if err != nil {
		return nil, fmt.Errorf("获取促销日历失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取促销日历失败: %s", resp.String())
	}

	return &result, nil
}

// GetPromotionDetails 获取促销活动详情
func (s *PromotionService) GetPromotionDetails(promotionID int) (*PromotionDetailsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result PromotionDetailsResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(promotionAPIURL + fmt.Sprintf("/adv/v1/promotion/%d", promotionID))

	if err != nil {
		return nil, fmt.Errorf("获取促销活动详情失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取促销活动详情失败: %s", resp.String())
	}

	return &result, nil
}

// ParticipateInPromotion 参与促销活动
func (s *PromotionService) ParticipateInPromotion(req *ParticipatePromotionRequest) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(req).
		Post(promotionAPIURL + "/adv/v1/promotion/participate")

	if err != nil {
		return fmt.Errorf("参与促销活动失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("参与促销活动失败: %s", resp.String())
	}

	return nil
}

// GetPromotionParticipation 获取促销参与状态
func (s *PromotionService) GetPromotionParticipation(promotionID int) (*PromotionParticipationResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result PromotionParticipationResponse
	resp, err := s.client.R().
		SetQueryParam("promotionId", fmt.Sprintf("%d", promotionID)).
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/promotion/participation")

	if err != nil {
		return nil, fmt.Errorf("获取促销参与状态失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取促销参与状态失败: %s", resp.String())
	}

	return &result, nil
}

// ===== 固定短语管理方法 =====

// GetFixedPhrases 获取固定短语列表
func (s *PromotionService) GetFixedPhrases(advertID int) (*FixedPhrasesResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result FixedPhrasesResponse
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/search/set-fixed")

	if err != nil {
		return nil, fmt.Errorf("获取固定短语列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取固定短语列表失败: %s", resp.String())
	}

	return &result, nil
}

// SetFixedPhrases 设置固定短语
func (s *PromotionService) SetFixedPhrases(advertID int, phrases []FixedPhraseItem) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"id":      advertID,
			"phrases": phrases,
		}).
		Post(promotionAPIURL + "/adv/v1/search/set-fixed")

	if err != nil {
		return fmt.Errorf("设置固定短语失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("设置固定短语失败: %s", resp.String())
	}

	return nil
}

// ===== 其他管理方法 =====

// GetCampaignStatistics 获取活动统计概览
func (s *PromotionService) GetCampaignStatistics(campaignID int, from, to time.Time) (*CampaignStatisticsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CampaignStatisticsResponse
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", campaignID)).
		SetQueryParam("from", from.Format("2006-01-02")).
		SetQueryParam("to", to.Format("2006-01-02")).
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/stat/summary")

	if err != nil {
		return nil, fmt.Errorf("获取活动统计概览失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取活动统计概览失败: %s", resp.String())
	}

	return &result, nil
}

// GetCampaignKeywords 获取活动关键词列表
func (s *PromotionService) GetCampaignKeywords(advertID int) (*CampaignKeywordsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CampaignKeywordsResponse
	resp, err := s.client.R().
		SetQueryParam("id", fmt.Sprintf("%d", advertID)).
		SetResult(&result).
		Get(promotionAPIURL + "/adv/v1/search/keywords")

	if err != nil {
		return nil, fmt.Errorf("获取活动关键词列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取活动关键词列表失败: %s", resp.String())
	}

	return &result, nil
}

// UpdateCampaignKeywords 更新活动关键词
func (s *PromotionService) UpdateCampaignKeywords(advertID int, keywords []KeywordItem) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(map[string]interface{}{
			"id":       advertID,
			"keywords": keywords,
		}).
		Post(promotionAPIURL + "/adv/v1/search/keywords")

	if err != nil {
		return fmt.Errorf("更新活动关键词失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("更新活动关键词失败: %s", resp.String())
	}

	return nil
}
