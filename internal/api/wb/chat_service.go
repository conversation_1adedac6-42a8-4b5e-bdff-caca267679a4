package wbapi

import (
	"context"
	"fmt"

	"github.com/go-resty/resty/v2"
)

// ChatService 聊天服务
type ChatService struct {
	client      *resty.Client
	rateLimiter *RateLimiter
}

// newChatService 创建新的聊天服务
func newChatService(c *Client) *ChatService {
	httpClient := resty.New()

	if c.httpClient != nil {
		httpClient.SetTimeout(c.httpClient.GetClient().Timeout)
		for k, v := range c.httpClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &ChatService{
		client:      httpClient,
		rateLimiter: NewRateLimiter(),
	}
}

// GetChats 获取聊天列表
func (s *ChatService) GetChats() (*ChatsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ChatsResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(buyerChatAPIURL + "/api/v1/chats")

	if err != nil {
		return nil, fmt.Errorf("获取聊天列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取聊天列表失败: %s", resp.String())
	}

	return &result, nil
}

// GetChatMessages 获取聊天消息
func (s *ChatService) GetChatMessages(chatID string) (*ChatMessagesResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ChatMessagesResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(buyerChatAPIURL + "/api/v1/chats/" + chatID + "/messages")

	if err != nil {
		return nil, fmt.Errorf("获取聊天消息失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取聊天消息失败: %s", resp.String())
	}

	return &result, nil
}

// SendMessage 发送消息
func (s *ChatService) SendMessage(chatID string, message *SendMessageRequest) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(message).
		Post(buyerChatAPIURL + "/api/v1/chats/" + chatID + "/messages")

	if err != nil {
		return fmt.Errorf("发送消息失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("发送消息失败: %s", resp.String())
	}

	return nil
}

// MarkChatAsRead 标记聊天为已读
func (s *ChatService) MarkChatAsRead(chatID string) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		Post(buyerChatAPIURL + "/api/v1/chats/" + chatID + "/mark-read")

	if err != nil {
		return fmt.Errorf("标记聊天为已读失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("标记聊天为已读失败: %s", resp.String())
	}

	return nil
}

// GetUnreadChatsCount 获取未读聊天数量
func (s *ChatService) GetUnreadChatsCount() (*UnreadChatsCountResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result UnreadChatsCountResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(buyerChatAPIURL + "/api/v1/chats/unread-count")

	if err != nil {
		return nil, fmt.Errorf("获取未读聊天数量失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取未读聊天数量失败: %s", resp.String())
	}

	return &result, nil
}
