package wbapi

// Category 分类信息
type Category struct {
	ID       int    `json:"id"`       // 分类ID
	Name     string `json:"name"`     // 分类名称
	ParentID *int   `json:"parentId"` // 父分类ID
}

// Subject 主题信息
type Subject struct {
	ID         int    `json:"id"`         // 主题ID
	Name       string `json:"name"`       // 主题名称
	CategoryID int    `json:"categoryId"` // 分类ID
}

// Characteristic 特征信息
type Characteristic struct {
	ID       int    `json:"id"`       // 特征ID
	Name     string `json:"name"`     // 特征名称
	Required bool   `json:"required"` // 是否必填
	Type     string `json:"type"`     // 特征类型
}

// MediaFile 媒体文件信息
type MediaFile struct {
	ID   string `json:"id"`   // 文件ID
	URL  string `json:"url"`  // 文件URL
	Type string `json:"type"` // 文件类型
}

// Tag 标签信息
type Tag struct {
	ID    int    `json:"id"`    // 标签ID
	Name  string `json:"name"`  // 标签名称
	Color string `json:"color"` // 标签颜色
}

// ===== API 响应结构 =====

// ParentCategoriesResponse 父分类响应
type ParentCategoriesResponse struct {
	Data             []ParentCategory `json:"data"`             // 分类数据
	Error            bool             `json:"error"`            // 错误标志
	ErrorText        string           `json:"errorText"`        // 错误信息
	AdditionalErrors *string          `json:"additionalErrors"` // 附加错误
}

// ParentCategory 父分类信息
type ParentCategory struct {
	Name      string `json:"name"`      // 分类名称
	ID        int    `json:"id"`        // 父分类ID
	IsVisible bool   `json:"isVisible"` // 网站可见性
}

// ===== 特征相关结构 =====

// CharacteristicsResponse 特征响应
type CharacteristicsResponse struct {
	Data             []ObjectCharacteristic `json:"data"`             // 特征数据
	Error            bool                   `json:"error"`            // 错误标志
	ErrorText        string                 `json:"errorText"`        // 错误信息
	AdditionalErrors *string                `json:"additionalErrors"` // 附加错误
}

// ObjectCharacteristic 对象特征
type ObjectCharacteristic struct {
	ID           int                    `json:"id"`           // 特征ID
	Name         string                 `json:"name"`         // 特征名称
	Required     bool                   `json:"required"`     // 是否必填
	Type         string                 `json:"type"`         // 特征类型
	PopularValue string                 `json:"popularValue"` // 热门值
	CharcType    int                    `json:"charcType"`    // 特征类型编号
	Dictionary   []CharacteristicOption `json:"dictionary"`   // 字典选项
}

// CharacteristicOption 特征选项
type CharacteristicOption struct {
	ID    int    `json:"id"`    // 选项ID
	Value string `json:"value"` // 选项值
}

// ===== 商品卡片相关结构 =====

// ProductCardRequest 创建商品卡片请求
type ProductCardRequest struct {
	SubjectID       int                        `json:"subjectID"`       // 类目ID
	Variants        []ProductVariant           `json:"variants"`        // 商品变体
	Characteristics []ProductCharacteristicReq `json:"characteristics"` // 特征
}

// ProductVariant 商品变体
type ProductVariant struct {
	VendorCode      string        `json:"vendorCode"`      // 商家编码
	Title           string        `json:"title"`           // 标题
	Description     string        `json:"description"`     // 描述
	Brand           string        `json:"brand"`           // 品牌
	Dimensions      ProductDim    `json:"dimensions"`      // 尺寸
	Characteristics []interface{} `json:"characteristics"` // 变体特征
	Sizes           []ProductSizeInfo `json:"sizes"`           // 尺码
}

// ProductDim 商品尺寸
type ProductDim struct {
	Length int `json:"length"` // 长度(mm)
	Width  int `json:"width"`  // 宽度(mm)
	Height int `json:"height"` // 高度(mm)
}

// ProductSizeInfo 商品尺码信息
type ProductSizeInfo struct {
	TechSize string   `json:"techSize"` // 技术尺码
	WbSize   string   `json:"wbSize"`   // WB尺码
	Price    int      `json:"price"`    // 价格(копейки)
	Skus     []string `json:"skus"`     // SKU列表
}

// ProductCharacteristicReq 商品特征请求
type ProductCharacteristicReq struct {
	ID    int         `json:"id"`    // 特征ID
	Value interface{} `json:"value"` // 特征值
}

// CreateCardResponse 创建卡片响应
type CreateCardResponse struct {
	Data struct {
		Cards []CreatedCard `json:"cards"` // 创建的卡片
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// CreatedCard 已创建的卡片
type CreatedCard struct {
	NMID       int    `json:"nmID"`       // 商品ID
	VendorCode string `json:"vendorCode"` // 商家编码
	Sizes      []struct {
		ChrtID   int    `json:"chrtID"`   // 特征ID
		TechSize string `json:"techSize"` // 技术尺码
		Skus     []string `json:"skus"`   // SKU列表
	} `json:"sizes"`
}

// ProductCardUpdateRequest 更新商品卡片请求
type ProductCardUpdateRequest struct {
	NMID            int                        `json:"nmID"`            // 商品ID
	VendorCode      string                     `json:"vendorCode"`      // 商家编码
	Title           string                     `json:"title"`           // 标题
	Description     string                     `json:"description"`     // 描述
	Brand           string                     `json:"brand"`           // 品牌
	Dimensions      ProductDim                 `json:"dimensions"`      // 尺寸
	Characteristics []ProductCharacteristicReq `json:"characteristics"` // 特征
}

// UpdateCardResponse 更新卡片响应
type UpdateCardResponse struct {
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}

// ===== 标签相关结构 =====

// TagsResponse 标签响应
type TagsResponse struct {
	Data             []ContentTag `json:"data"`             // 标签数据
	Error            bool         `json:"error"`            // 错误标志
	ErrorText        string       `json:"errorText"`        // 错误信息
	AdditionalErrors *string      `json:"additionalErrors"` // 附加错误
}

// ContentTag 内容标签
type ContentTag struct {
	ID    int    `json:"id"`    // 标签ID
	Name  string `json:"name"`  // 标签名称
	Color string `json:"color"` // 标签颜色
}

// CreateTagRequest 创建标签请求
type CreateTagRequest struct {
	Name  string `json:"name"`  // 标签名称
	Color string `json:"color"` // 标签颜色
}

// CreateTagResponse 创建标签响应
type CreateTagResponse struct {
	Data struct {
		ID int `json:"id"` // 新创建的标签ID
	} `json:"data"`
	Error            bool   `json:"error"`
	ErrorText        string `json:"errorText"`
	AdditionalErrors string `json:"additionalErrors"`
}
