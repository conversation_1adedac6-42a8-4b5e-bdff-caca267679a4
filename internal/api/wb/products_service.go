package wbapi

import (
	"context"
	"fmt"

	"github.com/go-resty/resty/v2"
)

// 使用 constants.go 中定义的 productsAPIURL



// ProductsService 商品服务
type ProductsService struct {
	client      *resty.Client // 独立的HTTP客户端
	rateLimiter *RateLimiter
}

// newProductsService 创建新的商品服务
func newProductsService(c *Client) *ProductsService {
	// 创建独立的HTTP客户端
	httpClient := resty.New()
	// 复制原始客户端的通用设置
	if c.httpClient != nil {
		httpClient.SetTimeout(c.httpClient.GetClient().Timeout)
		// 复制认证头
		for k, v := range c.httpClient.Header {
			httpClient.SetHeader(k, v[0])
		}
	}

	return &ProductsService{
		client:      httpClient,
		rateLimiter: NewRateLimiter(),
	}
}

// GetCardsList 获取商品卡片列表
func (s *ProductsService) GetCardsList(req *CardsListRequest, locale string) (*CardsListResponse, error) {
	// 等待限流器许可
	if err := s.rateLimiter.cardsListLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}
	resp := &CardsListResponse{}
	_, err := s.client.R().
		SetQueryParam("locale", locale).
		SetBody(req).
		SetResult(resp).
		Post(productsAPIURL + "/content/v2/get/cards/list")

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// ===== 目录和分类相关方法 =====

// GetAllObjects 获取所有分类
func (s *ProductsService) GetAllObjects(locale string) (*AllObjectsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result AllObjectsResponse
	req := s.client.R().SetResult(&result)

	if locale != "" {
		req.SetQueryParam("locale", locale)
	}

	resp, err := req.Get(productsAPIURL + "/content/v2/object/all")

	if err != nil {
		return nil, fmt.Errorf("获取所有分类失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取所有分类失败: %s", resp.String())
	}

	return &result, nil
}

// GetObjectCharacteristics 获取分类特征
func (s *ProductsService) GetObjectCharacteristics(subjectID int, locale string) (*ObjectCharacteristicsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ObjectCharacteristicsResponse
	req := s.client.R().SetResult(&result)

	if locale != "" {
		req.SetQueryParam("locale", locale)
	}

	resp, err := req.Get(productsAPIURL + fmt.Sprintf("/content/v2/object/charcs/%d", subjectID))

	if err != nil {
		return nil, fmt.Errorf("获取分类特征失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取分类特征失败: %s", resp.String())
	}

	return &result, nil
}

// GetColors 获取颜色目录
func (s *ProductsService) GetColors(locale string) (*ColorsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result ColorsResponse
	req := s.client.R().SetResult(&result)

	if locale != "" {
		req.SetQueryParam("locale", locale)
	}

	resp, err := req.Get(productsAPIURL + "/content/v2/directory/colors")

	if err != nil {
		return nil, fmt.Errorf("获取颜色目录失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取颜色目录失败: %s", resp.String())
	}

	return &result, nil
}

// GetKinds 获取种类目录
func (s *ProductsService) GetKinds(locale string) (*KindsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result KindsResponse
	req := s.client.R().SetResult(&result)

	if locale != "" {
		req.SetQueryParam("locale", locale)
	}

	resp, err := req.Get(productsAPIURL + "/content/v2/directory/kinds")

	if err != nil {
		return nil, fmt.Errorf("获取种类目录失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取种类目录失败: %s", resp.String())
	}

	return &result, nil
}

// GetCountries 获取国家目录
func (s *ProductsService) GetCountries(locale string) (*CountriesResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CountriesResponse
	req := s.client.R().SetResult(&result)

	if locale != "" {
		req.SetQueryParam("locale", locale)
	}

	resp, err := req.Get(productsAPIURL + "/content/v2/directory/countries")

	if err != nil {
		return nil, fmt.Errorf("获取国家目录失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取国家目录失败: %s", resp.String())
	}

	return &result, nil
}

// GetSeasons 获取季节目录
func (s *ProductsService) GetSeasons(locale string) (*SeasonsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result SeasonsResponse
	req := s.client.R().SetResult(&result)

	if locale != "" {
		req.SetQueryParam("locale", locale)
	}

	resp, err := req.Get(productsAPIURL + "/content/v2/directory/seasons")

	if err != nil {
		return nil, fmt.Errorf("获取季节目录失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取季节目录失败: %s", resp.String())
	}

	return &result, nil
}

// ===== 商品卡片管理方法 =====

// CreateCard 创建商品卡片
func (s *ProductsService) CreateCard(card *ProductCardRequest) (*CreateCardResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CreateCardResponse
	resp, err := s.client.R().
		SetBody(card).
		SetResult(&result).
		Post(productsAPIURL + "/content/v2/cards/upload")

	if err != nil {
		return nil, fmt.Errorf("创建商品卡片失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("创建商品卡片失败: %s", resp.String())
	}

	return &result, nil
}

// UpdateCard 更新商品卡片
func (s *ProductsService) UpdateCard(card *ProductCardUpdateRequest) (*UpdateCardResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result UpdateCardResponse
	resp, err := s.client.R().
		SetBody(card).
		SetResult(&result).
		Post(productsAPIURL + "/content/v2/cards/update")

	if err != nil {
		return nil, fmt.Errorf("更新商品卡片失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("更新商品卡片失败: %s", resp.String())
	}

	return &result, nil
}

// GetCardByNMID 根据NMID获取商品卡片
func (s *ProductsService) GetCardByNMID(nmID int, locale string) (*CardResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CardResponse
	req := s.client.R().SetResult(&result)

	if locale != "" {
		req.SetQueryParam("locale", locale)
	}

	resp, err := req.Get(productsAPIURL + fmt.Sprintf("/content/v2/get/cards/data/%d", nmID))

	if err != nil {
		return nil, fmt.Errorf("获取商品卡片失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取商品卡片失败: %s", resp.String())
	}

	return &result, nil
}

// DeleteCard 删除商品卡片
func (s *ProductsService) DeleteCard(nmID int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		Delete(productsAPIURL + fmt.Sprintf("/content/v2/cards/delete/%d", nmID))

	if err != nil {
		return fmt.Errorf("删除商品卡片失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("删除商品卡片失败: %s", resp.String())
	}

	return nil
}

// MoveCard 移动商品卡片到回收站
func (s *ProductsService) MoveCard(nmID int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		Post(productsAPIURL + fmt.Sprintf("/content/v2/cards/moveToTrash/%d", nmID))

	if err != nil {
		return fmt.Errorf("移动商品卡片到回收站失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("移动商品卡片到回收站失败: %s", resp.String())
	}

	return nil
}

// RecoverCard 从回收站恢复商品卡片
func (s *ProductsService) RecoverCard(nmID int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		Post(productsAPIURL + fmt.Sprintf("/content/v2/cards/recover/%d", nmID))

	if err != nil {
		return fmt.Errorf("恢复商品卡片失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("恢复商品卡片失败: %s", resp.String())
	}

	return nil
}

// ===== 价格管理方法 =====

// GetPrices 获取商品价格信息
func (s *ProductsService) GetPrices(nmIDs []int) (*PricesResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result PricesResponse
	resp, err := s.client.R().
		SetQueryParam("nm", fmt.Sprintf("%v", nmIDs)).
		SetResult(&result).
		Get(priceAPIURL + "/api/v2/list/goods/filter")

	if err != nil {
		return nil, fmt.Errorf("获取商品价格信息失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取商品价格信息失败: %s", resp.String())
	}

	return &result, nil
}

// GetPricesBySize 根据尺码获取价格信息
func (s *ProductsService) GetPricesBySize(nmID int) (*PricesBySizeResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result PricesBySizeResponse
	resp, err := s.client.R().
		SetQueryParam("nm", fmt.Sprintf("%d", nmID)).
		SetResult(&result).
		Get(priceAPIURL + "/api/v2/list/goods/size/nm")

	if err != nil {
		return nil, fmt.Errorf("根据尺码获取价格信息失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("根据尺码获取价格信息失败: %s", resp.String())
	}

	return &result, nil
}

// UpdatePrices 更新商品价格
func (s *ProductsService) UpdatePrices(prices []PriceUpdate) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(prices).
		Post(priceAPIURL + "/api/v2/upload/task")

	if err != nil {
		return fmt.Errorf("更新商品价格失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("更新商品价格失败: %s", resp.String())
	}

	return nil
}

// GetDiscounts 获取折扣信息
func (s *ProductsService) GetDiscounts(nmIDs []int) (*DiscountsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result DiscountsResponse
	resp, err := s.client.R().
		SetQueryParam("nm", fmt.Sprintf("%v", nmIDs)).
		SetResult(&result).
		Get(priceAPIURL + "/api/v2/list/goods/filter")

	if err != nil {
		return nil, fmt.Errorf("获取折扣信息失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取折扣信息失败: %s", resp.String())
	}

	return &result, nil
}

// UpdateDiscounts 更新折扣
func (s *ProductsService) UpdateDiscounts(discounts []DiscountUpdate) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(discounts).
		Post(priceAPIURL + "/api/v2/upload/task")

	if err != nil {
		return fmt.Errorf("更新折扣失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("更新折扣失败: %s", resp.String())
	}

	return nil
}

// ===== 库存管理方法 =====

// GetStocks 获取库存信息
func (s *ProductsService) GetStocks(warehouseID int) (*StocksResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result StocksResponse
	resp, err := s.client.R().
		SetQueryParam("warehouseId", fmt.Sprintf("%d", warehouseID)).
		SetResult(&result).
		Get(marketplaceAPIURL + "/api/v3/stocks")

	if err != nil {
		return nil, fmt.Errorf("获取库存信息失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取库存信息失败: %s", resp.String())
	}

	return &result, nil
}

// UpdateStocks 更新库存
func (s *ProductsService) UpdateStocks(stocks []StockUpdate) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(stocks).
		Put(marketplaceAPIURL + "/api/v3/stocks")

	if err != nil {
		return fmt.Errorf("更新库存失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("更新库存失败: %s", resp.String())
	}

	return nil
}

// GetWarehouses 获取仓库列表
func (s *ProductsService) GetWarehouses() (*WarehousesResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result WarehousesResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(marketplaceAPIURL + "/api/v3/warehouses")

	if err != nil {
		return nil, fmt.Errorf("获取仓库列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取仓库列表失败: %s", resp.String())
	}

	return &result, nil
}

// UpdateWarehouse 更新仓库信息
func (s *ProductsService) UpdateWarehouse(warehouseID int, warehouse *WarehouseUpdate) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(warehouse).
		Put(marketplaceAPIURL + fmt.Sprintf("/api/v3/warehouses/%d", warehouseID))

	if err != nil {
		return fmt.Errorf("更新仓库信息失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("更新仓库信息失败: %s", resp.String())
	}

	return nil
}

// ===== 媒体管理方法 =====

// UploadMedia 上传媒体文件
func (s *ProductsService) UploadMedia(media *MediaUploadRequest) (*MediaUploadResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result MediaUploadResponse
	resp, err := s.client.R().
		SetBody(media).
		SetResult(&result).
		Post(productsAPIURL + "/content/v2/media/save")

	if err != nil {
		return nil, fmt.Errorf("上传媒体文件失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("上传媒体文件失败: %s", resp.String())
	}

	return &result, nil
}

// DeleteMedia 删除媒体文件
func (s *ProductsService) DeleteMedia(vendorCode string) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetQueryParam("vendorCode", vendorCode).
		Delete(productsAPIURL + "/content/v2/media/delete")

	if err != nil {
		return fmt.Errorf("删除媒体文件失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("删除媒体文件失败: %s", resp.String())
	}

	return nil
}

// ===== 标签管理方法 =====

// GetTags 获取标签列表
func (s *ProductsService) GetTags() (*TagsResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result TagsResponse
	resp, err := s.client.R().
		SetResult(&result).
		Get(productsAPIURL + "/content/v2/tags")

	if err != nil {
		return nil, fmt.Errorf("获取标签列表失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("获取标签列表失败: %s", resp.String())
	}

	return &result, nil
}

// CreateTag 创建标签
func (s *ProductsService) CreateTag(tag *CreateTagRequest) (*CreateTagResponse, error) {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return nil, fmt.Errorf("rate limit exceeded: %w", err)
	}

	var result CreateTagResponse
	resp, err := s.client.R().
		SetBody(tag).
		SetResult(&result).
		Post(productsAPIURL + "/content/v2/tags")

	if err != nil {
		return nil, fmt.Errorf("创建标签失败: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("创建标签失败: %s", resp.String())
	}

	return &result, nil
}

// UpdateTag 更新标签
func (s *ProductsService) UpdateTag(tagID int, tag *UpdateTagRequest) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		SetBody(tag).
		Put(productsAPIURL + fmt.Sprintf("/content/v2/tags/%d", tagID))

	if err != nil {
		return fmt.Errorf("更新标签失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("更新标签失败: %s", resp.String())
	}

	return nil
}

// DeleteTag 删除标签
func (s *ProductsService) DeleteTag(tagID int) error {
	if err := s.rateLimiter.campaignsLimiter.Wait(context.Background()); err != nil {
		return fmt.Errorf("rate limit exceeded: %w", err)
	}

	resp, err := s.client.R().
		Delete(productsAPIURL + fmt.Sprintf("/content/v2/tags/%d", tagID))

	if err != nil {
		return fmt.Errorf("删除标签失败: %w", err)
	}

	if resp.IsError() {
		return fmt.Errorf("删除标签失败: %s", resp.String())
	}

	return nil
}
