package wbapi

import "fmt"

// APIError WB API 错误
type APIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *APIError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("WB API错误 [%d]: %s - %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("WB API错误 [%d]: %s", e.Code, e.Message)
}

// RateLimitError 速率限制错误
type RateLimitError struct {
	Service string
	Limit   string
}

func (e *RateLimitError) Error() string {
	return fmt.Sprintf("速率限制: %s 服务超过限制 %s", e.Service, e.Limit)
}

// AuthenticationError 认证错误
type AuthenticationError struct {
	Message string
}

func (e *AuthenticationError) Error() string {
	return fmt.Sprintf("认证失败: %s", e.Message)
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string
	Message string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("验证失败 [%s]: %s", e.Field, e.Message)
}
