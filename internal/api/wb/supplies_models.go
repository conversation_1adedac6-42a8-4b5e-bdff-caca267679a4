package wbapi

import "time"

// ===== 供货单相关模型 =====

// SuppliesResponse 供货单列表响应
type SuppliesResponse struct {
	Supplies []Supply `json:"supplies"` // 供货单列表
	Next     int      `json:"next"`     // 下一页标识
}

// Supply 供货单信息
type Supply struct {
	ID          string    `json:"id"`          // 供货单ID
	Name        string    `json:"name"`        // 供货单名称
	IsLarge     bool      `json:"isLarge"`     // 是否大件
	CreatedAt   time.Time `json:"createdAt"`   // 创建时间
	ClosedAt    *time.Time `json:"closedAt"`   // 关闭时间
	ScanDt      *time.Time `json:"scanDt"`     // 扫描时间
	IsDefective bool      `json:"isDefective"` // 是否有缺陷
	Done        bool      `json:"done"`        // 是否完成
}

// CreateSupplyResponse 创建供货单响应
type CreateSupplyResponse struct {
	ID string `json:"id"` // 新创建的供货单ID
}

// SupplyDetails 供货单详情
type SupplyDetails struct {
	ID          string    `json:"id"`          // 供货单ID
	Name        string    `json:"name"`        // 供货单名称
	IsLarge     bool      `json:"isLarge"`     // 是否大件
	CreatedAt   time.Time `json:"createdAt"`   // 创建时间
	ClosedAt    *time.Time `json:"closedAt"`   // 关闭时间
	ScanDt      *time.Time `json:"scanDt"`     // 扫描时间
	IsDefective bool      `json:"isDefective"` // 是否有缺陷
	Done        bool      `json:"done"`        // 是否完成
	OrdersCount int       `json:"ordersCount"` // 订单数量
}

// SupplyOrdersResponse 供货单订单响应
type SupplyOrdersResponse struct {
	Orders []SupplyOrder `json:"orders"` // 订单列表
}

// SupplyOrder 供货单中的订单
type SupplyOrder struct {
	ID              int       `json:"id"`              // 订单ID
	RID             string    `json:"rid"`             // 订单唯一标识
	CreatedAt       time.Time `json:"createdAt"`       // 创建时间
	OfficesNames    []string  `json:"officesNames"`    // 仓库名称列表
	SupplierArticle string    `json:"supplierArticle"` // 供应商商品编码
	TechSize        string    `json:"techSize"`        // 技术尺寸
	Barcode         string    `json:"barcode"`         // 条形码
	TotalPrice      int       `json:"totalPrice"`      // 总价格（копейки）
	IsLarge         bool      `json:"isLarge"`         // 是否大件
	WarehouseID     int       `json:"warehouseId"`     // 仓库ID
	NMID            int       `json:"nmId"`            // 商品ID
	ChrtID          int       `json:"chrtId"`          // 特征ID
	Price           int       `json:"price"`           // 价格（копейки）
	ConvertedPrice  int       `json:"convertedPrice"`  // 转换价格（копейки）
	CurrencyCode    int       `json:"currencyCode"`    // 货币代码
	ConvertedCurrencyCode int `json:"convertedCurrencyCode"` // 转换货币代码
}

// ===== 仓库相关模型 =====

// WarehousesResponse 仓库列表响应
type WarehousesResponse struct {
	Warehouses []Warehouse `json:"warehouses"` // 仓库列表
}

// Warehouse 仓库信息
type Warehouse struct {
	ID           int    `json:"id"`           // 仓库ID
	Name         string `json:"name"`         // 仓库名称
	Address      string `json:"address"`      // 仓库地址
	WorkTime     string `json:"workTime"`     // 工作时间
	AcceptLimits struct {
		Coefficient int `json:"coefficient"` // 系数
		MaxVolume   int `json:"maxVolume"`   // 最大体积
	} `json:"acceptLimits"` // 接收限制
	DeliveryType int  `json:"deliveryType"` // 配送类型
	IsLarge      bool `json:"isLarge"`      // 是否支持大件
}

// ===== 标签相关模型 =====

// LabelsResponse 标签响应
type LabelsResponse struct {
	Stickers []Label `json:"stickers"` // 标签列表
}

// Label 标签信息
type Label struct {
	PartA int    `json:"partA"` // 标签A部分
	PartB int    `json:"partB"` // 标签B部分
	File  string `json:"file"`  // 标签文件（base64编码的PDF）
}

// ===== 请求参数模型 =====

// CreateSupplyRequest 创建供货单请求
type CreateSupplyRequest struct {
	Name string `json:"name"` // 供货单名称
}

// AddOrdersRequest 添加订单请求
type AddOrdersRequest struct {
	Orders []int `json:"orders"` // 订单ID列表
}

// LabelsRequest 获取标签请求
type LabelsRequest struct {
	Orders []int  `json:"orders"` // 订单ID列表
	Type   string `json:"type"`   // 标签类型：png/svg/pdf
	Width  int    `json:"width"`  // 标签宽度
	Height int    `json:"height"` // 标签高度
}
