package supply

import "time"

// WarehouseRecommendRequest 仓库推荐请求
type WarehouseRecommendRequest struct {
	Params struct {
		DraftID string `json:"draftId"`
	} `json:"params"`
	JsonRPC string `json:"jsonrpc"`
	ID      string `json:"id"`
}

// WarehouseRecommendResponse 仓库推荐响应
type WarehouseRecommendResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		Warehouses []Warehouse `json:"warehouses"`
	} `json:"result"`
}

// Warehouse 仓库信息
type Warehouse struct {
	WarehouseID      int     `json:"warehouseID"`
	WarehouseName    string  `json:"warehouseName"`
	WarehouseAddress string  `json:"warehouseAddress,omitempty"`
	WarehouseMapID   int     `json:"warehouseMapID,omitempty"`
	IsRecommended    bool    `json:"isRecommended"`
	IsActive         bool    `json:"isActive"`
	BoxType          BoxType `json:"boxType"`
}

// BoxType 箱子类型信息
type BoxType struct {
	BoxTypeID              int                     `json:"boxTypeID"`
	BoxTypeName            string                  `json:"boxTypeName"`
	AcceptAll              bool                    `json:"acceptAll"`
	Count                  int                     `json:"count"`
	LogisticCoefficient    float64                 `json:"logisticCoefficient,omitempty"`
	LogisticLiter          string                  `json:"logisticLiter,omitempty"`
	LogisticBase           string                  `json:"logisticBase,omitempty"`
	StorageLiter           string                  `json:"storageLiter,omitempty"`
	StorageBase            string                  `json:"storageBase,omitempty"`
	StorageCoefficient     float64                 `json:"storageCoefficient,omitempty"`
	AcceptanceCoefficients []AcceptanceCoefficient `json:"acceptanceCoefficients,omitempty"`
	HasAvailableDate       bool                    `json:"hasAvailableDate"`
	TotalCount             int                     `json:"totalCount"`
}

// AcceptanceCoefficient 验收系数信息
type AcceptanceCoefficient struct {
	Date        time.Time `json:"date"`
	Coefficient float64   `json:"coefficient"`
}
