package supply

import "time"

// CreateSupplyRequest 创建供应单请求
type CreateSupplyRequest struct {
	Params struct {
		BoxTypeMask        int    `json:"boxTypeMask"`
		DraftID            string `json:"draftID"`
		TransitWarehouseId int    `json:"transitWarehouseId,omitempty"`
		WarehouseId        int    `json:"warehouseId"`
	} `json:"params"`
	JsonRPC string `json:"jsonrpc"`
	ID      string `json:"id"`
}

// CreateSupplyResponse 创建供应单响应
type CreateSupplyResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		Ids []struct {
			Id          int    `json:"Id"`
			BoxTypeId   int    `json:"boxTypeId"`
			BoxTypeName string `json:"boxTypeName"`
		} `json:"ids"`
	} `json:"result"`
}

type ListParams struct {
	PageNumber    int    `json:"pageNumber"`
	PageSize      int    `json:"pageSize"`
	SortBy        string `json:"sortBy"`
	SortDirection string `json:"sortDirection"`
	StatusId      int    `json:"statusId"`
}

type ListRequest struct {
	Params  ListParams `json:"params"`
	JsonRPC string     `json:"jsonrpc"`
	ID      string     `json:"id"`
}

type Supply struct {
	PreorderId                   int       `json:"preorderId"`
	SupplyId                     *int      `json:"supplyId"`
	BoxTypeId                    int       `json:"boxTypeId"`
	BoxTypeName                  string    `json:"boxTypeName"`
	CreateDate                   time.Time `json:"createDate"`
	ChangeDate                   time.Time `json:"changeDate"`
	DetailsQuantity              int       `json:"detailsQuantity"`
	WarehouseId                  int       `json:"warehouseId"`
	WarehouseName                string    `json:"warehouseName"`
	WarehouseAddress             string    `json:"warehouseAddress"`
	WarehouseMapID               int       `json:"warehouseMapID"`
	TransitWarehouseId           *int      `json:"transitWarehouseId"`
	TransitWarehouseName         string    `json:"transitWarehouseName"`
	TransitWarehouseAddress      *string   `json:"transitWarehouseAddress"`
	TransitWarehouseMapID        *int      `json:"transitWarehouseMapID"`
	SupplyDate                   *string   `json:"supplyDate"`
	FactDate                     *string   `json:"factDate"`
	IncomeQuantity               int       `json:"incomeQuantity"`
	StatusId                     int       `json:"statusId"`
	StatusName                   string    `json:"statusName"`
	RejectReason                 *string   `json:"rejectReason"`
	VirtualType                  *string   `json:"virtualType"`
	UserUid                      string    `json:"userUid"`
	PaidAcceptanceCoefficient    *float64  `json:"paidAcceptanceCoefficient"`
	AcceptanceCost               float64   `json:"acceptanceCost"`
	HasUnloadProblems            bool      `json:"hasUnloadProblems"`
	SupplierAssignUUID           *string   `json:"supplierAssignUUID"`
	SupplierAssignName           *string   `json:"supplierAssignName"`
	CanShowQuantity              bool      `json:"canShowQuantity"`
	SupplierBoxAmount            *int      `json:"supplierBoxAmount"`
	HasBoxes                     bool      `json:"hasBoxes"`
	FeedbackAllowed              bool      `json:"feedbackAllowed"`
	AcceptanceLiterValue         float64   `json:"acceptanceLiterValue"`
	AcceptanceLiterBase          float64   `json:"acceptanceLiterBase"`
	MonopalletAcceptanceCost     float64   `json:"monopalletAcceptanceCost"`
	MonopalletQuantity           *int      `json:"monopalletQuantity"`
	TariffVolume                 *float64  `json:"tariffVolume"`
	TariffPallet                 *float64  `json:"tariffPallet"`
	ActualWarehouseID            *int      `json:"actualWarehouseID"`
	ActualWarehouseName          string    `json:"actualWarehouseName"`
	ActualWarehouseAddress       *string   `json:"actualWarehouseAddress"`
	ActualWarehouseMapID         *int      `json:"actualWarehouseMapID"`
	OldAcceptanceCost            *float64  `json:"oldAcceptanceCost"`
	OldPaidAcceptanceCoefficient *float64  `json:"oldPaidAcceptanceCoefficient"`
	Volume                       *float64  `json:"volume"`
	TransitCost                  *float64  `json:"transitCost"`
	PassMonopalletQuantity       *int      `json:"passMonopalletQuantity"`
	IsWrongDate                  bool      `json:"isWrongDate"`
	HasPass                      bool      `json:"hasPass"`
	FeedbackDispatchmentAllowed  bool      `json:"feedbackDispatchmentAllowed"`
	FeedbackArrangementAllowed   bool      `json:"feedbackArrangementAllowed"`
	OldFeedbackAllowed           bool      `json:"oldFeedbackAllowed"`
	IsSplitFeedbackForWarehouse  bool      `json:"isSplitFeedbackForWarehouse"`
}

type ListResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		TotalCount int      `json:"totalCount"`
		Data       []Supply `json:"data"`
	} `json:"result"`
}

// GetAcceptanceCostsRequest 获取验收成本请求
type GetAcceptanceCostsRequest struct {
	Params struct {
		DateFrom   string `json:"dateFrom"`
		DateTo     string `json:"dateTo"`
		PreorderID int    `json:"preorderID"`
	} `json:"params"`
	JsonRPC string `json:"jsonrpc"`
	ID      string `json:"id"`
}

// GetAcceptanceCostsResponse 获取验收成本响应
type GetAcceptanceCostsResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		Costs []AcceptanceCost `json:"costs"`
	} `json:"result"`
}

// AcceptanceCost 验收成本信息
type AcceptanceCost struct {
	Date               string                       `json:"date"`
	Cost               float64                      `json:"cost"`
	Coefficient        float64                      `json:"coefficient"`
	DeliveryAndStorage AcceptanceDeliveryAndStorage `json:"deliveryAndStorage"`
	TransitActiveFrom  string                       `json:"transitActiveFrom"`
}

// AcceptanceDeliveryAndStorage 验收配送和存储信息
type AcceptanceDeliveryAndStorage struct {
	DeliveryAndStorageExpr string `json:"deliveryAndStorageExpr"`
	StorageVolumeCut       string `json:"storageVolumeCut"`
	DeliveryValueBase      string `json:"deliveryValueBase"`
	DeliveryValueLiter     string `json:"deliveryValueLiter"`
	StorageValue           string `json:"storageValue"`
	StorageLiter           string `json:"storageLiter"`
	StorageCoef            string `json:"storageCoef"`
	DeliveryCoef           string `json:"deliveryCoef"`
}

// DeleteDraftRequest 删除草稿请求
type DeleteDraftRequest struct {
	Params struct {
		DraftID string `json:"draftID"`
	} `json:"params"`
	JsonRPC string `json:"jsonrpc"`
	ID      string `json:"id"`
}

// DeleteDraftResponse 删除草稿响应
type DeleteDraftResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		Success bool `json:"success"`
	} `json:"result"`
}

// ListDraftsRequest 获取草稿列表请求
type ListDraftsRequest struct {
	Params struct {
		Filter struct {
			OrderBy struct {
				CreatedAt int `json:"createdAt"`
			} `json:"orderBy"`
		} `json:"filter"`
		Limit  int `json:"limit"`
		Offset int `json:"offset"`
	} `json:"params"`
	JsonRPC string `json:"jsonrpc"`
	ID      string `json:"id"`
}

// ListDraftsResponse 获取草稿列表响应
type ListDraftsResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		Total  int         `json:"total"`
		Drafts []DraftInfo `json:"drafts"`
	} `json:"result"`
}

// DraftInfo 草稿信息
type DraftInfo struct {
	ID        string    `json:"id"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
	Items     []struct {
		Barcode  string `json:"barcode"`
		Quantity int    `json:"quantity"`
	} `json:"items"`
}
