package supply

type DraftRequest struct {
	Params  struct{} `json:"params"`
	JsonRPC string   `json:"jsonrpc"`
	ID      string   `json:"id"`
}

type DraftResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		DraftID string `json:"draftID"`
	} `json:"result"`
}

// ListGoodsRequest 获取商品列表请求
type ListGoodsRequest struct {
	Params struct {
		Filter struct {
			Search   string   `json:"search"`
			Brands   []string `json:"brands"`
			Subjects []string `json:"subjects"`
		} `json:"filter"`
		Limit   int    `json:"limit"`
		Offset  int    `json:"offset"`
		DraftID string `json:"draftID"`
	} `json:"params"`
	JsonRPC string `json:"jsonrpc"`
	ID      string `json:"id"`
}

// ListGoodsResponse 获取商品列表响应
type ListGoodsResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		Goods []GoodsItem `json:"goods"`
		Total int         `json:"total"`
	} `json:"result"`
}

// GoodsItem 商品项
type GoodsItem struct {
	ID        int     `json:"ID"`
	Size      string  `json:"size"`
	Brand     string  `json:"brand"`
	Color     string  `json:"color"`
	Subject   string  `json:"subject"`
	Barcode   string  `json:"barcode"`
	Article   string  `json:"article"`
	ArticleWB int     `json:"articleWB"`
	ImgSrc    string  `json:"imgSrc"`
	ImgBigSrc string  `json:"imgBigSrc"`
	Volume    float64 `json:"volume"`
	ImtName   string  `json:"imtName"`
}

// UpdateDraftGoodsRequest 更新草稿商品请求
type UpdateDraftGoodsRequest struct {
	Params struct {
		Barcodes []struct {
			Barcode  string `json:"barcode"`
			Quantity int    `json:"quantity"`
		} `json:"barcodes"`
		DraftID string `json:"draftID"`
	} `json:"params"`
	JsonRPC string `json:"jsonrpc"`
	ID      string `json:"id"`
}

// UpdateDraftGoodsResponse 更新草稿商品响应
type UpdateDraftGoodsResponse struct {
	ID      string   `json:"id"`
	JsonRPC string   `json:"jsonrpc"`
	Result  struct{} `json:"result"`
}
