package supply

type DetailsParams struct {
	PageNumber int    `json:"pageNumber"`
	PageSize   int    `json:"pageSize"`
	PreorderID int    `json:"preorderID"`
	Search     string `json:"search"`
	SupplyID   *int   `json:"supplyID"`
}

type DetailsRequest struct {
	Params  DetailsParams `json:"params"`
	JsonRPC string        `json:"jsonrpc"`
	ID      string        `json:"id"`
}

type DeliveryAndStorage struct {
	DeliveryAndStorageExpr *string  `json:"deliveryAndStorageExpr"`
	StorageVolumeCut       *float64 `json:"storageVolumeCut"`
	DeliveryValueBase      *float64 `json:"deliveryValueBase"`
	DeliveryValueLiter     *float64 `json:"deliveryValueLiter"`
	StorageValue           *float64 `json:"storageValue"`
	StorageCoef            *float64 `json:"storageCoef"`
	DeliveryCoef           *float64 `json:"deliveryCoef"`
}

type FeedbackSplitSection struct {
	Workers  int `json:"workers"`
	Shipment int `json:"shipment"`
	AppealID int `json:"appealID"`
}

type FeedbackArrangeSection struct {
	Speed    int `json:"speed"`
	Quality  int `json:"quality"`
	AppealID int `json:"appealID"`
}

type FeedbackOldSection struct {
	Estimation int `json:"estimation"`
	AppealID   int `json:"appealID"`
}

type FeedbackSplit struct {
	Dispatch FeedbackSplitSection   `json:"dispatch"`
	Arrange  FeedbackArrangeSection `json:"arrange"`
}

type Feedback struct {
	SplitFeedback FeedbackSplit      `json:"splitFeedback"`
	OldFeedback   FeedbackOldSection `json:"oldFeedback"`
}

type SupplyDetails struct {
	PreorderId                     int                `json:"preorderId"`
	SupplyId                       *int               `json:"supplyId"`
	BoxTypeId                      int                `json:"boxTypeId"`
	BoxTypeName                    string             `json:"boxTypeName"`
	CreateDate                     string             `json:"createDate"`
	ChangeDate                     string             `json:"changeDate"`
	DetailsQuantity                int                `json:"detailsQuantity"`
	ArticlesQuantity               int                `json:"articlesQuantity"`
	WarehouseId                    int                `json:"warehouseId"`
	WarehouseName                  string             `json:"warehouseName"`
	WarehouseAddress               string             `json:"warehouseAddress"`
	WarehouseMapID                 int                `json:"warehouseMapID"`
	ElectronicQueueAvailable       bool               `json:"electronicQueueAvailable"`
	TransitWarehouseId             *int               `json:"transitWarehouseId"`
	TransitWarehouseName           string             `json:"transitWarehouseName"`
	TransitWarehouseAddress        *string            `json:"transitWarehouseAddress"`
	TransitWarehouseMapID          *int               `json:"transitWarehouseMapID"`
	SupplyDate                     *string            `json:"supplyDate"`
	FactDate                       *string            `json:"factDate"`
	AcceptedDate                   *string            `json:"acceptedDate"`
	GoodsDate                      *string            `json:"goodsDate"`
	IncomeQuantity                 int                `json:"incomeQuantity"`
	UnloadingQuantity              int                `json:"unloadingQuantity"`
	ReadyForSaleQuantity           int                `json:"readyForSaleQuantity"`
	DepersonalizedQuantity         int                `json:"depersonalizedQuantity"`
	StatusId                       int                `json:"statusId"`
	StatusName                     string             `json:"statusName"`
	RejectReason                   *string            `json:"rejectReason"`
	VirtualType                    *string            `json:"virtualType"`
	HasBoxes                       bool               `json:"hasBoxes"`
	BoxQuantity                    *int               `json:"boxQuantity"`
	HasKIZ                         bool               `json:"hasKIZ"`
	HasPass                        bool               `json:"hasPass"`
	HasBoxBarcodes                 bool               `json:"hasBoxBarcodes"`
	MonopalletQuantity             *int               `json:"monopalletQuantity"`
	PaidAcceptanceCoefficient      *float64           `json:"paidAcceptanceCoefficient"`
	SupplierBoxAmount              *int               `json:"supplierBoxAmount"`
	AcceptanceCost                 float64            `json:"acceptanceCost"`
	HasUnloadProblems              bool               `json:"hasUnloadProblems"`
	SupplierAssignUUID             *string            `json:"supplierAssignUUID"`
	SupplierAssignName             *string            `json:"supplierAssignName"`
	CanShowQuantity                bool               `json:"canShowQuantity"`
	DepersonalizedReasons          []string           `json:"depersonalizedReasons"`
	TariffValue                    *float64           `json:"tariffValue"`
	DeliveryAndStorage             DeliveryAndStorage `json:"deliveryAndStorage"`
	TariffVolume                   *float64           `json:"tariffVolume"`
	TariffPallet                   *float64           `json:"tariffPallet"`
	ActualWarehouseID              *int               `json:"actualWarehouseID"`
	ActualWarehouseName            string             `json:"actualWarehouseName"`
	ActualWarehouseAddress         *string            `json:"actualWarehouseAddress"`
	ActualWarehouseMapID           *int               `json:"actualWarehouseMapID"`
	OldAcceptanceCost              *float64           `json:"oldAcceptanceCost"`
	OldPaidAcceptanceCoefficient   *float64           `json:"oldPaidAcceptanceCoefficient"`
	AcceptanceLiterValue           float64            `json:"acceptanceLiterValue"`
	AcceptanceLiterBase            float64            `json:"acceptanceLiterBase"`
	Volume                         *float64           `json:"volume"`
	TransitCost                    *float64           `json:"transitCost"`
	PassMonopalletQuantity         *int               `json:"passMonopalletQuantity"`
	UserUid                        string             `json:"userUid"`
	MonopalletAcceptanceCost       float64            `json:"monopalletAcceptanceCost"`
	IsWrongDate                    bool               `json:"isWrongDate"`
	BoxBarcodesIncongruityQuantity *int               `json:"boxBarcodesIncongruityQuantity"`
	OriginalGiID                   int                `json:"originalGiID"`
	CorrectedQuantity              *int               `json:"correctedQuantity"`
	FeedbackDispatchmentAllowed    bool               `json:"feedbackDispatchmentAllowed"`
	FeedbackArrangementAllowed     bool               `json:"feedbackArrangementAllowed"`
	OldFeedbackAllowed             bool               `json:"oldFeedbackAllowed"`
	Feedback                       Feedback           `json:"feedback"`
	IsSplitFeedbackForWarehouse    bool               `json:"isSplitFeedbackForWarehouse"`
}

type SupplyItem struct {
	Barcode              string `json:"barcode"`
	ImgSrc               string `json:"imgSrc"`
	ImgBigSrc            string `json:"imgBigSrc"`
	SubjectName          string `json:"subjectName"`
	ColorName            string `json:"colorName"`
	Sa                   string `json:"sa"`
	BrandName            string `json:"brandName"`
	TsName               string `json:"tsName"`
	Quantity             int    `json:"quantity"`
	UnloadingQuantity    int    `json:"unloadingQuantity"`
	ReadyForSaleQuantity int    `json:"readyForSaleQuantity"`
	IncomeQuantity       int    `json:"incomeQuantity"`
	PreorderID           int    `json:"preorderID"`
	NmID                 int    `json:"nmID"`
	ImtName              string `json:"imtName"`
	SupplierBoxAmount    *int   `json:"supplierBoxAmount"`
	CorrectedQuantity    int    `json:"correctedQuantity"`
}

type DetailsResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		TotalCount    int           `json:"totalCount"`
		ItemsQuantity int           `json:"itemsQuantity"`
		Supply        SupplyDetails `json:"supply"`
		Data          []SupplyItem  `json:"data"`
	} `json:"result"`
}
