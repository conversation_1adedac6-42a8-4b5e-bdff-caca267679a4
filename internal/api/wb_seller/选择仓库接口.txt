接下来就是选择仓库接口
curl 'https://seller-supply.wildberries.ru/ns/sm-recommendations/supply-manager/api/v1/recommendations/warehouseRecommendation' \
  -X POST \
  --data-raw '{"params":{"draftId":"988a3c3a-736a-4953-8bce-2c1fc170b696"},"jsonrpc":"2.0","id":"json-rpc_72"}'

返回数据：
{
    "id": "json-rpc_72",
    "jsonrpc": "2.0",
    "result": {
        "warehouses": [
            {
                "warehouseID": 301805,
                "warehouseName": "Новосемейкино",
                "warehouseAddress": "Самарская область, муниципальный район Красноярский, поселок городского типа Новосемейкино, парк Индустриальный, здание 1",
                "warehouseMapID": 1978,
                "isRecommended": true,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 200,
                    "logisticLiter": "19",
                    "logisticBase": "76",
                    "storageLiter": "0,16",
                    "storageBase": "0,16",
                    "storageCoefficient": 200,
                    "acceptanceCoefficients": [
                        {
                            "date": "2025-07-11T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-12T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-13T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-14T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-15T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-16T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-17T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-18T00:00:00Z",
                            "coefficient": 0
                        }
                    ],
                    "hasAvailableDate": true,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 301987,
                "warehouseName": "Сарапул",
                "warehouseAddress": "Удмуртская Республика, г. Сарапул, Ижевский тракт, д. 22",
                "warehouseMapID": 2152,
                "isRecommended": true,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 210,
                    "logisticLiter": "19,95",
                    "logisticBase": "79,8",
                    "storageLiter": "0,17",
                    "storageBase": "0,17",
                    "storageCoefficient": 210,
                    "hasAvailableDate": true,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 324108,
                "warehouseName": "Астана 2",
                "warehouseAddress": "Караганда, 91/2",
                "warehouseMapID": 2142,
                "isRecommended": true,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 145,
                    "logisticLiter": "13,78",
                    "logisticBase": "55,1",
                    "storageLiter": "0,12",
                    "storageBase": "0,12",
                    "storageCoefficient": 145,
                    "acceptanceCoefficients": [
                        {
                            "date": "2025-07-11T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-12T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-13T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-14T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-15T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-16T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-17T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-18T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-19T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-20T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-21T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-22T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-23T00:00:00Z",
                            "coefficient": 0
                        }
                    ],
                    "hasAvailableDate": true,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 314137,
                "warehouseName": "СЦ Гродно",
                "warehouseAddress": "Беларусь, Гродно, улица Суворова, 298",
                "warehouseMapID": 2017,
                "isRecommended": true,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 225,
                    "logisticLiter": "21,38",
                    "logisticBase": "85,5",
                    "storageLiter": "0,18",
                    "storageBase": "0,18",
                    "storageCoefficient": 225,
                    "hasAvailableDate": true,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 300363,
                "warehouseName": "СЦ Брест",
                "warehouseAddress": "Брестская область, Брест, Дубровская улица, 36В",
                "warehouseMapID": 1401,
                "isRecommended": true,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 200,
                    "logisticLiter": "19",
                    "logisticBase": "76",
                    "storageLiter": "0,16",
                    "storageBase": "0,16",
                    "storageCoefficient": 200,
                    "hasAvailableDate": true,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 218987,
                "warehouseName": "Алматы Атакент",
                "warehouseAddress": "г.Алматы, ул. Тимирязева 42 павильон 7а",
                "warehouseMapID": 1344,
                "isRecommended": true,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 145,
                    "logisticLiter": "13,78",
                    "logisticBase": "55,1",
                    "storageLiter": "0,12",
                    "storageBase": "0,12",
                    "storageCoefficient": 145,
                    "acceptanceCoefficients": [
                        {
                            "date": "2025-07-11T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-12T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-13T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-14T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-15T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-16T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-17T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-18T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-19T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-20T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-21T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-22T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-23T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-24T00:00:00Z",
                            "coefficient": 0
                        }
                    ],
                    "hasAvailableDate": true,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 302571,
                "warehouseName": "СЦ Ереван",
                "warehouseAddress": "РА, г. Ереван, улица Арташисян 106",
                "warehouseMapID": 1461,
                "isRecommended": true,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 210,
                    "logisticLiter": "19,95",
                    "logisticBase": "79,8",
                    "storageLiter": "0,17",
                    "storageBase": "0,17",
                    "storageCoefficient": 210,
                    "acceptanceCoefficients": [
                        {
                            "date": "2025-07-15T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-16T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-17T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-18T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-19T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-23T00:00:00Z",
                            "coefficient": 0
                        }
                    ],
                    "hasAvailableDate": true,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 117414,
                "warehouseName": "СЦ СПБ1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6147,
                "warehouseName": "СЦ Иркутск old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 118365,
                "warehouseName": "СЦ Красноярск Полиго",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 312807,
                "warehouseName": "СЦ Обухово 2",
                "warehouseAddress": "Московская область, Богородский городской округ, рабочий поселок Обухово, территория Атлант-Парк, дом 36",
                "warehouseMapID": 1751,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132709,
                "warehouseName": "FBS Москва Гребнев Илья 4",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 119742,
                "warehouseName": "Холл.ру Питер",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1672,
                "warehouseName": "Пермь Сорокинская РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 302445,
                "warehouseName": "Сынково",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218916,
                "warehouseName": "СЦ Чебоксары 2",
                "warehouseAddress": "г. Чебоксары, проезд Лапсарский, д. 55, строение 1",
                "warehouseMapID": 1447,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 170,
                    "logisticLiter": "16,15",
                    "logisticBase": "64,6",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 170,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 132170,
                "warehouseName": "Краснодар (Грузовая доставка)",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 210937,
                "warehouseName": "СЦ Симферополь 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 135238,
                "warehouseName": "Москва Своими силами 4",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 106476,
                "warehouseName": "Оренбург",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1772,
                "warehouseName": "Ангарск 272 квартал РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 302856,
                "warehouseName": "СЦ Видное",
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 301562,
                "warehouseName": "Невинномысск СГТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132060,
                "warehouseName": "FBS Санкт-Петербург Экспресс 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 144154,
                "warehouseName": "СЦ Симферополь",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 172940,
                "warehouseName": "СЦ Брянск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 74,
                "warehouseName": "Сургут Сосновая РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1193,
                "warehouseName": "Хабаровск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 147019,
                "warehouseName": "СЦ Пермь",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117423,
                "warehouseName": "СЦ Екатеринбург 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117432,
                "warehouseName": "СЦ Подольск Б5",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 123818,
                "warehouseName": "FBS СПБ 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117721,
                "warehouseName": "СЦ Чита",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218732,
                "warehouseName": "СЦ Ош",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1725,
                "warehouseName": "Екатеринбург Горнистов РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 206298,
                "warehouseName": "СЦ Абакан",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218987,
                "warehouseName": "Алматы Атакент",
                "warehouseAddress": "г.Алматы, ул. Тимирязева 42 павильон 7а",
                "warehouseMapID": 1344,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 70,
                    "logisticLiter": "6,65",
                    "logisticBase": "26,6",
                    "storageBase": "17.5",
                    "storageCoefficient": 70,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 6144,
                "warehouseName": "СЦ Волгоград old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 213849,
                "warehouseName": "СЦ Кемерово",
                "warehouseAddress": "Кемерово, ул. Тухачевского, 58",
                "warehouseMapID": 1245,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 1921,
                "warehouseName": "С-П Черниговская РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 93,
                "warehouseName": "Южно-Сахалинск Ленина 123",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 209596,
                "warehouseName": "СЦ Солнцево",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 206348,
                "warehouseName": "Тула",
                "warehouseAddress": "Тульская область, муниципальное образование Алексин, 1",
                "warehouseMapID": 1187,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageBase": "65",
                    "storageCoefficient": 260,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 325360,
                "warehouseName": "Екатеринбург СГТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 301805,
                "warehouseName": "Новосемейкино",
                "warehouseAddress": "Самарская область, муниципальный район Красноярский, поселок городского типа Новосемейкино, парк Индустриальный, здание 1",
                "warehouseMapID": 1978,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 200,
                    "logisticLiter": "19",
                    "logisticBase": "76",
                    "storageBase": "50",
                    "storageCoefficient": 200,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 204387,
                "warehouseName": "СЦ Ереван район Эребуни",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1771,
                "warehouseName": "Ярославль",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 203631,
                "warehouseName": "СЦ Вологда",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1864,
                "warehouseName": "Братск Индустриальный РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 207404,
                "warehouseName": "СЦ Ярославль",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127457,
                "warehouseName": "FBS Магнит №11 Рэфил",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1790,
                "warehouseName": "Великий Новгород Десятинн",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132994,
                "warehouseName": "FBS Москва Курьер КБТ 48",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 316646,
                "warehouseName": "Шушары СГТ",
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 324108,
                "warehouseName": "Астана 2",
                "warehouseAddress": "Караганда, 91/2",
                "warehouseMapID": 2142,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 90,
                    "logisticLiter": "8,55",
                    "logisticBase": "34,2",
                    "storageBase": "22.5",
                    "storageCoefficient": 90,
                    "acceptanceCoefficients": [
                        {
                            "date": "2025-07-12T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-16T00:00:00Z",
                            "coefficient": 0
                        },
                        {
                            "date": "2025-07-17T00:00:00Z",
                            "coefficient": 0
                        }
                    ],
                    "hasAvailableDate": true,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 131,
                "warehouseName": "Ростов-на-Дону",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 322213,
                "warehouseName": "СЦ Ногир",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 50064844,
                "warehouseName": "Есипово 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1803,
                "warehouseName": "Уральск Громовой РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122316,
                "warehouseName": "Склад пост Алматы 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 118839,
                "warehouseName": "СЦ Новосибирск old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 123821,
                "warehouseName": "FBS Казань 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 119781,
                "warehouseName": "JoyJoy",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117734,
                "warehouseName": "СЦ Санкт-Петербург Ю",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 302988,
                "warehouseName": "СЦ Астрахань (Солянка)",
                "warehouseAddress": "Николаевское шоссе, 2В, пос. пригородный",
                "warehouseMapID": 1810,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 155,
                    "logisticLiter": "14,73",
                    "logisticBase": "58,9",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 132453,
                "warehouseName": "FBS Санкт-Петербург Спири",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124100,
                "warehouseName": "FBS Новосибирск 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117498,
                "warehouseName": "Воронеж - old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 160030,
                "warehouseName": "СЦ Липецк",
                "warehouseAddress": "д. Кулешовка, ул. Орловская, 25А",
                "warehouseMapID": 385,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 1814,
                "warehouseName": "Пятигорск Ермолова РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 215020,
                "warehouseName": "СЦ Байсерке",
                "warehouseAddress": "Алм. Обл с Байсерке Султана Бейбарса 1",
                "warehouseMapID": 1241,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 135,
                    "logisticLiter": "12,83",
                    "logisticBase": "51,3",
                    "storageLiter": "0,11",
                    "storageBase": "0,11",
                    "storageCoefficient": 135,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 215020,
                "warehouseName": "СЦ Байсерке",
                "warehouseAddress": "Алм. Обл с Байсерке Султана Бейбарса 1",
                "warehouseMapID": 1241,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 70,
                    "logisticLiter": "6,65",
                    "logisticBase": "26,6",
                    "storageBase": "17.5",
                    "storageCoefficient": 70,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 1824,
                "warehouseName": "Пенза Аустрина РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 50062129,
                "warehouseName": "Софрино СГТ",
                "warehouseAddress": "Московская область, городской округ Пушкинский, территория Автодорога М8 Холмогоры, километр 48-й, дом 2, строение 3",
                "warehouseMapID": 2153,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 321922,
                "warehouseName": "Калуга СГТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117430,
                "warehouseName": "СЦ Краснодар 4",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218225,
                "warehouseName": "СЦ Челябинск 2",
                "warehouseAddress": "Копейское шоссе, 88/5, Челябинск, 454010",
                "warehouseMapID": 1359,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 180,
                    "logisticLiter": "17,1",
                    "logisticBase": "68,4",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 180,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 158929,
                "warehouseName": "СЦ Саратов",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 144649,
                "warehouseName": "СЦ Владимир",
                "warehouseAddress": "ул. Мещерская, 7а",
                "warehouseMapID": 98,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 133084,
                "warehouseName": "Москва Джема 24 КГТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 119798,
                "warehouseName": "220вольт склад",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 211415,
                "warehouseName": "СЦ Воронеж",
                "warehouseAddress": "Айдаровское сельское поселение, территория Промышленная, ул. 1-я Промышленная зона, 9",
                "warehouseMapID": 148,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 175,
                    "logisticLiter": "16,63",
                    "logisticBase": "66,5",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 175,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 125186,
                "warehouseName": "FBS Москва Фуд 12ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 125238,
                "warehouseName": "Склад пос-ка СПБ КБТ 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133174,
                "warehouseName": "Санкт-Петербург Своими си-4",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133533,
                "warehouseName": "СЦ Ростов-на-Дону old-2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 118106,
                "warehouseName": "Склад Диваны24",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 158140,
                "warehouseName": "СЦ Ижевск (до 29.05)",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 300864,
                "warehouseName": "Шелепаново",
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 322487,
                "warehouseName": "СЦ Старый Оскол",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132869,
                "warehouseName": "Москва Своими силами 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 120762,
                "warehouseName": "Электросталь",
                "warehouseAddress": "Московская область, Электросталь, посёлок Случайный, территория Массив 3, 5",
                "warehouseMapID": 1013,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 170,
                    "logisticLiter": "16,15",
                    "logisticBase": "64,6",
                    "storageBase": "41.25",
                    "storageCoefficient": 165,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 1799,
                "warehouseName": "Махачкала Акушинского РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117779,
                "warehouseName": "СЦ Казань",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 223,
                "warehouseName": "Новосибирск Петухова РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117497,
                "warehouseName": "СЦ Смоленск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 301983,
                "warehouseName": "Волгоград",
                "warehouseAddress": "Дзержинский район, городской округ Волгоград",
                "warehouseMapID": 2019,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127445,
                "warehouseName": "FBS Краснодар Магнит №1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 313603,
                "warehouseName": "Вологда КБТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218636,
                "warehouseName": "СЦ Ташкент",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 208277,
                "warehouseName": "Невинномысск",
                "warehouseAddress": "ул. Тимирязева 16",
                "warehouseMapID": 1205,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": false,
                    "count": 3,
                    "logisticCoefficient": 115,
                    "logisticLiter": "10,93",
                    "logisticBase": "43,7",
                    "storageLiter": "0,09",
                    "storageBase": "0,09",
                    "storageCoefficient": 115,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 208277,
                "warehouseName": "Невинномысск",
                "warehouseAddress": "ул. Тимирязева 16",
                "warehouseMapID": 1205,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": false,
                    "count": 3,
                    "logisticCoefficient": 115,
                    "logisticLiter": "10,93",
                    "logisticBase": "43,7",
                    "storageBase": "43.75",
                    "storageCoefficient": 175,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 123816,
                "warehouseName": "FBS СПБ 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124098,
                "warehouseName": "FBS Краснодар 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117456,
                "warehouseName": "СЦ Тверь",
                "warehouseAddress": "ул. Волоколамское шоссе, 51Б",
                "warehouseMapID": 821,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 121631,
                "warehouseName": "Склад пост Минск 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 206968,
                "warehouseName": "Чехов 1, Новоселки вл 11 стр 2",
                "warehouseAddress": "Московская обл. городской округ Чехов. Промышленная зона Новоселки, вл. 11 стр 2",
                "warehouseMapID": 1190,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133176,
                "warehouseName": "Санкт-Петербург Своими си-6",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122590,
                "warehouseName": "FBS Очаково 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 161812,
                "warehouseName": "СПБ Шушары КБТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117443,
                "warehouseName": "СЦ Коледино К1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 126670,
                "warehouseName": "FBS Москва Экспресс-доставка 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 116338,
                "warehouseName": "СЦ Дзержинский",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 126674,
                "warehouseName": "FBS Москва Экспресс-доставка 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 311800,
                "warehouseName": "СЦ Шушары 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 325179,
                "warehouseName": "СЦ Нижневартовск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6140,
                "warehouseName": "СЦ Нижний Новгород old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 2737,
                "warehouseName": "Санкт-Петербург (Уткина Заводь)",
                "warehouseAddress": "Всеволожский р-н, г.п. Свердловское, дер. Новосаратовка, участок № 1 (промзона Уткина Заводь, комплекс МЛП, корпус 4, парадная 4)",
                "warehouseMapID": 635,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 300,
                    "logisticLiter": "28,5",
                    "logisticBase": "114",
                    "storageLiter": "0,24",
                    "storageBase": "0,24",
                    "storageCoefficient": 300,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 117424,
                "warehouseName": "СЦ Екатеринбург 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132294,
                "warehouseName": "FBS Санкт-Петербург Семиц",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 119070,
                "warehouseName": "Север-Авто-М",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 149445,
                "warehouseName": "СЦ Уфа",
                "warehouseAddress": "Мокроусовская улица, 8Г",
                "warehouseMapID": 893,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 210,
                    "logisticLiter": "19,95",
                    "logisticBase": "79,8",
                    "storageLiter": "0,17",
                    "storageBase": "0,17",
                    "storageCoefficient": 210,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 305110,
                "warehouseName": "Красноярск, Солонцовский",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 50045612,
                "warehouseName": "Санкт-Петербург СГТ",
                "warehouseAddress": "Ленинградская область, Ломоносовский район, Лаголовское с/пос, Промышленной зоны Восточная тер., 1",
                "warehouseMapID": 2150,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 362877,
                "warehouseName": "СЦ Ташкент Фергана Йули",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127447,
                "warehouseName": "FBS Краснодар Магнит №2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117480,
                "warehouseName": "FBS ПВЗ Электросталь",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 300461,
                "warehouseName": "Гомель 2",
                "warehouseAddress": "Гомель, Могилёвская улица 1/А",
                "warehouseMapID": 1455,
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 301229,
                "warehouseName": "Подольск 4",
                "warehouseAddress": "Домодедовское шоссе 22",
                "warehouseMapID": 1363,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 210515,
                "warehouseName": "СЦ Вёшки",
                "warehouseAddress": "Липкинское шоссе, 2-й километр, вл1с1, посёлок Вёшки, городской округ Мытищи, Московская область",
                "warehouseMapID": 1220,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 161003,
                "warehouseName": "СЦ Сургут",
                "warehouseAddress": "ул. Сосновая, 29/12",
                "warehouseMapID": 1169,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 6153,
                "warehouseName": "Белгород",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 319389,
                "warehouseName": "Екатеринбург Серовский",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 115650,
                "warehouseName": "СЦ Мытищи",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 321932,
                "warehouseName": "Чашниково",
                "warehouseAddress": "Территория Индустриальный парк Чашниково, городской округ Солнечногорск, Московская область",
                "warehouseMapID": 2141,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 123822,
                "warehouseName": "FBS Казань 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 301920,
                "warehouseName": "СЦ Пятигорск (Этока)",
                "warehouseAddress": "г Пятигорск район Этока Георгиевское шоссе 4 км. 1",
                "warehouseMapID": 1421,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 135,
                    "logisticLiter": "12,83",
                    "logisticBase": "51,3",
                    "storageLiter": "0,11",
                    "storageBase": "0,11",
                    "storageCoefficient": 140,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 217678,
                "warehouseName": "Внуково СГТ",
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 126680,
                "warehouseName": "FBS Москва Экспресс 24/7",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127456,
                "warehouseName": "FBS Магнит №10 Гидрострой",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 118105,
                "warehouseName": "СЦ Обнинск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218616,
                "warehouseName": "СЦ Ростов-на-Дону",
                "warehouseAddress": "улица Гайдара, 8 корпус 2, хутор Нижнетемерницкий, Щепкинское сельское поселение, Аксайский район, Ростовская область",
                "warehouseMapID": 1302,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 175,
                    "logisticLiter": "16,63",
                    "logisticBase": "66,5",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 175,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 169537,
                "warehouseName": "СЦ Серов",
                "warehouseAddress": "ул. Новоуральская, 2А",
                "warehouseMapID": 1118,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 119204,
                "warehouseName": "Склад Чехов",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132036,
                "warehouseName": "FBS Магнит №18 Байронизм",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133173,
                "warehouseName": "Санкт-Петербург Своими си-3",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 316563,
                "warehouseName": "Склад Волгоград old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122495,
                "warehouseName": "Сц Нурсултан",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 571,
                "warehouseName": "Минск Колодищи РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 317460,
                "warehouseName": "СЦ Красноярск Томская",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 118535,
                "warehouseName": "СЦ Нижний Новгород",
                "warehouseAddress": "Посёлок Кудьма, логистический комплекс Южный, ул. Индустриальная, 10",
                "warehouseMapID": 459,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 127014,
                "warehouseName": "FBS Москва Экспресс-доставка 5",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 317470,
                "warehouseName": "Голицыно СГТ",
                "warehouseAddress": "Минское шоссе, 43-й километр, вл1к3",
                "warehouseMapID": 2140,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1797,
                "warehouseName": "Кыштым Возмездия РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 158751,
                "warehouseName": "СЦ Владикавказ",
                "warehouseAddress": "Северная Осетия-Алания, с. Михайловское, Бесланское шоссе",
                "warehouseMapID": 1121,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 130,
                    "logisticLiter": "12,35",
                    "logisticBase": "49,4",
                    "storageLiter": "0,1",
                    "storageBase": "0,1",
                    "storageCoefficient": 130,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 330363,
                "warehouseName": "СЦ Белозерки",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1785,
                "warehouseName": "Алматы 8 мкр РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 156814,
                "warehouseName": "СЦ Курьяновская",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132473,
                "warehouseName": "FBS Санкт-Петербург Импер",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133618,
                "warehouseName": "FBS Москва AMF 4",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 115577,
                "warehouseName": "Крёкшино",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 119408,
                "warehouseName": "СЦ Очаково",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 302737,
                "warehouseName": "СЦ Барнаул",
                "warehouseAddress": "Барнаул, улица Попова, 179Б",
                "warehouseMapID": 1744,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 220,
                    "logisticLiter": "20,9",
                    "logisticBase": "83,6",
                    "storageLiter": "0,18",
                    "storageBase": "0,18",
                    "storageCoefficient": 220,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 302737,
                "warehouseName": "СЦ Барнаул",
                "warehouseAddress": "Барнаул, улица Попова, 179Б",
                "warehouseMapID": 1744,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 210,
                    "logisticLiter": "19,95",
                    "logisticBase": "79,8",
                    "storageBase": "52.5",
                    "storageCoefficient": 210,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 37,
                "warehouseName": "Красноярск Семафорная РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117427,
                "warehouseName": "СЦ Краснодар 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1801,
                "warehouseName": "П-Камчатский Лукашевского",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 207022,
                "warehouseName": "СЦ Чёрная Грязь",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 300711,
                "warehouseName": "СЦ Уральск",
                "warehouseAddress": "РК, г. Уральск, Район ТЭЦ 10/3",
                "warehouseMapID": 1420,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 302738,
                "warehouseName": "СЦ Саратов Зоринский",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127459,
                "warehouseName": "FBS Магнит №12 Красных Па",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133172,
                "warehouseName": "Санкт-Петербург Своими си-2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133175,
                "warehouseName": "Санкт-Петербург Своими си-5",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132829,
                "warehouseName": "Москва ПроЛофт КГТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 300363,
                "warehouseName": "СЦ Брест",
                "warehouseAddress": "Брестская область, Брест, Дубровская улица, 36В",
                "warehouseMapID": 1401,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 200,
                    "logisticLiter": "19",
                    "logisticBase": "76",
                    "storageBase": "50",
                    "storageCoefficient": 200,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 122498,
                "warehouseName": "Склад пост Нурсултан 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122492,
                "warehouseName": "Склад пост Алматы 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 50045246,
                "warehouseName": "Склад Шушары",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 214951,
                "warehouseName": "СЦ Артем",
                "warehouseAddress": "Приморский край, г. Артем, ул. Солнечная, д.46",
                "warehouseMapID": 1244,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 132291,
                "warehouseName": "FBS Москва Курьер 48",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 125611,
                "warehouseName": "FBS Фарма",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1716,
                "warehouseName": "Краснодар Почтовая РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1796,
                "warehouseName": "Караганда Бухар Жырау РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 316491,
                "warehouseName": "СЦ Энгельс Строителей",
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1680,
                "warehouseName": "Саратов Депутатская РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124584,
                "warehouseName": "FBS Китай",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117230,
                "warehouseName": "СЦ Самара",
                "warehouseAddress": "Самарская область, Волжский район, село Преображенка, ул. Индустриальная, 1Б",
                "warehouseMapID": 1343,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 315199,
                "warehouseName": "СЦ Оренбург Центральная",
                "warehouseAddress": "Оренбург, Центральная улица, 29",
                "warehouseMapID": 2040,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 195,
                    "logisticLiter": "18,53",
                    "logisticBase": "74,1",
                    "storageLiter": "0,16",
                    "storageBase": "0,16",
                    "storageCoefficient": 195,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 312259,
                "warehouseName": "СЦ Шушары",
                "warehouseAddress": "Шушары, Московское шоссе, 143",
                "warehouseMapID": 1694,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 230,
                    "logisticLiter": "21,85",
                    "logisticBase": "87,4",
                    "storageBase": "57.5",
                    "storageCoefficient": 230,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 122491,
                "warehouseName": "Склад пост Алматы 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 365116,
                "warehouseName": "Воронеж СГТ",
                "warehouseAddress": "Воронежская область, м.р-н Новоусманский, с.п. Усманское 1-е, с Нечаевка, ул Виноградная, д. 97а",
                "warehouseMapID": 2149,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6152,
                "warehouseName": "СЦ Омск - old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127465,
                "warehouseName": "FBS Магнит №15 Архат",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117501,
                "warehouseName": "Подольск",
                "warehouseAddress": "ул. Поливановская, 9с5",
                "warehouseMapID": 1043,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132597,
                "warehouseName": "FBS Москва Джема 24ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132318,
                "warehouseName": "FBS Москва Своими силами 24ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122467,
                "warehouseName": "Склад пост Минск 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117493,
                "warehouseName": "СЦ Челябинск old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 206708,
                "warehouseName": "СЦ Новокузнецк",
                "warehouseAddress": "г. Новокузнецк, Центральный район, просп. Курако, д. 51а, корпус 23 ",
                "warehouseMapID": 1198,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 180,
                    "logisticLiter": "17,1",
                    "logisticBase": "68,4",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 180,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 319391,
                "warehouseName": "СЦ Екатеринбург (Чкаловский)",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 126667,
                "warehouseName": "FBS Москва Цветы 12ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132344,
                "warehouseName": "FBS Краснодар Шипы и Розы",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 211622,
                "warehouseName": "СЦ Минск",
                "warehouseAddress": "Беларусь, Минская обл., Минский р-н, Щомыслицкий с/с, 118/2",
                "warehouseMapID": 1215,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 195,
                    "logisticLiter": "18,53",
                    "logisticBase": "74,1",
                    "storageLiter": "0,16",
                    "storageBase": "0,16",
                    "storageCoefficient": 195,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 211622,
                "warehouseName": "СЦ Минск",
                "warehouseAddress": "Беларусь, Минская обл., Минский р-н, Щомыслицкий с/с, 118/2",
                "warehouseMapID": 1215,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 195,
                    "logisticLiter": "18,53",
                    "logisticBase": "74,1",
                    "storageBase": "48.75",
                    "storageCoefficient": 195,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 140302,
                "warehouseName": "СЦ Курск",
                "warehouseAddress": "ул. 50 лет Октября, 179",
                "warehouseMapID": 1108,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 205859,
                "warehouseName": "Бишкек",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 303221,
                "warehouseName": "СЦ Ноябрьск",
                "warehouseAddress": "г. Ноябрьск,  3й проезд, панель 9в/2",
                "warehouseMapID": 2139,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 210127,
                "warehouseName": "СЦ Внуково",
                "warehouseAddress": "Поселение Марушкинское, квартал № 8",
                "warehouseMapID": 1217,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 175,
                    "logisticLiter": "16,63",
                    "logisticBase": "66,5",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 175,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 301981,
                "warehouseName": "Владимир Воршинское",
                "warehouseAddress": "Владимир",
                "warehouseMapID": 2144,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 141637,
                "warehouseName": "СЦ Новокосино",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117446,
                "warehouseName": "СЦ Коледино К3",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 502,
                "warehouseName": "Самара",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117059,
                "warehouseName": "СЦ Гомель",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 340443,
                "warehouseName": "СЦ Новосибирск Докучаева ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 208647,
                "warehouseName": "СЦ Печатники",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 300169,
                "warehouseName": "Радумля СГТ",
                "warehouseAddress": "деревня Радумля, с4А\t",
                "warehouseMapID": 2122,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 123817,
                "warehouseName": "FBS СПБ 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 341991,
                "warehouseName": "Астана КБТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 318388,
                "warehouseName": "СЦ Адыгея",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 320551,
                "warehouseName": "СЦ Бийск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 311895,
                "warehouseName": "СЦ Череповец",
                "warehouseAddress": "Кирилловское шоссе, 82",
                "warehouseMapID": 1811,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 150,
                    "logisticLiter": "14,25",
                    "logisticBase": "57",
                    "storageLiter": "0,12",
                    "storageBase": "0,12",
                    "storageCoefficient": 150,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 132015,
                "warehouseName": "FBS Санкт-Петербург Холод",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 211644,
                "warehouseName": "СЦ Екатеринбург 2 (Альпинистов)",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 130744,
                "warehouseName": "Краснодар (Тихорецкая)",
                "warehouseAddress": "ул. Тихорецкая, 40с1",
                "warehouseMapID": 307,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 205205,
                "warehouseName": "СЦ Киров (old)",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1789,
                "warehouseName": "Брянск Ленина РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1686,
                "warehouseName": "Орел Северная РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132320,
                "warehouseName": "FBS Москва Своими силами 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132043,
                "warehouseName": "Санкт-Петербург КБТ (не используем)",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 323608,
                "warehouseName": "СЦ Сабурово",
                "warehouseAddress": "Каширское шоссе д. 57 к. 2",
                "warehouseMapID": 2143,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133171,
                "warehouseName": "Санкт-Петербург Своими си-1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124093,
                "warehouseName": "FBS Екатеринбург 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 131643,
                "warehouseName": "СЦ Иркутск",
                "warehouseAddress": "ул. Трактовая, 18/38а",
                "warehouseMapID": 223,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 170,
                    "logisticLiter": "16,15",
                    "logisticBase": "64,6",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 170,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 117819,
                "warehouseName": "СЦ Тюмень",
                "warehouseAddress": "ул.Харьковская, 77с1",
                "warehouseMapID": 871,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 190,
                    "logisticLiter": "18,05",
                    "logisticBase": "72,2",
                    "storageLiter": "0,15",
                    "storageBase": "0,15",
                    "storageCoefficient": 190,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 121709,
                "warehouseName": "Электросталь СГТ",
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117401,
                "warehouseName": "СЦ Тула",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1724,
                "warehouseName": "Астана Жумабаева РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 302222,
                "warehouseName": "Уфа, Зубово",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 300862,
                "warehouseName": "СЦ Абакан 2",
                "warehouseAddress": "РФ, Республика Хакасия, г. Абакан, ул. Складская 11 стр.",
                "warehouseMapID": 1454,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 200,
                    "logisticLiter": "19",
                    "logisticBase": "76",
                    "storageLiter": "0,16",
                    "storageBase": "0,16",
                    "storageCoefficient": 200,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 314137,
                "warehouseName": "СЦ Гродно",
                "warehouseAddress": "Беларусь, Гродно, улица Суворова, 298",
                "warehouseMapID": 2017,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 225,
                    "logisticLiter": "21,38",
                    "logisticBase": "85,5",
                    "storageBase": "56.25",
                    "storageCoefficient": 225,
                    "hasAvailableDate": true,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 313214,
                "warehouseName": "СЦ Магнитогорск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 303295,
                "warehouseName": "Клин",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1793,
                "warehouseName": "Елизово Вилюйская РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 301516,
                "warehouseName": "СЦ Волгоград 2",
                "warehouseAddress": "Волгоград , улица Пржевальского, 20",
                "warehouseMapID": 1468,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 1774,
                "warehouseName": "Тверь Коминтерна РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 301760,
                "warehouseName": "Рязань (Тюшевское)",
                "warehouseAddress": "Индустриальный промышленный парк Рязанский, Тюшевское сельское поселение, Рязанский район",
                "warehouseMapID": 1446,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 100,
                    "logisticLiter": "9,5",
                    "logisticBase": "38",
                    "storageBase": "45",
                    "storageCoefficient": 180,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 301760,
                "warehouseName": "Рязань (Тюшевское)",
                "warehouseAddress": "Индустриальный промышленный парк Рязанский, Тюшевское сельское поселение, Рязанский район",
                "warehouseMapID": 1446,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 140,
                    "logisticLiter": "13,3",
                    "logisticBase": "53,2",
                    "storageLiter": "0,11",
                    "storageBase": "0,11",
                    "storageCoefficient": 140,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 127453,
                "warehouseName": "FBS Магнит №7 Зеленый",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 208941,
                "warehouseName": "Домодедово",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 572,
                "warehouseName": "Минск old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117231,
                "warehouseName": "СЦ Сургут old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 206236,
                "warehouseName": "Белые Столбы",
                "warehouseAddress": "Московская область, Домодедово, микрорайон Белые Столбы, владение Склады 104, с 4/1 ",
                "warehouseMapID": 1191,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133917,
                "warehouseName": "Москва Экомаркет 2ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 172430,
                "warehouseName": "СЦ Барнаул old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117462,
                "warehouseName": "СЦ Солнечногорск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 210557,
                "warehouseName": "СЦ Белогорск",
                "warehouseAddress": "Амурская область, г. Белогорск, ул. Кирова, 306",
                "warehouseMapID": 1234,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 124716,
                "warehouseName": "СЦ Подрезково",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 207803,
                "warehouseName": "СЦ Смоленск 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 315704,
                "warehouseName": "Ярославль СГТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218644,
                "warehouseName": "СЦ Хабаровск",
                "warehouseAddress": "Хабаровский край, г.Хабаровск, ул. Зеленая д.3А",
                "warehouseMapID": 1382,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 190,
                    "logisticLiter": "18,05",
                    "logisticBase": "72,2",
                    "storageLiter": "0,15",
                    "storageBase": "0,15",
                    "storageCoefficient": 190,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 218644,
                "warehouseName": "СЦ Хабаровск",
                "warehouseAddress": "Хабаровский край, г.Хабаровск, ул. Зеленая д.3А",
                "warehouseMapID": 1382,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 85,
                    "logisticLiter": "8,08",
                    "logisticBase": "32,3",
                    "storageBase": "21.25",
                    "storageCoefficient": 85,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 207743,
                "warehouseName": "СЦ Пушкино",
                "warehouseAddress": "М-8 Холмогоры, 33-й километр, 16В, 4 корпус ",
                "warehouseMapID": 1206,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 150,
                    "logisticLiter": "14,25",
                    "logisticBase": "57",
                    "storageLiter": "0,12",
                    "storageBase": "0,12",
                    "storageCoefficient": 150,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 207743,
                "warehouseName": "СЦ Пушкино",
                "warehouseAddress": "М-8 Холмогоры, 33-й километр, 16В, 4 корпус ",
                "warehouseMapID": 1206,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 205,
                    "logisticLiter": "19,48",
                    "logisticBase": "77,9",
                    "storageBase": "51.25",
                    "storageCoefficient": 205,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 205349,
                "warehouseName": "СЦ Мурманск",
                "warehouseAddress": "ул. Промышленная, 19",
                "warehouseMapID": 1170,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 165,
                    "logisticLiter": "15,68",
                    "logisticBase": "62,7",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 165,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 507,
                "warehouseName": "Коледино",
                "warehouseAddress": "дер. Коледино, ул. Троицкая, 20",
                "warehouseMapID": 302,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 200,
                    "logisticLiter": "19",
                    "logisticBase": "76",
                    "storageLiter": "0,16",
                    "storageBase": "0,16",
                    "storageCoefficient": 200,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 117433,
                "warehouseName": "СЦ Крёкшино 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 375489,
                "warehouseName": "СЦ Ульяновск Инженерная",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117986,
                "warehouseName": "Казань",
                "warehouseAddress": "Республика Татарстан, Зеленодольск, промышленный парк Зеленодольск, 20",
                "warehouseMapID": 254,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 240,
                    "logisticLiter": "22,8",
                    "logisticBase": "91,2",
                    "storageBase": "65",
                    "storageCoefficient": 260,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 161520,
                "warehouseName": "СЦ Новосибирск Пасечная",
                "warehouseAddress": "Новосибирская область,  Новосибирский район, п. Садовый, ул. Пасечная, дом 11/1, корпус 2 ",
                "warehouseMapID": 1239,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 350,
                    "logisticLiter": "33,25",
                    "logisticBase": "133",
                    "storageLiter": "0,28",
                    "storageBase": "0,28",
                    "storageCoefficient": 350,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 1733,
                "warehouseName": "Екатеринбург - Испытателей 14г",
                "warehouseAddress": "ул. Испытателей, 14Г",
                "warehouseMapID": 171,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 225,
                    "logisticLiter": "21,38",
                    "logisticBase": "85,5",
                    "storageLiter": "0,18",
                    "storageBase": "0,18",
                    "storageCoefficient": 225,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 205104,
                "warehouseName": "СЦ Ульяновск",
                "warehouseAddress": "14 проезд Инженерный, 9в",
                "warehouseMapID": 1200,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 180,
                    "logisticLiter": "17,1",
                    "logisticBase": "68,4",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 180,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 1729,
                "warehouseName": "Омск 2-я Солнечная РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 216476,
                "warehouseName": "СЦ Бишкек",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6154,
                "warehouseName": "СЦ Ярославль old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 205228,
                "warehouseName": "СЦ Белая Дача",
                "warehouseAddress": "Яничкин проезд, 3",
                "warehouseMapID": 1130,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 205,
                    "logisticLiter": "19,48",
                    "logisticBase": "77,9",
                    "storageLiter": "0,16",
                    "storageBase": "0,16",
                    "storageCoefficient": 205,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 301987,
                "warehouseName": "Сарапул",
                "warehouseAddress": "Удмуртская Республика, г. Сарапул, Ижевский тракт, д. 22",
                "warehouseMapID": 2152,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 210,
                    "logisticLiter": "19,95",
                    "logisticBase": "79,8",
                    "storageBase": "52.5",
                    "storageCoefficient": 210,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 336442,
                "warehouseName": "Щербинка",
                "warehouseAddress": "Московская область, Подольск, Вишнёвая улица, 11Б",
                "warehouseMapID": 2145,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 210001,
                "warehouseName": "Чехов 2, Новоселки вл 11 стр 7",
                "warehouseAddress": "Московская обл. городской округ Чехов. Промышленная зона Новоселки, вл. 11 стр 7",
                "warehouseMapID": 1231,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218628,
                "warehouseName": "СЦ Ижевск",
                "warehouseAddress": "г. Ижевск, Воткинское ш., 302",
                "warehouseMapID": 1303,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 218628,
                "warehouseName": "СЦ Ижевск",
                "warehouseAddress": "г. Ижевск, Воткинское ш., 302",
                "warehouseMapID": 1303,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 190,
                    "logisticLiter": "18,05",
                    "logisticBase": "72,2",
                    "storageBase": "47.5",
                    "storageCoefficient": 190,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 117451,
                "warehouseName": "СЦ Коледино К8",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117544,
                "warehouseName": "Санкт-Петербург Север",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 316879,
                "warehouseName": "СЦ Актобе",
                "warehouseAddress": "г. Актобе, район Астана, Промзона 679/12",
                "warehouseMapID": 2137,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 60,
                    "logisticLiter": "5,7",
                    "logisticBase": "22,8",
                    "storageBase": "15",
                    "storageCoefficient": 60,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 123820,
                "warehouseName": "FBS Казань 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117393,
                "warehouseName": "СЦ Минск old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 159402,
                "warehouseName": "Санкт-Петербург (Шушары) OLD",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 318294,
                "warehouseName": "СЦ Софьино",
                "warehouseAddress": "Логопарк Софьино, контрольно-пропускной пункт № 2 Московская область, Раменский городской округ, территория Логистический технопарк Софьино Корпус 8",
                "warehouseMapID": 2147,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 216566,
                "warehouseName": "СЦ Пермь 2",
                "warehouseAddress": "Пермский край, город Пермь, Промышленная 123а",
                "warehouseMapID": 1246,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 165,
                    "logisticLiter": "15,68",
                    "logisticBase": "62,7",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 165,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 6155,
                "warehouseName": "СЦ Уфа old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 303024,
                "warehouseName": "Улан-Удэ, Ботаническая ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127452,
                "warehouseName": "FBS Магнит №6 Зиповский",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124099,
                "warehouseName": "FBS Новосибирск 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 114813,
                "warehouseName": "Пятигорск old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132807,
                "warehouseName": "FBS Москва ПроЛофт  5 дне",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117450,
                "warehouseName": "СЦ Коледино К7",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 209211,
                "warehouseName": "СЦ Махачкала",
                "warehouseAddress": "2-й проезд Казбекова, 16",
                "warehouseMapID": 1235,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 133348,
                "warehouseName": "FBS Москва Русский икорный 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 204939,
                "warehouseName": "Астана",
                "warehouseAddress": "Астана (Нур-Султан), Шоссе Алаш, 69/1",
                "warehouseMapID": 1122,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 6143,
                "warehouseName": "Липецк",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1097,
                "warehouseName": "Могилев",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117392,
                "warehouseName": "СЦ Владимир old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 314753,
                "warehouseName": "Ереван КБТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124096,
                "warehouseName": "FBS Краснодар 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117449,
                "warehouseName": "СЦ Коледино К6",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 158311,
                "warehouseName": "СЦ Пятигорск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 174,
                "warehouseName": "old Казань",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122258,
                "warehouseName": "Склад поставщика КБТ 72 ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 126676,
                "warehouseName": "FBS Москва Экспресс-доставка 4",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1699,
                "warehouseName": "Краснодар Старый",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132012,
                "warehouseName": "FBS Москва Склад Экспресс 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 217081,
                "warehouseName": "Сц Брянск 2",
                "warehouseAddress": "Брянская обл, г. Брянск, ул. Сталелитейная, дом 1",
                "warehouseMapID": 1267,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 155,
                    "logisticLiter": "14,73",
                    "logisticBase": "58,9",
                    "storageLiter": "0,12",
                    "storageBase": "0,12",
                    "storageCoefficient": 155,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 127454,
                "warehouseName": "FBS Магнит №8 Мореттини",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 143772,
                "warehouseName": "Москва Издательские решен",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 303189,
                "warehouseName": "СЦ Семей",
                "warehouseAddress": "РК, г. Семей, трасса Семей-Алматы 7/1",
                "warehouseMapID": 1451,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117673,
                "warehouseName": "Склад поставщика КБТ 48ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117700,
                "warehouseName": "Н.Новгород Чаадаева1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 120719,
                "warehouseName": "СЦ Казань 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122591,
                "warehouseName": "FBS Очаково 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 125240,
                "warehouseName": "Склад пос-ка СПБ КБТ 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1791,
                "warehouseName": "Владивосток Тухачевского",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122466,
                "warehouseName": "Склад пост Минск 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 207726,
                "warehouseName": "Жуковский",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 121703,
                "warehouseName": "СЦ Электросталь 4",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 119261,
                "warehouseName": "Склад поставщика",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 209207,
                "warehouseName": "СЦ Архангельск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1782,
                "warehouseName": "Ижевск Гагарина РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124101,
                "warehouseName": "FBS Новосибирск 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 204952,
                "warehouseName": "СЦ Набережные Челны",
                "warehouseAddress": "Республика Татарстан, Набережные Челны, Моторная улица, 16",
                "warehouseMapID": 1145,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 6145,
                "warehouseName": "Красноярск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1723,
                "warehouseName": "Воронеж Фестивальный РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 206844,
                "warehouseName": "Калининград",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6149,
                "warehouseName": "СЦ Симферополь old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1812,
                "warehouseName": "Архангельск Революции РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218674,
                "warehouseName": "СЦ Чита 2",
                "warehouseAddress": "Забайкальский край, г. Чита, ул. Казачья 29",
                "warehouseMapID": 1360,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 130,
                    "logisticLiter": "12,35",
                    "logisticBase": "49,4",
                    "storageLiter": "0,1",
                    "storageBase": "0,1",
                    "storageCoefficient": 130,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 154371,
                "warehouseName": "СЦ Комсомольская",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1681,
                "warehouseName": "Тамбов Бастионная РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6148,
                "warehouseName": "СЦ Архангельск old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1227,
                "warehouseName": "Нижний Новгород",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133355,
                "warehouseName": "Воронеж улица Урывского 6",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6157,
                "warehouseName": "Склад Варшава",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218623,
                "warehouseName": "Подольск 3",
                "warehouseAddress": "Московская область, Подольск, Поливановская улица, 9с3",
                "warehouseMapID": 1269,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 68,
                "warehouseName": "Санкт-Петербург",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 209513,
                "warehouseName": "Домодедово КБТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127449,
                "warehouseName": "FBS Магнит №3 Ницшеанец",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132035,
                "warehouseName": "FBS Магнит №17 Раздув",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 121700,
                "warehouseName": "СЦ Минск 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 115398,
                "warehouseName": "Владикавказ - old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132508,
                "warehouseName": "СЦ Челябинск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 209209,
                "warehouseName": "СЦ Псков",
                "warehouseAddress": "ул. Индустриальная, д. 9/1",
                "warehouseMapID": 1232,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 180,
                    "logisticLiter": "17,1",
                    "logisticBase": "68,4",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 180,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 302335,
                "warehouseName": "СЦ Кузнецк",
                "warehouseAddress": "РФ, Пензенская область, город Кузнецк, улица Победы, 53",
                "warehouseMapID": 1452,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 170,
                    "logisticLiter": "16,15",
                    "logisticBase": "64,6",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 170,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 302335,
                "warehouseName": "СЦ Кузнецк",
                "warehouseAddress": "РФ, Пензенская область, город Кузнецк, улица Победы, 53",
                "warehouseMapID": 1452,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 165,
                    "logisticLiter": "15,68",
                    "logisticBase": "62,7",
                    "storageBase": "41.25",
                    "storageCoefficient": 165,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 353731,
                "warehouseName": "СЦ Кавказский Бульвар 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 367641,
                "warehouseName": "Склад Ярославль Громова 9",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 206319,
                "warehouseName": "СЦ Оренбург",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122496,
                "warehouseName": "Склад пост Нурсултан 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122259,
                "warehouseName": "Склад поставщика КБТ 96 ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117445,
                "warehouseName": "СЦ Коледино К2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 300571,
                "warehouseName": "Екатеринбург - Перспективный 12/2",
                "warehouseAddress": "Екатеринбург, Перспективная улица 12/2",
                "warehouseMapID": 1358,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 686,
                "warehouseName": "Новосибирск",
                "warehouseAddress": "ул. Петухова, 71",
                "warehouseMapID": 474,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117442,
                "warehouseName": "СЦ Калуга",
                "warehouseAddress": "2-й Академический проезд, 12",
                "warehouseMapID": 277,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 170,
                    "logisticLiter": "16,15",
                    "logisticBase": "64,6",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 170,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 578,
                "warehouseName": "Волгоград old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 118301,
                "warehouseName": "СЦ Ногинск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127466,
                "warehouseName": "FBS Холодильник КГТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 3158,
                "warehouseName": "Коледино склад",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1798,
                "warehouseName": "Магадан Советская РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 317996,
                "warehouseName": "Оренбург СГТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 310498,
                "warehouseName": "Чебоксары КБТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117413,
                "warehouseName": "Виртуальный склад Кари",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 115651,
                "warehouseName": "Тамбов",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 312617,
                "warehouseName": "Обухово СГТ",
                "warehouseAddress": "Московская область, Богородский городской округ, рабочий поселок Обухово, территория Атлант-Парк, дом 36",
                "warehouseMapID": 2113,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 338827,
                "warehouseName": "СЦ Ярославль Громова",
                "warehouseAddress": "Ярославская обл, г. Ярославль. ул. Громова, д. 9В",
                "warehouseMapID": 2148,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 120602,
                "warehouseName": "Коледино КБТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6159,
                "warehouseName": "СЦ Красногорск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132061,
                "warehouseName": "FBS Санкт-Петербург Экспресс 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 131999,
                "warehouseName": "FBS Москва Склад Экспресс 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 203799,
                "warehouseName": "СЦ Чебоксары",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 116433,
                "warehouseName": "Домодедово old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 133347,
                "warehouseName": "FBS Москва Русский икорный 1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127455,
                "warehouseName": "FBS Магнит №9 Балака",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124094,
                "warehouseName": "FBS Екатеринбург 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1769,
                "warehouseName": "Гомель Шилова РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122256,
                "warehouseName": "Склад поставщика 96 часов",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124097,
                "warehouseName": "FBS Краснодар 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 204615,
                "warehouseName": "СЦ Томск",
                "warehouseAddress": "ул. Мичурина, 47с14 ",
                "warehouseMapID": 1144,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 200,
                    "logisticLiter": "19",
                    "logisticBase": "76",
                    "storageLiter": "0,16",
                    "storageBase": "0,16",
                    "storageCoefficient": 200,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 300219,
                "warehouseName": "СЦ Вологда 2",
                "warehouseAddress": "Вологда, Окружное шоссе 13 корпус 5",
                "warehouseMapID": 1445,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 185,
                    "logisticLiter": "17,58",
                    "logisticBase": "70,3",
                    "storageLiter": "0,15",
                    "storageBase": "0,15",
                    "storageCoefficient": 185,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 300219,
                "warehouseName": "СЦ Вологда 2",
                "warehouseAddress": "Вологда, Окружное шоссе 13 корпус 5",
                "warehouseMapID": 1445,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 180,
                    "logisticLiter": "17,1",
                    "logisticBase": "68,4",
                    "storageBase": "45",
                    "storageCoefficient": 180,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 6156,
                "warehouseName": "СЦ Рязань",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1804,
                "warehouseName": "Челябинск Дзержинского РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127450,
                "warehouseName": "FBS Магнит №4 Домино",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6146,
                "warehouseName": "Брянск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 300987,
                "warehouseName": "СЦ Смоленск 3",
                "warehouseAddress": "Смоленск, Улица Лавочкина, 107",
                "warehouseMapID": 1496,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 3140,
                "warehouseName": "СЦ Нижний Новгород не исп",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6150,
                "warehouseName": "СЦ Ульяновск old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 315036,
                "warehouseName": "Астрахань КБТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117419,
                "warehouseName": "СЦ Новосибирск 1 old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127463,
                "warehouseName": "FBS Магнит №13 Театральны",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124731,
                "warehouseName": "Крёкшино СГТ",
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 158328,
                "warehouseName": "СЦ Южные Ворота",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 115574,
                "warehouseName": "Склад Климовск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117447,
                "warehouseName": "СЦ Коледино К4",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117429,
                "warehouseName": "СЦ Краснодар 3",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132150,
                "warehouseName": "FBS Магнит №16 Хёрбранц",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124095,
                "warehouseName": "FBS Екатеринбург 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132321,
                "warehouseName": "FBS Москва Своими силами 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1775,
                "warehouseName": "Владимир Матросова РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122252,
                "warehouseName": "Склад поставщика 72 часа ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 115649,
                "warehouseName": "СЦ Балашиха",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 209208,
                "warehouseName": "СЦ Сыктывкар",
                "warehouseAddress": "Республика Коми, г.Сыктывкар ул. 1-ая Промышленная д.19",
                "warehouseMapID": 1229,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 200,
                    "logisticLiter": "19",
                    "logisticBase": "76",
                    "storageLiter": "0,16",
                    "storageBase": "0,16",
                    "storageCoefficient": 200,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 203632,
                "warehouseName": "СЦ Иваново (до 03.05.23)",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 303219,
                "warehouseName": "СЦ Киров",
                "warehouseAddress": "Киров ул. Павла Корчагина, 227А",
                "warehouseMapID": 2013,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 185,
                    "logisticLiter": "17,58",
                    "logisticBase": "70,3",
                    "storageLiter": "0,15",
                    "storageBase": "0,15",
                    "storageCoefficient": 185,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 218698,
                "warehouseName": "СЦ Шымкент",
                "warehouseAddress": "г. Шымкент микрорайон Самал 3 улица А Шеримкулова 5",
                "warehouseMapID": 1300,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 115,
                    "logisticLiter": "10,93",
                    "logisticBase": "43,7",
                    "storageLiter": "0,09",
                    "storageBase": "0,09",
                    "storageCoefficient": 115,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 132870,
                "warehouseName": "Москва Своими силами 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 301809,
                "warehouseName": "Котовск",
                "warehouseAddress": "Тамбовская область, муниципальное образование город Котовск, район индустриальный парк Котовск, 3/8",
                "warehouseMapID": 1794,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 325342,
                "warehouseName": "Волгоград СГТ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 122592,
                "warehouseName": "FBS Очаково 96ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 118019,
                "warehouseName": "СЦ Ростов-на-Дону old-1",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127451,
                "warehouseName": "FBS Магнит №5 Рубероидный",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 205985,
                "warehouseName": "СЦ Крыловская",
                "warehouseAddress": "Россия, станица Крыловская, Краснодарский край, ул. Орджоникидзе д.161",
                "warehouseMapID": 1193,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 140,
                    "logisticLiter": "13,3",
                    "logisticBase": "53,2",
                    "storageLiter": "0,12",
                    "storageBase": "0,12",
                    "storageCoefficient": 145,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 205985,
                "warehouseName": "СЦ Крыловская",
                "warehouseAddress": "Россия, станица Крыловская, Краснодарский край, ул. Орджоникидзе д.161",
                "warehouseMapID": 1193,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 225,
                    "logisticLiter": "21,38",
                    "logisticBase": "85,5",
                    "storageBase": "56.25",
                    "storageCoefficient": 225,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 1786,
                "warehouseName": "Артем Интернациональная Р",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1810,
                "warehouseName": "Кандалакша Советская РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 302769,
                "warehouseName": "СЦ Архангельск (ул Ленина)",
                "warehouseAddress": "Архангельск, улица Ленина, 29",
                "warehouseMapID": 1497,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 180,
                    "logisticLiter": "17,1",
                    "logisticBase": "68,4",
                    "storageLiter": "0,14",
                    "storageBase": "0,14",
                    "storageCoefficient": 180,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 6158,
                "warehouseName": "Николо-Хованское",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 118400,
                "warehouseName": "СЦ Екатеринбург",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117448,
                "warehouseName": "СЦ Коледино К5",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117289,
                "warehouseName": "СЦ Лобня",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 126675,
                "warehouseName": "FBS Москва Экспресс-доставка 3",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 125239,
                "warehouseName": "Склад пос-ка СПБ КБТ 72ч",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 132871,
                "warehouseName": "Москва Своими силами 3",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218402,
                "warehouseName": "СЦ Иваново",
                "warehouseAddress": "Ивановская область, ул. Окружная д. 16",
                "warehouseMapID": 1295,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 5,
                    "boxTypeName": "Монопаллета",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 135,
                    "logisticLiter": "12,83",
                    "logisticBase": "51,3",
                    "storageBase": "33.75",
                    "storageCoefficient": 135,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 218402,
                "warehouseName": "СЦ Иваново",
                "warehouseAddress": "Ивановская область, ул. Окружная д. 16",
                "warehouseMapID": 1295,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 1800,
                "warehouseName": "Мирный Комсомольская РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 1678,
                "warehouseName": "Мурманск Промышленная РЦ",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 116471,
                "warehouseName": "СЦ Бронницы",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117428,
                "warehouseName": "СЦ Краснодар 2",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 333530,
                "warehouseName": "СЦ Симферополь Молодежненское",
                "warehouseAddress": "Симферополь, Московское шоссе, 12 км",
                "warehouseMapID": 2146,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 145,
                    "logisticLiter": "13,78",
                    "logisticBase": "55,1",
                    "storageLiter": "0,12",
                    "storageBase": "0,12",
                    "storageCoefficient": 145,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 211470,
                "warehouseName": "СЦ Нижний Тагил",
                "warehouseAddress": "Свердловская область, г. Нижний Тагил, ул. Индустриальная, д. 30/1",
                "warehouseMapID": 1227,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 210,
                    "logisticLiter": "19,95",
                    "logisticBase": "79,8",
                    "storageLiter": "0,17",
                    "storageBase": "0,17",
                    "storageCoefficient": 210,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 50049050,
                "warehouseName": "Софьино СГТ",
                "warehouseAddress": "Логопарк Софьино, контрольно-пропускной пункт № 2 Московская область, Раменский городской округ, территория Логистический технопарк Софьино, 15 корпус 6.1",
                "warehouseMapID": 2151,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 124583,
                "warehouseName": "FBS Европа",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 127464,
                "warehouseName": "FBS Магнит №14 Леваневски",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 168458,
                "warehouseName": "СЦ Омск",
                "warehouseAddress": "г. Омск, ул. Раздольная, д 1к3",
                "warehouseMapID": 493,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 300168,
                "warehouseName": "СЦ Радумля",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 6151,
                "warehouseName": "Курск",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 218210,
                "warehouseName": "Обухово",
                "warehouseAddress": "Московская область, Богородский городской округ, рабочий поселок Обухово, территория Атлант-Парк, дом 35",
                "warehouseMapID": 1248,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 126679,
                "warehouseName": "FBS Москва Экспресс 9-18",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 118026,
                "warehouseName": "СЦ Омск 2-я Казахста",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 119400,
                "warehouseName": "Склад Пушкино old",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 117866,
                "warehouseName": "СЦ Тамбов",
                "warehouseAddress": "ул. Волжская, 71Б",
                "warehouseMapID": 802,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 130,
                    "logisticLiter": "12,35",
                    "logisticBase": "49,4",
                    "storageLiter": "0,1",
                    "storageBase": "0,1",
                    "storageCoefficient": 130,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            },
            {
                "warehouseID": 135243,
                "warehouseName": "FBS Москва Экспресс-доставка 6",
                "isRecommended": false,
                "isActive": false,
                "boxType": {
                    "boxTypeID": 0,
                    "boxTypeName": "",
                    "acceptAll": false,
                    "count": 0,
                    "hasAvailableDate": false,
                    "totalCount": 0
                }
            },
            {
                "warehouseID": 169872,
                "warehouseName": "СЦ Астрахань",
                "warehouseAddress": "улица Рождественского, 17",
                "warehouseMapID": 23,
                "isRecommended": false,
                "isActive": true,
                "boxType": {
                    "boxTypeID": 2,
                    "boxTypeName": "Короб",
                    "acceptAll": true,
                    "count": 4,
                    "logisticCoefficient": 160,
                    "logisticLiter": "15,2",
                    "logisticBase": "60,8",
                    "storageLiter": "0,13",
                    "storageBase": "0,13",
                    "storageCoefficient": 160,
                    "hasAvailableDate": false,
                    "totalCount": 4
                }
            }
        ]
    }
}