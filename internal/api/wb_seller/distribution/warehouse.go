package distribution

// GetAllWarehouseRequest 获取所有仓库请求
type GetAllWarehouseRequest struct {
	Params struct {
		Filters []interface{} `json:"filters"`
		Sorting string        `json:"sorting"`
	} `json:"params"`
	JsonRPC string `json:"jsonrpc"`
	ID      string `json:"id"`
}

// GetAllWarehouseResponse 获取所有仓库响应
type GetAllWarehouseResponse struct {
	ID      string `json:"id"`
	JsonRPC string `json:"jsonrpc"`
	Result  struct {
		Resp struct {
			Data []WarehouseInfo `json:"data"`
		} `json:"resp"`
	} `json:"result"`
}

// WarehouseInfo 仓库信息
type WarehouseInfo struct {
	ID              int     `json:"id"`              // 仓库ID
	OrigID          int     `json:"origid"`          // 原始仓库ID
	Name            string  `json:"warehouse"`       // 仓库名称
	LoadingSchedule string  `json:"loadingSchedule"` // 装货时间表
	WorkTime        string  `json:"workTime"`        // 工作时间
	Address         string  `json:"address"`         // 仓库地址
	Latitude        float64 `json:"latitude"`        // 纬度
	Longitude       float64 `json:"longitude"`       // 经度
	Gates           string  `json:"gates,omitempty"` // 仓库门号
	PassText        string  `json:"passText"`        // 通行证说明文本
	IsPassNeeded    bool    `json:"isPassNeeded"`    // 是否需要通行证
	NearCityID      int     `json:"nearCityId"`      // 邻近城市ID
	NearCity        struct {
		ID    int    `json:"id"`    // 城市ID
		Title string `json:"title"` // 城市名称
	} `json:"nearCity"` // 邻近城市信息

	SortingTime int      `json:"sortingTime"` // 分拣时间（分钟）
	Photos      []string `json:"photos"`      // 仓库照片URL列表

	// FBW - Fulfillment by Wildberries（威尔伯雷斯代发货）
	IsFbw                 bool   `json:"isFbw"`                 // 是否支持FBW业务
	IsFbwRestrictionCargo string `json:"isFbwRestrictionCargo"` // FBW业务货物限制说明

	// FBS - Fulfillment by Seller（卖家自行发货）
	IsFbs                 bool   `json:"isFbs"`                 // 是否支持FBS业务
	IsFbsRestrictionCargo string `json:"isFbsRestrictionCargo"` // FBS业务货物限制说明

	// 配送相关
	IsDistribution                 bool   `json:"isDistribution"`                 // 是否为配送中心
	IsDistributionRestrictionCargo string `json:"isDistributionRestrictionCargo"` // 配送中心货物限制说明
	IsPickPoints                   bool   `json:"isPickPoints"`                   // 是否支持自提点
	IsPickPointsRestrictionCargo   string `json:"isPickPointsRestrictionCargo"`   // 自提点货物限制说明
	IsService                      bool   `json:"isService"`                      // 是否提供服务
	IsServiceRestrictionCargo      string `json:"isServiceRestrictionCargo"`      // 服务类货物限制说明
	IsInternational                bool   `json:"isInternational"`                // 是否支持国际业务

	Restrictions string  `json:"restrictions"` // 仓库限制说明
	Rating       float64 `json:"rating"`       // 仓库评分

	// 操作相关
	IsAcceptsQRScan           bool `json:"isAcceptsQRScan"`           // 是否接受QR码扫描
	IsDontCheckDeliveryPeriod bool `json:"isDontCheckDeliveryPeriod"` // 是否不检查交货期
	IsDontCheckSortingTime    bool `json:"isDontCheckSortingTime"`    // 是否不检查分拣时间

	// 付费验收相关
	IsDefaultPaidAcceptance         bool `json:"isDefaultPaidAcceptance"`         // 是否默认付费验收
	IsDefaultPaidAcceptanceOfSupply bool `json:"isDefaultPaidAcceptanceOfSupply"` // 是否默认供应付费验收

	// 交货期相关
	DeliveryPeriodToShelfInt int64  `json:"deliveryPeriodToShelfInt,omitempty"` // 到货架交货期（整数，单位：分钟）
	DeliveryPeriodToShelf    string `json:"deliveryPeriodToShelf,omitempty"`    // 到货架交货期（字符串描述）

	BoxTypeMask         int  `json:"boxTypeMask"`         // 箱子类型掩码（用于标识支持的箱子类型）
	NoPassEqueueAllowed bool `json:"noPassEqueueAllowed"` // 是否允许无通行证电子排队

	OfficeTimeOffset int `json:"officeTimeOffset,omitempty"` // 办公时间偏移（单位：小时，用于处理时区差异）
}
