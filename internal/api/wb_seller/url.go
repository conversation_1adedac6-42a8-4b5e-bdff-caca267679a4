package wb_seller

const (
	// 获取每周关键词榜
	UrlGetWeeklyTrendingSearches = "https://seller-weekly-report.wildberries.ru/ns/trending-searches/suppliers-portal-analytics/api"
	// 获取供应列表
	UrlListSupplies = "https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/listSupplies"
	// 获取供应详情
	UrlSupplyDetails = "https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/supplyDetails"
	// 创建供应草稿
	UrlCreateDraft = "https://seller-supply.wildberries.ru/ns/sm-draft/supply-manager/api/v1/draft/create"
	UrlDeleteDraft = "https://seller-supply.wildberries.ru/ns/sm-draft/supply-manager/api/v1/draft/delete"
	UrlListDrafts  = "https://seller-supply.wildberries.ru/ns/sm-draft/supply-manager/api/v1/draft/listDrafts"
	// 获取商品列表
	UrlListGoods = "https://seller-supply.wildberries.ru/ns/sm/supply-manager/api/v1/content/listGoodsIncludingAdded"
	// 更新草稿商品
	UrlUpdateDraftGoods = "https://seller-supply.wildberries.ru/ns/sm-draft/supply-manager/api/v1/draft/UpdateDraftGoods"
	// 获取仓库推荐
	UrlWarehouseRecommend = "https://seller-supply.wildberries.ru/ns/sm-recommendations/supply-manager/api/v1/recommendations/warehouseRecommendation"
	// 获取所有仓库
	UrlGetAllWarehouse = "https://seller.wildberries.ru/ns/distribution-offices/distribution-offices/api/v1/office/getAllWarehouse"
	// 创建供应单
	UrlCreateSupply = "https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/create"
	// 获取验收成本
	UrlGetAcceptanceCosts = "https://seller-supply.wildberries.ru/ns/sm-supply/supply-manager/api/v1/supply/getAcceptanceCosts"
)
