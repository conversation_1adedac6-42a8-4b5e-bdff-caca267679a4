package wb_seller

import (
	"fmt"
	"lens/internal/api/wb_seller/distribution"
	"lens/internal/api/wb_seller/report"
	"lens/internal/api/wb_seller/supply"
	"net/http"
	"sync/atomic"
	"time"

	"github.com/go-resty/resty/v2"
)

type Api interface {
	GetWeeklyTrendingSearches(query string, offset int) ([]report.TrendingKeywordInfo, error)
	GetAllWeeklyTrendingSearches(query string) ([]report.TrendingKeywordInfo, error)
	ListSupplies(params supply.ListParams) (*supply.ListResponse, error)
	GetSupplyDetails(params supply.DetailsParams) (*supply.DetailsResponse, error)
	CreateDraft() (*supply.DraftResponse, error)
	DeleteDraft(draftID string) (*supply.DeleteDraftResponse, error)
	ListDrafts(limit int, offset int, orderByCreatedAt int) (*supply.ListDraftsResponse, error)
	ListGoods(search string, limit int, offset int, draftID string) (*supply.ListGoodsResponse, error)
	UpdateDraftGoods(draftID string, barcodes []struct {
		Barcode  string
		Quantity int
	}) (*supply.UpdateDraftGoodsResponse, error)
	GetWarehouseRecommend(draftID string) (*supply.WarehouseRecommendResponse, error)
	GetAllWarehouse(sorting string) (*distribution.GetAllWarehouseResponse, error)
	CreateSupply(draftID string, boxTypeMask int, transitWarehouseId int, warehouseId int) (*supply.CreateSupplyResponse, error)
	GetAcceptanceCosts(preorderID int, dateFrom, dateTo time.Time) (*supply.GetAcceptanceCostsResponse, error)
}

type api struct {
	*resty.Client
	requestCounter uint64
}

// generateRequestID 生成唯一的 JSON-RPC 请求 ID
func (a *api) generateRequestID() string {
	counter := atomic.AddUint64(&a.requestCounter, 1)
	return fmt.Sprintf("json-rpc_%d", counter)
}

func Init() Api {
	userAgent := "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0"
	authorize := "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
	client := resty.New().
		//SetHeader("Host", "seller-weekly-report.wildberries.ru").
		SetHeader("Connection", "keep-alive").
		SetHeader("sec-ch-ua-platform", "macOS").
		SetHeader("User-Agent", userAgent).
		SetHeader("sec-ch-ua", `"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"`).
		SetHeader("AuthorizeV3", authorize).
		SetHeader("Content-type", "application/json").
		SetHeader("sec-ch-ua-mobile", "?0").
		SetHeader("Accept", "*/*").
		SetHeader("Origin", "https://seller.wildberries.ru").
		SetHeader("Sec-Fetch-Site", "same-site").
		SetHeader("Sec-Fetch-Mode", "cors").
		SetHeader("Sec-Fetch-Dest", "empty").
		SetHeader("Referer", "https://seller.wildberries.ru/").
		SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6").
		SetCookie(&http.Cookie{
			Name:  "_wbauid",
			Value: "10670702781754989657",
		}).
		SetCookie(&http.Cookie{
			Name:  "wbx-validation-key",
			Value: "a27c5fa2-d66e-410e-affc-01f2dc1468f7",
		}).
		SetCookie(&http.Cookie{
			Name:  "x-supplier-id-external",
			Value: "47b418ca-bf65-4755-bd58-0fffd9985525",
		}).
		// 设置重试选项
		SetRetryCount(3).
		SetRetryWaitTime(2 * time.Second).
		SetRetryMaxWaitTime(5 * time.Second).
		SetProxy("socks5://lens:ls3903850@185.22.152.62:23481").
		// 添加重试条件
		AddRetryCondition(func(r *resty.Response, err error) bool {
			return err != nil || r.StatusCode() == http.StatusTooManyRequests
		})

	return &api{
		Client:         client,
		requestCounter: 0,
	}
}

func (a *api) GetWeeklyTrendingSearches(query string, offset int) ([]report.TrendingKeywordInfo, error) {
	var result report.RespTrendingKeywords

	resp, err := a.R().
		SetQueryParams(map[string]string{
			"itemsPerPage": "100",
			"offset":       fmt.Sprintf("%d", offset),
			"period":       "week",
			"query":        query,
			"sort":         "desc",
		}).
		SetResult(&result).
		Get(UrlGetWeeklyTrendingSearches)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	if result.Error {
		return nil, fmt.Errorf("API返回错误: %s", result.ErrorText)
	}

	// 设置处理时间
	now := time.Now()
	for i := range result.Data.List {
		result.Data.List[i].PlatformId = "wb"
		result.Data.List[i].InProcessAt = now
	}

	return result.Data.List, nil
}

func (a *api) GetAllWeeklyTrendingSearches(query string) ([]report.TrendingKeywordInfo, error) {
	var allKeywords []report.TrendingKeywordInfo
	offset := 0

	// 获取第一页数据
	firstPage, err := a.GetWeeklyTrendingSearches(query, offset)
	if err != nil {
		return nil, err
	}

	allKeywords = firstPage

	// 获取总数
	var result report.RespTrendingKeywords
	resp, err := a.R().
		SetQueryParams(map[string]string{
			"itemsPerPage": "100",
			"offset":       "0",
			"period":       "week",
			"query":        query,
			"sort":         "desc",
		}).
		SetResult(&result).
		Get(UrlGetWeeklyTrendingSearches)

	if err != nil {
		return nil, err
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("获取总数时API返回错误状态码: %d", resp.StatusCode())
	}

	totalCount := result.Data.Count

	// 如果总数大于100，继续获取剩余数据
	for offset += 100; offset < totalCount; offset += 100 {
		// 添加请求间隔
		time.Sleep(2 * time.Second)

		page, err := a.GetWeeklyTrendingSearches(query, offset)
		if err != nil {
			return nil, fmt.Errorf("获取偏移量 %d 的数据时出错: %w", offset, err)
		}
		allKeywords = append(allKeywords, page...)
	}

	return allKeywords, nil
}

func (a *api) ListSupplies(params supply.ListParams) (*supply.ListResponse, error) {
	request := supply.ListRequest{
		Params:  params,
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.ListResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlListSupplies)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) GetSupplyDetails(params supply.DetailsParams) (*supply.DetailsResponse, error) {
	request := supply.DetailsRequest{
		Params:  params,
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.DetailsResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlSupplyDetails)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) CreateDraft() (*supply.DraftResponse, error) {
	request := supply.DraftRequest{
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.DraftResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlCreateDraft)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) DeleteDraft(draftID string) (*supply.DeleteDraftResponse, error) {
	request := supply.DeleteDraftRequest{
		Params: struct {
			DraftID string `json:"draftID"`
		}{
			DraftID: draftID,
		},
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.DeleteDraftResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlDeleteDraft)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) ListDrafts(limit int, offset int, orderByCreatedAt int) (*supply.ListDraftsResponse, error) {
	request := supply.ListDraftsRequest{
		Params: struct {
			Filter struct {
				OrderBy struct {
					CreatedAt int `json:"createdAt"`
				} `json:"orderBy"`
			} `json:"filter"`
			Limit  int `json:"limit"`
			Offset int `json:"offset"`
		}{
			Filter: struct {
				OrderBy struct {
					CreatedAt int `json:"createdAt"`
				} `json:"orderBy"`
			}{
				OrderBy: struct {
					CreatedAt int `json:"createdAt"`
				}{
					CreatedAt: orderByCreatedAt,
				},
			},
			Limit:  limit,
			Offset: offset,
		},
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.ListDraftsResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlListDrafts)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) ListGoods(search string, limit int, offset int, draftID string) (*supply.ListGoodsResponse, error) {
	request := supply.ListGoodsRequest{
		Params: struct {
			Filter struct {
				Search   string   `json:"search"`
				Brands   []string `json:"brands"`
				Subjects []string `json:"subjects"`
			} `json:"filter"`
			Limit   int    `json:"limit"`
			Offset  int    `json:"offset"`
			DraftID string `json:"draftID"`
		}{
			Filter: struct {
				Search   string   `json:"search"`
				Brands   []string `json:"brands"`
				Subjects []string `json:"subjects"`
			}{
				Search:   search,
				Brands:   []string{},
				Subjects: []string{},
			},
			Limit:   limit,
			Offset:  offset,
			DraftID: draftID,
		},
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.ListGoodsResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlListGoods)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) UpdateDraftGoods(draftID string, barcodes []struct {
	Barcode  string
	Quantity int
}) (*supply.UpdateDraftGoodsResponse, error) {
	// 创建一个新的切片来存储带有json标签的结构体
	jsonBarcodes := make([]struct {
		Barcode  string `json:"barcode"`
		Quantity int    `json:"quantity"`
	}, len(barcodes))

	// 复制数据
	for i, b := range barcodes {
		jsonBarcodes[i].Barcode = b.Barcode
		jsonBarcodes[i].Quantity = b.Quantity
	}

	request := supply.UpdateDraftGoodsRequest{
		Params: struct {
			Barcodes []struct {
				Barcode  string `json:"barcode"`
				Quantity int    `json:"quantity"`
			} `json:"barcodes"`
			DraftID string `json:"draftID"`
		}{
			Barcodes: jsonBarcodes,
			DraftID:  draftID,
		},
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.UpdateDraftGoodsResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlUpdateDraftGoods)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) GetWarehouseRecommend(draftID string) (*supply.WarehouseRecommendResponse, error) {
	request := supply.WarehouseRecommendRequest{
		Params: struct {
			DraftID string `json:"draftId"`
		}{
			DraftID: draftID,
		},
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.WarehouseRecommendResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlWarehouseRecommend)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) GetAllWarehouse(sorting string) (*distribution.GetAllWarehouseResponse, error) {
	request := distribution.GetAllWarehouseRequest{
		Params: struct {
			Filters []interface{} `json:"filters"`
			Sorting string        `json:"sorting"`
		}{
			Filters: []interface{}{},
			Sorting: sorting,
		},
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response distribution.GetAllWarehouseResponse

	resp, err := a.R().
		SetHeader("Host", "seller.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlGetAllWarehouse)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) CreateSupply(draftID string, boxTypeMask int, transitWarehouseId int, warehouseId int) (*supply.CreateSupplyResponse, error) {
	params := struct {
		BoxTypeMask        int    `json:"boxTypeMask"`
		DraftID            string `json:"draftID"`
		TransitWarehouseId int    `json:"transitWarehouseId,omitempty"`
		WarehouseId        int    `json:"warehouseId"`
	}{
		BoxTypeMask: boxTypeMask,
		DraftID:     draftID,
		WarehouseId: warehouseId,
	}

	// 只有当 transitWarehouseId 不为 0 时才设置
	if transitWarehouseId != 0 {
		params.TransitWarehouseId = transitWarehouseId
	}

	request := supply.CreateSupplyRequest{
		Params:  params,
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.CreateSupplyResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		SetCookies([]*http.Cookie{
			{
				Name:  "_wbauid",
				Value: "2346201101749683656",
			},
			{
				Name:  "wbx-validation-key",
				Value: "dc5ec171-7aae-4a66-b06a-10ba92181060",
			},
			{
				Name:  "x-supplier-id-external",
				Value: "47b418ca-bf65-4755-bd58-0fffd9985525",
			},
			{
				Name:  "external-locale",
				Value: "ru",
			},
		}).
		Post(UrlCreateSupply)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}

func (a *api) GetAcceptanceCosts(preorderID int, dateFrom, dateTo time.Time) (*supply.GetAcceptanceCostsResponse, error) {
	request := supply.GetAcceptanceCostsRequest{
		Params: struct {
			DateFrom   string `json:"dateFrom"`
			DateTo     string `json:"dateTo"`
			PreorderID int    `json:"preorderID"`
		}{
			DateFrom:   dateFrom.Format(time.RFC3339),
			DateTo:     dateTo.Format(time.RFC3339),
			PreorderID: preorderID,
		},
		JsonRPC: "2.0",
		ID:      a.generateRequestID(),
	}

	var response supply.GetAcceptanceCostsResponse

	resp, err := a.R().
		SetHeader("Host", "seller-supply.wildberries.ru").
		SetBody(request).
		SetResult(&response).
		Post(UrlGetAcceptanceCosts)

	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode())
	}

	return &response, nil
}
