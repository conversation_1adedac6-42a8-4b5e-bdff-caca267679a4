package report

import "time"

type RespTrendingKeywords struct {
	Error            bool                 `json:"error"`
	Data             TrendingKeywordsData `json:"data"`
	AdditionalErrors interface{}          `json:"additionalErrors"`
	ErrorText        string               `json:"errorText"`
}

type TrendingKeywordsData struct {
	List  []TrendingKeywordInfo `json:"list"`
	Count int                   `json:"count"`
}

type TrendingKeywordInfo struct {
	PlatformId   string    `json:"platform_id"` // 平台ID
	Text         string    `json:"text"`
	RequestCount int       `json:"requestCount"`
	InProcessAt  time.Time `json:"in_process_at"`
}
