package handlers

import (
	"bufio"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

type WebSocketHandler struct {
	clients   map[*websocket.Conn]bool
	broadcast chan []byte
	upgrader  websocket.Upgrader
	mu        sync.RWMutex // 保护clients map的并发访问
}

func NewWebSocketHandler(broadcast chan []byte) *WebSocketHandler {
	return &WebSocketHandler{
		clients:   make(map[*websocket.Conn]bool),
		broadcast: broadcast,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许跨域
			},
		},
	}
}

// HandleConnection 处理WebSocket连接
func (h *WebSocketHandler) HandleConnection(w http.ResponseWriter, r *http.Request) {
	// 设置连接超时
	conn, err := h.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}
	defer func() {
		h.removeClient(conn)
		conn.Close()
	}()

	// 设置读写超时
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))

	// 设置Pong处理器来重置读取超时
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// 注册客户端
	h.addClient(conn)
	log.Printf("新的WebSocket客户端连接，当前连接数: %d", h.getClientCount())

	// 启动ping协程
	go h.pingClient(conn)

	for {
		// 读取消息
		messageType, p, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket连接异常关闭: %v", err)
			}
			break
		}

		// 重置读取超时
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))

		// 回显消息（可选，根据需要决定是否保留）
		if err := conn.WriteMessage(messageType, p); err != nil {
			log.Printf("发送WebSocket消息失败: %v", err)
			break
		}
	}
}

// addClient 线程安全地添加客户端
func (h *WebSocketHandler) addClient(conn *websocket.Conn) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.clients[conn] = true
}

// removeClient 线程安全地移除客户端
func (h *WebSocketHandler) removeClient(conn *websocket.Conn) {
	h.mu.Lock()
	defer h.mu.Unlock()
	delete(h.clients, conn)
}

// getClientCount 获取当前客户端连接数
func (h *WebSocketHandler) getClientCount() int {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return len(h.clients)
}

// pingClient 定期发送ping消息保持连接活跃
func (h *WebSocketHandler) pingClient(conn *websocket.Conn) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// HandleBroadcast 处理广播消息
func (h *WebSocketHandler) HandleBroadcast() {
	for {
		msg := <-h.broadcast
		h.broadcastMessage(msg)
	}
}

// broadcastMessage 向所有客户端广播消息
func (h *WebSocketHandler) broadcastMessage(msg []byte) {
	h.mu.RLock()
	clients := make([]*websocket.Conn, 0, len(h.clients))
	for client := range h.clients {
		clients = append(clients, client)
	}
	h.mu.RUnlock()

	// 并发发送消息给所有客户端
	var wg sync.WaitGroup
	for _, client := range clients {
		wg.Add(1)
		go func(conn *websocket.Conn) {
			defer wg.Done()

			// 设置写入超时
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))

			if err := conn.WriteMessage(websocket.TextMessage, msg); err != nil {
				log.Printf("向客户端发送消息失败: %v", err)
				h.removeClient(conn)
				conn.Close()
			}
		}(client)
	}

	// 等待所有消息发送完成，但设置超时避免无限等待
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 所有消息发送完成
	case <-time.After(5 * time.Second):
		log.Printf("广播消息超时，部分客户端可能未收到消息")
	}
}

// Write writes data to the websocket connection
func (h *WebSocketHandler) Write(conn *websocket.Conn, data []byte) error {
	writer, err := conn.NextWriter(websocket.TextMessage)
	if err != nil {
		return err
	}
	defer writer.Close()

	_, err = writer.Write(data)
	return err
}

// Read reads data from the websocket connection
func (h *WebSocketHandler) Read(conn *websocket.Conn) ([]byte, error) {
	_, reader, err := conn.NextReader()
	if err != nil {
		return nil, err
	}

	buffer := make([]byte, 1024)
	n, err := reader.Read(buffer)
	if err != nil && err != bufio.ErrBufferFull {
		return nil, err
	}

	return buffer[:n], nil
}