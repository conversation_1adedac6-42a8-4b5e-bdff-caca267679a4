package handlers

import (
	"lens/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
)

type ProductHandler struct {
	monitorService *service.ProductMonitorService
}

func NewProductHandler(monitorService *service.ProductMonitorService) *ProductHandler {
	return &ProductHandler{
		monitorService: monitorService,
	}
}

// CreateProduct 创建新的监控产品
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	var req struct {
		ProductID string   `json:"productId" binding:"required"`
		SKU       string   `json:"sku" binding:"required"`
		Keywords  []string `json:"keywords" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.monitorService.CreateProduct(req.ProductID, req.SKU, req.Keywords); err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "产品创建成功"})
}

// DeleteProduct 删除监控产品
func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	productID := c.Param("productId")
	if productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "产品ID不能为空"})
		return
	}

	if err := h.monitorService.DeleteProduct(productID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "产品删除成功"})
}

// GetProduct 获取产品信息
func (h *ProductHandler) GetProduct(c *gin.Context) {
	productID := c.Param("productId")
	if productID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "产品ID不能为空"})
		return
	}

	product, err := h.monitorService.GetProduct(productID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, product)
}

// GetAllProducts 获取所有产品信息
func (h *ProductHandler) GetAllProducts(c *gin.Context) {
	products := h.monitorService.GetAllProducts()
	c.JSON(http.StatusOK, products)
}
