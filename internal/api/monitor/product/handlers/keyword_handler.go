package handlers

import (
	"lens/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
)

type KeywordHandler struct {
	monitorService *service.ProductMonitorService
}

func NewKeywordHandler(monitorService *service.ProductMonitorService) *KeywordHandler {
	return &KeywordHandler{
		monitorService: monitorService,
	}
}

// AppendKeywords 为指定产品追加关键词
func (h *KeywordHandler) AppendKeywords(c *gin.Context) {
	var req struct {
		ProductID string   `json:"productId" binding:"required"`
		Keywords  []string `json:"keywords" binding:"required"`
	}

	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.monitorService.AppendProductKeywords(req.ProductID, req.Keywords); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "关键词追加成功"})
}

// RemoveKeywords 从指定产品中删除关键词
func (h *KeywordHandler) RemoveKeywords(c *gin.Context) {
	var req struct {
		ProductID string   `json:"productId" binding:"required"`
		Keywords  []string `json:"keywords" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := h.monitorService.RemoveProductKeywords(req.ProductID, req.Keywords); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "关键词删除成功"})
}
