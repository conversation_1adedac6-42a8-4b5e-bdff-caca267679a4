package ozonapi

import (
	"fmt"
)

const (
	// Miscellaneous API endpoints
	barcodeAddEndpoint                = "/v1/barcode/add"
	barcodeGenerateEndpoint           = "/v1/barcode/generate"
	supplierAvailableWarehousesEndpoint = "/v1/supplier/available_warehouses"
)

// MiscService 其他服务
type MiscService struct {
	client *Client
}

// NewMiscService 创建其他服务实例
func NewMiscService(client *Client) *MiscService {
	return &MiscService{client: client}
}

// AddBarcode 添加条码
func (s *MiscService) AddBarcode(req *BarcodeAddRequest) (*BarcodeAddResponse, error) {
	resp := &BarcodeAddResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(barcodeAddEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to add barcode: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("add barcode failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GenerateBarcode 生成条码
func (s *MiscService) GenerateBarcode(req *BarcodeGenerateRequest) (*BarcodeGenerateResponse, error) {
	resp := &BarcodeGenerateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(barcodeGenerateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to generate barcode: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("generate barcode failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetSupplierAvailableWarehouses 获取供应商可用仓库
func (s *MiscService) GetSupplierAvailableWarehouses(req *SupplierAvailableWarehousesRequest) (*SupplierAvailableWarehousesResponse, error) {
	resp := &SupplierAvailableWarehousesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplierAvailableWarehousesEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supplier available warehouses: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supplier available warehouses failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}
