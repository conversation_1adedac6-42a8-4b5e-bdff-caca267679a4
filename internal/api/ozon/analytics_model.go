package ozonapi

import (
	"time"
)

// AnalyticsDataRequest 获取分析数据请求
type AnalyticsDataRequest struct {
	DateFrom   time.Time `json:"date_from"`
	DateTo     time.Time `json:"date_to"`
	Metrics    []string  `json:"metrics"`
	Dimension  []string  `json:"dimension,omitempty"`
	Filters    []AnalyticsFilter `json:"filters,omitempty"`
	Sort       []AnalyticsSort   `json:"sort,omitempty"`
	Limit      int32     `json:"limit,omitempty"`
	Offset     int32     `json:"offset,omitempty"`
}

// AnalyticsFilter 分析过滤器
type AnalyticsFilter struct {
	Key    string      `json:"key"`
	Op     string      `json:"op"`     // EQ, IN, RANGE
	Value  interface{} `json:"value"`
}

// AnalyticsSort 分析排序
type AnalyticsSort struct {
	Key   string `json:"key"`
	Order string `json:"order"` // ASC, DESC
}

// AnalyticsDataResponse 获取分析数据响应
type AnalyticsDataResponse struct {
	Result struct {
		Data      []AnalyticsDataItem `json:"data"`
		Totals    []float64           `json:"totals"`
		Timestamp time.Time           `json:"timestamp"`
	} `json:"result"`
}

// AnalyticsDataItem 分析数据项
type AnalyticsDataItem struct {
	Dimensions []AnalyticsDimension `json:"dimensions"`
	Metrics    []float64            `json:"metrics"`
}

// AnalyticsDimension 分析维度
type AnalyticsDimension struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// StockOnWarehousesRequest 获取仓库库存请求
type StockOnWarehousesRequest struct {
	Limit      int32  `json:"limit,omitempty"`
	Offset     int32  `json:"offset,omitempty"`
	WarehouseType string `json:"warehouse_type,omitempty"` // ALL, MY, PARTNER
}

// StockOnWarehousesResponse 获取仓库库存响应
type StockOnWarehousesResponse struct {
	Result struct {
		Rows []StockWarehouseItem `json:"rows"`
	} `json:"result"`
}

// StockWarehouseItem 仓库库存项
type StockWarehouseItem struct {
	ItemCode        string `json:"item_code"`
	ItemName        string `json:"item_name"`
	FreeToSellAmount int32 `json:"free_to_sell_amount"`
	PromisedAmount   int32 `json:"promised_amount"`
	ReservedAmount   int32 `json:"reserved_amount"`
	WarehouseID      int64 `json:"warehouse_id"`
	WarehouseName    string `json:"warehouse_name"`
}

// ItemTurnoverRequest 获取商品周转率请求
type ItemTurnoverRequest struct {
	CategoryID []int64   `json:"category_id,omitempty"`
	ItemCode   []string  `json:"item_code,omitempty"`
	DateFrom   time.Time `json:"date_from"`
	DateTo     time.Time `json:"date_to"`
	Limit      int32     `json:"limit,omitempty"`
	Offset     int32     `json:"offset,omitempty"`
}

// ItemTurnoverResponse 获取商品周转率响应
type ItemTurnoverResponse struct {
	Result struct {
		Turnovers []ItemTurnover `json:"turnovers"`
	} `json:"result"`
}

// ItemTurnover 商品周转率
type ItemTurnover struct {
	ItemCode         string  `json:"item_code"`
	ItemName         string  `json:"item_name"`
	DaysInStock      int32   `json:"days_in_stock"`
	TurnoverDays     float64 `json:"turnover_days"`
	WarehouseID      int64   `json:"warehouse_id"`
	WarehouseName    string  `json:"warehouse_name"`
	CategoryID       int64   `json:"category_id"`
	CategoryName     string  `json:"category_name"`
}

// ItemForecastRequest 获取商品需求预测请求
type ItemForecastRequest struct {
	ItemCode    []string  `json:"item_code,omitempty"`
	WarehouseID []int64   `json:"warehouse_id,omitempty"`
	DateFrom    time.Time `json:"date_from"`
	DateTo      time.Time `json:"date_to"`
	Limit       int32     `json:"limit,omitempty"`
	Offset      int32     `json:"offset,omitempty"`
}

// ItemForecastResponse 获取商品需求预测响应
type ItemForecastResponse struct {
	Result struct {
		Forecasts []ItemForecast `json:"forecasts"`
	} `json:"result"`
}

// ItemForecast 商品需求预测
type ItemForecast struct {
	ItemCode      string  `json:"item_code"`
	ItemName      string  `json:"item_name"`
	WarehouseID   int64   `json:"warehouse_id"`
	WarehouseName string  `json:"warehouse_name"`
	Date          time.Time `json:"date"`
	Forecast      int32   `json:"forecast"`
	ForecastType  string  `json:"forecast_type"` // SALES, DEMAND
}

// ReportInfoRequest 获取报告信息请求
type ReportInfoRequest struct {
	Code string `json:"code"`
}

// ReportInfoResponse 获取报告信息响应
type ReportInfoResponse struct {
	Result ReportInfo `json:"result"`
}

// ReportInfo 报告信息
type ReportInfo struct {
	AvailableFilters []ReportFilter `json:"available_filters"`
	AvailableGroupByFields []ReportGroupBy `json:"available_group_by_fields"`
	DefaultFilters   []ReportFilter `json:"default_filters"`
	Description      string         `json:"description"`
	Name             string         `json:"name"`
}

// ReportFilter 报告过滤器
type ReportFilter struct {
	FilterType string `json:"filter_type"`
	Key        string `json:"key"`
	Name       string `json:"name"`
	Type       string `json:"type"`
}

// ReportGroupBy 报告分组字段
type ReportGroupBy struct {
	Key  string `json:"key"`
	Name string `json:"name"`
	Type string `json:"type"`
}

// ReportCreateRequest 创建报告请求
type ReportCreateRequest struct {
	Language string `json:"language,omitempty"` // DEFAULT, EN, RU
	ReportType string `json:"report_type"`
	Filter     map[string]interface{} `json:"filter,omitempty"`
}

// ReportCreateResponse 创建报告响应
type ReportCreateResponse struct {
	Result struct {
		Code string `json:"code"`
	} `json:"result"`
}

// ReportStatusRequest 获取报告状态请求
type ReportStatusRequest struct {
	Code string `json:"code"`
}

// ReportStatusResponse 获取报告状态响应
type ReportStatusResponse struct {
	Result ReportStatus `json:"result"`
}

// ReportStatus 报告状态
type ReportStatus struct {
	Code        string    `json:"code"`
	CreatedAt   time.Time `json:"created_at"`
	Error       string    `json:"error"`
	File        string    `json:"file"`
	Status      string    `json:"status"` // success, processing, failed
	UpdatedAt   time.Time `json:"updated_at"`
}

// ReportListRequest 获取报告列表请求
type ReportListRequest struct {
	Page     int32  `json:"page,omitempty"`
	PageSize int32  `json:"page_size,omitempty"`
	ReportType string `json:"report_type,omitempty"`
}

// ReportListResponse 获取报告列表响应
type ReportListResponse struct {
	Result struct {
		Reports []ReportListItem `json:"reports"`
	} `json:"result"`
}

// ReportListItem 报告列表项
type ReportListItem struct {
	Code        string    `json:"code"`
	CreatedAt   time.Time `json:"created_at"`
	Error       string    `json:"error"`
	File        string    `json:"file"`
	Parameters  map[string]interface{} `json:"parameters"`
	ReportType  string    `json:"report_type"`
	Status      string    `json:"status"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// FinanceRealizationRequest 获取财务实现报告请求
type FinanceRealizationRequest struct {
	Date  time.Time `json:"date"`
	Limit int32     `json:"limit,omitempty"`
	Offset int32    `json:"offset,omitempty"`
}

// FinanceRealizationResponse 获取财务实现报告响应
type FinanceRealizationResponse struct {
	Result struct {
		Header []string                   `json:"header"`
		Data   [][]interface{}            `json:"data"`
		Totals []interface{}              `json:"totals"`
	} `json:"result"`
}

// FinanceTransactionListRequest 获取财务交易列表请求
type FinanceTransactionListRequest struct {
	Filter FinanceTransactionFilter `json:"filter"`
	Page   int32                    `json:"page"`
	PageSize int32                  `json:"page_size"`
}

// FinanceTransactionFilter 财务交易过滤器
type FinanceTransactionFilter struct {
	Date          FinanceDateFilter `json:"date"`
	OperationType []string          `json:"operation_type,omitempty"`
	PostingNumber string            `json:"posting_number,omitempty"`
	TransactionType string          `json:"transaction_type,omitempty"`
}

// FinanceDateFilter 财务日期过滤器
type FinanceDateFilter struct {
	From time.Time `json:"from"`
	To   time.Time `json:"to"`
}

// FinanceTransactionListResponse 获取财务交易列表响应
type FinanceTransactionListResponse struct {
	Result struct {
		Operations []FinanceTransaction `json:"operations"`
		PageCount  int32                `json:"page_count"`
	} `json:"result"`
}

// FinanceTransaction 财务交易
type FinanceTransaction struct {
	OperationID     int64     `json:"operation_id"`
	OperationType   string    `json:"operation_type"`
	OperationDate   time.Time `json:"operation_date"`
	OperationTypeName string  `json:"operation_type_name"`
	DeliverySchema  string    `json:"delivery_schema"`
	PostingNumber   string    `json:"posting_number"`
	Items           []FinanceTransactionItem `json:"items"`
	Services        []FinanceTransactionService `json:"services"`
}

// FinanceTransactionItem 财务交易商品
type FinanceTransactionItem struct {
	SKU      int64   `json:"sku"`
	Name     string  `json:"name"`
	Price    float64 `json:"price"`
	Quantity int32   `json:"quantity"`
}

// FinanceTransactionService 财务交易服务
type FinanceTransactionService struct {
	Name  string  `json:"name"`
	Price float64 `json:"price"`
}
