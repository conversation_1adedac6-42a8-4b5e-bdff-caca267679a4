package ozonapi

import (
	"time"
)

// RatingSummaryRequest 获取评级摘要请求
type RatingSummaryRequest struct {
	Filter RatingSummaryFilter `json:"filter,omitempty"`
}

// RatingSummaryFilter 评级摘要过滤器
type RatingSummaryFilter struct {
	ProductID []int64  `json:"product_id,omitempty"`
	OfferID   []string `json:"offer_id,omitempty"`
	Period    string   `json:"period,omitempty"` // "week", "month", "quarter", "year"
}

// RatingSummaryResponse 获取评级摘要响应
type RatingSummaryResponse struct {
	Result RatingSummary `json:"result"`
}

// RatingSummary 评级摘要
type RatingSummary struct {
	OverallRating    float64              `json:"overall_rating"`
	TotalReviews     int32                `json:"total_reviews"`
	AverageRating    float64              `json:"average_rating"`
	RatingDistribution RatingDistribution `json:"rating_distribution"`
	RecentTrend      string               `json:"recent_trend"` // "up", "down", "stable"
	ComparedToPrevious RatingComparison   `json:"compared_to_previous"`
	TopProducts      []RatingProductInfo  `json:"top_products"`
	WorstProducts    []RatingProductInfo  `json:"worst_products"`
}

// RatingDistribution 评级分布
type RatingDistribution struct {
	FiveStars  int32 `json:"five_stars"`
	FourStars  int32 `json:"four_stars"`
	ThreeStars int32 `json:"three_stars"`
	TwoStars   int32 `json:"two_stars"`
	OneStar    int32 `json:"one_star"`
}

// RatingComparison 评级对比
type RatingComparison struct {
	RatingChange  float64 `json:"rating_change"`
	ReviewsChange int32   `json:"reviews_change"`
	Period        string  `json:"period"`
}

// RatingProductInfo 商品评级信息 (评级服务专用)
type RatingProductInfo struct {
	ProductID     int64     `json:"product_id"`
	OfferID       string    `json:"offer_id"`
	Name          string    `json:"name"`
	Rating        float64   `json:"rating"`
	ReviewsCount  int32     `json:"reviews_count"`
	LastReviewAt  time.Time `json:"last_review_at"`
}

// RatingHistoryRequest 获取评级历史请求
type RatingHistoryRequest struct {
	Filter RatingHistoryFilter `json:"filter,omitempty"`
	Limit  int32               `json:"limit,omitempty"`
	Offset int32               `json:"offset,omitempty"`
}

// RatingHistoryFilter 评级历史过滤器
type RatingHistoryFilter struct {
	ProductID []int64           `json:"product_id,omitempty"`
	OfferID   []string          `json:"offer_id,omitempty"`
	DateRange RatingDateRange   `json:"date_range,omitempty"`
	Rating    []int32           `json:"rating,omitempty"` // 1-5 stars
}

// RatingDateRange 评级日期范围
type RatingDateRange struct {
	From time.Time `json:"from,omitempty"`
	To   time.Time `json:"to,omitempty"`
}

// RatingHistoryResponse 获取评级历史响应
type RatingHistoryResponse struct {
	Result struct {
		Items []RatingHistoryItem `json:"items"`
		Total int32               `json:"total"`
	} `json:"result"`
}

// RatingHistoryItem 评级历史项
type RatingHistoryItem struct {
	ID            int64     `json:"id"`
	ProductID     int64     `json:"product_id"`
	OfferID       string    `json:"offer_id"`
	ProductName   string    `json:"product_name"`
	Rating        int32     `json:"rating"`
	Review        string    `json:"review"`
	ReviewerName  string    `json:"reviewer_name"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	IsVerified    bool      `json:"is_verified"`
	HelpfulCount  int32     `json:"helpful_count"`
	Photos        []string  `json:"photos,omitempty"`
	Pros          []string  `json:"pros,omitempty"`
	Cons          []string  `json:"cons,omitempty"`
}
