package ozonapi

import (
	"fmt"
)

const (
	// Actions API endpoints
	actionsListEndpoint              = "/v1/actions"
	actionsCandidatesEndpoint        = "/v1/actions/candidates"
	actionsProductsEndpoint          = "/v1/actions/products"
	actionsProductsActivateEndpoint  = "/v1/actions/products/activate"
	actionsProductsDeactivateEndpoint = "/v1/actions/products/deactivate"
	actionsDiscountsTaskListEndpoint = "/v1/actions/discounts-task/list"
	actionsDiscountsTaskApproveEndpoint = "/v1/actions/discounts-task/approve"
	actionsDiscountsTaskDeclineEndpoint = "/v1/actions/discounts-task/decline"
)

// ActionsService 营销活动服务
type ActionsService struct {
	client *Client
}

// NewActionsService 创建营销活动服务实例
func NewActionsService(client *Client) *ActionsService {
	return &ActionsService{client: client}
}

// GetActionsList 获取营销活动列表
func (s *ActionsService) GetActionsList(req *ActionsListRequest) (*ActionsListResponse, error) {
	resp := &ActionsListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(actionsListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get actions list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get actions list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetActionsCandidates 获取候选营销活动
func (s *ActionsService) GetActionsCandidates(req *ActionsCandidatesRequest) (*ActionsCandidatesResponse, error) {
	resp := &ActionsCandidatesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(actionsCandidatesEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get actions candidates: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get actions candidates failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetActionsProducts 获取活动商品
func (s *ActionsService) GetActionsProducts(req *ActionsProductsRequest) (*ActionsProductsResponse, error) {
	resp := &ActionsProductsResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(actionsProductsEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get actions products: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get actions products failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// ActivateActionsProducts 激活活动商品
func (s *ActionsService) ActivateActionsProducts(req *ActionsProductsActivateRequest) (*ActionsProductsActivateResponse, error) {
	resp := &ActionsProductsActivateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(actionsProductsActivateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to activate actions products: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("activate actions products failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// DeactivateActionsProducts 停用活动商品
func (s *ActionsService) DeactivateActionsProducts(req *ActionsProductsDeactivateRequest) (*ActionsProductsDeactivateResponse, error) {
	resp := &ActionsProductsDeactivateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(actionsProductsDeactivateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to deactivate actions products: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("deactivate actions products failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetDiscountsTaskList 获取折扣任务列表
func (s *ActionsService) GetDiscountsTaskList(req *ActionsDiscountsTaskListRequest) (*ActionsDiscountsTaskListResponse, error) {
	resp := &ActionsDiscountsTaskListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(actionsDiscountsTaskListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get discounts task list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get discounts task list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// ApproveDiscountsTask 批准折扣任务
func (s *ActionsService) ApproveDiscountsTask(req *ActionsDiscountsTaskApproveRequest) (*ActionsDiscountsTaskApproveResponse, error) {
	resp := &ActionsDiscountsTaskApproveResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(actionsDiscountsTaskApproveEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to approve discounts task: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("approve discounts task failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// DeclineDiscountsTask 拒绝折扣任务
func (s *ActionsService) DeclineDiscountsTask(req *ActionsDiscountsTaskDeclineRequest) (*ActionsDiscountsTaskDeclineResponse, error) {
	resp := &ActionsDiscountsTaskDeclineResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(actionsDiscountsTaskDeclineEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to decline discounts task: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("decline discounts task failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}
