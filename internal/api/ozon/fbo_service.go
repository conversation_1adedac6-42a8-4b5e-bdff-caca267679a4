package ozonapi

import (
	"fmt"

	"github.com/go-resty/resty/v2"
)

const (
	// API endpoints
	clusterListEndpoint                    = "/v1/cluster/list"
	warehouseFboListEndpoint               = "/v1/warehouse/fbo/list"
	draftCreateEndpoint                    = "/v1/draft/create"
	draftCreateInfoEndpoint                = "/v1/draft/create/info"
	draftListEndpoint                      = "/v1/draft/list"
	draftTimeslotInfoEndpoint              = "/v1/draft/timeslot/info"
	draftSupplyCreateEndpoint              = "/v1/draft/supply/create"
	draftSupplyCreateStatusEndpoint        = "/v1/draft/supply/create/status"
	cargoesCreateEndpoint                  = "/v1/cargoes/create"
	cargoesCreateInfoEndpoint              = "/v1/cargoes/create/info"
	cargoesDeleteEndpoint                  = "/v1/cargoes/delete"
	cargoesDeleteStatusEndpoint            = "/v1/cargoes/delete/status"
	cargoesRulesGetEndpoint                = "/v1/cargoes/rules/get"
	cargoesLabelCreateEndpoint             = "/v1/cargoes-label/create"
	cargoesLabelGetEndpoint                = "/v1/cargoes-label/get"
	supplyOrderCancelEndpoint              = "/v1/supply-order/cancel"
	supplyOrderCancelStatusEndpoint        = "/v1/supply-order/cancel/status"
	supplyOrderContentUpdateEndpoint       = "/v1/supply-order/content/update"
	supplyOrderContentUpdateStatusEndpoint = "/v1/supply-order/content/update/status"
	supplyOrderListEndpoint                = "/v2/supply-order/list"  // 更新到 v2 最新版本
	supplyOrderGetEndpoint                 = "/v2/supply-order/get"   // 更新到 v2 最新版本
	supplyOrderItemsUpdateEndpoint         = "/v1/supply-order/items/update"
	supplyOrderItemsUpdateStatusEndpoint   = "/v1/supply-order/items/update/status"
	supplyOrderShipEndpoint                = "/v1/supply-order/ship"
	supplyOrderShipStatusEndpoint          = "/v1/supply-order/ship/status"
	supplyOrderActCreateEndpoint           = "/v1/supply-order/act/create"
	supplyOrderActGetEndpoint              = "/v1/supply-order/act/get"
	supplyOrderActCheckStatusEndpoint      = "/v1/supply-order/act/check-status"
	supplyOrderBundleEndpoint              = "/v1/supply-order/bundle"
)

// FBOService FBO服务
type FBOService struct {
	client *resty.Client
}

// newFBOService 创建新的FBO服务
func newFBOService(c *Client) *FBOService {
	return &FBOService{
		client: c.GetHTTPClient(),
	}
}

// GetClusterAndWarehouseList 获取集群和仓库信息
func (s *FBOService) GetClusterAndWarehouseList(req *ClusterListRequest) (*ClusterListResponse, error) {
	resp := &ClusterListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(clusterListEndpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to get cluster and warehouse list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cluster and warehouse list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// SearchFboWarehouses 搜索FBO仓库
func (s *FBOService) SearchFboWarehouses(req *WarehouseFboListRequest) (*WarehouseFboListResponse, error) {
	resp := &WarehouseFboListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(warehouseFboListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to search FBO warehouses: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("search FBO warehouses failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateSupplyDraft 创建供应草稿
func (s *FBOService) CreateSupplyDraft(req *DraftCreateRequest) (string, error) {
	resp := make(map[string]interface{})
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(draftCreateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create supply draft: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create supply draft failed with status code: %d", httpResp.StatusCode())
	}

	operationID, ok := resp["operation_id"].(string)
	if !ok {
		return "", fmt.Errorf("failed to get operation_id from response")
	}

	return operationID, nil
}

// GetSupplyDraftInfo 获取供应草稿信息
func (s *FBOService) GetSupplyDraftInfo(operationID string) (*DraftCreateInfoResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &DraftCreateInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(draftCreateInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply draft info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply draft info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetDraftTimeslotInfo 获取草稿时间段信息
func (s *FBOService) GetDraftTimeslotInfo(req *DraftTimeslotInfoRequest) (*DraftTimeslotInfoResponse, error) {
	resp := &DraftTimeslotInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(draftTimeslotInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get draft timeslot info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get draft timeslot info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateSupplyFromDraft 从草稿创建供应单
func (s *FBOService) CreateSupplyFromDraft(req *DraftSupplyCreateRequest) (string, error) {
	resp := make(map[string]interface{})
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(draftSupplyCreateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create supply from draft: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create supply from draft failed with status code: %d", httpResp.StatusCode())
	}

	operationID, ok := resp["operation_id"].(string)
	if !ok {
		return "", fmt.Errorf("failed to get operation_id from response")
	}

	return operationID, nil
}

// GetDraftSupplyCreateStatus 获取草稿供应单创建状态
func (s *FBOService) GetDraftSupplyCreateStatus(operationID string) (*DraftSupplyCreateStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &DraftSupplyCreateStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(draftSupplyCreateStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get draft supply create status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get draft supply create status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateOrUpdateCargoes 创建或更新货物
func (s *FBOService) CreateOrUpdateCargoes(req *CargoesCreateRequest) (string, error) {
	resp := &CargoesCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesCreateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create or update cargoes: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create or update cargoes failed with status code: %d", httpResp.StatusCode())
	}

	return resp.OperationID, nil
}

// GetCargoesCreateInfo 获取货物创建信息
func (s *FBOService) GetCargoesCreateInfo(operationID string) (*CargoesCreateInfoResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &CargoesCreateInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesCreateInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cargoes create info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cargoes create info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// DeleteCargoes 删除货物
func (s *FBOService) DeleteCargoes(req *CargoesDeleteRequest) (string, error) {
	resp := &CargoesDeleteResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesDeleteEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to delete cargoes: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("delete cargoes failed with status code: %d", httpResp.StatusCode())
	}

	return resp.OperationID, nil
}

// GetCargoesDeleteStatus 获取货物删除状态
func (s *FBOService) GetCargoesDeleteStatus(operationID string) (*CargoesDeleteStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &CargoesDeleteStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesDeleteStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cargoes delete status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cargoes delete status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetCargoesRules 获取货物规则
func (s *FBOService) GetCargoesRules(supplyIDs []int64) (*CargoesRulesResponse, error) {
	req := map[string]interface{}{
		"supply_ids": supplyIDs,
	}
	resp := &CargoesRulesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesRulesGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cargoes rules: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cargoes rules failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateCargoesLabel 创建货物标签
func (s *FBOService) CreateCargoesLabel(req *CargoesLabelCreateRequest) (string, error) {
	resp := make(map[string]interface{})
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(cargoesLabelCreateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create cargoes label: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create cargoes label failed with status code: %d", httpResp.StatusCode())
	}

	operationID, ok := resp["operation_id"].(string)
	if !ok {
		return "", fmt.Errorf("failed to get operation_id from response")
	}

	return operationID, nil
}

// GetCargoesLabelInfo 获取货物标签信息
func (s *FBOService) GetCargoesLabelInfo(operationID string) (*CargoesLabelGetResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &CargoesLabelGetResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(cargoesLabelGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cargoes label info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cargoes label info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CancelSupplyOrder 取消供应单
func (s *FBOService) CancelSupplyOrder(req *SupplyOrderCancelRequest) (string, error) {
	resp := make(map[string]interface{})
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(supplyOrderCancelEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to cancel supply order: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("cancel supply order failed with status code: %d", httpResp.StatusCode())
	}

	operationID, ok := resp["operation_id"].(string)
	if !ok {
		return "", fmt.Errorf("failed to get operation_id from response")
	}

	return operationID, nil
}

// GetSupplyOrderCancelStatus 获取供应单取消状态
func (s *FBOService) GetSupplyOrderCancelStatus(operationID string) (*SupplyOrderCancelStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &SupplyOrderCancelStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderCancelStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order cancel status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order cancel status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// UpdateSupplyOrderContent 更新供应单内容
func (s *FBOService) UpdateSupplyOrderContent(req *SupplyOrderContentUpdateRequest) (string, error) {
	resp := &SupplyOrderContentUpdateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderContentUpdateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to update supply order content: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("update supply order content failed with status code: %d", httpResp.StatusCode())
	}

	return resp.OperationID, nil
}

// GetSupplyOrderContentUpdateStatus 获取供应单内容更新状态
func (s *FBOService) GetSupplyOrderContentUpdateStatus(operationID string) (*SupplyOrderContentUpdateStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &SupplyOrderContentUpdateStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderContentUpdateStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order content update status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order content update status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetSupplyDraftList 获取供应草稿列表
func (s *FBOService) GetSupplyDraftList(req *DraftListRequest) (*DraftListResponse, error) {
	resp := &DraftListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(draftListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply draft list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply draft list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetSupplyOrderList 获取供应单列表
func (s *FBOService) GetSupplyOrderList(req *SupplyOrderListRequest) (*SupplyOrderListResponse, error) {
	resp := &SupplyOrderListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetSupplyOrder 获取供应单详情
func (s *FBOService) GetSupplyOrder(req *SupplyOrderGetRequest) (*SupplyOrderGetResponse, error) {
	resp := &SupplyOrderGetResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// UpdateSupplyOrderItems 更新供应单商品
func (s *FBOService) UpdateSupplyOrderItems(req *SupplyOrderItemsUpdateRequest) (string, error) {
	resp := &SupplyOrderItemsUpdateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderItemsUpdateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to update supply order items: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("update supply order items failed with status code: %d", httpResp.StatusCode())
	}

	return resp.OperationID, nil
}

// GetSupplyOrderItemsUpdateStatus 获取供应单商品更新状态
func (s *FBOService) GetSupplyOrderItemsUpdateStatus(operationID string) (*SupplyOrderItemsUpdateStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &SupplyOrderItemsUpdateStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderItemsUpdateStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order items update status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order items update status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// ShipSupplyOrder 发货供应单
func (s *FBOService) ShipSupplyOrder(req *SupplyOrderShipRequest) (string, error) {
	resp := &SupplyOrderShipResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderShipEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to ship supply order: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("ship supply order failed with status code: %d", httpResp.StatusCode())
	}

	return resp.OperationID, nil
}

// GetSupplyOrderShipStatus 获取供应单发货状态
func (s *FBOService) GetSupplyOrderShipStatus(operationID string) (*SupplyOrderShipStatusResponse, error) {
	req := map[string]interface{}{
		"operation_id": operationID,
	}
	resp := &SupplyOrderShipStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderShipStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order ship status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order ship status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateSupplyOrderAct 创建供应单交接单
func (s *FBOService) CreateSupplyOrderAct(req *SupplyOrderActCreateRequest) (string, error) {
	resp := &SupplyOrderActCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderActCreateEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create supply order act: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create supply order act failed with status code: %d", httpResp.StatusCode())
	}

	return resp.OperationID, nil
}

// GetSupplyOrderAct 获取供应单交接单
func (s *FBOService) GetSupplyOrderAct(req *SupplyOrderActGetRequest) (*SupplyOrderActGetResponse, error) {
	resp := &SupplyOrderActGetResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderActGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order act: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order act failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CheckSupplyOrderActStatus 检查供应单交接单状态
func (s *FBOService) CheckSupplyOrderActStatus(req *SupplyOrderActCheckStatusRequest) (*SupplyOrderActCheckStatusResponse, error) {
	resp := &SupplyOrderActCheckStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderActCheckStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to check supply order act status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("check supply order act status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetSupplyOrderBundle 获取供应单组合信息
func (s *FBOService) GetSupplyOrderBundle(req *SupplyOrderBundleRequest) (*SupplyOrderBundleResponse, error) {
	resp := &SupplyOrderBundleResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(supplyOrderBundleEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get supply order bundle: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get supply order bundle failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}
