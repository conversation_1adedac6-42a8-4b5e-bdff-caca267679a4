package ozonapi

import (
	"fmt"

	"github.com/go-resty/resty/v2"
)

const (
	// Order API endpoints - 根据 OpenAPI 文档更新
	orderListEndpoint                    = "/v3/posting/fbs/list"
	orderUnfulfilledListEndpoint         = "/v3/posting/fbs/unfulfilled/list"
	orderGetEndpoint                     = "/v3/posting/fbs/get"
	orderCancelEndpoint                  = "/v2/posting/fbs/cancel"
	orderShipEndpoint                    = "/v2/posting/fbs/ship"
	orderActEndpoint                     = "/v2/posting/fbs/act/create"
	orderActListEndpoint                 = "/v2/posting/fbs/act/list"
	orderActGetEndpoint                  = "/v2/posting/fbs/act/get-pdf"
	orderActGetPostingsEndpoint          = "/v2/posting/fbs/act/get-postings"
	orderPackageLabelsEndpoint           = "/v2/posting/fbs/package-label"
	orderPackageLabelsCreateEndpoint     = "/v1/posting/fbs/package-label/create"
	orderPackageLabelsGetEndpoint        = "/v1/posting/fbs/package-label/get"
	orderArbitrageEndpoint               = "/v2/posting/fbs/arbitrage"
	orderDeliveryDateEndpoint            = "/v1/posting/fbs/timeslot/set"
	orderDeliveryRestrictionsEndpoint    = "/v1/posting/fbs/timeslot/change-restrictions"
	orderTrackingEndpoint                = "/v2/posting/tracking-number"
	orderFBOListEndpoint                 = "/v2/posting/fbo/list"
	orderFBOGetEndpoint                  = "/v2/posting/fbo/get"
	orderReturnEndpoint                  = "/v3/returns/company/fbo"
	orderReturnFBSEndpoint               = "/v3/returns/company/fbs"  // 更新到 v3 最新版本
	orderChatListEndpoint                = "/v1/chat/list"
	orderChatHistoryEndpoint             = "/v1/chat/history"
	orderChatSendEndpoint                = "/v1/chat/send/message"
	orderChatSendFileEndpoint            = "/v1/chat/send/file"
	orderCancellationListEndpoint        = "/v1/cancellation/list"
	orderCancellationReasonEndpoint      = "/v2/cancellation/reason/list"
	// 新增的端点
	orderMultiBoxQtyEndpoint             = "/v1/posting/multiboxqty"
	orderAwaitingDeliveryEndpoint        = "/v1/posting/fbs/awaiting-delivery"
	orderShippingLabelEndpoint           = "/v1/posting/fbs/digital/label"
	orderDigitalActEndpoint              = "/v1/posting/fbs/digital/act"
)

// OrderService 订单服务
type OrderService struct {
	client *resty.Client
}

// newOrderService 创建新的订单服务
func newOrderService(c *Client) *OrderService {
	return &OrderService{
		client: c.GetHTTPClient(),
	}
}

// GetOrderList 获取订单列表
func (s *OrderService) GetOrderList(req *OrderListRequest) (*OrderListResponse, error) {
	resp := &OrderListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get order list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get order list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetUnfulfilledOrderList 获取未履行订单列表
func (s *OrderService) GetUnfulfilledOrderList(req *OrderUnfulfilledListRequest) (*OrderUnfulfilledListResponse, error) {
	resp := &OrderUnfulfilledListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderUnfulfilledListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get unfulfilled order list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get unfulfilled order list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetOrder 获取订单详情
func (s *OrderService) GetOrder(postingNumber string, withAnalytics, withFinancial bool) (*Order, error) {
	req := map[string]interface{}{
		"posting_number": postingNumber,
		"with": map[string]bool{
			"analytics_data": withAnalytics,
			"financial_data": withFinancial,
		},
	}

	var resp struct {
		Result Order `json:"result"`
	}

	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(orderGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get order failed with status code: %d", httpResp.StatusCode())
	}

	return &resp.Result, nil
}

// CancelOrder 取消订单
func (s *OrderService) CancelOrder(req *OrderCancelRequest) (*OrderCancelResponse, error) {
	resp := &OrderCancelResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderCancelEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to cancel order: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("cancel order failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// ShipOrder 发货
func (s *OrderService) ShipOrder(req *OrderShipRequest) (*OrderShipResponse, error) {
	resp := &OrderShipResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderShipEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to ship order: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("ship order failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateAct 创建交接单
func (s *OrderService) CreateAct(postingNumbers []string) (string, error) {
	req := map[string]interface{}{
		"containers": []map[string]interface{}{
			{
				"posting_numbers": postingNumbers,
			},
		},
	}

	var resp struct {
		Result struct {
			ID int64 `json:"id"`
		} `json:"result"`
	}

	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(orderActEndpoint)

	if err != nil {
		return "", fmt.Errorf("failed to create act: %w", err)
	}

	if !httpResp.IsSuccess() {
		return "", fmt.Errorf("create act failed with status code: %d", httpResp.StatusCode())
	}

	return fmt.Sprintf("%d", resp.Result.ID), nil
}

// GetActPDF 获取交接单PDF
func (s *OrderService) GetActPDF(actID string) ([]byte, error) {
	req := map[string]interface{}{
		"id": actID,
	}

	httpResp, err := s.client.R().
		SetBody(req).
		Post(orderActGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get act PDF: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get act PDF failed with status code: %d", httpResp.StatusCode())
	}

	return httpResp.Body(), nil
}

// GetPackageLabels 获取包裹标签
func (s *OrderService) GetPackageLabels(postingNumbers []string) ([]byte, error) {
	req := map[string]interface{}{
		"posting_number": postingNumbers,
	}

	httpResp, err := s.client.R().
		SetBody(req).
		Post(orderPackageLabelsEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get package labels: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get package labels failed with status code: %d", httpResp.StatusCode())
	}

	return httpResp.Body(), nil
}

// CreateArbitrage 创建仲裁
func (s *OrderService) CreateArbitrage(postingNumber string) error {
	req := map[string]interface{}{
		"posting_number": postingNumber,
	}

	httpResp, err := s.client.R().
		SetBody(req).
		Post(orderArbitrageEndpoint)

	if err != nil {
		return fmt.Errorf("failed to create arbitrage: %w", err)
	}

	if !httpResp.IsSuccess() {
		return fmt.Errorf("create arbitrage failed with status code: %d", httpResp.StatusCode())
	}

	return nil
}

// SetDeliveryDate 设置配送日期
func (s *OrderService) SetDeliveryDate(postingNumber string, timeslotID int64) error {
	req := map[string]interface{}{
		"posting_number": postingNumber,
		"timeslot_id":    timeslotID,
	}

	httpResp, err := s.client.R().
		SetBody(req).
		Post(orderDeliveryDateEndpoint)

	if err != nil {
		return fmt.Errorf("failed to set delivery date: %w", err)
	}

	if !httpResp.IsSuccess() {
		return fmt.Errorf("set delivery date failed with status code: %d", httpResp.StatusCode())
	}

	return nil
}

// SetTrackingNumber 设置跟踪号
func (s *OrderService) SetTrackingNumber(postingNumber, trackingNumber string) error {
	req := map[string]interface{}{
		"posting_number":  postingNumber,
		"tracking_number": trackingNumber,
	}

	httpResp, err := s.client.R().
		SetBody(req).
		Post(orderTrackingEndpoint)

	if err != nil {
		return fmt.Errorf("failed to set tracking number: %w", err)
	}

	if !httpResp.IsSuccess() {
		return fmt.Errorf("set tracking number failed with status code: %d", httpResp.StatusCode())
	}

	return nil
}

// GetOrdersByStatus 根据状态获取订单
func (s *OrderService) GetOrdersByStatus(status string, limit int32, offset int32) (*OrderListResponse, error) {
	req := &OrderListRequest{
		Dir: "DESC",
		Filter: OrderFilter{
			Status: status,
		},
		Limit:  limit,
		Offset: offset,
		With: OrderWith{
			AnalyticsData: true,
			FinancialData: true,
		},
	}

	return s.GetOrderList(req)
}

// GetAwaitingPackagingOrders 获取待打包订单
func (s *OrderService) GetAwaitingPackagingOrders(limit int32, offset int32) (*OrderListResponse, error) {
	return s.GetOrdersByStatus("awaiting_packaging", limit, offset)
}

// GetAwaitingDeliverOrders 获取待发货订单
func (s *OrderService) GetAwaitingDeliverOrders(limit int32, offset int32) (*OrderListResponse, error) {
	return s.GetOrdersByStatus("awaiting_deliver", limit, offset)
}

// GetDeliveredOrders 获取已配送订单
func (s *OrderService) GetDeliveredOrders(limit int32, offset int32) (*OrderListResponse, error) {
	return s.GetOrdersByStatus("delivered", limit, offset)
}

// GetCancelledOrders 获取已取消订单
func (s *OrderService) GetCancelledOrders(limit int32, offset int32) (*OrderListResponse, error) {
	return s.GetOrdersByStatus("cancelled", limit, offset)
}

// GetFBOOrderList 获取FBO订单列表
func (s *OrderService) GetFBOOrderList(req *OrderListRequest) (*OrderListResponse, error) {
	resp := &OrderListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderFBOListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get FBO order list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get FBO order list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetFBOOrder 获取FBO订单详情
func (s *OrderService) GetFBOOrder(postingNumber string, withAnalytics, withFinancial bool) (*Order, error) {
	req := map[string]interface{}{
		"posting_number": postingNumber,
		"with": map[string]bool{
			"analytics_data": withAnalytics,
			"financial_data": withFinancial,
		},
	}

	var resp struct {
		Result Order `json:"result"`
	}

	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(&resp).
		Post(orderFBOGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get FBO order: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get FBO order failed with status code: %d", httpResp.StatusCode())
	}

	return &resp.Result, nil
}

// GetReturns 获取退货信息
func (s *OrderService) GetReturns(req *ReturnRequest) (*ReturnResponse, error) {
	resp := &ReturnResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderReturnEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get returns: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get returns failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetFBSReturns 获取FBS退货信息
func (s *OrderService) GetFBSReturns(req *ReturnRequest) (*ReturnResponse, error) {
	resp := &ReturnResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderReturnFBSEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get FBS returns: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get FBS returns failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetChatList 获取聊天列表
func (s *OrderService) GetChatList(req *ChatListRequest) (*ChatListResponse, error) {
	resp := &ChatListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderChatListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get chat list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get chat list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetChatHistory 获取聊天历史
func (s *OrderService) GetChatHistory(req *ChatHistoryRequest) (*ChatHistoryResponse, error) {
	resp := &ChatHistoryResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderChatHistoryEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get chat history: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get chat history failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// SendChatMessage 发送聊天消息
func (s *OrderService) SendChatMessage(req *ChatSendMessageRequest) (*ChatSendMessageResponse, error) {
	resp := &ChatSendMessageResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderChatSendEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to send chat message: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("send chat message failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetCancellationList 获取取消申请列表
func (s *OrderService) GetCancellationList(req *CancellationListRequest) (*CancellationListResponse, error) {
	resp := &CancellationListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderCancellationListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cancellation list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cancellation list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetCancellationReasons 获取取消原因列表
func (s *OrderService) GetCancellationReasons() (*CancellationReasonResponse, error) {
	resp := &CancellationReasonResponse{}
	httpResp, err := s.client.R().
		Post(orderCancellationReasonEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cancellation reasons: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cancellation reasons failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetActList 获取交接单列表
func (s *OrderService) GetActList(req *ActListRequest) (*ActListResponse, error) {
	resp := &ActListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderActListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get act list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get act list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetActPostings 获取交接单中的订单
func (s *OrderService) GetActPostings(req *ActPostingsRequest) (*ActPostingsResponse, error) {
	resp := &ActPostingsResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderActGetPostingsEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get act postings: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get act postings failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreatePackageLabels 创建包裹标签任务
func (s *OrderService) CreatePackageLabels(req *PackageLabelsCreateRequest) (*PackageLabelsCreateResponse, error) {
	resp := &PackageLabelsCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderPackageLabelsCreateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to create package labels: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("create package labels failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetPackageLabelsResult 获取包裹标签结果
func (s *OrderService) GetPackageLabelsResult(req *PackageLabelsGetRequest) (*PackageLabelsGetResponse, error) {
	resp := &PackageLabelsGetResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderPackageLabelsGetEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get package labels result: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get package labels result failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetDeliveryRestrictions 获取配送日期变更限制
func (s *OrderService) GetDeliveryRestrictions(req *DeliveryRestrictionsRequest) (*DeliveryRestrictionsResponse, error) {
	resp := &DeliveryRestrictionsResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderDeliveryRestrictionsEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get delivery restrictions: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get delivery restrictions failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetMultiBoxQty 获取多箱订单信息
func (s *OrderService) GetMultiBoxQty(req *MultiBoxQtyRequest) (*MultiBoxQtyResponse, error) {
	resp := &MultiBoxQtyResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderMultiBoxQtyEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get multi box qty: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get multi box qty failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetAwaitingDeliveryOrders 获取待配送订单
func (s *OrderService) GetAwaitingDeliveryOrders(req *AwaitingDeliveryRequest) (*AwaitingDeliveryResponse, error) {
	resp := &AwaitingDeliveryResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderAwaitingDeliveryEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get awaiting delivery orders: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get awaiting delivery orders failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetShippingLabel 获取数字化配送标签
func (s *OrderService) GetShippingLabel(req *ShippingLabelRequest) (*ShippingLabelResponse, error) {
	resp := &ShippingLabelResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderShippingLabelEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get shipping label: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get shipping label failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetDigitalAct 获取数字化交接单
func (s *OrderService) GetDigitalAct(req *DigitalActRequest) (*DigitalActResponse, error) {
	resp := &DigitalActResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(orderDigitalActEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get digital act: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get digital act failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}
