package ozonapi

import (
	"fmt"
)

const (
	// Report API endpoints - 使用不同的常量名避免与 analytics_service.go 冲突
	reportInfoV1Endpoint         = "/v1/report/info"
	reportListV1Endpoint         = "/v1/report/list"
	reportProductsCreateEndpoint = "/v1/report/products/create"
	reportReturnsCreateEndpoint  = "/v2/report/returns/create"
	reportPostingsCreateEndpoint = "/v1/report/postings/create"
	reportDiscountedCreateEndpoint = "/v1/report/discounted/create"
	reportWarehouseStockEndpoint = "/v1/report/warehouse/stock"
	financeCashFlowStatementV1Endpoint = "/v1/finance/cash-flow-statement/list"
)

// ReportService 报告服务
type ReportService struct {
	client *Client
}

// NewReportService 创建报告服务实例
func NewReportService(client *Client) *ReportService {
	return &ReportService{client: client}
}

// GetReportInfo 获取报告信息
func (s *ReportService) GetReportInfo(req *ReportServiceInfoRequest) (*ReportServiceInfoResponse, error) {
	resp := &ReportServiceInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportInfoV1Endpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get report info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get report info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetReportList 获取报告列表
func (s *ReportService) GetReportList(req *ReportServiceListRequest) (*ReportServiceListResponse, error) {
	resp := &ReportServiceListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportListV1Endpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get report list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get report list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateProductsReport 创建商品报告
func (s *ReportService) CreateProductsReport(req *ReportServiceProductsCreateRequest) (*ReportProductsCreateResponse, error) {
	resp := &ReportProductsCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportProductsCreateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to create products report: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("create products report failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateReturnsReport 创建退货报告
func (s *ReportService) CreateReturnsReport(req *ReportServiceReturnsCreateRequest) (*ReportReturnsCreateResponse, error) {
	resp := &ReportReturnsCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportReturnsCreateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to create returns report: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("create returns report failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreatePostingsReport 创建订单报告
func (s *ReportService) CreatePostingsReport(req *ReportPostingsCreateRequest) (*ReportPostingsCreateResponse, error) {
	resp := &ReportPostingsCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportPostingsCreateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to create postings report: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("create postings report failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateDiscountedReport 创建折扣报告
func (s *ReportService) CreateDiscountedReport(req *ReportDiscountedCreateRequest) (*ReportDiscountedCreateResponse, error) {
	resp := &ReportDiscountedCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportDiscountedCreateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to create discounted report: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("create discounted report failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetWarehouseStockReport 获取仓库库存报告
func (s *ReportService) GetWarehouseStockReport(req *ReportWarehouseStockRequest) (*ReportWarehouseStockResponse, error) {
	resp := &ReportWarehouseStockResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportWarehouseStockEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get warehouse stock report: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get warehouse stock report failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetCashFlowStatement 获取现金流报告
func (s *ReportService) GetCashFlowStatement(req *FinanceCashFlowStatementRequest) (*FinanceCashFlowStatementResponse, error) {
	resp := &FinanceCashFlowStatementResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(financeCashFlowStatementV1Endpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cash flow statement: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cash flow statement failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}
