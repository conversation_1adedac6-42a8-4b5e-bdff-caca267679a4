package ozonapi

import (
	"time"
)

// ReportServiceInfoRequest 获取报告信息请求 (报告服务专用)
type ReportServiceInfoRequest struct {
	ReportID string `json:"report_id"`
}

// ReportServiceInfoResponse 获取报告信息响应 (报告服务专用)
type ReportServiceInfoResponse struct {
	Result ReportServiceInfo `json:"result"`
}

// ReportServiceInfo 报告信息 (报告服务专用)
type ReportServiceInfo struct {
	ReportID    string    `json:"report_id"`
	Status      string    `json:"status"`
	Type        string    `json:"type"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	CompletedAt time.Time `json:"completed_at,omitempty"`
	FileURL     string    `json:"file_url,omitempty"`
	FileName    string    `json:"file_name,omitempty"`
	FileSize    int64     `json:"file_size,omitempty"`
	Error       string    `json:"error,omitempty"`
}

// ReportServiceListRequest 获取报告列表请求 (报告服务专用)
type ReportServiceListRequest struct {
	Filter ReportServiceListFilter `json:"filter,omitempty"`
	Limit  int32                   `json:"limit,omitempty"`
	Offset int32                   `json:"offset,omitempty"`
}

// ReportServiceListFilter 报告列表过滤器 (报告服务专用)
type ReportServiceListFilter struct {
	Type      []string                `json:"type,omitempty"`
	Status    []string                `json:"status,omitempty"`
	CreatedAt ReportServiceDateRange  `json:"created_at,omitempty"`
}

// ReportServiceDateRange 报告日期范围 (报告服务专用)
type ReportServiceDateRange struct {
	From time.Time `json:"from,omitempty"`
	To   time.Time `json:"to,omitempty"`
}

// ReportServiceListResponse 获取报告列表响应 (报告服务专用)
type ReportServiceListResponse struct {
	Result struct {
		Items []ReportServiceListItem `json:"items"`
		Total int32                   `json:"total"`
	} `json:"result"`
}

// ReportServiceListItem 报告列表项 (报告服务专用)
type ReportServiceListItem struct {
	ReportID    string    `json:"report_id"`
	Type        string    `json:"type"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	CompletedAt time.Time `json:"completed_at,omitempty"`
	FileURL     string    `json:"file_url,omitempty"`
	FileName    string    `json:"file_name,omitempty"`
}

// ReportServiceProductsCreateRequest 创建商品报告请求 (报告服务专用)
type ReportServiceProductsCreateRequest struct {
	Filter   ReportServiceProductsFilter `json:"filter,omitempty"`
	Language string                      `json:"language,omitempty"`
}

// ReportServiceProductsFilter 商品报告过滤器 (报告服务专用)
type ReportServiceProductsFilter struct {
	OfferID    []string `json:"offer_id,omitempty"`
	ProductID  []int64  `json:"product_id,omitempty"`
	Visibility string   `json:"visibility,omitempty"`
}

// ReportProductsCreateResponse 创建商品报告响应
type ReportProductsCreateResponse struct {
	Result struct {
		ReportID string `json:"report_id"`
	} `json:"result"`
}

// ReportServiceReturnsCreateRequest 创建退货报告请求 (报告服务专用)
type ReportServiceReturnsCreateRequest struct {
	Filter   ReportServiceReturnsFilter `json:"filter"`
	Language string                     `json:"language,omitempty"`
}

// ReportServiceReturnsFilter 退货报告过滤器 (报告服务专用)
type ReportServiceReturnsFilter struct {
	Date      ReportServiceDateRange `json:"date"`
	Status    []string               `json:"status,omitempty"`
	Type      []string               `json:"type,omitempty"`
	ProductID []int64                `json:"product_id,omitempty"`
}

// ReportReturnsCreateResponse 创建退货报告响应
type ReportReturnsCreateResponse struct {
	Result struct {
		ReportID string `json:"report_id"`
	} `json:"result"`
}

// ReportPostingsCreateRequest 创建订单报告请求
type ReportPostingsCreateRequest struct {
	Filter   ReportPostingsFilter `json:"filter"`
	Language string               `json:"language,omitempty"`
}

// ReportPostingsFilter 订单报告过滤器
type ReportPostingsFilter struct {
	Date           ReportServiceDateRange `json:"date"`
	Status         []string               `json:"status,omitempty"`
	DeliverySchema []string               `json:"delivery_schema,omitempty"`
	WarehouseID    []int64                `json:"warehouse_id,omitempty"`
}

// ReportPostingsCreateResponse 创建订单报告响应
type ReportPostingsCreateResponse struct {
	Result struct {
		ReportID string `json:"report_id"`
	} `json:"result"`
}

// ReportDiscountedCreateRequest 创建折扣报告请求
type ReportDiscountedCreateRequest struct {
	Filter   ReportDiscountedFilter `json:"filter,omitempty"`
	Language string                 `json:"language,omitempty"`
}

// ReportDiscountedFilter 折扣报告过滤器
type ReportDiscountedFilter struct {
	OfferID   []string `json:"offer_id,omitempty"`
	ProductID []int64  `json:"product_id,omitempty"`
}

// ReportDiscountedCreateResponse 创建折扣报告响应
type ReportDiscountedCreateResponse struct {
	Result struct {
		ReportID string `json:"report_id"`
	} `json:"result"`
}

// ReportWarehouseStockRequest 获取仓库库存报告请求
type ReportWarehouseStockRequest struct {
	Filter   ReportWarehouseStockFilter `json:"filter,omitempty"`
	Language string                     `json:"language,omitempty"`
}

// ReportWarehouseStockFilter 仓库库存报告过滤器
type ReportWarehouseStockFilter struct {
	OfferID     []string `json:"offer_id,omitempty"`
	ProductID   []int64  `json:"product_id,omitempty"`
	WarehouseID []int64  `json:"warehouse_id,omitempty"`
}

// ReportWarehouseStockResponse 获取仓库库存报告响应
type ReportWarehouseStockResponse struct {
	Result struct {
		ReportID string `json:"report_id"`
	} `json:"result"`
}

// FinanceCashFlowStatementRequest 获取现金流报告请求
type FinanceCashFlowStatementRequest struct {
	Filter FinanceCashFlowFilter `json:"filter"`
	Limit  int32                 `json:"limit,omitempty"`
	Offset int32                 `json:"offset,omitempty"`
}

// FinanceCashFlowFilter 现金流过滤器
type FinanceCashFlowFilter struct {
	Date         ReportServiceDateRange `json:"date"`
	OperationType []string              `json:"operation_type,omitempty"`
}

// FinanceCashFlowStatementResponse 获取现金流报告响应
type FinanceCashFlowStatementResponse struct {
	Result struct {
		Items []FinanceCashFlowItem `json:"items"`
		Total int32                 `json:"total"`
	} `json:"result"`
}

// FinanceCashFlowItem 现金流项目
type FinanceCashFlowItem struct {
	Date          time.Time `json:"date"`
	OperationType string    `json:"operation_type"`
	Amount        string    `json:"amount"`
	Currency      string    `json:"currency"`
	Description   string    `json:"description"`
	PostingNumber string    `json:"posting_number,omitempty"`
	ProductID     int64     `json:"product_id,omitempty"`
	OfferID       string    `json:"offer_id,omitempty"`
}
