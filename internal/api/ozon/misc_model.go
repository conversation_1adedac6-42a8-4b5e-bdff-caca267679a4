package ozonapi

// BarcodeAddRequest 添加条码请求
type BarcodeAddRequest struct {
	Barcodes []BarcodeItem `json:"barcodes"`
}

// BarcodeItem 条码项
type BarcodeItem struct {
	OfferID string `json:"offer_id"`
	Barcode string `json:"barcode"`
}

// BarcodeAddResponse 添加条码响应
type BarcodeAddResponse struct {
	Result struct {
		AddedCount int32                `json:"added_count"`
		Errors     []BarcodeAddError    `json:"errors,omitempty"`
	} `json:"result"`
}

// BarcodeAddError 添加条码错误
type BarcodeAddError struct {
	OfferID string `json:"offer_id"`
	Barcode string `json:"barcode"`
	Error   string `json:"error"`
}

// BarcodeGenerateRequest 生成条码请求
type BarcodeGenerateRequest struct {
	OfferIDs []string `json:"offer_ids"`
}

// BarcodeGenerateResponse 生成条码响应
type BarcodeGenerateResponse struct {
	Result struct {
		Barcodes []GeneratedBarcode `json:"barcodes"`
		Errors   []BarcodeGenerateError `json:"errors,omitempty"`
	} `json:"result"`
}

// GeneratedBarcode 生成的条码
type GeneratedBarcode struct {
	OfferID string `json:"offer_id"`
	Barcode string `json:"barcode"`
}

// BarcodeGenerateError 生成条码错误
type BarcodeGenerateError struct {
	OfferID string `json:"offer_id"`
	Error   string `json:"error"`
}

// SupplierAvailableWarehousesRequest 获取供应商可用仓库请求
type SupplierAvailableWarehousesRequest struct {
	Filter SupplierWarehouseFilter `json:"filter,omitempty"`
}

// SupplierWarehouseFilter 供应商仓库过滤器
type SupplierWarehouseFilter struct {
	WarehouseType []string `json:"warehouse_type,omitempty"`
	Region        []string `json:"region,omitempty"`
}

// SupplierAvailableWarehousesResponse 获取供应商可用仓库响应
type SupplierAvailableWarehousesResponse struct {
	Result []SupplierWarehouse `json:"result"`
}

// SupplierWarehouse 供应商仓库
type SupplierWarehouse struct {
	WarehouseID   int64  `json:"warehouse_id"`
	Name          string `json:"name"`
	Type          string `json:"type"`
	Region        string `json:"region"`
	City          string `json:"city"`
	Address       string `json:"address"`
	IsActive      bool   `json:"is_active"`
	AcceptedTypes []string `json:"accepted_types"`
	WorkingHours  WarehouseWorkingHours `json:"working_hours"`
}

// WarehouseWorkingHours 仓库工作时间
type WarehouseWorkingHours struct {
	Monday    string `json:"monday,omitempty"`
	Tuesday   string `json:"tuesday,omitempty"`
	Wednesday string `json:"wednesday,omitempty"`
	Thursday  string `json:"thursday,omitempty"`
	Friday    string `json:"friday,omitempty"`
	Saturday  string `json:"saturday,omitempty"`
	Sunday    string `json:"sunday,omitempty"`
}
