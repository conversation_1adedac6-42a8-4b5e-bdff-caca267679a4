package ozonapi

import (
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	// Analytics API endpoints
	analyticsDataEndpoint              = "/v1/analytics/data"
	stockOnWarehousesEndpoint          = "/v2/analytics/stock_on_warehouses"  // 更新到 v2 最新版本
	itemTurnoverEndpoint               = "/v1/analytics/item_turnover"
	itemForecastEndpoint               = "/v1/analytics/item_forecast"
	reportInfoEndpoint                 = "/v1/report/info"
	reportCreateEndpoint               = "/v1/report/create"
	reportStatusEndpoint               = "/v1/report/status"
	reportListEndpoint                 = "/v1/report/list"
	financeRealizationEndpoint         = "/v3/finance/realization"
	financeTransactionListEndpoint     = "/v3/finance/transaction/list"
	financeTransactionTotalsEndpoint   = "/v3/finance/transaction/totals"
	financeReportEndpoint              = "/v1/finance/cash-flow-statement/list"
)

// AnalyticsService 分析服务
type AnalyticsService struct {
	client *resty.Client
}

// newAnalyticsService 创建新的分析服务
func newAnalyticsService(c *Client) *AnalyticsService {
	return &AnalyticsService{
		client: c.GetHTTPClient(),
	}
}

// GetAnalyticsData 获取分析数据
func (s *AnalyticsService) GetAnalyticsData(req *AnalyticsDataRequest) (*AnalyticsDataResponse, error) {
	resp := &AnalyticsDataResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(analyticsDataEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get analytics data: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get analytics data failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetStockOnWarehouses 获取仓库库存
func (s *AnalyticsService) GetStockOnWarehouses(req *StockOnWarehousesRequest) (*StockOnWarehousesResponse, error) {
	resp := &StockOnWarehousesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(stockOnWarehousesEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get stock on warehouses: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get stock on warehouses failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetItemTurnover 获取商品周转率
func (s *AnalyticsService) GetItemTurnover(req *ItemTurnoverRequest) (*ItemTurnoverResponse, error) {
	resp := &ItemTurnoverResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(itemTurnoverEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get item turnover: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get item turnover failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetItemForecast 获取商品需求预测
func (s *AnalyticsService) GetItemForecast(req *ItemForecastRequest) (*ItemForecastResponse, error) {
	resp := &ItemForecastResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(itemForecastEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get item forecast: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get item forecast failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetReportInfo 获取报告信息
func (s *AnalyticsService) GetReportInfo(req *ReportInfoRequest) (*ReportInfoResponse, error) {
	resp := &ReportInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get report info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get report info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateReport 创建报告
func (s *AnalyticsService) CreateReport(req *ReportCreateRequest) (*ReportCreateResponse, error) {
	resp := &ReportCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportCreateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to create report: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("create report failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetReportStatus 获取报告状态
func (s *AnalyticsService) GetReportStatus(req *ReportStatusRequest) (*ReportStatusResponse, error) {
	resp := &ReportStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get report status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get report status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetReportList 获取报告列表
func (s *AnalyticsService) GetReportList(req *ReportListRequest) (*ReportListResponse, error) {
	resp := &ReportListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(reportListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get report list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get report list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetFinanceRealization 获取财务实现报告
func (s *AnalyticsService) GetFinanceRealization(req *FinanceRealizationRequest) (*FinanceRealizationResponse, error) {
	resp := &FinanceRealizationResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(financeRealizationEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get finance realization: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get finance realization failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetFinanceTransactionList 获取财务交易列表
func (s *AnalyticsService) GetFinanceTransactionList(req *FinanceTransactionListRequest) (*FinanceTransactionListResponse, error) {
	resp := &FinanceTransactionListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(financeTransactionListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get finance transaction list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get finance transaction list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetFinanceTransactionTotals 获取财务交易汇总
func (s *AnalyticsService) GetFinanceTransactionTotals(req *FinanceTransactionListRequest) (*FinanceTransactionListResponse, error) {
	resp := &FinanceTransactionListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(financeTransactionTotalsEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get finance transaction totals: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get finance transaction totals failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetFinanceReport 获取财务报告
func (s *AnalyticsService) GetFinanceReport(dateFrom, dateTo string) ([]byte, error) {
	req := map[string]interface{}{
		"date_from": dateFrom,
		"date_to":   dateTo,
	}

	httpResp, err := s.client.R().
		SetBody(req).
		Post(financeReportEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get finance report: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get finance report failed with status code: %d", httpResp.StatusCode())
	}

	return httpResp.Body(), nil
}

// GetSalesAnalytics 获取销售分析数据
func (s *AnalyticsService) GetSalesAnalytics(dateFrom, dateTo string, metrics []string) (*AnalyticsDataResponse, error) {
	req := &AnalyticsDataRequest{
		Metrics: metrics,
		Dimension: []string{"sku", "day"},
	}

	// 解析日期
	if dateFrom != "" {
		if parsedDate, err := parseDate(dateFrom); err == nil {
			req.DateFrom = parsedDate
		}
	}
	if dateTo != "" {
		if parsedDate, err := parseDate(dateTo); err == nil {
			req.DateTo = parsedDate
		}
	}

	return s.GetAnalyticsData(req)
}

// GetProductAnalytics 获取商品分析数据
func (s *AnalyticsService) GetProductAnalytics(productIDs []int64, metrics []string) (*AnalyticsDataResponse, error) {
	req := &AnalyticsDataRequest{
		Metrics: metrics,
		Dimension: []string{"sku"},
		Filters: []AnalyticsFilter{
			{
				Key:   "sku",
				Op:    "IN",
				Value: productIDs,
			},
		},
	}

	return s.GetAnalyticsData(req)
}

// GetWarehouseAnalytics 获取仓库分析数据
func (s *AnalyticsService) GetWarehouseAnalytics(warehouseIDs []int64) (*StockOnWarehousesResponse, error) {
	req := &StockOnWarehousesRequest{
		WarehouseType: "ALL",
		Limit:         1000,
	}

	return s.GetStockOnWarehouses(req)
}

// Helper function to parse date string
func parseDate(dateStr string) (time.Time, error) {
	// Try different date formats
	formats := []string{
		"2006-01-02",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02 15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("unable to parse date: %s", dateStr)
}
