package ozonapi

import (
	"fmt"

	"github.com/go-resty/resty/v2"
)

const (
	// Category API endpoints
	categoryTreeEndpoint              = "/v1/description-category/tree"
	categoryAttributeEndpoint         = "/v1/description-category/attribute"
	attributeValuesEndpoint           = "/v1/description-category/attribute/values"
	searchAttributeValuesEndpoint     = "/v1/description-category/attribute/values/search"
	brandEndpoint                     = "/v1/brand/list"
	brandCompanyEndpoint              = "/v1/brand/company-certificate/list"
	brandCertificationEndpoint        = "/v1/brand/brand-certificate/list"
	warehouseEndpoint                 = "/v1/warehouse/list"
	deliveryMethodEndpoint            = "/v1/delivery-method/list"
	countryEndpoint                   = "/v1/country/list"
	currencyEndpoint                  = "/v1/currency/list"
)

// CategoryService 分类服务
type CategoryService struct {
	client *resty.Client
}

// newCategoryService 创建新的分类服务
func newCategoryService(c *Client) *CategoryService {
	return &CategoryService{
		client: c.GetHTTPClient(),
	}
}

// GetCategoryTree 获取分类树
func (s *CategoryService) GetCategoryTree(req *CategoryTreeRequest) (*CategoryTreeResponse, error) {
	resp := &CategoryTreeResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(categoryTreeEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get category tree: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get category tree failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetCategoryAttributes 获取分类属性
func (s *CategoryService) GetCategoryAttributes(req *CategoryAttributeRequest) (*CategoryAttributeResponse, error) {
	resp := &CategoryAttributeResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(categoryAttributeEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get category attributes: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get category attributes failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetAttributeValues 获取属性值
func (s *CategoryService) GetAttributeValues(req *AttributeValuesRequest) (*AttributeValuesResponse, error) {
	resp := &AttributeValuesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(attributeValuesEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get attribute values: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get attribute values failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// SearchAttributeValues 搜索属性值
func (s *CategoryService) SearchAttributeValues(req *SearchAttributeValuesRequest) (*SearchAttributeValuesResponse, error) {
	resp := &SearchAttributeValuesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(searchAttributeValuesEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to search attribute values: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("search attribute values failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetBrands 获取品牌列表
func (s *CategoryService) GetBrands(req *BrandRequest) (*BrandResponse, error) {
	resp := &BrandResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(brandEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get brands: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get brands failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetBrandCompanies 获取品牌公司
func (s *CategoryService) GetBrandCompanies(req *BrandCompanyRequest) (*BrandCompanyResponse, error) {
	resp := &BrandCompanyResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(brandCompanyEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get brand companies: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get brand companies failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetBrandCertifications 获取品牌认证
func (s *CategoryService) GetBrandCertifications(req *BrandCertificationRequest) (*BrandCertificationResponse, error) {
	resp := &BrandCertificationResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(brandCertificationEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get brand certifications: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get brand certifications failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetWarehouses 获取仓库列表
func (s *CategoryService) GetWarehouses(req *WarehouseRequest) (*WarehouseResponse, error) {
	resp := &WarehouseResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(warehouseEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get warehouses: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get warehouses failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetDeliveryMethods 获取配送方式
func (s *CategoryService) GetDeliveryMethods(req *DeliveryMethodRequest) (*DeliveryMethodResponse, error) {
	resp := &DeliveryMethodResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(deliveryMethodEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get delivery methods: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get delivery methods failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetCountries 获取国家列表
func (s *CategoryService) GetCountries(req *CountryRequest) (*CountryResponse, error) {
	resp := &CountryResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(countryEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get countries: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get countries failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetCurrencies 获取货币列表
func (s *CategoryService) GetCurrencies(req *CurrencyRequest) (*CurrencyResponse, error) {
	resp := &CurrencyResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(currencyEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get currencies: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get currencies failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetAllCategories 获取所有分类
func (s *CategoryService) GetAllCategories() (*CategoryTreeResponse, error) {
	req := &CategoryTreeRequest{
		Language: "RU",
	}
	return s.GetCategoryTree(req)
}

// GetCategoryByID 根据ID获取分类
func (s *CategoryService) GetCategoryByID(categoryID int64) (*CategoryTreeResponse, error) {
	req := &CategoryTreeRequest{
		CategoryID: categoryID,
		Language:   "RU",
	}
	return s.GetCategoryTree(req)
}

// GetAttributesByCategory 根据分类获取属性
func (s *CategoryService) GetAttributesByCategory(categoryID, typeID int64) (*CategoryAttributeResponse, error) {
	req := &CategoryAttributeRequest{
		CategoryID: categoryID,
		TypeID:     typeID,
		Language:   "RU",
	}
	return s.GetCategoryAttributes(req)
}

// SearchBrands 搜索品牌
func (s *CategoryService) SearchBrands(brandIDs []int64) (*BrandResponse, error) {
	req := &BrandRequest{
		BrandID:  brandIDs,
		Language: "RU",
	}
	return s.GetBrands(req)
}

// GetAllWarehouses 获取所有仓库
func (s *CategoryService) GetAllWarehouses() (*WarehouseResponse, error) {
	req := &WarehouseRequest{}
	return s.GetWarehouses(req)
}

// GetWarehousesByIDs 根据ID获取仓库
func (s *CategoryService) GetWarehousesByIDs(warehouseIDs []int64) (*WarehouseResponse, error) {
	req := &WarehouseRequest{
		WarehouseID: warehouseIDs,
	}
	return s.GetWarehouses(req)
}

// GetAllDeliveryMethods 获取所有配送方式
func (s *CategoryService) GetAllDeliveryMethods() (*DeliveryMethodResponse, error) {
	req := &DeliveryMethodRequest{
		Limit: 1000,
	}
	return s.GetDeliveryMethods(req)
}

// SearchCountries 搜索国家
func (s *CategoryService) SearchCountries(nameSearch string) (*CountryResponse, error) {
	req := &CountryRequest{
		NameSearch: nameSearch,
	}
	return s.GetCountries(req)
}
