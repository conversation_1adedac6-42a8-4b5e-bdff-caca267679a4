package ozonapi

import (
	"fmt"
)

const (
	// Rating API endpoints
	ratingSummaryEndpoint = "/v1/rating/summary"
	ratingHistoryEndpoint = "/v1/rating/history"
)

// RatingService 评级服务
type RatingService struct {
	client *Client
}

// NewRatingService 创建评级服务实例
func NewRatingService(client *Client) *RatingService {
	return &RatingService{client: client}
}

// GetRatingSummary 获取评级摘要
func (s *RatingService) GetRatingSummary(req *RatingSummaryRequest) (*RatingSummaryResponse, error) {
	resp := &RatingSummaryResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(ratingSummaryEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get rating summary: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get rating summary failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetRatingHistory 获取评级历史
func (s *RatingService) GetRatingHistory(req *RatingHistoryRequest) (*RatingHistoryResponse, error) {
	resp := &RatingHistoryResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(ratingHistoryEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get rating history: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get rating history failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}
