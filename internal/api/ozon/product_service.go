package ozonapi

import (
	"fmt"

	"github.com/go-resty/resty/v2"
)

const (
	// Product API endpoints
	productListEndpoint             = "/v3/product/list"
	productInfoEndpoint             = "/v3/product/info/list" // 更新到 v2 最新版本
	productCreateEndpoint           = "/v3/product/import"
	productImportInfoEndpoint       = "/v1/product/import/info"
	productUpdateEndpoint           = "/v1/product/attributes/update"
	productStocksEndpoint           = "/v2/products/stocks" // 更新到 v2 最新版本
	productPricesEndpoint           = "/v1/product/import/prices"
	productArchiveEndpoint          = "/v1/product/archive"
	productUnarchiveEndpoint        = "/v1/product/unarchive"
	productDeleteEndpoint           = "/v1/product/delete"
	productPicturesEndpoint         = "/v1/product/pictures/import"
	productRatingEndpoint           = "/v1/product/rating-by-sku"
	productAttributesEndpoint       = "/v4/product/info/attributes"
	productImportBySKUEndpoint      = "/v1/product/import-by-sku"
	productCertificateEndpoint      = "/v2/product/certificate"
	productCertificateTypesEndpoint = "/v2/product/certificate/types"
	productGeoRestrictionsEndpoint  = "/v1/product/geo-restrictions-catalog-by-filter"
	productSubscriptionEndpoint     = "/v1/product/info/subscription"
	productDiscountedEndpoint       = "/v1/product/info/discounted"
	productLimitEndpoint            = "/v4/product/info/limit" // 更新到 v4 最新版本
	productRelatedEndpoint          = "/v1/product/related-sku/get"
	// 根据 OpenAPI 文档新增的端点
	productInfoListV1Endpoint        = "/v1/product/info/list"
	productPicturesInfoEndpoint      = "/v2/product/pictures/info"
	productUpdateOfferIDEndpoint     = "/v1/product/update/offer-id"
	productPricesInfoV4Endpoint      = "/v4/product/info/prices"
	productPricesInfoV5Endpoint      = "/v5/product/info/prices"
	productStocksInfoV4Endpoint      = "/v4/product/info/stocks"
	productStocksByWarehouseEndpoint = "/v1/product/info/stocks-by-warehouse/fbs"
	productDiscountUpdateEndpoint    = "/v1/product/update/discount"
	// Certificate endpoints 根据 OpenAPI 文档
	productCertificateListEndpoint   = "/v1/product/certificate/list"
	productCertificateInfoEndpoint   = "/v1/product/certificate/info"
	productCertificateCreateEndpoint = "/v1/product/certificate/create"
	productCertificateBindEndpoint   = "/v1/product/certificate/bind"
	productCertificateUnbindEndpoint = "/v1/product/certificate/unbind"
	productCertificateDeleteEndpoint = "/v1/product/certificate/delete"
	// 淘汰 v1 版本，使用 v2 最新版本
	productCertificateAccordanceEndpoint    = "/v2/product/certificate/accordance-types/list"
	productCertificationListEndpoint        = "/v2/product/certification/list"
	productCertificateStatusEndpoint        = "/v1/product/certificate/status/list"
	productCertificateProductStatusEndpoint = "/v1/product/certificate/product_status/list"
	productCertificateProductsEndpoint      = "/v1/product/certificate/products/list"
	productCertificateRejectionEndpoint     = "/v1/product/certificate/rejection_reasons/list"
)

// ProductService 商品服务
type ProductService struct {
	client *resty.Client
}

// newProductService 创建新的商品服务
func newProductService(c *Client) *ProductService {
	return &ProductService{
		client: c.GetHTTPClient(),
	}
}

// GetProductList 获取商品列表
func (s *ProductService) GetProductList(req *ProductListRequest) (*ProductListResponse, error) {
	resp := &ProductListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get product list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetProductInfo 获取商品信息 (使用 v2 最新版本)
func (s *ProductService) GetProductInfo(req *ProductInfoRequest) (*ProductInfoResponse, error) {
	resp := &ProductInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productInfoEndpoint) // 现在使用 v2 端点

	if err != nil {
		return nil, fmt.Errorf("failed to get product info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateProducts 创建商品
func (s *ProductService) CreateProducts(req *ProductCreateRequest) (*ProductCreateResponse, error) {
	resp := &ProductCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productCreateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to create products: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("create products failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetProductImportInfo 获取商品导入信息
func (s *ProductService) GetProductImportInfo(req *ProductImportInfoRequest) (*ProductImportInfoResponse, error) {
	resp := &ProductImportInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productImportInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get product import info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product import info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// UpdateProducts 更新商品
func (s *ProductService) UpdateProducts(req *ProductUpdateRequest) (*ProductUpdateResponse, error) {
	resp := &ProductUpdateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productUpdateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to update products: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("update products failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// UpdateProductStocks 更新商品库存
func (s *ProductService) UpdateProductStocks(req *ProductStocksRequest) (*ProductStocksResponse, error) {
	resp := &ProductStocksResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productStocksEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to update product stocks: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("update product stocks failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// UpdateProductPrices 更新商品价格
func (s *ProductService) UpdateProductPrices(req *ProductPricesRequest) (*ProductPricesResponse, error) {
	resp := &ProductPricesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productPricesEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to update product prices: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("update product prices failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// ArchiveProducts 归档商品
func (s *ProductService) ArchiveProducts(productIDs []int64) error {
	req := map[string]interface{}{
		"product_id": productIDs,
	}

	httpResp, err := s.client.R().
		SetBody(req).
		Post(productArchiveEndpoint)

	if err != nil {
		return fmt.Errorf("failed to archive products: %w", err)
	}

	if !httpResp.IsSuccess() {
		return fmt.Errorf("archive products failed with status code: %d", httpResp.StatusCode())
	}

	return nil
}

// UnarchiveProducts 取消归档商品
func (s *ProductService) UnarchiveProducts(productIDs []int64) error {
	req := map[string]interface{}{
		"product_id": productIDs,
	}

	httpResp, err := s.client.R().
		SetBody(req).
		Post(productUnarchiveEndpoint)

	if err != nil {
		return fmt.Errorf("failed to unarchive products: %w", err)
	}

	if !httpResp.IsSuccess() {
		return fmt.Errorf("unarchive products failed with status code: %d", httpResp.StatusCode())
	}

	return nil
}

// DeleteProducts 删除商品
func (s *ProductService) DeleteProducts(productIDs []int64) error {
	req := map[string]interface{}{
		"products": productIDs,
	}

	httpResp, err := s.client.R().
		SetBody(req).
		Post(productDeleteEndpoint)

	if err != nil {
		return fmt.Errorf("failed to delete products: %w", err)
	}

	if !httpResp.IsSuccess() {
		return fmt.Errorf("delete products failed with status code: %d", httpResp.StatusCode())
	}

	return nil
}

// GetProductsByOfferIDs 根据OfferID获取商品信息
func (s *ProductService) GetProductsByOfferIDs(offerIDs []string) (*ProductListResponse, error) {
	req := &ProductListRequest{
		Filter: &ProductFilter{
			OfferID: offerIDs,
		},
		Limit: 1000,
	}

	return s.GetProductList(req)
}

// GetProductsByIDs 根据ProductID获取商品信息
func (s *ProductService) GetProductsByIDs(productIDs []int64) (*ProductListResponse, error) {
	req := &ProductListRequest{
		Filter: &ProductFilter{
			ProductID: productIDs,
		},
		Limit: 1000,
	}

	return s.GetProductList(req)
}

// GetVisibleProducts 获取可见商品
func (s *ProductService) GetVisibleProducts(limit int32, lastID string) (*ProductListResponse, error) {
	req := &ProductListRequest{
		Filter: &ProductFilter{
			Visibility: "VISIBLE",
		},
		Limit:  limit,
		LastID: lastID,
	}

	return s.GetProductList(req)
}

// GetInvisibleProducts 获取不可见商品
func (s *ProductService) GetInvisibleProducts(limit int32, lastID string) (*ProductListResponse, error) {
	req := &ProductListRequest{
		Filter: &ProductFilter{
			Visibility: "INVISIBLE",
		},
		Limit:  limit,
		LastID: lastID,
	}

	return s.GetProductList(req)
}

// GetAllProducts 获取所有商品
func (s *ProductService) GetAllProducts(limit int32, lastID string) (*ProductListResponse, error) {
	req := &ProductListRequest{
		Filter: &ProductFilter{
			Visibility: "ALL",
		},
		Limit:  limit,
		LastID: lastID,
	}

	return s.GetProductList(req)
}

// ImportProductPictures 导入商品图片
func (s *ProductService) ImportProductPictures(req *ProductPicturesRequest) (*ProductPicturesResponse, error) {
	resp := &ProductPicturesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productPicturesEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to import product pictures: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("import product pictures failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetProductRating 获取商品评分
func (s *ProductService) GetProductRating(req *ProductRatingRequest) (*ProductRatingResponse, error) {
	resp := &ProductRatingResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productRatingEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get product rating: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product rating failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetProductAttributes 获取商品属性
func (s *ProductService) GetProductAttributes(req *ProductAttributesRequest) (*ProductAttributesResponse, error) {
	resp := &ProductAttributesResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productAttributesEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get product attributes: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product attributes failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// ImportProductBySKU 通过SKU导入商品
func (s *ProductService) ImportProductBySKU(req *ProductImportBySKURequest) (*ProductImportBySKUResponse, error) {
	resp := &ProductImportBySKUResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productImportBySKUEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to import product by SKU: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("import product by SKU failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetProductCertificates 获取商品证书
func (s *ProductService) GetProductCertificates(req *ProductCertificateRequest) (*ProductCertificateResponse, error) {
	resp := &ProductCertificateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productCertificateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get product certificates: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product certificates failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetCertificateTypes 获取证书类型
func (s *ProductService) GetCertificateTypes() (*CertificateTypesResponse, error) {
	resp := &CertificateTypesResponse{}
	httpResp, err := s.client.R().
		Post(productCertificateTypesEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get certificate types: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get certificate types failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetProductPicturesInfo 获取商品图片信息
func (s *ProductService) GetProductPicturesInfo(req *ProductPicturesInfoRequest) (*ProductPicturesInfoResponse, error) {
	resp := &ProductPicturesInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productPicturesInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get product pictures info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product pictures info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// UpdateProductOfferID 更新商品OfferID
func (s *ProductService) UpdateProductOfferID(req *ProductUpdateOfferIDRequest) (*ProductUpdateOfferIDResponse, error) {
	resp := &ProductUpdateOfferIDResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productUpdateOfferIDEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to update product offer ID: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("update product offer ID failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetProductPricesInfoV4 获取商品价格信息 (v4版本)
func (s *ProductService) GetProductPricesInfoV4(req *ProductPricesInfoV4Request) (*ProductPricesInfoV4Response, error) {
	resp := &ProductPricesInfoV4Response{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productPricesInfoV4Endpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get product prices info v4: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product prices info v4 failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetProductStocksInfoV4 获取商品库存信息 (v4版本)
func (s *ProductService) GetProductStocksInfoV4(req *ProductStocksInfoV4Request) (*ProductStocksInfoV4Response, error) {
	resp := &ProductStocksInfoV4Response{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productStocksInfoV4Endpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get product stocks info v4: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product stocks info v4 failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetProductStocksByWarehouse 获取FBS仓库商品库存
func (s *ProductService) GetProductStocksByWarehouse(req *ProductStocksByWarehouseRequest) (*ProductStocksByWarehouseResponse, error) {
	resp := &ProductStocksByWarehouseResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productStocksByWarehouseEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get product stocks by warehouse: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get product stocks by warehouse failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// UpdateProductDiscount 更新商品折扣
func (s *ProductService) UpdateProductDiscount(req *ProductDiscountUpdateRequest) (*ProductDiscountUpdateResponse, error) {
	resp := &ProductDiscountUpdateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(productDiscountUpdateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to update product discount: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("update product discount failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}
