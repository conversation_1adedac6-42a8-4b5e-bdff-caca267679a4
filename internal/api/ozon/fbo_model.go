package ozonapi

import (
	"time"
)

// ClusterListRequest 获取集群和仓库信息请求
type ClusterListRequest struct {
	ClusterIDs  []int64 `json:"cluster_ids,omitempty"`
	ClusterType string  `json:"cluster_type"` // CLUSTER_TYPE_OZON, CLUSTER_TYPE_CIS
}

// ClusterListResponse 获取集群和仓库信息响应
type ClusterListResponse struct {
	Clusters []ClusterInfo `json:"clusters"`
	Id       int           `json:"id"`
	Name     string        `json:"name"`
	Type     string        `json:"type"`
}

type ClusterInfo struct {
	LogisticClusters []LogisticClusters `json:"logistic_clusters"`
	Id               int                `json:"id"`
	Name             string             `json:"name"`
	Type             string             `json:"type"`
}
type LogisticClusters struct {
	Warehouses []struct {
		WarehouseId int64  `json:"warehouse_id"`
		Type        string `json:"type"`
		Name        string `json:"name"`
	} `json:"warehouses"`
}

// WarehouseFboListRequest 搜索供应点请求
type WarehouseFboListRequest struct {
	FilterBySupplyType []string `json:"filter_by_supply_type"` //跨区直送 CREATE_TYPE_CROSSDOCK, 直接 CREATE_TYPE_DIRECT
	Search             string   `json:"search"`
}

// WarehouseFboListResponse 搜索供应点响应
type WarehouseFboListResponse struct {
	Search []struct {
		Address     string `json:"address"`
		Coordinates struct {
			Latitude  float64 `json:"latitude"`
			Longitude float64 `json:"longitude"`
		} `json:"coordinates"`
		Name          string `json:"name"`
		WarehouseID   int64  `json:"warehouse_id"`
		WarehouseType string `json:"warehouse_type"` // WAREHOUSE_TYPE_DELIVERY_POINT, WAREHOUSE_TYPE_ORDERS_RECEIVING_POINT, etc.
	} `json:"search"`
}

// DraftCreateRequest 创建供应草稿请求
type DraftCreateRequest struct {
	ClusterIDs              []int64 `json:"cluster_ids,omitempty"`
	DropOffPointWarehouseID int64   `json:"drop_off_point_warehouse_id,omitempty"`
	Items                   []struct {
		Quantity int   `json:"quantity"`
		SKU      int64 `json:"sku"`
	} `json:"items"`
	Type string `json:"type"` // CREATE_TYPE_CROSSDOCK, CREATE_TYPE_DIRECT
}

// DraftCreateInfoResponse 创建供应草稿响应
type DraftCreateInfoResponse struct {
	Clusters []DraftCreateInfoCluster `json:"clusters"`
	DraftID  int64                    `json:"draft_id"`
	Errors   []interface{}            `json:"errors"`
	Status   string                   `json:"status"` // CALCULATION_STATUS_FAILED, CALCULATION_STATUS_SUCCESS, etc.
}

// DraftCreateInfoCluster 草稿创建信息中的集群
type DraftCreateInfoCluster struct {
	ClusterID   int64                      `json:"cluster_id"`
	ClusterName string                     `json:"cluster_name"`
	Warehouses  []DraftCreateInfoWarehouse `json:"warehouses"`
}

// DraftCreateInfoWarehouse 草稿创建信息中的仓库
type DraftCreateInfoWarehouse struct {
	SupplyWarehouse struct {
		Name        string `json:"name"`
		WarehouseID int64  `json:"warehouse_id"`
	} `json:"supply_warehouse"`
}

// DraftTimeslotInfoRequest 获取可用时间段请求
type DraftTimeslotInfoRequest struct {
	DateFrom     time.Time `json:"date_from"`
	DateTo       time.Time `json:"date_to"`
	DraftID      int64     `json:"draft_id"`
	WarehouseIDs []int64   `json:"warehouse_ids"`
}

// DraftTimeslotInfoResponse 获取可用时间段响应
type DraftTimeslotInfoResponse struct {
	DropOffWarehouseTimeslots []WarehouseTimeslot `json:"drop_off_warehouse_timeslots"`
	RequestedDateFrom         time.Time           `json:"requested_date_from"`
	RequestedDateTo           time.Time           `json:"requested_date_to"`
}

// WarehouseTimeslot 仓库时间段
type WarehouseTimeslot struct {
	DropOffWarehouseID int64 `json:"drop_off_warehouse_id"`
	Days               []struct {
		DateInTimezone time.Time  `json:"date_in_timezone"`
		Timeslots      []Timeslot `json:"timeslots"`
	} `json:"days"`
}

// Timeslot 时间段
type Timeslot struct {
	FromInTimezone time.Time `json:"from_in_timezone"`
	ToInTimezone   time.Time `json:"to_in_timezone"`
}

// DraftSupplyCreateRequest 从草稿创建供应单请求
type DraftSupplyCreateRequest struct {
	DraftID     int64     `json:"draft_id"`
	Timeslot    *Timeslot `json:"timeslot,omitempty"`
	WarehouseID int64     `json:"warehouse_id"`
}

// DraftSupplyCreateStatusResponse 从草稿创建供应单状态响应
type DraftSupplyCreateStatusResponse struct {
	ErrorMessages []string `json:"error_messages"`
	Result        struct {
		OrderIDs []int64 `json:"order_ids"`
	} `json:"result"`
	Status string `json:"status"` // DraftSupplyCreateStatusUnknown, DraftSupplyCreateStatusSuccess, etc.
}

// CargoesCreateRequest 设置货物单位请求
type CargoesCreateRequest struct {
	Cargoes              []CargoItem `json:"cargoes"`
	DeleteCurrentVersion bool        `json:"delete_current_version,omitempty"`
	SupplyID             int64       `json:"supply_id"`
}

// CargoItem 货物项
type CargoItem struct {
	Key   string `json:"key"`
	Value struct {
		Items []struct {
			Barcode   string    `json:"barcode"`
			ExpiresAt time.Time `json:"expires_at,omitempty"`
			Quant     int       `json:"quant"`
			Quantity  int       `json:"quantity"`
		} `json:"items"`
		Type string `json:"type"` // BOX, PALLET
	} `json:"value"`
}

// CargoesCreateResponse 设置货物单位响应
type CargoesCreateResponse struct {
	OperationID string      `json:"operation_id"`
	Errors      interface{} `json:"errors"` // Complex error object
}

// CargoesCreateInfoResponse 获取货物单位设置状态响应
type CargoesCreateInfoResponse struct {
	Result struct {
		Cargoes []struct {
			Key   string `json:"key"`
			Value struct {
				CargoID int64 `json:"cargo_id"`
			} `json:"value"`
		} `json:"cargoes"`
	} `json:"result"`
	Status string      `json:"status"` // SUCCESS, IN_PROGRESS, FAILED
	Errors interface{} `json:"errors"` // Complex error object
}

// CargoesDeleteRequest 删除货物单位请求
type CargoesDeleteRequest struct {
	CargoIDs []int64 `json:"cargo_ids,omitempty"`
	SupplyID int64   `json:"supply_id"`
}

// CargoesDeleteResponse 删除货物单位响应
type CargoesDeleteResponse struct {
	OperationID string      `json:"operation_id"`
	Errors      interface{} `json:"errors"` // Complex error object
}

// CargoesDeleteStatusResponse 获取货物单位删除状态响应
type CargoesDeleteStatusResponse struct {
	Status string      `json:"status"` // SUCCESS, IN_PROGRESS, ERROR
	Errors interface{} `json:"errors"` // Complex error object
}

// CargoesRulesResponse 获取FBO货物设置检查列表响应
type CargoesRulesResponse struct {
	SupplyCheckLists []struct {
		SupplyID  int64 `json:"supply_id"`
		Satisfied bool  `json:"satisfied"`
	} `json:"supply_check_lists"`
}

// CargoesLabelCreateRequest 生成货物标签请求
type CargoesLabelCreateRequest struct {
	Cargoes []struct {
		CargoID int64 `json:"cargo_id"`
	} `json:"cargoes,omitempty"`
	SupplyID int64 `json:"supply_id"`
}

// CargoesLabelGetResponse 获取标签标识符响应
type CargoesLabelGetResponse struct {
	Result struct {
		FileGUID string `json:"file_guid"`
	} `json:"result"`
	Status string      `json:"status"` // SUCCESS, IN_PROGRESS, FAILED
	Errors interface{} `json:"errors"`
}

// SupplyOrderCancelRequest 取消供应单请求
type SupplyOrderCancelRequest struct {
	OrderID int64 `json:"order_id"`
}

// SupplyOrderCancelStatusResponse 获取供应单取消状态响应
type SupplyOrderCancelStatusResponse struct {
	Status       string   `json:"status"` // SUCCESS, IN_PROGRESS, ERROR
	ErrorReasons []string `json:"error_reasons"`
	Result       struct {
		IsOrderCancelled bool `json:"is_order_cancelled"`
		Supplies         []struct {
			SupplyID          int64 `json:"supply_id"`
			IsSupplyCancelled bool  `json:"is_supply_cancelled"`
		} `json:"supplies"`
	} `json:"result"`
}

// SupplyOrderContentUpdateRequest 更新供应单内容请求
type SupplyOrderContentUpdateRequest struct {
	Items []struct {
		Quant    int   `json:"quant,omitempty"`
		Quantity int   `json:"quantity"`
		SKU      int64 `json:"sku"`
	} `json:"items"`
	OrderID  int64 `json:"order_id"`
	SupplyID int64 `json:"supply_id"`
}

// SupplyOrderContentUpdateResponse 更新供应单内容响应
type SupplyOrderContentUpdateResponse struct {
	OperationID string   `json:"operation_id"`
	Errors      []string `json:"errors"`
}

// SupplyOrderContentUpdateStatusResponse 获取供应单内容更新状态响应
type SupplyOrderContentUpdateStatusResponse struct {
	Status string   `json:"status"` // SUCCESS, IN_PROGRESS, ERROR
	Errors []string `json:"errors"`
}

// DraftListRequest 获取供应草稿列表请求
type DraftListRequest struct {
	Limit  int32 `json:"limit,omitempty"`
	Offset int32 `json:"offset,omitempty"`
}

// DraftListResponse 获取供应草稿列表响应
type DraftListResponse struct {
	Result struct {
		Items []DraftItem `json:"items"`
		Total int32       `json:"total"`
	} `json:"result"`
}

// DraftItem 草稿项
type DraftItem struct {
	ID          int64     `json:"id"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	ClusterIDs  []int64   `json:"cluster_ids"`
	WarehouseID int64     `json:"warehouse_id"`
	Items       []struct {
		SKU      string `json:"sku"`
		Quantity int32  `json:"quantity"`
	} `json:"items"`
}

// SupplyOrderListRequest 获取供应单列表请求 (根据官方文档修正)
type SupplyOrderListRequest struct {
	Filter SupplyOrderListFilter `json:"filter"`
	Paging SupplyOrderListPaging `json:"paging"`
}

// SupplyOrderListFilter 供应单列表过滤器
// ORDER_STATE_DATA_FILLING — заполнение данных;
// ORDER_STATE_READY_TO_SUPPLY — готова к отгрузке;
// ORDER_STATE_ACCEPTED_AT_SUPPLY_WAREHOUSE — принята на точке отгрузки;
// ORDER_STATE_IN_TRANSIT — в пути;
// ORDER_STATE_ACCEPTANCE_AT_STORAGE_WAREHOUSE — приёмка на складе;
// ORDER_STATE_REPORTS_CONFIRMATION_AWAITING — согласование актов;
// ORDER_STATE_REPORT_REJECTED — спор;
// ORDER_STATE_COMPLETED — завершена;
// ORDER_STATE_REJECTED_AT_SUPPLY_WAREHOUSE — отказано в приёмке;
// ORDER_STATE_CANCELLED — отменена.
type SupplyOrderListFilter struct {
	States []string `json:"states,omitempty"` // 供应单状态列表，如: ORDER_STATE_COMPLETED, ORDER_STATE_DATA_FILLING
}

// SupplyOrderListPaging 供应单列表分页参数
type SupplyOrderListPaging struct {
	FromSupplyOrderID int64 `json:"from_supply_order_id,omitempty"` // 起始供应单ID
	Limit             int32 `json:"limit,omitempty"`                // 限制数量，默认100
}

// SupplyOrderListResponse 获取供应单列表响应 (根据官方文档修正)
type SupplyOrderListResponse struct {
	LastSupplyOrderID int64   `json:"last_supply_order_id"`
	SupplyOrderID     []int64 `json:"supply_order_id"`
}

// SupplyOrderListItem 供应单列表项 (用于详情获取后的数据结构)
// 注意：GetSupplyOrderList 接口实际只返回ID列表，详细信息需要通过 GetSupplyOrder 获取
type SupplyOrderListItem struct {
	ID            int64     `json:"id"`
	Number        string    `json:"number"`
	Status        string    `json:"status"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	WarehouseID   int64     `json:"warehouse_id"`
	WarehouseName string    `json:"warehouse_name"`
	TimeslotFrom  time.Time `json:"timeslot_from"`
	TimeslotTo    time.Time `json:"timeslot_to"`
	ItemsCount    int32     `json:"items_count"`
	TotalQuantity int32     `json:"total_quantity"`
}

// SupplyOrderGetRequest 获取供应单详情请求 (根据官方文档修正)
type SupplyOrderGetRequest struct {
	OrderIDs []int64 `json:"order_ids"`
}

// SupplyOrderGetResponse 获取供应单详情响应 (根据官方文档修正)
type SupplyOrderGetResponse struct {
	Orders     []SupplyOrderDetail `json:"orders"`
	Warehouses []SupplyWarehouse   `json:"warehouses"`
}
type SupplyWarehouse struct {
	Address     string `json:"address"`
	Name        string `json:"name"`
	WarehouseId int64  `json:"warehouse_id"`
}

// SupplyOrderDetail 供应单详情 (根据官方文档修正)
type SupplyOrderDetail struct {
	SupplyOrderId          int                  `json:"supply_order_id"`
	SupplyOrderNumber      string               `json:"supply_order_number"`
	CreationDate           string               `json:"creation_date"`
	State                  string               `json:"state"`
	DataFillingDeadlineUtc time.Time            `json:"data_filling_deadline_utc"`
	CreationFlow           string               `json:"creation_flow"`
	DropoffWarehouseId     int64                `json:"dropoff_warehouse_id"`
	Timeslot               *SupplyOrderTimeslot `json:"timeslot"`
	Vehicle                *SupplyOrderVehicle  `json:"vehicle"`
	Supplies               []SupplyInfo         `json:"supplies"`
	CanCancel              bool                 `json:"can_cancel"`
	IsEconom               bool                 `json:"is_econom"`
	IsVirtual              bool                 `json:"is_virtual"`
	IsSuperFbo             bool                 `json:"is_super_fbo"`
	ProductSuperFbo        bool                 `json:"product_super_fbo"`
}

// SupplyInfo 供应信息
type SupplyInfo struct {
	BundleID           string    `json:"bundle_id"`
	StorageWarehouseID int64     `json:"storage_warehouse_id"`
	SupplyID           int64     `json:"supply_id"`
	SupplyState        string    `json:"supply_state"`
	SupplyTags         SupplyTag `json:"supply_tags"`
}

// SupplyTag 供应标签
type SupplyTag struct {
	IsETTNRequired    bool `json:"is_ettn_required"`
	IsEVSDRequired    bool `json:"is_evsd_required"`
	IsJewelry         bool `json:"is_jewelry"`
	IsMarkingPossible bool `json:"is_marking_possible"`
	IsMarkingRequired bool `json:"is_marking_required"`
	IsTraceable       bool `json:"is_traceable"`
}

// SupplyOrderTimeslot 供应单时间段
type SupplyOrderTimeslot struct {
	CanNotSetReasons []string                  `json:"can_not_set_reasons"`
	CanSet           bool                      `json:"can_set"`
	IsRequired       bool                      `json:"is_required"`
	Value            *SupplyOrderTimeslotValue `json:"value,omitempty"`
}

// SupplyOrderTimeslotValue 时间段值
type SupplyOrderTimeslotValue struct {
	Timeslot     TimeslotPeriod `json:"timeslot"`
	TimezoneInfo TimezoneInfo   `json:"timezone_info"`
}

// TimeslotPeriod 时间段
type TimeslotPeriod struct {
	From time.Time `json:"from"`
	To   time.Time `json:"to"`
}

// TimezoneInfo 时区信息
type TimezoneInfo struct {
	IanaName string `json:"iana_name"`
	Offset   string `json:"offset"`
}

// SupplyOrderVehicle 供应单车辆信息
type SupplyOrderVehicle struct {
	CanNotSetReasons []string                  `json:"can_not_set_reasons"`
	CanSet           bool                      `json:"can_set"`
	IsRequired       bool                      `json:"is_required"`
	Value            []SupplyOrderVehicleValue `json:"value,omitempty"`
}

// SupplyOrderVehicleValue 车辆信息值
type SupplyOrderVehicleValue struct {
	DriverName    string `json:"driver_name"`
	DriverPhone   string `json:"driver_phone"`
	VehicleModel  string `json:"vehicle_model"`
	VehicleNumber string `json:"vehicle_number"`
}

// SupplyOrderItemsUpdateRequest 更新供应单商品请求
type SupplyOrderItemsUpdateRequest struct {
	SupplyOrderID int64                        `json:"supply_order_id"`
	Items         []SupplyOrderItemsUpdateItem `json:"items"`
}

// SupplyOrderItemsUpdateItem 更新供应单商品项
type SupplyOrderItemsUpdateItem struct {
	SKU      string `json:"sku"`
	Quantity int32  `json:"quantity"`
}

// SupplyOrderItemsUpdateResponse 更新供应单商品响应
type SupplyOrderItemsUpdateResponse struct {
	OperationID string `json:"operation_id"`
}

// SupplyOrderItemsUpdateStatusResponse 供应单商品更新状态响应
type SupplyOrderItemsUpdateStatusResponse struct {
	Status string `json:"status"`
	Error  string `json:"error,omitempty"`
}

// SupplyOrderShipRequest 发货供应单请求
type SupplyOrderShipRequest struct {
	SupplyOrderID int64 `json:"supply_order_id"`
}

// SupplyOrderShipResponse 发货供应单响应
type SupplyOrderShipResponse struct {
	OperationID string `json:"operation_id"`
}

// SupplyOrderShipStatusResponse 供应单发货状态响应
type SupplyOrderShipStatusResponse struct {
	Status string `json:"status"`
	Error  string `json:"error,omitempty"`
}

// SupplyOrderActCreateRequest 创建供应单交接单请求
type SupplyOrderActCreateRequest struct {
	SupplyOrderIDs []int64 `json:"supply_order_ids"`
}

// SupplyOrderActCreateResponse 创建供应单交接单响应
type SupplyOrderActCreateResponse struct {
	OperationID string `json:"operation_id"`
}

// SupplyOrderActGetRequest 获取供应单交接单请求
type SupplyOrderActGetRequest struct {
	OperationID string `json:"operation_id"`
}

// SupplyOrderActGetResponse 获取供应单交接单响应
type SupplyOrderActGetResponse struct {
	Result struct {
		Status   string `json:"status"`
		FileURL  string `json:"file_url,omitempty"`
		FileName string `json:"file_name,omitempty"`
	} `json:"result"`
}

// SupplyOrderActCheckStatusRequest 检查供应单交接单状态请求
type SupplyOrderActCheckStatusRequest struct {
	OperationID string `json:"operation_id"`
}

// SupplyOrderActCheckStatusResponse 检查供应单交接单状态响应
type SupplyOrderActCheckStatusResponse struct {
	Result struct {
		Status string `json:"status"`
		Error  string `json:"error,omitempty"`
	} `json:"result"`
}

// SupplyOrderBundleRequest 获取供应单组合信息请求
type SupplyOrderBundleRequest struct {
	BundleIds []string `json:"bundle_ids"`
	IsAsc     bool     `json:"is_asc,omitempty"`
	Limit     int      `json:"limit"`
	Query     string   `json:"query,omitempty"`
	SortField string   `json:"sort_field"`
}

// SupplyOrderBundleResponse 获取供应单组合信息响应
type SupplyOrderBundleResponse struct {
	Items      []SupplyOrderBundleItem `json:"items"`
	TotalCount int                     `json:"total_count"`
	LastId     string                  `json:"last_id"`
	HasNext    bool                    `json:"has_next"`
}

type SupplyOrderBundleItem struct {
	Sku                 int           `json:"sku"`
	Quantity            int           `json:"quantity"`
	OfferId             string        `json:"offer_id"`
	IconPath            string        `json:"icon_path"`
	Name                string        `json:"name"`
	VolumeInLitres      float64       `json:"volume_in_litres"`
	TotalVolumeInLitres int           `json:"total_volume_in_litres"`
	Barcode             string        `json:"barcode"`
	ProductId           int           `json:"product_id"`
	Quant               int           `json:"quant"`
	SfboAttribute       string        `json:"sfbo_attribute"`
	ShipmentType        string        `json:"shipment_type"`
	IsQuantEditable     bool          `json:"is_quant_editable"`
	Tags                []interface{} `json:"tags"`
	PlacementZone       string        `json:"placement_zone"`
}
