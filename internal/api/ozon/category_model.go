package ozonapi

// CategoryTreeRequest 获取分类树请求
type CategoryTreeRequest struct {
	CategoryID int64  `json:"category_id,omitempty"`
	Language   string `json:"language,omitempty"` // DEFAULT, EN, RU
}

// CategoryTreeResponse 获取分类树响应
type CategoryTreeResponse struct {
	Result []CategoryTree `json:"result"`
}

// CategoryTree 分类树
type CategoryTree struct {
	CategoryID   int64          `json:"category_id"`
	Title        string         `json:"title"`
	Disabled     bool           `json:"disabled"`
	Children     []CategoryTree `json:"children"`
	TypeID       int64          `json:"type_id"`
	TypeName     string         `json:"type_name"`
	DescriptionCategoryID int64 `json:"description_category_id"`
}

// CategoryAttributeRequest 获取分类属性请求
type CategoryAttributeRequest struct {
	CategoryID int64  `json:"category_id"`
	TypeID     int64  `json:"type_id"`
	Language   string `json:"language,omitempty"` // DEFAULT, EN, RU
}

// CategoryAttributeResponse 获取分类属性响应
type CategoryAttributeResponse struct {
	Result []CategoryAttribute `json:"result"`
}

// CategoryAttribute 分类属性
type CategoryAttribute struct {
	ID                int64                    `json:"id"`
	Name              string                   `json:"name"`
	Description       string                   `json:"description"`
	Type              string                   `json:"type"`
	IsCollection      bool                     `json:"is_collection"`
	IsRequired        bool                     `json:"is_required"`
	IsAspect          bool                     `json:"is_aspect"`
	MaxValueCount     int32                    `json:"max_value_count"`
	GroupID           int64                    `json:"group_id"`
	GroupName         string                   `json:"group_name"`
	DictionaryID      int64                    `json:"dictionary_id"`
	CategoryDependent bool                     `json:"category_dependent"`
	UsageType         string                   `json:"usage_type"`
	Values            []CategoryAttributeValue `json:"values"`
}

// CategoryAttributeValue 分类属性值
type CategoryAttributeValue struct {
	ID       int64  `json:"id"`
	Value    string `json:"value"`
	Info     string `json:"info"`
	Picture  string `json:"picture"`
}

// AttributeValuesRequest 获取属性值请求
type AttributeValuesRequest struct {
	AttributeID  int64  `json:"attribute_id"`
	CategoryID   int64  `json:"category_id"`
	TypeID       int64  `json:"type_id"`
	Language     string `json:"language,omitempty"` // DEFAULT, EN, RU
	LastValueID  int64  `json:"last_value_id,omitempty"`
	Limit        int32  `json:"limit,omitempty"`
}

// AttributeValuesResponse 获取属性值响应
type AttributeValuesResponse struct {
	Result []AttributeValue `json:"result"`
	HasNext bool            `json:"has_next"`
}

// AttributeValue 属性值
type AttributeValue struct {
	ID       int64  `json:"id"`
	Value    string `json:"value"`
	Info     string `json:"info"`
	Picture  string `json:"picture"`
}

// SearchAttributeValuesRequest 搜索属性值请求
type SearchAttributeValuesRequest struct {
	AttributeID int64  `json:"attribute_id"`
	CategoryID  int64  `json:"category_id"`
	TypeID      int64  `json:"type_id"`
	Value       string `json:"value"`
	Language    string `json:"language,omitempty"` // DEFAULT, EN, RU
	Limit       int32  `json:"limit,omitempty"`
}

// SearchAttributeValuesResponse 搜索属性值响应
type SearchAttributeValuesResponse struct {
	Result []AttributeValue `json:"result"`
}

// BrandRequest 获取品牌请求
type BrandRequest struct {
	BrandID  []int64 `json:"brand_id,omitempty"`
	Language string  `json:"language,omitempty"` // DEFAULT, EN, RU
}

// BrandResponse 获取品牌响应
type BrandResponse struct {
	Result []Brand `json:"result"`
}

// Brand 品牌
type Brand struct {
	BrandID int64  `json:"brand_id"`
	Name    string `json:"name"`
	Picture string `json:"picture"`
}

// BrandCompanyRequest 获取品牌公司请求
type BrandCompanyRequest struct {
	BrandID  int64  `json:"brand_id"`
	Language string `json:"language,omitempty"` // DEFAULT, EN, RU
}

// BrandCompanyResponse 获取品牌公司响应
type BrandCompanyResponse struct {
	Result []BrandCompany `json:"result"`
}

// BrandCompany 品牌公司
type BrandCompany struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

// BrandCertificationRequest 获取品牌认证请求
type BrandCertificationRequest struct {
	BrandID  int64  `json:"brand_id"`
	Language string `json:"language,omitempty"` // DEFAULT, EN, RU
}

// BrandCertificationResponse 获取品牌认证响应
type BrandCertificationResponse struct {
	Result []BrandCertification `json:"result"`
}

// BrandCertification 品牌认证
type BrandCertification struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`
}

// WarehouseRequest 获取仓库请求
type WarehouseRequest struct {
	WarehouseID []int64 `json:"warehouse_id,omitempty"`
}

// WarehouseResponse 获取仓库响应
type WarehouseResponse struct {
	Result []Warehouse `json:"result"`
}

// Warehouse 仓库
type Warehouse struct {
	WarehouseID int64  `json:"warehouse_id"`
	Name        string `json:"name"`
	IsRFBS      bool   `json:"is_rfbs"`
	HasEntrustedAcceptance bool `json:"has_entrusted_acceptance"`
	CanPrintActInAdvance   bool `json:"can_print_act_in_advance"`
	WorkingHours []WorkingHour `json:"working_hours"`
	MinWorkingDays int32       `json:"min_working_days"`
	HasPostingServices bool    `json:"has_posting_services"`
	Address        WarehouseAddress `json:"address"`
}

// WorkingHour 工作时间
type WorkingHour struct {
	Day  string `json:"day"`
	From string `json:"from"`
	To   string `json:"to"`
}

// WarehouseAddress 仓库地址
type WarehouseAddress struct {
	City        string `json:"city"`
	Region      string `json:"region"`
	Address     string `json:"address"`
	PostalCode  string `json:"postal_code"`
	Country     string `json:"country"`
	Coordinates struct {
		Latitude  float64 `json:"latitude"`
		Longitude float64 `json:"longitude"`
	} `json:"coordinates"`
}

// DeliveryMethodRequest 获取配送方式请求
type DeliveryMethodRequest struct {
	Filter DeliveryMethodFilter `json:"filter,omitempty"`
	Limit  int32                `json:"limit,omitempty"`
	Offset int32                `json:"offset,omitempty"`
}

// DeliveryMethodFilter 配送方式过滤器
type DeliveryMethodFilter struct {
	ProviderID  []int64 `json:"provider_id,omitempty"`
	Status      string  `json:"status,omitempty"`
	WarehouseID []int64 `json:"warehouse_id,omitempty"`
}

// DeliveryMethodResponse 获取配送方式响应
type DeliveryMethodResponse struct {
	Result []DeliveryMethodInfo `json:"result"`
}

// DeliveryMethodInfo 配送方式信息
type DeliveryMethodInfo struct {
	ID           int64  `json:"id"`
	Name         string `json:"name"`
	WarehouseID  int64  `json:"warehouse_id"`
	Warehouse    string `json:"warehouse"`
	TPLProviderID int64 `json:"tpl_provider_id"`
	TPLProvider  string `json:"tpl_provider"`
	Status       string `json:"status"`
}

// CountryRequest 获取国家请求
type CountryRequest struct {
	NameSearch string `json:"name_search,omitempty"`
}

// CountryResponse 获取国家响应
type CountryResponse struct {
	Result []Country `json:"result"`
}

// Country 国家
type Country struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
	ISO  string `json:"iso"`
}

// CurrencyRequest 获取货币请求
type CurrencyRequest struct{}

// CurrencyResponse 获取货币响应
type CurrencyResponse struct {
	Result []Currency `json:"result"`
}

// Currency 货币
type Currency struct {
	Code string `json:"code"`
	Name string `json:"name"`
}
