package ozonapi

import (
	"time"
)

// ActionsListRequest 获取营销活动列表请求
type ActionsListRequest struct {
	Filter ActionsListFilter `json:"filter,omitempty"`
	Limit  int32             `json:"limit,omitempty"`
	Offset int32             `json:"offset,omitempty"`
}

// ActionsListFilter 营销活动列表过滤器
type ActionsListFilter struct {
	Status    []string        `json:"status,omitempty"`
	Type      []string        `json:"type,omitempty"`
	DateRange ActionsDateRange `json:"date_range,omitempty"`
}

// ActionsDateRange 活动日期范围
type ActionsDateRange struct {
	From time.Time `json:"from,omitempty"`
	To   time.Time `json:"to,omitempty"`
}

// ActionsListResponse 获取营销活动列表响应
type ActionsListResponse struct {
	Result struct {
		Items []ActionItem `json:"items"`
		Total int32        `json:"total"`
	} `json:"result"`
}

// ActionItem 营销活动项
type ActionItem struct {
	ID          int64     `json:"id"`
	Title       string    `json:"title"`
	Type        string    `json:"type"`
	Status      string    `json:"status"`
	StartDate   time.Time `json:"start_date"`
	EndDate     time.Time `json:"end_date"`
	Description string    `json:"description"`
	Conditions  ActionConditions `json:"conditions"`
	Benefits    ActionBenefits   `json:"benefits"`
}

// ActionConditions 活动条件
type ActionConditions struct {
	MinOrderAmount    string   `json:"min_order_amount,omitempty"`
	MaxOrderAmount    string   `json:"max_order_amount,omitempty"`
	ProductCategories []int64  `json:"product_categories,omitempty"`
	ProductIDs        []int64  `json:"product_ids,omitempty"`
	CustomerSegments  []string `json:"customer_segments,omitempty"`
}

// ActionBenefits 活动优惠
type ActionBenefits struct {
	DiscountType       string `json:"discount_type"`
	DiscountValue      string `json:"discount_value"`
	MaxDiscountAmount  string `json:"max_discount_amount,omitempty"`
	FreeShipping       bool   `json:"free_shipping,omitempty"`
	BonusPoints        int32  `json:"bonus_points,omitempty"`
}

// ActionsCandidatesRequest 获取候选营销活动请求
type ActionsCandidatesRequest struct {
	Filter ActionsCandidatesFilter `json:"filter,omitempty"`
	Limit  int32                   `json:"limit,omitempty"`
	Offset int32                   `json:"offset,omitempty"`
}

// ActionsCandidatesFilter 候选活动过滤器
type ActionsCandidatesFilter struct {
	ProductID []int64  `json:"product_id,omitempty"`
	OfferID   []string `json:"offer_id,omitempty"`
	Type      []string `json:"type,omitempty"`
}

// ActionsCandidatesResponse 获取候选营销活动响应
type ActionsCandidatesResponse struct {
	Result struct {
		Items []ActionCandidate `json:"items"`
		Total int32             `json:"total"`
	} `json:"result"`
}

// ActionCandidate 候选活动
type ActionCandidate struct {
	ActionID        int64     `json:"action_id"`
	Title           string    `json:"title"`
	Type            string    `json:"type"`
	StartDate       time.Time `json:"start_date"`
	EndDate         time.Time `json:"end_date"`
	EstimatedBenefit string   `json:"estimated_benefit"`
	Requirements    ActionRequirements `json:"requirements"`
}

// ActionRequirements 活动要求
type ActionRequirements struct {
	MinStock       int32  `json:"min_stock,omitempty"`
	MinRating      float64 `json:"min_rating,omitempty"`
	MaxPrice       string `json:"max_price,omitempty"`
	CategoryMatch  bool   `json:"category_match,omitempty"`
}

// ActionsProductsRequest 获取活动商品请求
type ActionsProductsRequest struct {
	ActionID int64                   `json:"action_id"`
	Filter   ActionsProductsFilter   `json:"filter,omitempty"`
	Limit    int32                   `json:"limit,omitempty"`
	Offset   int32                   `json:"offset,omitempty"`
}

// ActionsProductsFilter 活动商品过滤器
type ActionsProductsFilter struct {
	ProductID []int64  `json:"product_id,omitempty"`
	OfferID   []string `json:"offer_id,omitempty"`
	Status    []string `json:"status,omitempty"`
}

// ActionsProductsResponse 获取活动商品响应
type ActionsProductsResponse struct {
	Result struct {
		Items []ActionProduct `json:"items"`
		Total int32           `json:"total"`
	} `json:"result"`
}

// ActionProduct 活动商品
type ActionProduct struct {
	ProductID       int64     `json:"product_id"`
	OfferID         string    `json:"offer_id"`
	Name            string    `json:"name"`
	Status          string    `json:"status"`
	OriginalPrice   string    `json:"original_price"`
	ActionPrice     string    `json:"action_price"`
	DiscountPercent float64   `json:"discount_percent"`
	StartDate       time.Time `json:"start_date"`
	EndDate         time.Time `json:"end_date"`
	Stock           int32     `json:"stock"`
}

// ActionsProductsActivateRequest 激活活动商品请求
type ActionsProductsActivateRequest struct {
	ActionID   int64   `json:"action_id"`
	ProductIDs []int64 `json:"product_ids"`
}

// ActionsProductsActivateResponse 激活活动商品响应
type ActionsProductsActivateResponse struct {
	Result struct {
		ActivatedCount int32                        `json:"activated_count"`
		Errors         []ActionProductActivateError `json:"errors,omitempty"`
	} `json:"result"`
}

// ActionProductActivateError 激活商品错误
type ActionProductActivateError struct {
	ProductID int64  `json:"product_id"`
	Error     string `json:"error"`
}

// ActionsProductsDeactivateRequest 停用活动商品请求
type ActionsProductsDeactivateRequest struct {
	ActionID   int64   `json:"action_id"`
	ProductIDs []int64 `json:"product_ids"`
}

// ActionsProductsDeactivateResponse 停用活动商品响应
type ActionsProductsDeactivateResponse struct {
	Result struct {
		DeactivatedCount int32                          `json:"deactivated_count"`
		Errors           []ActionProductDeactivateError `json:"errors,omitempty"`
	} `json:"result"`
}

// ActionProductDeactivateError 停用商品错误
type ActionProductDeactivateError struct {
	ProductID int64  `json:"product_id"`
	Error     string `json:"error"`
}

// ActionsDiscountsTaskListRequest 获取折扣任务列表请求
type ActionsDiscountsTaskListRequest struct {
	Filter ActionsDiscountsTaskFilter `json:"filter,omitempty"`
	Limit  int32                      `json:"limit,omitempty"`
	Offset int32                      `json:"offset,omitempty"`
}

// ActionsDiscountsTaskFilter 折扣任务过滤器
type ActionsDiscountsTaskFilter struct {
	Status    []string        `json:"status,omitempty"`
	Type      []string        `json:"type,omitempty"`
	CreatedAt ActionsDateRange `json:"created_at,omitempty"`
}

// ActionsDiscountsTaskListResponse 获取折扣任务列表响应
type ActionsDiscountsTaskListResponse struct {
	Result struct {
		Items []DiscountTask `json:"items"`
		Total int32          `json:"total"`
	} `json:"result"`
}

// DiscountTask 折扣任务
type DiscountTask struct {
	TaskID      int64     `json:"task_id"`
	Type        string    `json:"type"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Description string    `json:"description"`
	ProductID   int64     `json:"product_id"`
	OfferID     string    `json:"offer_id"`
	CurrentPrice string   `json:"current_price"`
	SuggestedPrice string `json:"suggested_price"`
	DiscountPercent float64 `json:"discount_percent"`
	Reason      string    `json:"reason"`
}

// ActionsDiscountsTaskApproveRequest 批准折扣任务请求
type ActionsDiscountsTaskApproveRequest struct {
	TaskIDs []int64 `json:"task_ids"`
}

// ActionsDiscountsTaskApproveResponse 批准折扣任务响应
type ActionsDiscountsTaskApproveResponse struct {
	Result struct {
		ApprovedCount int32                        `json:"approved_count"`
		Errors        []DiscountTaskApproveError   `json:"errors,omitempty"`
	} `json:"result"`
}

// DiscountTaskApproveError 批准任务错误
type DiscountTaskApproveError struct {
	TaskID int64  `json:"task_id"`
	Error  string `json:"error"`
}

// ActionsDiscountsTaskDeclineRequest 拒绝折扣任务请求
type ActionsDiscountsTaskDeclineRequest struct {
	TaskIDs []int64 `json:"task_ids"`
	Reason  string  `json:"reason,omitempty"`
}

// ActionsDiscountsTaskDeclineResponse 拒绝折扣任务响应
type ActionsDiscountsTaskDeclineResponse struct {
	Result struct {
		DeclinedCount int32                        `json:"declined_count"`
		Errors        []DiscountTaskDeclineError   `json:"errors,omitempty"`
	} `json:"result"`
}

// DiscountTaskDeclineError 拒绝任务错误
type DiscountTaskDeclineError struct {
	TaskID int64  `json:"task_id"`
	Error  string `json:"error"`
}
