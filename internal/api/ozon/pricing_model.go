package ozonapi

import (
	"time"
)

// PricingCompetitorsListRequest 获取竞争对手列表请求
type PricingCompetitorsListRequest struct {
	Filter PricingCompetitorsFilter `json:"filter,omitempty"`
	Limit  int32                    `json:"limit,omitempty"`
	Offset int32                    `json:"offset,omitempty"`
}

// PricingCompetitorsFilter 竞争对手过滤器
type PricingCompetitorsFilter struct {
	ProductID []int64 `json:"product_id,omitempty"`
	OfferID   []string `json:"offer_id,omitempty"`
}

// PricingCompetitorsListResponse 获取竞争对手列表响应
type PricingCompetitorsListResponse struct {
	Result struct {
		Items []PricingCompetitor `json:"items"`
		Total int32               `json:"total"`
	} `json:"result"`
}

// PricingCompetitor 竞争对手信息
type PricingCompetitor struct {
	ProductID     int64   `json:"product_id"`
	OfferID       string  `json:"offer_id"`
	CompetitorURL string  `json:"competitor_url"`
	Price         string  `json:"price"`
	Currency      string  `json:"currency"`
	InStock       bool    `json:"in_stock"`
	LastUpdate    time.Time `json:"last_update"`
}

// PricingStrategyListRequest 获取定价策略列表请求
type PricingStrategyListRequest struct {
	Filter PricingStrategyFilter `json:"filter,omitempty"`
	Limit  int32                 `json:"limit,omitempty"`
	Offset int32                 `json:"offset,omitempty"`
}

// PricingStrategyFilter 定价策略过滤器
type PricingStrategyFilter struct {
	Status []string `json:"status,omitempty"`
	Type   []string `json:"type,omitempty"`
}

// PricingStrategyListResponse 获取定价策略列表响应
type PricingStrategyListResponse struct {
	Result struct {
		Items []PricingStrategy `json:"items"`
		Total int32             `json:"total"`
	} `json:"result"`
}

// PricingStrategy 定价策略
type PricingStrategy struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`
	Type        string    `json:"type"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Description string    `json:"description"`
	Settings    PricingStrategySettings `json:"settings"`
}

// PricingStrategySettings 定价策略设置
type PricingStrategySettings struct {
	MinMargin         float64 `json:"min_margin"`
	MaxMargin         float64 `json:"max_margin"`
	CompetitorOffset  float64 `json:"competitor_offset"`
	AutoUpdate        bool    `json:"auto_update"`
	UpdateFrequency   string  `json:"update_frequency"`
}

// PricingStrategyCreateRequest 创建定价策略请求
type PricingStrategyCreateRequest struct {
	Name        string                  `json:"name"`
	Type        string                  `json:"type"`
	Description string                  `json:"description,omitempty"`
	Settings    PricingStrategySettings `json:"settings"`
}

// PricingStrategyCreateResponse 创建定价策略响应
type PricingStrategyCreateResponse struct {
	Result struct {
		ID int64 `json:"id"`
	} `json:"result"`
}

// PricingStrategyInfoRequest 获取定价策略信息请求
type PricingStrategyInfoRequest struct {
	StrategyID int64 `json:"strategy_id"`
}

// PricingStrategyInfoResponse 获取定价策略信息响应
type PricingStrategyInfoResponse struct {
	Result PricingStrategyDetail `json:"result"`
}

// PricingStrategyDetail 定价策略详情
type PricingStrategyDetail struct {
	ID            int64                   `json:"id"`
	Name          string                  `json:"name"`
	Type          string                  `json:"type"`
	Status        string                  `json:"status"`
	CreatedAt     time.Time               `json:"created_at"`
	UpdatedAt     time.Time               `json:"updated_at"`
	Description   string                  `json:"description"`
	Settings      PricingStrategySettings `json:"settings"`
	ProductsCount int32                   `json:"products_count"`
	LastRunAt     time.Time               `json:"last_run_at"`
}

// PricingStrategyUpdateRequest 更新定价策略请求
type PricingStrategyUpdateRequest struct {
	StrategyID  int64                   `json:"strategy_id"`
	Name        string                  `json:"name,omitempty"`
	Description string                  `json:"description,omitempty"`
	Settings    PricingStrategySettings `json:"settings,omitempty"`
}

// PricingStrategyUpdateResponse 更新定价策略响应
type PricingStrategyUpdateResponse struct {
	Result struct {
		Success bool `json:"success"`
	} `json:"result"`
}

// PricingStrategyProductsAddRequest 添加商品到策略请求
type PricingStrategyProductsAddRequest struct {
	StrategyID int64   `json:"strategy_id"`
	ProductIDs []int64 `json:"product_ids"`
}

// PricingStrategyProductsAddResponse 添加商品到策略响应
type PricingStrategyProductsAddResponse struct {
	Result struct {
		AddedCount int32                              `json:"added_count"`
		Errors     []PricingStrategyProductAddError   `json:"errors,omitempty"`
	} `json:"result"`
}

// PricingStrategyProductAddError 添加商品错误
type PricingStrategyProductAddError struct {
	ProductID int64  `json:"product_id"`
	Error     string `json:"error"`
}

// PricingStrategyIDsByProductIDsRequest 根据商品ID获取策略ID请求
type PricingStrategyIDsByProductIDsRequest struct {
	ProductIDs []int64 `json:"product_ids"`
}

// PricingStrategyIDsByProductIDsResponse 根据商品ID获取策略ID响应
type PricingStrategyIDsByProductIDsResponse struct {
	Result []PricingStrategyProductMapping `json:"result"`
}

// PricingStrategyProductMapping 策略商品映射
type PricingStrategyProductMapping struct {
	ProductID   int64   `json:"product_id"`
	StrategyIDs []int64 `json:"strategy_ids"`
}

// PricingStrategyProductsListRequest 获取策略商品列表请求
type PricingStrategyProductsListRequest struct {
	StrategyID int64                           `json:"strategy_id"`
	Filter     PricingStrategyProductsFilter   `json:"filter,omitempty"`
	Limit      int32                           `json:"limit,omitempty"`
	Offset     int32                           `json:"offset,omitempty"`
}

// PricingStrategyProductsFilter 策略商品过滤器
type PricingStrategyProductsFilter struct {
	ProductID []int64  `json:"product_id,omitempty"`
	OfferID   []string `json:"offer_id,omitempty"`
	Status    []string `json:"status,omitempty"`
}

// PricingStrategyProductsListResponse 获取策略商品列表响应
type PricingStrategyProductsListResponse struct {
	Result struct {
		Items []PricingStrategyProduct `json:"items"`
		Total int32                    `json:"total"`
	} `json:"result"`
}

// PricingStrategyProduct 策略商品
type PricingStrategyProduct struct {
	ProductID       int64     `json:"product_id"`
	OfferID         string    `json:"offer_id"`
	Name            string    `json:"name"`
	CurrentPrice    string    `json:"current_price"`
	SuggestedPrice  string    `json:"suggested_price"`
	CompetitorPrice string    `json:"competitor_price"`
	Status          string    `json:"status"`
	LastUpdate      time.Time `json:"last_update"`
}

// PricingStrategyProductInfoRequest 获取策略商品信息请求
type PricingStrategyProductInfoRequest struct {
	StrategyID int64 `json:"strategy_id"`
	ProductID  int64 `json:"product_id"`
}

// PricingStrategyProductInfoResponse 获取策略商品信息响应
type PricingStrategyProductInfoResponse struct {
	Result PricingStrategyProductDetail `json:"result"`
}

// PricingStrategyProductDetail 策略商品详情
type PricingStrategyProductDetail struct {
	ProductID       int64                    `json:"product_id"`
	OfferID         string                   `json:"offer_id"`
	Name            string                   `json:"name"`
	CurrentPrice    string                   `json:"current_price"`
	SuggestedPrice  string                   `json:"suggested_price"`
	CompetitorPrice string                   `json:"competitor_price"`
	Status          string                   `json:"status"`
	LastUpdate      time.Time                `json:"last_update"`
	PriceHistory    []PricingProductHistory  `json:"price_history"`
	Competitors     []PricingCompetitor      `json:"competitors"`
}

// PricingProductHistory 商品价格历史
type PricingProductHistory struct {
	Price     string    `json:"price"`
	Timestamp time.Time `json:"timestamp"`
	Source    string    `json:"source"`
}

// PricingStrategyProductsDeleteRequest 从策略删除商品请求
type PricingStrategyProductsDeleteRequest struct {
	StrategyID int64   `json:"strategy_id"`
	ProductIDs []int64 `json:"product_ids"`
}

// PricingStrategyProductsDeleteResponse 从策略删除商品响应
type PricingStrategyProductsDeleteResponse struct {
	Result struct {
		DeletedCount int32                                 `json:"deleted_count"`
		Errors       []PricingStrategyProductDeleteError   `json:"errors,omitempty"`
	} `json:"result"`
}

// PricingStrategyProductDeleteError 删除商品错误
type PricingStrategyProductDeleteError struct {
	ProductID int64  `json:"product_id"`
	Error     string `json:"error"`
}

// PricingStrategyStatusRequest 获取策略状态请求
type PricingStrategyStatusRequest struct {
	StrategyID int64 `json:"strategy_id"`
}

// PricingStrategyStatusResponse 获取策略状态响应
type PricingStrategyStatusResponse struct {
	Result PricingStrategyStatus `json:"result"`
}

// PricingStrategyStatus 策略状态
type PricingStrategyStatus struct {
	StrategyID    int64     `json:"strategy_id"`
	Status        string    `json:"status"`
	LastRunAt     time.Time `json:"last_run_at"`
	NextRunAt     time.Time `json:"next_run_at"`
	ProductsCount int32     `json:"products_count"`
	UpdatedCount  int32     `json:"updated_count"`
	ErrorsCount   int32     `json:"errors_count"`
}

// PricingStrategyDeleteRequest 删除策略请求
type PricingStrategyDeleteRequest struct {
	StrategyID int64 `json:"strategy_id"`
}

// PricingStrategyDeleteResponse 删除策略响应
type PricingStrategyDeleteResponse struct {
	Result struct {
		Success bool `json:"success"`
	} `json:"result"`
}
