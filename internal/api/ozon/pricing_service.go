package ozonapi

import (
	"fmt"
)

const (
	// Pricing Strategy API endpoints
	pricingCompetitorsListEndpoint     = "/v1/pricing-strategy/competitors/list"
	pricingStrategyListEndpoint        = "/v1/pricing-strategy/list"
	pricingStrategyCreateEndpoint      = "/v1/pricing-strategy/create"
	pricingStrategyInfoEndpoint        = "/v1/pricing-strategy/info"
	pricingStrategyUpdateEndpoint      = "/v1/pricing-strategy/update"
	pricingStrategyProductsAddEndpoint = "/v1/pricing-strategy/products/add"
	pricingStrategyIDsByProductIDsEndpoint = "/v1/pricing-strategy/strategy-ids-by-product-ids"
	pricingStrategyProductsListEndpoint = "/v1/pricing-strategy/products/list"
	pricingStrategyProductInfoEndpoint  = "/v1/pricing-strategy/product/info"
	pricingStrategyProductsDeleteEndpoint = "/v1/pricing-strategy/products/delete"
	pricingStrategyStatusEndpoint       = "/v1/pricing-strategy/status"
	pricingStrategyDeleteEndpoint       = "/v1/pricing-strategy/delete"
)

// PricingService 定价策略服务
type PricingService struct {
	client *Client
}

// NewPricingService 创建定价策略服务实例
func NewPricingService(client *Client) *PricingService {
	return &PricingService{client: client}
}

// GetCompetitorsList 获取竞争对手列表
func (s *PricingService) GetCompetitorsList(req *PricingCompetitorsListRequest) (*PricingCompetitorsListResponse, error) {
	resp := &PricingCompetitorsListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingCompetitorsListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get competitors list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get competitors list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetStrategyList 获取定价策略列表
func (s *PricingService) GetStrategyList(req *PricingStrategyListRequest) (*PricingStrategyListResponse, error) {
	resp := &PricingStrategyListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get strategy list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get strategy list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// CreateStrategy 创建定价策略
func (s *PricingService) CreateStrategy(req *PricingStrategyCreateRequest) (*PricingStrategyCreateResponse, error) {
	resp := &PricingStrategyCreateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyCreateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to create strategy: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("create strategy failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetStrategyInfo 获取定价策略信息
func (s *PricingService) GetStrategyInfo(req *PricingStrategyInfoRequest) (*PricingStrategyInfoResponse, error) {
	resp := &PricingStrategyInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get strategy info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get strategy info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// UpdateStrategy 更新定价策略
func (s *PricingService) UpdateStrategy(req *PricingStrategyUpdateRequest) (*PricingStrategyUpdateResponse, error) {
	resp := &PricingStrategyUpdateResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyUpdateEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to update strategy: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("update strategy failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// AddProductsToStrategy 添加商品到定价策略
func (s *PricingService) AddProductsToStrategy(req *PricingStrategyProductsAddRequest) (*PricingStrategyProductsAddResponse, error) {
	resp := &PricingStrategyProductsAddResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyProductsAddEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to add products to strategy: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("add products to strategy failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetStrategyIDsByProductIDs 根据商品ID获取策略ID
func (s *PricingService) GetStrategyIDsByProductIDs(req *PricingStrategyIDsByProductIDsRequest) (*PricingStrategyIDsByProductIDsResponse, error) {
	resp := &PricingStrategyIDsByProductIDsResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyIDsByProductIDsEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get strategy IDs by product IDs: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get strategy IDs by product IDs failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetStrategyProductsList 获取策略商品列表
func (s *PricingService) GetStrategyProductsList(req *PricingStrategyProductsListRequest) (*PricingStrategyProductsListResponse, error) {
	resp := &PricingStrategyProductsListResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyProductsListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get strategy products list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get strategy products list failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetStrategyProductInfo 获取策略商品信息
func (s *PricingService) GetStrategyProductInfo(req *PricingStrategyProductInfoRequest) (*PricingStrategyProductInfoResponse, error) {
	resp := &PricingStrategyProductInfoResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyProductInfoEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get strategy product info: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get strategy product info failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// DeleteProductsFromStrategy 从策略中删除商品
func (s *PricingService) DeleteProductsFromStrategy(req *PricingStrategyProductsDeleteRequest) (*PricingStrategyProductsDeleteResponse, error) {
	resp := &PricingStrategyProductsDeleteResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyProductsDeleteEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to delete products from strategy: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("delete products from strategy failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// GetStrategyStatus 获取策略状态
func (s *PricingService) GetStrategyStatus(req *PricingStrategyStatusRequest) (*PricingStrategyStatusResponse, error) {
	resp := &PricingStrategyStatusResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyStatusEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get strategy status: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get strategy status failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}

// DeleteStrategy 删除定价策略
func (s *PricingService) DeleteStrategy(req *PricingStrategyDeleteRequest) (*PricingStrategyDeleteResponse, error) {
	resp := &PricingStrategyDeleteResponse{}
	httpResp, err := s.client.R().
		SetBody(req).
		SetResult(resp).
		Post(pricingStrategyDeleteEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to delete strategy: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("delete strategy failed with status code: %d", httpResp.StatusCode())
	}

	return resp, nil
}
