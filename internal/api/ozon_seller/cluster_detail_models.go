package ozon_seller

// ClusterDetailListRequest 获取集群详情列表请求
type ClusterDetailListRequest struct {
	Limit     string            `json:"limit"`            // 限制返回结果数量
	Offset    string            `json:"offset"`           // 偏移量，用于分页
	Filter    *DetailListFilter `json:"filter,omitempty"` // 过滤条件
	ClusterId string            `json:"clusterId"`        // 集群ID
}

// DetailListFilter 详情列表过滤器
type DetailListFilter struct {
	SupplyPeriod string `json:"supplyPeriod"` // 供应周期：7 ONE_WEEK, 14 TWO_WEEKS, 28 FOUR_WEEKS, 56 EIGHT_WEEKS
}

// ClusterDetailListResponse 获取集群详情列表响应
type ClusterDetailListResponse struct {
	Items                []ClusterDetailItem `json:"items"`                // 集群详情项列表
	TotalRows            string              `json:"totalRows"`            // 总行数
	DeliveryNotAvailable []DeliveryIssue     `json:"deliveryNotAvailable"` // 配送不可用信息
}

// ClusterDetailItem 集群详情项
type ClusterDetailItem struct {
	Item                    *ItemInfo      `json:"item"`                    // 商品信息
	Accessibility           *Accessibility `json:"accessibility"`           // 可达性数据
	Discount                *DiscountInfo  `json:"discount"`                // 折扣信息
	OverpaymentFbs          int            `json:"overpaymentFbs"`          // FBS超付
	KeyDeliveryNotAvailable []string       `json:"keyDeliveryNotAvailable"` // 配送不可用键列表
}

// ItemInfo 商品信息
type ItemInfo struct {
	ImgPath             string   `json:"imgPath"`                         // 图片路径
	Name                string   `json:"name"`                            // 商品名称
	Sku                 string   `json:"sku"`                             // SKU编码
	SalesScheme         string   `json:"salesScheme"`                     // 销售方案
	Article             string   `json:"article"`                         // 商品编号
	ItemFeature         []string `json:"itemFeature"`                     // 商品特性
	LocalPercentFbo     int      `json:"localPercentFbo"`                 // FBO本地百分比
	LocalPercentFbs     int      `json:"localPercentFbs"`                 // FBS本地百分比
	SalesSchemeStatic   string   `json:"salesSchemeStatic"`               // 静态销售方案
	AverageDeliveryTime int      `json:"average_delivery_time,omitempty"` // 平均配送时间
	DeliveryTimeStatus  string   `json:"delivery_time_status"`            // 配送时间状态
}

// DeliveryIssue 配送问题
type DeliveryIssue struct {
	Key         string `json:"key"`         // 问题键
	Description string `json:"description"` // 问题描述
}
