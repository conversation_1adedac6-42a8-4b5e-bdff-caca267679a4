package ozon_seller

// ClusterListRequest 获取集群销售数据列表请求
type ClusterListRequest struct {
	Limit  string      `json:"limit"`  // 限制返回结果数量
	Offset string      `json:"offset"` // 偏移量，用于分页
	Filter *ListFilter `json:"filter,omitempty"` // 过滤条件
}

// ListFilter 请求过滤器
type ListFilter struct {
	SupplyPeriod string `json:"supplyPeriod"` // 供应周期：7天ONE_WEEK ，14天TWO_WEEKS，28天FOUR_WEEKS, 56天：EIGHT_WEEKS.
}

// ClusterListResponse 获取集群销售数据列表响应
type ClusterListResponse struct {
	Clusters                []ClusterData `json:"clusters"` // 集群数据列表
	TotalRows               string        `json:"totalRows"` // 总行数
	IsOverpaymentFbsEnabled bool          `json:"isOverpaymentFbsEnabled"` // 是否启用FBS超付
}

// ClusterData 集群销售数据
type ClusterData struct {
	Cluster             *ClusterInfo   `json:"cluster"` // 集群信息
	Accessibility       *Accessibility `json:"accessibility"` // 可达性数据
	Discount            *DiscountInfo  `json:"discount"` // 折扣信息
	LocalPercent        int            `json:"local_percent,omitempty"` // 本地百分比
	NeedItems           string         `json:"needItems"` // 需要商品数量
	TotalItems          string         `json:"totalItems"` // 总商品数量
	SalesScheme         string         `json:"salesScheme"` // 销售方案
	OverpaymentFbs      int            `json:"overpaymentFbs"` // FBS超付
	SalesSchemeStatic   string         `json:"salesSchemeStatic"` // 静态销售方案
	AverageDeliveryTime int            `json:"average_delivery_time"` // 平均配送时间
	DeliveryTimeStatus  string         `json:"delivery_time_status"` // 配送时间状态
}

// ClusterInfo 集群信息
type ClusterInfo struct {
	ID   string `json:"id"`   // 集群ID
	Name string `json:"name"` // 集群名称
}

// Accessibility 可达性数据
type Accessibility struct {
	Accessibility            int     `json:"accessibility"` // 可达性指数
	AccessibilityIndexDelta  int     `json:"accessibilityIndexDelta"` // 可达性指数变化量
	MissedSales              int     `json:"missedSales"` // 错失销售
	AdsRub                   float64 `json:"adsRub"` // 广告费用(卢布)
	Prompt                   string  `json:"prompt"` // 提示信息
	InStockFbo               int     `json:"inStockFbo"` // FBO库存
	InStockFbs               int     `json:"inStockFbs"` // FBS库存
	Transit                  int     `json:"transit"` // 在途数量
	AdsQuantity              float64 `json:"adsQuantity"` // 广告数量
	IdcFbo                   int     `json:"idcFbo"` // FBO库存天数
	IdcFbs                   int     `json:"idcFbs"` // FBS库存天数
	StockAvailDays           int     `json:"stockAvailDays"` // 库存可用天数
	RecommendedSupply        string  `json:"recommendedSupply"` // 推荐供应量
	AccessibilityByDays      int     `json:"accessibilityByDays"` // 按天计算的可达性
	AccessibilityByDaysDelta int     `json:"accessibilityByDaysDelta"` // 按天计算的可达性变化量
	ItemsInShipment          int     `json:"itemsInShipment"` // 运输中的商品数量
}

// DiscountInfo 折扣信息
type DiscountInfo struct {
	IsDiscount           bool `json:"isDiscount"` // 是否有折扣
	DiscountDurationDays int  `json:"discountDurationDays"` // 折扣持续天数
	Discount             int  `json:"discount"` // 折扣金额
	IsException          bool `json:"isException"` // 是否为例外情况
}