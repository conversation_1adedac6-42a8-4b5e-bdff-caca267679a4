package ozon_seller

import (
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	defaultTimeout            = 10 * time.Second // 默认超时时间
	clusterListEndpoint       = "/api/analytics-accessibility/v2/clusters/list" // 集群列表端点
	clusterDetailListEndpoint = "/api/analytics-accessibility/v2/clusters/list/detail" // 集群详情列表端点
)

// Client Ozon Seller API 客户端
type Client struct {
	httpClient *resty.Client // HTTP 请求客户端
	companyID  string        // 公司ID
	cookie     string        // Cookie认证信息
}

// ClientOption 客户端配置选项
type ClientOption func(*Client)

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *Client) {
		c.httpClient.SetTimeout(timeout)
	}
}

// WithCompanyID 设置公司ID
func WithCompanyID(companyID string) ClientOption {
	return func(c *Client) {
		c.companyID = companyID
	}
}

// WithCookie 设置Cookie
func WithCookie(cookie string) ClientOption {
	return func(c *Client) {
		c.cookie = cookie
	}
}

// NewClient 创建新的 Ozon Seller API 客户端
func NewClient(opts ...ClientOption) *Client {
	rc := resty.New()
	rc.SetTimeout(defaultTimeout)
	rc.SetHeader("Host", "seller.ozon.ru")
	rc.SetBaseURL("https://seller.ozon.ru")
	rc.SetHeader("Content-Type", "application/json")
	rc.SetHeader("Accept", "application/json, text/plain, */*")
	rc.SetHeader("x-o3-app-name", "seller-ui")
	//rc.SetHeader("x-o3-language", "zh-Hans")
	
	c := &Client{
		httpClient: rc,
	}

	// 应用自定义选项
	for _, opt := range opts {
		opt(c)
	}

	// 如果提供了companyID，设置相关header
	if c.companyID != "" {
		c.httpClient.SetHeader("x-o3-company-id", c.companyID)
	}

	// 如果提供了cookie，设置Cookie header
	if c.cookie != "" {
		c.httpClient.SetHeader("cookie", c.cookie)
	}

	return c
}

// GetHTTPClient 获取HTTP客户端
func (c *Client) GetHTTPClient() *resty.Client {
	return c.httpClient
}

// GetClusterList 获取集群销售数据列表
func (c *Client) GetClusterList(req *ClusterListRequest) (*ClusterListResponse, error) {
	resp := &ClusterListResponse{}
	httpResp, err := c.httpClient.R().
		SetBody(req).
		SetResult(resp).
		Post(clusterListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cluster list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cluster list failed with status code: %d, body: %s", httpResp.StatusCode(), httpResp.String())
	}

	return resp, nil
}

// GetClusterDetailList 获取集群详情列表
func (c *Client) GetClusterDetailList(req *ClusterDetailListRequest) (*ClusterDetailListResponse, error) {
	resp := &ClusterDetailListResponse{}
	httpResp, err := c.httpClient.R().
		SetBody(req).
		SetResult(resp).
		Post(clusterDetailListEndpoint)

	if err != nil {
		return nil, fmt.Errorf("failed to get cluster detail list: %w", err)
	}

	if !httpResp.IsSuccess() {
		return nil, fmt.Errorf("get cluster detail list failed with status code: %d, body: %s", httpResp.StatusCode(), httpResp.String())
	}

	return resp, nil
}