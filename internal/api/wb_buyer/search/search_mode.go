package search

import "time"

// 搜索接口主响应结构体
// 包含搜索元数据、状态、数据等
type RespSearch struct {
	Metadata       SearchMetadata `json:"metadata"`       // 搜索元信息（如分页、查询等）
	State          int            `json:"state"`          // 状态码，具体业务含义需结合接口文档
	Version        int            `json:"version"`        // 响应版本号
	PayloadVersion int            `json:"payloadVersion"` // 负载版本号
	Data           SearchData     `json:"data"`           // 实际搜索数据（产品列表、总数等）
	Code           int            `json:"code"`           // 接口返回码（如200、500等）
}

// 搜索结果核心数据
type SearchData struct {
	Products []SearchProduct `json:"products"` // 产品列表
	Total    int             `json:"total"`    // 总产品数
}

// 单个商品的详细信息
type SearchProduct struct {
	SKU             string        `json:"sku"`                      // 产品编号
	Keyword         string        `json:"keyword"`                  // 关键词
	PlatformId      string        `json:"platform_id"`              // 平台ID
	Rank            int           `json:"rank"`                     // 产品位置
	InProcessAt     time.Time     `json:"in_process_at"`            // 爬取回的时间
	Time1           int           `json:"time1"`                    // 时间戳或时间相关字段
	Time2           int           `json:"time2"`                    // 时间戳或时间相关字段
	Wh              int           `json:"wh"`                       // 仓库ID或相关标识
	Dtype           int           `json:"dtype"`                    // 数据类型标识
	Dist            int           `json:"dist"`                     // 距离或分布相关字段
	Id              int           `json:"id"`                       // 商品ID
	Root            int           `json:"root"`                     // 根类目ID或父级ID
	KindId          int           `json:"kindId"`                   // 商品类型ID
	Brand           string        `json:"brand"`                    // 品牌名
	BrandId         int           `json:"brandId"`                  // 品牌ID
	SiteBrandId     int           `json:"siteBrandId"`              // 站点品牌ID
	Colors          []SearchColor `json:"colors"`                   // 商品颜色列表
	SubjectId       int           `json:"subjectId"`                // 主题ID或类目ID
	SubjectParentId int           `json:"subjectParentId"`          // 父主题ID或父类目ID
	Name            string        `json:"name"`                     // 商品名称
	Entity          string        `json:"entity"`                   // 实体类型
	MatchId         int           `json:"matchId"`                  // 匹配ID
	Supplier        string        `json:"supplier"`                 // 供应商名称
	SupplierId      int           `json:"supplierId"`               // 供应商ID
	SupplierRating  float64       `json:"supplierRating"`           // 供应商评分
	SupplierFlags   int           `json:"supplierFlags"`            // 供应商标志位
	Pics            int           `json:"pics"`                     // 图片数量
	Rating          int           `json:"rating"`                   // 评分
	ReviewRating    float64       `json:"reviewRating"`             // 评论评分
	NmReviewRating  float64       `json:"nmReviewRating"`           // 新模型评论评分
	Feedbacks       int           `json:"feedbacks"`                // 评论数
	NmFeedbacks     int           `json:"nmFeedbacks"`              // 新模型评论数
	Volume          int           `json:"volume"`                   // 体积或销量
	ViewFlags       int           `json:"viewFlags"`                // 展示标志位
	Sizes           []SearchSize  `json:"sizes"`                    // 商品尺码列表
	TotalQuantity   int           `json:"totalQuantity"`            // 总库存数量
	Log             *SearchLog    `json:"log,omitempty"`            // 广告/日志信息
	Logs            string        `json:"logs,omitempty"`           // 日志字符串
	Meta            *SearchMeta   `json:"meta"`                     // 商品元信息
	PanelPromoId    int           `json:"panelPromoId,omitempty"`   // 促销面板ID
	PromoTextCard   string        `json:"promoTextCard,omitempty"`  // 卡片促销文案
	PromoTextCat    string        `json:"promoTextCat,omitempty"`   // 类目促销文案
	IsNew           bool          `json:"isNew,omitempty"`          // 是否新品
	FeedbackPoints  int           `json:"feedbackPoints,omitempty"` // 评论分数
}

// 搜索元信息，主要用于分页、查询等
type SearchMetadata struct {
	CatalogType  string      `json:"catalog_type"`  // 目录类型
	CatalogValue string      `json:"catalog_value"` // 目录值
	Normquery    string      `json:"normquery"`     // 标准化后的查询词
	SearchResult interface{} `json:"search_result"` // 搜索结果（类型不定）
	Name         string      `json:"name"`          // 名称
	Rmi          string      `json:"rmi"`           // 相关标识
	Title        string      `json:"title"`         // 标题
	Rs           int         `json:"rs"`            // 每页返回的商品数量
	Qv           string      `json:"qv"`            // 查询版本或标识
}

// 商品颜色信息
type SearchColor struct {
	Name string `json:"name"` // 颜色名称
	Id   int    `json:"id"`   // 颜色ID
}

// 商品尺码信息
type SearchSize struct {
	Name           string      `json:"name"`           // 尺码名称
	OrigName       string      `json:"origName"`       // 原始尺码名称
	Rank           int         `json:"rank"`           // 排名
	OptionId       int         `json:"optionId"`       // 选项ID
	Wh             int         `json:"wh"`             // 仓库ID
	Time1          int         `json:"time1"`          // 时间1
	Time2          int         `json:"time2"`          // 时间2
	Dtype          int         `json:"dtype"`          // 数据类型
	Price          SearchPrice `json:"price"`          // 价格信息
	SaleConditions int64       `json:"saleConditions"` // 销售条件
	Payload        string      `json:"payload"`        // 负载数据
}

// 商品价格信息
type SearchPrice struct {
	Basic     int64 `json:"basic"`     // 基础价格
	Product   int64 `json:"product"`   // 产品价格
	Total     int64 `json:"total"`     // 总价
	Logistics int64 `json:"logistics"` // 物流费用
	Return    int64 `json:"return"`    // 退款金额
}

// 广告/日志相关信息
type SearchLog struct {
	Cpm           int    `json:"cpm"`           // 千次展示费用
	Promotion     int    `json:"promotion"`     // 促销ID
	PromoPosition int    `json:"promoPosition"` // 促销位置
	Position      int    `json:"position"`      // 展示位置
	AdvertId      int    `json:"advertId"`      // 广告ID
	Tp            string `json:"tp"`            // 类型
}

// 商品元信息
type SearchMeta struct {
	Tokens   []interface{} `json:"tokens"`   // 分词或标签
	PresetId int           `json:"presetId"` // 预设ID
}
