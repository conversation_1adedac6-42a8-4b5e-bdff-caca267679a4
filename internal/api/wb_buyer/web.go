package wb_buyer

import (
	"encoding/json"
	"errors"
	"fmt"
	"lens/internal/api/wb_buyer/search"
	"log"
	"net/http"

	"github.com/go-resty/resty/v2"
	"github.com/gogf/gf/v2/util/gconv"
)

type Web interface {
	GetSearchInfoByKeyword(keyword string, page int) ([]search.SearchProduct, error)
	GetRecommendSearch(query string, page int) ([]search.SearchProduct, error)
}

type web struct {
	*resty.Client
}

func Init() Web {
	userAgent := "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.3"
	client := resty.New()
	client.SetHeader("user-agent", userAgent)
	client.SetProxy("socks5://lens:ls3903850@185.22.152.62:23481")
	return &web{
		client,
	}
}

func (o *web) GetSearchInfoByKeyword(keyword string, page int) ([]search.SearchProduct, error) {
	var allProducts []search.SearchProduct
	currentPage := page
	for {
		params := map[string]string{
			"ab_testing":         "false",
			"appType":            "64",
			"curr":               "rub",
			"dest":               "-1257786",
			"hide_dtype":         "13",
			"lang":               "ru",
			"page":               gconv.String(currentPage),
			"query":              keyword,
			"resultset":          "catalog",
			"sort":               "popular",
			"spp":                "30",
			"suppressSpellcheck": "false",
		}
		resp, err := o.R().SetQueryParams(params).Get(UrlGetSearchInfoByKeyword)
		if err != nil {
			log.Printf("获取关键词信息请求失败: %v", err)
			return nil, err
		}
		if resp.StatusCode() != http.StatusOK {
			log.Printf("获取关键词信息请求失败: 状态码 %d", resp.StatusCode())
			return nil, errors.New("请求失败")
		}
		var result search.RespSearch
		//fmt.Println(string(resp.Body())) // 如需调试可打开
		err = json.Unmarshal(resp.Body(), &result)
		if err != nil {
			log.Printf("解析关键词信息响应失败: %v", err)
			return nil, err
		}
		if result.Code == 500 {
			return allProducts, errors.New("500")
		}
		allProducts = append(allProducts, result.Data.Products...)
		// 判断是否还有下一页
		if len(result.Data.Products) < result.Metadata.Rs {
			break // 当前页产品数小于每页数量，说明已到最后一页
		}
		if (currentPage * result.Metadata.Rs) >= result.Data.Total {
			break // 已经拉取完所有产品
		}
		if currentPage >= 10 {
			break // 限制只拉取前10页的数据
		}
		currentPage++
	}
	//log.Printf("关键词信息 共获取产品数: %d", len(allProducts))
	return allProducts, nil
}

func (o *web) GetRecommendSearch(query string, page int) ([]search.SearchProduct, error) {
	params := map[string]string{
		"appType":   "2",
		"dest":      "-59202",
		"lang":      "ru",
		"page":      gconv.String(page),
		"query":     query,
		"resultset": "catalog",
	}

	resp, err := o.R().SetQueryParams(params).Get(UrlGetRecommendSearch)
	if err != nil {
		log.Printf("获取推荐搜索信息请求失败: %v", err)
		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		log.Printf("获取推荐搜索信息请求失败: 状态码 %d", resp.StatusCode())
		return nil, errors.New("请求失败")
	}

	fmt.Println(string(resp.Body()))

	var result search.RespSearch
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		log.Printf("解析推荐搜索信息响应失败: %v", err)
		return nil, err
	}

	if result.Code == 500 {
		return nil, errors.New("500")
	}

	return result.Data.Products, nil
}
