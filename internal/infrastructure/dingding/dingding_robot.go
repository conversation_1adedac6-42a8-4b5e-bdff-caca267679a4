package dingding

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

// DingTalkRobot 钉钉机器人客户端
type DingTalkRobot struct {
	accessToken string
	secret      string
	baseURL     string
}

// NewDingTalkRobot 创建新的钉钉机器人实例
func NewDingTalkRobot(accessToken, secret string) (*DingTalkRobot, error) {
	if accessToken == "" || secret == "" {
		return nil, fmt.Errorf("missing DingTalk credentials")
	}

	return &DingTalkRobot{
		accessToken: accessToken,
		secret:      secret,
		baseURL:     "https://oapi.dingtalk.com/robot/send",
	}, nil
}

// generateSignature 生成加签参数
func (d *DingTalkRobot) generateSignature() (timestamp string, sign string) {
	timestamp = strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	stringToSign := fmt.Sprintf("%s\n%s", timestamp, d.secret)

	h := hmac.New(sha256.New, []byte(d.secret))
	h.Write([]byte(stringToSign))
	sign = url.QueryEscape(base64.StdEncoding.EncodeToString(h.Sum(nil)))
	return
}

// Message 消息结构体
type Message struct {
	MsgType  string                 `json:"msgtype"`
	Content  map[string]interface{} `json:"-"`
	At       *AtConfig              `json:"at,omitempty"`
	Text     *TextMessage           `json:"text,omitempty"`
	Link     *LinkMessage           `json:"link,omitempty"`
	Markdown *MarkdownMessage       `json:"markdown,omitempty"`
}

// AtConfig @功能配置
type AtConfig struct {
	AtUserIds []string `json:"atUserIds,omitempty"`
	IsAtAll   bool     `json:"isAtAll"`
}

// TextMessage 文本消息
type TextMessage struct {
	Content string `json:"content"`
}

// LinkMessage 链接消息
type LinkMessage struct {
	Title      string `json:"title"`
	Text       string `json:"text"`
	PicURL     string `json:"picUrl"`
	MessageURL string `json:"messageUrl"`
}

// MarkdownMessage markdown消息
type MarkdownMessage struct {
	Title string `json:"title"`
	Text  string `json:"text"`
}

// Send 发送消息到钉钉群
func (d *DingTalkRobot) Send(message Message) error {
	timestamp, sign := d.generateSignature()

	// 构建URL
	u, err := url.Parse(d.baseURL)
	if err != nil {
		return fmt.Errorf("invalid base URL: %v", err)
	}

	q := u.Query()
	q.Set("access_token", d.accessToken)
	q.Set("timestamp", timestamp)
	q.Set("sign", sign)
	u.RawQuery = q.Encode()

	// 准备请求体
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %v", err)
	}

	// 发送请求
	resp, err := http.Post(u.String(), "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("request failed with status code: %d", resp.StatusCode)
	}

	return nil
}

// MessageBuilder 消息构造器
type MessageBuilder struct{}

// NewTextMessage 创建文本消息
func (MessageBuilder) NewTextMessage(content string) Message {
	return Message{
		MsgType: "text",
		Text: &TextMessage{
			Content: content,
		},
	}
}

// NewLinkMessage 创建链接消息
func (MessageBuilder) NewLinkMessage(title, text, picURL, messageURL string) Message {
	return Message{
		MsgType: "link",
		Link: &LinkMessage{
			Title:      title,
			Text:       text,
			PicURL:     picURL,
			MessageURL: messageURL,
		},
	}
}

// NewMarkdownMessage 创建markdown消息
func (MessageBuilder) NewMarkdownMessage(title, text string) Message {
	return Message{
		MsgType: "markdown",
		Markdown: &MarkdownMessage{
			Title: title,
			Text:  text,
		},
	}
}
