package rocket_mq

import (
	"fmt"
	"strings"
	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/producer"
	"log"
	"sync"
)

// RocketMQClient RocketMQ客户端单例
type RocketMQClient struct {
	producer  rocketmq.Producer
	groupName string
	mu        sync.RWMutex
}

var (
	instance *RocketMQClient
	once     sync.Once
	mu       sync.Mutex
)

// GetInstance 获取 RocketMQ 客户端单例
func GetInstance() *RocketMQClient {
	once.Do(func() {
		instance = &RocketMQClient{}
	})
	return instance
}

// GetProducer 获取生产者实例，如果不可用则尝试重新初始化
func (c *RocketMQClient) GetProducer() rocketmq.Producer {
	c.mu.RLock()
	producer := c.producer
	c.mu.RUnlock()

	if producer == nil {
		// 如果producer为空，尝试重新初始化
		c.mu.Lock()
		defer c.mu.Unlock()

		if c.producer == nil && c.groupName != "" {
			err := c.InitProducer(c.groupName)
			if err != nil {
				log.Printf("重新初始化Producer失败: %v", err)
				return nil
			}
		}
		producer = c.producer
	}

	return producer
}

// defaultRocketMQConfig 默认RocketMQ配置结构体
type defaultRocketMQConfig struct {
	nameserver string
	group      string
}

func (d *defaultRocketMQConfig) RocketMQNameserver() string {
	return d.nameserver
}

func (d *defaultRocketMQConfig) RocketMQGroup() string {
	return d.group
}

// InitProducer 初始化生产者（使用默认配置）
func (c *RocketMQClient) InitProducer(groupName string) error {
	// 创建默认配置
	defaultCfg := &defaultRocketMQConfig{
		nameserver: "139.9.49.228:9876,139.9.49.228:18876,139.9.49.228:19876",
		group:      groupName,
	}
	
	return c.InitProducerWithConfig(defaultCfg)
}

// RocketMQConfig RocketMQ配置接口
type RocketMQConfig interface {
	RocketMQNameserver() string
	RocketMQGroup() string
}

// InitProducerWithConfig 使用配置初始化生产者
func (c *RocketMQClient) InitProducerWithConfig(cfg RocketMQConfig) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	groupName := cfg.RocketMQGroup()
	if groupName == "" {
		groupName = "default_producer_group"
	}

	// 如果已经初始化且 groupName 相同，直接返回
	if c.producer != nil && c.groupName == groupName {
		return nil
	}

	// 如果已存在producer，先关闭
	if c.producer != nil {
		err := c.producer.Shutdown()
		if err != nil {
			return err
		}
	}

	// 解析 NameServer 地址
	nameServers := []string{}
	for _, ns := range strings.Split(cfg.RocketMQNameserver(), ",") {
		nameServers = append(nameServers, strings.TrimSpace(ns))
	}

	producer, err := rocketmq.NewProducer(
		producer.WithNameServer(nameServers),
		producer.WithRetry(3), // 减少重试次数，避免阻塞太久
		producer.WithGroupName(groupName),
		producer.WithSendMsgTimeout(3000), // 20秒超时
		producer.WithInstanceName("monitor_producer"),
	)

	if err != nil {
		return fmt.Errorf("创建Producer失败: %v", err)
	}

	err = producer.Start()
	if err != nil {
		return fmt.Errorf("启动Producer失败: %v", err)
	}

	c.producer = producer
	c.groupName = groupName
	return nil
}

// Shutdown 关闭生产者连接
func (c *RocketMQClient) Shutdown() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.producer != nil {
		err := c.producer.Shutdown()
		if err != nil {
			return err
		}
		c.producer = nil
		c.groupName = ""
	}
	return nil
}
