package main

import (
	"encoding/json"
	"lens/internal/api/wb"
	"testing"
)

// TestNMReportDetailHistoryResponseUnmarshal 测试 JSON 反序列化
func TestNMReportDetailHistoryResponseUnmarshal(t *testing.T) {
	// 实际的 API 响应 JSON
	jsonResponse := `{
		"data": [
			{
				"nmID": 254778783,
				"imtName": "Торшер напольный Maghome, бежевый, с E27 лампочка",
				"vendorCode": "MNT101-1",
				"history": [
					{
						"dt": "2025-08-04",
						"openCardCount": 534,
						"addToCartCount": 63,
						"ordersCount": 5,
						"ordersSumRub": 18375,
						"buyoutsCount": 0,
						"buyoutsSumRub": 0,
						"buyoutPercent": 0,
						"addToCartConversion": 12,
						"cartToOrderConversion": 8
					}
				]
			},
			{
				"nmID": 253486274,
				"imtName": "Люстра потолочная светодиодная, 30cm, 24Вт",
				"vendorCode": "MLN101-2",
				"history": [
					{
						"dt": "2025-08-04",
						"openCardCount": 335,
						"addToCartCount": 25,
						"ordersCount": 4,
						"ordersSumRub": 7156,
						"buyoutsCount": 0,
						"buyoutsSumRub": 0,
						"buyoutPercent": 0,
						"addToCartConversion": 7,
						"cartToOrderConversion": 16
					}
				]
			}
		],
		"error": false,
		"errorText": "",
		"additionalErrors": null
	}`

	// 尝试反序列化
	var response wbapi.NMReportDetailHistoryResponse
	err := json.Unmarshal([]byte(jsonResponse), &response)
	
	if err != nil {
		t.Fatalf("JSON 反序列化失败: %v", err)
	}

	// 验证基本字段
	if response.Error {
		t.Error("Error 字段应该为 false")
	}

	if response.ErrorText != "" {
		t.Error("ErrorText 字段应该为空")
	}

	// 验证数据数组
	if len(response.Data) != 2 {
		t.Errorf("期望 2 个商品，实际得到 %d 个", len(response.Data))
	}

	// 验证第一个商品
	firstCard := response.Data[0]
	if firstCard.NMID != 254778783 {
		t.Errorf("期望 NMID 为 254778783，实际为 %d", firstCard.NMID)
	}

	if firstCard.ImtName != "Торшер напольный Maghome, бежевый, с E27 лампочка" {
		t.Errorf("商品名称不匹配: %s", firstCard.ImtName)
	}

	if firstCard.VendorCode != "MNT101-1" {
		t.Errorf("期望 VendorCode 为 MNT101-1，实际为 %s", firstCard.VendorCode)
	}

	// 验证历史数据
	if len(firstCard.History) != 1 {
		t.Errorf("期望 1 个历史记录，实际得到 %d 个", len(firstCard.History))
	}

	firstHistory := firstCard.History[0]
	if firstHistory.Dt != "2025-08-04" {
		t.Errorf("期望日期为 2025-08-04，实际为 %s", firstHistory.Dt)
	}

	if firstHistory.OpenCardCount != 534 {
		t.Errorf("期望浏览量为 534，实际为 %d", firstHistory.OpenCardCount)
	}

	if firstHistory.AddToCartCount != 63 {
		t.Errorf("期望加购数量为 63，实际为 %d", firstHistory.AddToCartCount)
	}

	if firstHistory.OrdersCount != 5 {
		t.Errorf("期望订单数量为 5，实际为 %d", firstHistory.OrdersCount)
	}

	if firstHistory.OrdersSumRub != 18375 {
		t.Errorf("期望订单金额为 18375，实际为 %d", firstHistory.OrdersSumRub)
	}

	if firstHistory.AddToCartConversion != 12 {
		t.Errorf("期望加购转化率为 12，实际为 %d", firstHistory.AddToCartConversion)
	}

	if firstHistory.CartToOrderConversion != 8 {
		t.Errorf("期望购物车转化率为 8，实际为 %d", firstHistory.CartToOrderConversion)
	}

	// 验证第二个商品
	secondCard := response.Data[1]
	if secondCard.NMID != 253486274 {
		t.Errorf("期望第二个商品 NMID 为 253486274，实际为 %d", secondCard.NMID)
	}

	t.Logf("✅ JSON 反序列化测试通过")
	t.Logf("📦 成功解析 %d 个商品", len(response.Data))
	t.Logf("🆔 第一个商品 ID: %d", firstCard.NMID)
	t.Logf("📦 第一个商品名称: %s", firstCard.ImtName)
	t.Logf("📊 第一个商品历史记录数: %d", len(firstCard.History))
}

// TestEmptyResponse 测试空响应
func TestEmptyResponse(t *testing.T) {
	jsonResponse := `{
		"data": [],
		"error": false,
		"errorText": "",
		"additionalErrors": null
	}`

	var response wbapi.NMReportDetailHistoryResponse
	err := json.Unmarshal([]byte(jsonResponse), &response)
	
	if err != nil {
		t.Fatalf("空响应 JSON 反序列化失败: %v", err)
	}

	if len(response.Data) != 0 {
		t.Errorf("期望空数组，实际得到 %d 个元素", len(response.Data))
	}

	t.Logf("✅ 空响应测试通过")
}

// TestErrorResponse 测试错误响应
func TestErrorResponse(t *testing.T) {
	jsonResponse := `{
		"data": [],
		"error": true,
		"errorText": "Invalid token",
		"additionalErrors": [
			{
				"field": "token",
				"description": "Token is invalid or expired"
			}
		]
	}`

	var response wbapi.NMReportDetailHistoryResponse
	err := json.Unmarshal([]byte(jsonResponse), &response)
	
	if err != nil {
		t.Fatalf("错误响应 JSON 反序列化失败: %v", err)
	}

	if !response.Error {
		t.Error("Error 字段应该为 true")
	}

	if response.ErrorText != "Invalid token" {
		t.Errorf("期望错误信息为 'Invalid token'，实际为 '%s'", response.ErrorText)
	}

	t.Logf("✅ 错误响应测试通过")
	t.Logf("❌ 错误信息: %s", response.ErrorText)
}
