package main

import (
	"lens/internal/config"
	"lens/internal/service"
	"testing"
	"time"
)

// TestDailyReportService 测试每日报告服务
func TestDailyReportService(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		WBHubApiKey:         "test-api-key",
		DingTalkAccessToken: "test-access-token",
		DingTalkSecret:      "test-secret",
		MonitorInterval:     5 * time.Minute,
	}

	// 创建广播通道
	broadcast := make(chan []byte, 100)

	// 创建产品监控服务
	service := service.NewProductMonitorService(5*time.Minute, broadcast, cfg)

	// 测试启动每日报告服务
	t.Log("启动每日报告服务...")
	service.StartDailyReportService()

	// 等待一小段时间确保服务启动
	time.Sleep(1 * time.Second)

	// 测试手动触发报告
	t.Log("手动触发每日报告...")
	service.TriggerDailyReport()

	// 等待报告生成完成
	time.Sleep(5 * time.Second)

	// 停止服务
	t.Log("停止每日报告服务...")
	service.StopDailyReportService()

	t.Log("每日报告服务测试完成")
}

// TestTimeZoneCalculation 测试时区计算
func TestTimeZoneCalculation(t *testing.T) {
	// 测试中国时间和莫斯科时间的转换
	chinaLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		t.Fatalf("加载中国时区失败: %v", err)
	}

	moscowLocation, err := time.LoadLocation("Europe/Moscow")
	if err != nil {
		t.Fatalf("加载莫斯科时区失败: %v", err)
	}

	// 模拟中国时间早上8点
	chinaTime := time.Date(2024, 1, 15, 8, 0, 0, 0, chinaLocation)
	t.Logf("中国时间: %s", chinaTime.Format("2006-01-02 15:04:05 MST"))

	// 转换为莫斯科时间
	moscowTime := chinaTime.In(moscowLocation)
	t.Logf("对应莫斯科时间: %s", moscowTime.Format("2006-01-02 15:04:05 MST"))

	// 计算莫斯科时间的昨天
	yesterday := moscowTime.AddDate(0, 0, -1)
	dateFrom := yesterday.Format("2006-01-02")
	t.Logf("统计日期 (莫斯科时间昨天): %s", dateFrom)
}
