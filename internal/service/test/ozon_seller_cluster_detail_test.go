package main

import (
	"fmt"
	"lens/internal/api/ozon_seller"
	"testing"
)

func TestGetClusterDetailList(t *testing.T) {
	// 创建 Ozon Seller API 客户端
	// 注意：这里需要替换为实际的 Cookie
	cookie := `__Secure-ab-group=62; __Secure-ext_xcid=8c1c81b170445fdc51713ca8dba66637; rfuid=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; xcid=8c1c81b170445fdc51713ca8dba66637; sc_company_id=2206838; contentId=2206838; TS01f9fe57=0187c00a186611bd73d3fc52cb510e3ef5bd5d4186d54f31ce5e196ebac9e7593b9b3d0de77b12e8947da54511a50b769facd0a83f; TS015d2969=0187c00a186611bd73d3fc52cb510e3ef5bd5d4186d54f31ce5e196ebac9e7593b9b3d0de77b12e8947da54511a50b769facd0a83f; TS013595b9=0187c00a18e03c5d52b197528e27d4142edc66a8cf01e6b63658dec430bb126bafc5de836f18996485c27230f750a4d4035dd183b9; TS0149423d=0187c00a18388f67485b10a85ede866f7a2e862ca48811ea10bbea48734356c1d148a9b239b6b23493e1984d79a1cc74cbc5ed8d59; TS018529d3=0187c00a18c890628bc44c471510060e7adf002efb4de072a580a7b8fb9f6dab15af741fa492ba98ded01a245105f06deeba41a700; TS0121feed=0187c00a18c890628bc44c471510060e7adf002efb4de072a580a7b8fb9f6dab15af741fa492ba98ded01a245105f06deeba41a700; guest=true; TSDK_trackerSessionId=ab06b44b-1489-fc5f-c0b5; ob_theme=SYSTEM; __Secure-user-id=*********; bacntid=4579365; abt_data=7.QkLqKozZ9A8fJcG5bdUscqcYqSHi6eG5ajUr63akCCqs4uZv3VNyTTYZjLa28s0miCG8W-G2rJSNxDj4tay6ayAwuALRfaLkO-znGVGno2JzJLjCxbL4VbSVU8YQosSM5CUk8Q7lwX-nYhhviBGj4PJeWlAh_FWtspzoFnGON1C0AsdpOfLPUIvm3Ompr1wz4kCaQHxxRxKiS-Jr0RXNftDh_05b52WQfgTfwpRieBWF8BHe3NJ9mBDde2RmblXUZ7BLJfKcJIj_9FpD8KRmc5k9i1bIcjVEdwhb6jaqIIMb-pVGMgVGmrFbDteWalwp-RUUDzhYgzVL8zjA6m8inI67A6feY3rd9P-ftKJxjfihVJGPNfi9J_a1e2oh4zHr2ptoKxkckeKGvTvzIsI_rZ79IiFC2dDjIdE4923H11ZRn8pW32gCke8fu9e5GUw8XCIkSgp-JtRohIi_bNKHldhNwi9h8QqgzshDXtfmokrIAMe7HFrDpsT5QMO-bLalb4etz7MuyeMIUwMZZ0gAryznrX8rWKhp4L8R-80CoNrOCyZYOCxXKhlL_QCPDnMbqjMNag; __Secure-refresh-token=8.*********.z4sgMz4HRbOY4JazSYzsRQ.62.AUV4mM2v8IATdyDKsF4Dh702LTEjtYHjBQ0cd-sKXAI_zcEeb632-KCxJFFsG-lCwFl6zGoSCUtHrB96BccvdG7ipTyXtY1ofvnYcuetPXk84NsOCJvWfHu1JT8I_7MCSw.20240802162746.20250726174505.PCsyIDoCBNGGveIHFa0RrOW1LIR671ZXhjTUHGy8Hto.1ecbae480c865e743; __Secure-access-token=8.*********.z4sgMz4HRbOY4JazSYzsRQ.62.AUV4mM2v8IATdyDKsF4Dh702LTEjtYHjBQ0cd-sKXAI_zcEeb632-KCxJFFsG-lCwFl6zGoSCUtHrB96BccvdG7ipTyXtY1ofvnYcuetPXk84NsOCJvWfHu1JT8I_7MCSw.20240802162746.20250726174505.jOYr_n5FewYNFW7hdeq7y84etv_wZe7hQCSzJldF9uU.12551f8a917bff4b6; x-o3-language=zh-Hans; abt_data=7._brI2znjnKF8-NmuytfQ7aD-47dYJiJ7EIA28B1ljiXTShLkrKRr3o0QIzl0JPMqRL-zEh8Jm1Kgn4sn2bHlCRODolXQ6QviBPAg2Hl7A8tQsmnMJ8bJ9VcbCJPtcPMyVljo61u45MROJc5hVm4JGC5S8QTwTqemcb9UB_mZ0tRWQlN8hSdo9mGOQQNc3hjpxX0Q4X-kbNLP-7xqWjWaZzJiqlv5QI8aS7dbUaR1o4DbLezWOdtRQ7ctIJFMTheEY-D6LoNtS9d0SV8GN03c9D79in2k_MW1z-3FiEoZVma3XB5oep8tKruiwCRKPjnm59bLuPH9zM4_nyA_euHbdDsNqpF2FORHjdeOJ4KJlEXhOajVbFLqC8to7jNNU1I2O3kNwdL-vAf9Mdb8n6K-6POgmMrDLH6rQxgfMtzRaFy-dS6U1KgJDA8vu6am80UC_uXO3zO0zHseuCwXNnACP3Qn6byE6cmVTMZaucxp4QamOgjelGgLXqKP7VIL58ybMkQS9VMzRxbpbPhPKwgC0F52SNe_WICccKyYRDf60-8Ao3770ydRen-CSXk9bv2bLxxubA; __Secure-ETC=1bef2d24b3f10e2414366bbcc6d5b2ae`

	client := ozon_seller.NewClient(
		ozon_seller.WithCompanyID("2206838"),
		ozon_seller.WithCookie(cookie),
	)

	// 创建请求参数
	req := &ozon_seller.ClusterDetailListRequest{
		Limit:  "50",
		Offset: "0",
		Filter: &ozon_seller.DetailListFilter{
			SupplyPeriod: "ONE_WEEK",
		},
		ClusterId: "4002",
	}

	// 调用API
	resp, err := client.GetClusterDetailList(req)
	if err != nil {
		t.Fatalf("获取集群详情列表失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印结果
	fmt.Println("\n=== 集群详情列表 ===")
	fmt.Printf("总行数: %s\n", resp.TotalRows)
	fmt.Printf("商品数量: %d\n", len(resp.Items))

	// 打印商品详细信息
	for i, item := range resp.Items {
		fmt.Printf("\n商品 #%d:\n", i+1)
		if item.Item != nil {
			fmt.Printf("- 商品名称: %s\n", item.Item.Name)
			fmt.Printf("- SKU: %s\n", item.Item.Sku)
			fmt.Printf("- 商品编号: %s\n", item.Item.Article)
			fmt.Printf("- 销售方案: %s\n", item.Item.SalesScheme)
		}

		if item.Accessibility != nil {
			fmt.Printf("- 可达性指数: %d\n", item.Accessibility.Accessibility)
			fmt.Printf("- 错失销售: %d\n", item.Accessibility.MissedSales)
			fmt.Printf("- 广告费用(卢布): %.2f\n", item.Accessibility.AdsRub)
			fmt.Printf("- FBO库存: %d\n", item.Accessibility.InStockFbo)
			fmt.Printf("- 在途数量: %d\n", item.Accessibility.Transit)
		}

		fmt.Printf("- FBS超付: %d\n", item.OverpaymentFbs)
		fmt.Printf("- 配送不可用键数量: %d\n", len(item.KeyDeliveryNotAvailable))
	}

	// 打印配送不可用信息
	fmt.Printf("\n=== 配送不可用原因 ===\n")
	fmt.Printf("配送问题数量: %d\n", len(resp.DeliveryNotAvailable))
	for i, issue := range resp.DeliveryNotAvailable {
		fmt.Printf("\n问题 #%d:\n", i+1)
		fmt.Printf("- 键: %s\n", issue.Key)
		fmt.Printf("- 描述: %s\n", issue.Description)
	}
}
