package main

import (
	"encoding/json"
	"fmt"
	"lens/internal/api/ozon_seller"
	"log"
	"math"
	"os"
	"sort"
	"strconv"
	"testing"

	"github.com/xuri/excelize/v2"
)

// ProductInfo 商品信息
type ProductInfo struct {
	Article        string  `json:"article"`
	WarehouseStock int     `json:"warehouse_stock"`   // 中转仓库存
	QuantityPerBox int     `json:"quantity_per_box"`  // 单箱数量
	WeightPerBoxKg float64 `json:"weight_per_box_kg"` // 当箱重量
}

// ClusterDetailStats 集群详情统计
type ClusterDetailStats struct {
	ClusterName         string
	Article             string
	InStockFbo          int
	Transit             int
	MissedSales         int
	AdsRub              float64
	StockAvailDays      int
	RecommendedSupply   string
	WarehouseStock      int     // 中转仓库存
	QuantityPerBox      int     // 单箱数量
	WeightPerBoxKg      float64 // 单箱重量(kg)
	RequiredBoxes       int     // 需要备货箱数(取整)
	TotalWeightRequired float64 // 总重量需求(kg)
}

func TestGetClusterAndList(t *testing.T) {
	// 创建 Ozon Seller API 客户端
	// 注意：这里需要替换为实际的 Cookie
	cookie := `__Secure-ab-group=62; __Secure-ext_xcid=8c1c81b170445fdc51713ca8dba66637; rfuid=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; xcid=8c1c81b170445fdc51713ca8dba66637; sc_company_id=2206838; contentId=2206838; TS01f9fe57=0187c00a186611bd73d3fc52cb510e3ef5bd5d4186d54f31ce5e196ebac9e7593b9b3d0de77b12e8947da54511a50b769facd0a83f; TS015d2969=0187c00a186611bd73d3fc52cb510e3ef5bd5d4186d54f31ce5e196ebac9e7593b9b3d0de77b12e8947da54511a50b769facd0a83f; TS013595b9=0187c00a18e03c5d52b197528e27d4142edc66a8cf01e6b63658dec430bb126bafc5de836f18996485c27230f750a4d4035dd183b9; TS0149423d=0187c00a18388f67485b10a85ede866f7a2e862ca48811ea10bbea48734356c1d148a9b239b6b23493e1984d79a1cc74cbc5ed8d59; TS018529d3=0187c00a18c890628bc44c471510060e7adf002efb4de072a580a7b8fb9f6dab15af741fa492ba98ded01a245105f06deeba41a700; TS0121feed=0187c00a18c890628bc44c471510060e7adf002efb4de072a580a7b8fb9f6dab15af741fa492ba98ded01a245105f06deeba41a700; guest=true; TSDK_trackerSessionId=ab06b44b-1489-fc5f-c0b5; ob_theme=SYSTEM; __Secure-user-id=*********; bacntid=4579365; abt_data=7.QkLqKozZ9A8fJcG5bdUscqcYqSHi6eG5ajUr63akCCqs4uZv3VNyTTYZjLa28s0miCG8W-G2rJSNxDj4tay6ayAwuALRfaLkO-znGVGno2JzJLjCxbL4VbSVU8YQosSM5CUk8Q7lwX-nYhhviBGj4PJeWlAh_FWtspzoFnGON1C0AsdpOfLPUIvm3Ompr1wz4kCaQHxxRxKiS-Jr0RXNftDh_05b52WQfgTfwpRieBWF8BHe3NJ9mBDde2RmblXUZ7BLJfKcJIj_9FpD8KRmc5k9i1bIcjVEdwhb6jaqIIMb-pVGMgVGmrFbDteWalwp-RUUDzhYgzVL8zjA6m8inI67A6feY3rd9P-ftKJxjfihVJGPNfi9J_a1e2oh4zHr2ptoKxkckeKGvTvzIsI_rZ79IiFC2dDjIdE4923H11ZRn8pW32gCke8fu9e5GUw8XCIkSgp-JtRohIi_bNKHldhNwi9h8QqgzshDXtfmokrIAMe7HFrDpsT5QMO-bLalb4etz7MuyeMIUwMZZ0gAryznrX8rWKhp4L8R-80CoNrOCyZYOCxXKhlL_QCPDnMbqjMNag; __Secure-refresh-token=8.*********.z4sgMz4HRbOY4JazSYzsRQ.62.AUV4mM2v8IATdyDKsF4Dh702LTEjtYHjBQ0cd-sKXAI_zcEeb632-KCxJFFsG-lCwFl6zGoSCUtHrB96BccvdG7ipTyXtY1ofvnYcuetPXk84NsOCJvWfHu1JT8I_7MCSw.20240802162746.20250726174505.PCsyIDoCBNGGveIHFa0RrOW1LIR671ZXhjTUHGy8Hto.1ecbae480c865e743; __Secure-access-token=8.*********.z4sgMz4HRbOY4JazSYzsRQ.62.AUV4mM2v8IATdyDKsF4Dh702LTEjtYHjBQ0cd-sKXAI_zcEeb632-KCxJFFsG-lCwFl6zGoSCUtHrB96BccvdG7ipTyXtY1ofvnYcuetPXk84NsOCJvWfHu1JT8I_7MCSw.20240802162746.20250726174505.jOYr_n5FewYNFW7hdeq7y84etv_wZe7hQCSzJldF9uU.12551f8a917bff4b6; x-o3-language=zh-Hans; abt_data=7._brI2znjnKF8-NmuytfQ7aD-47dYJiJ7EIA28B1ljiXTShLkrKRr3o0QIzl0JPMqRL-zEh8Jm1Kgn4sn2bHlCRODolXQ6QviBPAg2Hl7A8tQsmnMJ8bJ9VcbCJPtcPMyVljo61u45MROJc5hVm4JGC5S8QTwTqemcb9UB_mZ0tRWQlN8hSdo9mGOQQNc3hjpxX0Q4X-kbNLP-7xqWjWaZzJiqlv5QI8aS7dbUaR1o4DbLezWOdtRQ7ctIJFMTheEY-D6LoNtS9d0SV8GN03c9D79in2k_MW1z-3FiEoZVma3XB5oep8tKruiwCRKPjnm59bLuPH9zM4_nyA_euHbdDsNqpF2FORHjdeOJ4KJlEXhOajVbFLqC8to7jNNU1I2O3kNwdL-vAf9Mdb8n6K-6POgmMrDLH6rQxgfMtzRaFy-dS6U1KgJDA8vu6am80UC_uXO3zO0zHseuCwXNnACP3Qn6byE6cmVTMZaucxp4QamOgjelGgLXqKP7VIL58ybMkQS9VMzRxbpbPhPKwgC0F52SNe_WICccKyYRDf60-8Ao3770ydRen-CSXk9bv2bLxxubA; __Secure-ETC=1bef2d24b3f10e2414366bbcc6d5b2ae`

	client := ozon_seller.NewClient(
		ozon_seller.WithCompanyID("2206838"),
		ozon_seller.WithCookie(cookie),
	)

	// 创建请求参数
	req := &ozon_seller.ClusterListRequest{
		Limit:  "30",
		Offset: "0",
		Filter: &ozon_seller.ListFilter{
			SupplyPeriod: "FOUR_WEEKS",
		},
	}

	// 调用API
	resp, err := client.GetClusterList(req)
	if err != nil {
		fmt.Printf("获取集群列表失败: %v\n", err)
		return
	}

	// 验证响应
	if resp == nil {
		fmt.Println("响应为空")
		return
	}

	// 打印结果
	fmt.Println("\n=== 集群销售数据列表 ===")
	fmt.Printf("总行数: %s\n", resp.TotalRows)
	fmt.Printf("集群数量: %d\n", len(resp.Clusters))

	// 打印集群详细信息
	for i, clusterData := range resp.Clusters {
		fmt.Printf("\n集群 #%d:\n", i+1)
		if clusterData.Cluster != nil {
			fmt.Printf("- 集群ID: %s\n", clusterData.Cluster.ID)
			fmt.Printf("- 集群名称: %s\n", clusterData.Cluster.Name)
		}

		if clusterData.Accessibility != nil {
			fmt.Printf("- 可达性指数: %d\n", clusterData.Accessibility.Accessibility)
			fmt.Printf("- 错失销售: %d\n", clusterData.Accessibility.MissedSales)
			fmt.Printf("- 广告费用(卢布): %.2f\n", clusterData.Accessibility.AdsRub)
			fmt.Printf("- FBO库存: %d\n", clusterData.Accessibility.InStockFbo)
			fmt.Printf("- 在途数量: %d\n", clusterData.Accessibility.Transit)
		}

		fmt.Printf("- 需要商品数量: %s\n", clusterData.NeedItems)
		fmt.Printf("- 总商品数量: %s\n", clusterData.TotalItems)
		fmt.Printf("- 销售方案: %s\n", clusterData.SalesScheme)
	}
}

// loadProductInfo 从JSON文件加载商品信息
func loadProductInfo(filename string) (map[string]ProductInfo, error) {
	// 读取JSON文件
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("无法读取文件 %s: %w", filename, err)
	}

	// 解析JSON数据
	var products []ProductInfo
	if err := json.Unmarshal(data, &products); err != nil {
		return nil, fmt.Errorf("无法解析JSON数据: %w", err)
	}

	// 创建以Article为键的映射
	productMap := make(map[string]ProductInfo)
	for _, product := range products {
		productMap[product.Article] = product
	}

	return productMap, nil
}

// TestGetClusterDetailsAndExportToExcel 测试获取集群详情并导出到Excel
func TestGetClusterDetailsAndExportToExcel(t *testing.T) {
	// 创建 Ozon Seller API 客户端
	// 注意：这里需要替换为实际的 Cookie
	cookie := `__Secure-ab-group=62; __Secure-ext_xcid=8c1c81b170445fdc51713ca8dba66637; rfuid=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; xcid=8c1c81b170445fdc51713ca8dba66637; sc_company_id=2206838; contentId=2206838; TS01f9fe57=0187c00a186611bd73d3fc52cb510e3ef5bd5d4186d54f31ce5e196ebac9e7593b9b3d0de77b12e8947da54511a50b769facd0a83f; TS015d2969=0187c00a186611bd73d3fc52cb510e3ef5bd5d4186d54f31ce5e196ebac9e7593b9b3d0de77b12e8947da54511a50b769facd0a83f; TS013595b9=0187c00a18e03c5d52b197528e27d4142edc66a8cf01e6b63658dec430bb126bafc5de836f18996485c27230f750a4d4035dd183b9; TS0149423d=0187c00a18388f67485b10a85ede866f7a2e862ca48811ea10bbea48734356c1d148a9b239b6b23493e1984d79a1cc74cbc5ed8d59; TS018529d3=0187c00a18c890628bc44c471510060e7adf002efb4de072a580a7b8fb9f6dab15af741fa492ba98ded01a245105f06deeba41a700; TS0121feed=0187c00a18c890628bc44c471510060e7adf002efb4de072a580a7b8fb9f6dab15af741fa492ba98ded01a245105f06deeba41a700; guest=true; TSDK_trackerSessionId=ab06b44b-1489-fc5f-c0b5; ob_theme=SYSTEM; __Secure-user-id=*********; bacntid=4579365; abt_data=7.QkLqKozZ9A8fJcG5bdUscqcYqSHi6eG5ajUr63akCCqs4uZv3VNyTTYZjLa28s0miCG8W-G2rJSNxDj4tay6ayAwuALRfaLkO-znGVGno2JzJLjCxbL4VbSVU8YQosSM5CUk8Q7lwX-nYhhviBGj4PJeWlAh_FWtspzoFnGON1C0AsdpOfLPUIvm3Ompr1wz4kCaQHxxRxKiS-Jr0RXNftDh_05b52WQfgTfwpRieBWF8BHe3NJ9mBDde2RmblXUZ7BLJfKcJIj_9FpD8KRmc5k9i1bIcjVEdwhb6jaqIIMb-pVGMgVGmrFbDteWalwp-RUUDzhYgzVL8zjA6m8inI67A6feY3rd9P-ftKJxjfihVJGPNfi9J_a1e2oh4zHr2ptoKxkckeKGvTvzIsI_rZ79IiFC2dDjIdE4923H11ZRn8pW32gCke8fu9e5GUw8XCIkSgp-JtRohIi_bNKHldhNwi9h8QqgzshDXtfmokrIAMe7HFrDpsT5QMO-bLalb4etz7MuyeMIUwMZZ0gAryznrX8rWKhp4L8R-80CoNrOCyZYOCxXKhlL_QCPDnMbqjMNag; __Secure-refresh-token=8.*********.z4sgMz4HRbOY4JazSYzsRQ.62.AUV4mM2v8IATdyDKsF4Dh702LTEjtYHjBQ0cd-sKXAI_zcEeb632-KCxJFFsG-lCwFl6zGoSCUtHrB96BccvdG7ipTyXtY1ofvnYcuetPXk84NsOCJvWfHu1JT8I_7MCSw.20240802162746.20250726174505.PCsyIDoCBNGGveIHFa0RrOW1LIR671ZXhjTUHGy8Hto.1ecbae480c865e743; __Secure-access-token=8.*********.z4sgMz4HRbOY4JazSYzsRQ.62.AUV4mM2v8IATdyDKsF4Dh702LTEjtYHjBQ0cd-sKXAI_zcEeb632-KCxJFFsG-lCwFl6zGoSCUtHrB96BccvdG7ipTyXtY1ofvnYcuetPXk84NsOCJvWfHu1JT8I_7MCSw.20240802162746.20250726174505.jOYr_n5FewYNFW7hdeq7y84etv_wZe7hQCSzJldF9uU.12551f8a917bff4b6; x-o3-language=zh-Hans; abt_data=7._brI2znjnKF8-NmuytfQ7aD-47dYJiJ7EIA28B1ljiXTShLkrKRr3o0QIzl0JPMqRL-zEh8Jm1Kgn4sn2bHlCRODolXQ6QviBPAg2Hl7A8tQsmnMJ8bJ9VcbCJPtcPMyVljo61u45MROJc5hVm4JGC5S8QTwTqemcb9UB_mZ0tRWQlN8hSdo9mGOQQNc3hjpxX0Q4X-kbNLP-7xqWjWaZzJiqlv5QI8aS7dbUaR1o4DbLezWOdtRQ7ctIJFMTheEY-D6LoNtS9d0SV8GN03c9D79in2k_MW1z-3FiEoZVma3XB5oep8tKruiwCRKPjnm59bLuPH9zM4_nyA_euHbdDsNqpF2FORHjdeOJ4KJlEXhOajVbFLqC8to7jNNU1I2O3kNwdL-vAf9Mdb8n6K-6POgmMrDLH6rQxgfMtzRaFy-dS6U1KgJDA8vu6am80UC_uXO3zO0zHseuCwXNnACP3Qn6byE6cmVTMZaucxp4QamOgjelGgLXqKP7VIL58ybMkQS9VMzRxbpbPhPKwgC0F52SNe_WICccKyYRDf60-8Ao3770ydRen-CSXk9bv2bLxxubA; __Secure-ETC=1bef2d24b3f10e2414366bbcc6d5b2ae`

	client := ozon_seller.NewClient(
		ozon_seller.WithCompanyID("2206838"),
		ozon_seller.WithCookie(cookie),
	)
	supplyPeriod := "FOUR_WEEKS"
	// 创建请求参数，使用 EIGHT_WEEKS
	req := &ozon_seller.ClusterListRequest{
		Limit:  "50",
		Offset: "0",
		Filter: &ozon_seller.ListFilter{
			SupplyPeriod: supplyPeriod,
		},
	}

	// 调用API获取集群列表
	resp, err := client.GetClusterList(req)
	if err != nil {
		t.Fatalf("获取集群列表失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	fmt.Printf("总行数: %s\n", resp.TotalRows)
	fmt.Printf("集群数量: %d\n", len(resp.Clusters))

	// 加载商品信息
	productInfoMap, err := loadProductInfo("product_info.json")
	if err != nil {
		log.Printf("警告: 无法加载商品信息: %v", err)
		productInfoMap = make(map[string]ProductInfo) // 创建空映射以避免nil引用
	}

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Println(err)
		}
	}()

	// 创建一个工作表
	index, err := f.NewSheet("集群详情统计")
	if err != nil {
		log.Fatal(err)
	}

	// 设置表头
	headers := []string{
		"集群名称", "商品SKU", "FBO库存", "在途数量", "错失销售", "广告费用(卢布)", "可用天数", "推荐供应量",
		"中转仓库存", "单箱数量", "单箱重量(kg)", "需要备货箱数(取整)", "总重量需求(kg)",
	}
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue("集群详情统计", cell, header)
	}

	// 收集所有数据项
	var allData []ClusterDetailStats

	// 遍历每个集群，获取详细信息
	for i, clusterData := range resp.Clusters {
		if clusterData.Cluster == nil {
			continue
		}

		fmt.Printf("\n处理集群 #%d (ID: %s, 名称: %s)\n", i+1, clusterData.Cluster.ID, clusterData.Cluster.Name)

		// 创建请求参数获取集群详情
		detailReq := &ozon_seller.ClusterDetailListRequest{
			Limit:  "100",
			Offset: "0",
			Filter: &ozon_seller.DetailListFilter{
				SupplyPeriod: supplyPeriod,
			},
			ClusterId: clusterData.Cluster.ID,
		}

		// 调用API获取集群详情
		detailResp, err := client.GetClusterDetailList(detailReq)
		if err != nil {
			fmt.Printf("获取集群 %s 详情失败: %v\n", clusterData.Cluster.ID, err)
			continue
		}

		// 验证响应
		if detailResp == nil {
			fmt.Printf("集群 %s 详情响应为空\n", clusterData.Cluster.ID)
			continue
		}

		fmt.Printf("集群 %s 商品数量: %d\n", clusterData.Cluster.ID, len(detailResp.Items))

		// 遍历商品并收集统计数据
		for _, item := range detailResp.Items {
			if item.Item == nil || item.Accessibility == nil {
				continue
			}

			// 获取商品信息
			var warehouseStock, quantityPerBox int
			var weightPerBoxKg float64

			if productInfo, exists := productInfoMap[item.Item.Article]; exists {
				warehouseStock = productInfo.WarehouseStock
				quantityPerBox = productInfo.QuantityPerBox
				weightPerBoxKg = productInfo.WeightPerBoxKg
			}

			// 计算需要备货的箱数（向上取整）
			var requiredBoxes int
			if quantityPerBox > 0 {
				recommendedSupply, _ := strconv.Atoi(item.Accessibility.RecommendedSupply)
				requiredBoxes = int(math.Ceil(float64(recommendedSupply) / float64(quantityPerBox)))
			}

			// 计算总重量需求
			totalWeightRequired := float64(requiredBoxes) * weightPerBoxKg

			// 创建统计数据
			stats := ClusterDetailStats{
				ClusterName:         clusterData.Cluster.Name,
				Article:             item.Item.Article,
				InStockFbo:          item.Accessibility.InStockFbo,
				Transit:             item.Accessibility.Transit,
				MissedSales:         item.Accessibility.MissedSales,
				AdsRub:              item.Accessibility.AdsRub,
				StockAvailDays:      item.Accessibility.StockAvailDays,
				RecommendedSupply:   item.Accessibility.RecommendedSupply,
				WarehouseStock:      warehouseStock,
				QuantityPerBox:      quantityPerBox,
				WeightPerBoxKg:      weightPerBoxKg,
				RequiredBoxes:       requiredBoxes,
				TotalWeightRequired: totalWeightRequired,
			}

			allData = append(allData, stats)
		}
	}

	// 按照Article字段排序
	sort.Slice(allData, func(i, j int) bool {
		return allData[i].Article < allData[j].Article
	})

	// 写入排序后的数据到Excel
	for i, data := range allData {
		rowIndex := i + 2 // 表头占第一行，数据从第二行开始
		excelData := []interface{}{
			data.ClusterName,
			data.Article,
			data.InStockFbo,
			data.Transit,
			data.MissedSales,
			data.AdsRub,
			data.StockAvailDays,
			data.RecommendedSupply,
			data.WarehouseStock,
			data.QuantityPerBox,
			data.WeightPerBoxKg,
			data.RequiredBoxes,
			data.TotalWeightRequired,
		}

		for j, value := range excelData {
			cell, _ := excelize.CoordinatesToCellName(j+1, rowIndex)
			f.SetCellValue("集群详情统计", cell, value)
		}
	}

	// 设置活动工作表
	f.SetActiveSheet(index)

	// 保存Excel文件
	if err := f.SaveAs("集群详情统计.xlsx"); err != nil {
		log.Fatal(err)
	}

	fmt.Println("\nExcel文件已保存为: 集群详情统计.xlsx")
}
