package main

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/unidoc/unipdf/v4/creator"
	"github.com/unidoc/unipdf/v4/extractor"
	"github.com/unidoc/unipdf/v4/model"
	"github.com/xuri/excelize/v2"
	"lens/internal/api/ozon"
	"lens/internal/infrastructure/redis"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func TestGetClusterAndWarehouseList(t *testing.T) {
	// 创建 Ozon API 客户端
	// 注意：这里需要替换为实际的 API Key 和 Client ID
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 创建请求参数
	req := &ozonapi.ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON", // 俄罗斯集群 或 "CLUSTER_TYPE_CIS" 独联体集群
	}

	// 调用API
	resp, err := client.FBO.GetClusterAndWarehouseList(req)
	if err != nil {
		t.Fatalf("获取集群和仓库列表失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印结果
	fmt.Println("\n=== 集群和仓库列表 ===")
	fmt.Printf("集群数量: %d\n", len(resp.Clusters))

	// 打印集群详细信息
	for i, cluster := range resp.Clusters {
		fmt.Printf("\n集群 #%d:\n", i+1)
		fmt.Printf("- 集群ID: %d\n", cluster.Id)
		fmt.Printf("- 集群名称: %s\n", cluster.Name)
		fmt.Printf("- 仓库数量: %d\n", len(cluster.LogisticClusters))
		//if cluster.Id == 154 {
		// 打印仓库信息
		for j, logisticCluster := range cluster.LogisticClusters {
			fmt.Printf("  物流集群 #%d:\n", j+1)
			fmt.Printf("  - 仓库数量: %d\n", len(logisticCluster.Warehouses))
			for k, warehouse := range logisticCluster.Warehouses {
				//if warehouse.Type == "FULL_FILLMENT" {
				fmt.Printf("    仓库 #%d:\n", k+1)
				fmt.Printf("    - 仓库名称: %s\n", warehouse.Name)
				fmt.Printf("    - 仓库ID: %d\n", warehouse.WarehouseId)
				fmt.Printf("    - 类型: %s\n", warehouse.Type)
				//}
			}
			//}
		}
	}
}

func TestSearchFboWarehouses(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 创建请求参数
	req := &ozonapi.WarehouseFboListRequest{
		FilterBySupplyType: []string{"CREATE_TYPE_CROSSDOCK"}, // 或 "CREATE_TYPE_DIRECT"
		Search:             "Москва",                          // 搜索莫斯科的仓库
	}

	// 调用API
	resp, err := client.FBO.SearchFboWarehouses(req)
	if err != nil {
		t.Fatalf("搜索FBO仓库失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印结果
	fmt.Println("\n=== FBO仓库搜索结果 ===")
	fmt.Printf("找到仓库数量: %d\n", len(resp.Search))

	// 打印仓库详细信息
	for i, warehouse := range resp.Search {
		fmt.Printf("\n仓库 #%d:\n", i+1)
		fmt.Printf("- 仓库名称: %s\n", warehouse.Name)
		fmt.Printf("- 仓库ID: %d\n", warehouse.WarehouseID)
		fmt.Printf("- 仓库类型: %s\n", warehouse.WarehouseType)
		fmt.Printf("- 地址: %s\n", warehouse.Address)

		// 打印坐标信息
		fmt.Printf("- 纬度: %f\n", warehouse.Coordinates.Latitude)
		fmt.Printf("- 经度: %f\n", warehouse.Coordinates.Longitude)
	}
}

// TestCreateSupplyDraftFromCluster 测试根据集群列表创建供应草稿
func TestCreateSupplyDraftFromCluster(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")
	redisClient := redis.GetInstance()
	ctx := context.Background()
	// 1. 读取Excel表格数据
	fmt.Println("正在读取Excel表格数据...")
	f, err := excelize.OpenFile("20250825库存情况和分配计划-对外.xlsx")
	if err != nil {
		t.Fatalf("打开Excel文件失败: %v", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			t.Fatalf("关闭Excel文件失败: %v", err)
		}
	}()

	// 获取第一个工作表的名称
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		t.Fatal("无法获取Excel工作表名称")
	}

	// 解析每个集群下的SKU数量和箱数
	type ClusterProductInfo struct {
		ClusterName string
		SKU         int64
		Article     string
		Quantity    int
		Boxes       float64
	}

	clusterProductsMap := make(map[string][]ClusterProductInfo)
	clusters := []string{}
	products := []string{}

	fmt.Printf("读取工作表: %s\n", sheetName)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		t.Fatalf("读取行数据失败: %v", err)
	}

	// 读取集群名称和产品SKU
	for i, row := range rows {
		if i == 0 { // 第一行是集群名称
			for j := 10; j < 52 && j < len(row); j += 2 {
				if row[j] != "" {
					clusters = append(clusters, strings.Split(row[j], "\n")[0])
				}
			}
		}
		if i >= 3 && len(row) > 0 { // 从第4行开始是产品数据
			if row[0] != "" {
				products = append(products, row[0]) // SKU名称在第1列
			}
		}
	}

	// 解析每个集群下的产品数据
	for i, product := range products {
		rowIndex := i + 3 // 数据从第4行开始
		if rowIndex >= len(rows) {
			break
		}

		row := rows[rowIndex]
		for j, cluster := range clusters {
			colIndex := 10 + j*2 // 数据从第11列开始，每两个列为一个集群的数据
			if colIndex < len(row) {
				quantity := 0
				boxes := 0.0
				// 数量在当前列
				if colIndex < len(row) && row[colIndex] != "" {
					quantity = gconv.Int(row[colIndex])
				}
				// 箱数在下一列
				if colIndex+1 < len(row) && row[colIndex+1] != "" {
					boxes = gconv.Float64(strings.TrimSpace(row[colIndex+1]))
				}
				cpi := ClusterProductInfo{
					ClusterName: cluster,
					SKU:         0,
					Article:     product,
					Quantity:    quantity,
					Boxes:       boxes,
				}
				clusterProductsMap[cluster] = append(clusterProductsMap[cluster], cpi)
			}
		}
	}

	fmt.Printf("从Excel文件中读取到 %d 个集群商品信息\n", len(clusterProductsMap))
	//Article 映射sku
	articleToSKU := make(map[string]int64)
	offerIdsMaps := make(map[string]int64)
	offerIds := []string{}
	for _, products_info := range clusterProductsMap {
		for _, info := range products_info {
			if _, ok := offerIdsMaps[info.Article]; !ok {
				offerIdsMaps[info.Article] = 1
				offerIds = append(offerIds, info.Article)
			}
		}
	}

	// 通过 client.Products.GetProductList() 获得 ozon sku
	skuReq := &ozonapi.ProductInfoRequest{
		OfferID: offerIds,
	}
	oProducts, err := client.Products.GetProductInfo(skuReq)

	for _, item := range oProducts.Items {
		if _, ok := articleToSKU[item.OfferId]; !ok {
			articleToSKU[item.OfferId] = gconv.Int64(item.Sku)
		}
	}

	// 打印每个集群的信息
	//for clusterName, products_info := range clusterProductsMap {
	//	fmt.Printf("\n集群 %s 的商品信息:\n", clusterName)
	//	totalQuantity := 0
	//	totalBoxes := 0.0
	//	for _, product := range products_info {
	//		fmt.Printf("  - SKU: %s, 数量: %d, 箱数: %.1f \n", product.Article, product.Quantity, product.Boxes)
	//		totalQuantity += product.Quantity
	//		totalBoxes += product.Boxes
	//	}
	//	fmt.Printf("  集群总计: 数量 %d, 箱数 %.1f \n", totalQuantity, totalBoxes)
	//}

	// 2. 获取集群和仓库列表
	clusterReq := &ozonapi.ClusterListRequest{
		ClusterType: "CLUSTER_TYPE_OZON", // 俄罗斯集群
	}

	clusterResp, err := client.FBO.GetClusterAndWarehouseList(clusterReq)
	if err != nil {
		t.Fatalf("获取集群和仓库列表失败: %v", err)
	}

	// 验证响应
	if clusterResp == nil {
		t.Fatal("集群和仓库列表响应为空")
	}

	fmt.Printf("\n获取到 %d 个集群\n", len(clusterResp.Clusters))

	// 打印集群信息
	//for i, cluster := range clusterResp.Clusters {
	//	fmt.Printf("\n集群 #%d:\n", i+1)
	//	fmt.Printf("- 集群ID: %d\n", cluster.Id)
	//	fmt.Printf("- 集群名称: %s\n", cluster.Name)
	//	fmt.Printf("- 集群类型: %s\n", cluster.Type)
	//
	//	// 打印物流集群中的仓库信息
	//	for j, logisticCluster := range cluster.LogisticClusters {
	//		fmt.Printf("  物流集群 #%d:\n", j+1)
	//		for k, warehouse := range logisticCluster.Warehouses {
	//			fmt.Printf("    仓库 #%d:\n", k+1)
	//			fmt.Printf("    - 仓库名称: %s\n", warehouse.Name)
	//			fmt.Printf("    - 仓库ID: %d\n", warehouse.WarehouseId)
	//			fmt.Printf("    - 类型: %s\n", warehouse.Type)
	//		}
	//	}
	//}

	// 3. 从集群列表中选择第一个集群创建供应草稿
	if len(clusterResp.Clusters) == 0 {
		t.Fatal("没有获取到任何集群信息")
	}
	operationIDs := make(map[string]string)
	//判断 draft_ids 是否有数据
	draft_ids_json, _ := redisClient.Get(ctx, "draft_ids")
	if draft_ids_json != "" {
		json.Unmarshal([]byte(draft_ids_json), &operationIDs)
	}
	for _, cluster := range clusterResp.Clusters {
		cpms := clusterProductsMap[cluster.Name]
		if _, ok := operationIDs[cluster.Name]; ok {
			fmt.Printf("\n[%s]已存在草稿，跳过...\n", cluster.Name)
			continue
		}
		// 准备草稿中的商品项
		var draftItems []struct {
			Quantity int   `json:"quantity"`
			SKU      int64 `json:"sku"`
		}
		for _, cpm := range cpms {
			if cpm.Quantity == 0 {
				continue
			}
			draftItems = append(draftItems, struct {
				Quantity int   `json:"quantity"`
				SKU      int64 `json:"sku"`
			}{Quantity: cpm.Quantity, SKU: articleToSKU[cpm.Article]})
		}
		if len(draftItems) == 0 {
			fmt.Printf("\n[%s]请出入商品数量...\n", cluster.Name)
			continue
		}
		// 创建供应草稿请求
		draftReq := &ozonapi.DraftCreateRequest{
			ClusterIDs:              []int64{gconv.Int64(cluster.Id)},
			DropOffPointWarehouseID: 1020002160211000, //发货仓库 需要靠近赵总仓库的仓 1020002201523000,1020002160211000
			Items:                   draftItems,
			Type:                    "CREATE_TYPE_CROSSDOCK", // CREATE_TYPE_CROSSDOCK 或者 "CREATE_TYPE_DIRECT"
		}

		// 4. 创建供应草稿
		fmt.Printf("\n正在创建供应草稿...\n")
		fmt.Printf("- 商品项数量: %d\n", len(draftReq.Items))

		for i, item := range draftReq.Items {
			fmt.Printf("  商品 #%d: SKU=%d, 数量=%d\n", i+1, item.SKU, item.Quantity)
		}
		operationID, err := client.FBO.CreateSupplyDraft(draftReq)
		if err != nil {
			fmt.Printf("创建供应草稿失败: %v", err)
			continue
		}
		if operationID != "" {
			operationIDs[cluster.Name] = operationID
			redisClient.Set(ctx, "draft_ids", operationIDs, 0)
			fmt.Printf("成功创建供应草稿，[%s]操作ID: %s\n", cluster.Name, operationID)
			time.Sleep(23 * time.Second)
		}
	}
	for name, id := range operationIDs {
		// 5. 获取草稿信息
		fmt.Printf("\n正在获取[%s]草稿信息...\n", name)
		draftInfo, err := client.FBO.GetSupplyDraftInfo(id)
		if err != nil {
			fmt.Printf("获取供应草稿信息失败: %v", err)
			continue
		}

		fmt.Printf("草稿ID: %d, 状态: %s\n", draftInfo.DraftID, draftInfo.Status)

		// 打印草稿中的集群信息
		for i, cluster := range draftInfo.Clusters {
			fmt.Printf("\n草稿集群 #%d:\n", i+1)
			fmt.Printf("- 集群ID: %d\n", cluster.ClusterID)
			fmt.Printf("- 集群名称: %s\n", cluster.ClusterName)

			// 打印仓库信息
			for j, warehouse := range cluster.Warehouses {
				fmt.Printf("  仓库 #%d:\n", j+1)
				fmt.Printf("  - 仓库ID: %d\n", warehouse.SupplyWarehouse.WarehouseID)
				fmt.Printf("  - 仓库名称: %s\n", warehouse.SupplyWarehouse.Name)
			}
		}

		// 检查是否有错误信息
		if len(draftInfo.Errors) > 0 {
			fmt.Printf("\n草稿创建过程中出现 %d 个错误:\n", len(draftInfo.Errors))
			for i, errMsg := range draftInfo.Errors {
				fmt.Printf("错误 #%d: %v\n", i+1, errMsg)
			}
			continue
		}
		fmt.Printf("\n草稿创建成功，无错误信息。\n")

		// 6. 获取可用时间段信息
		if draftInfo.DraftID > 0 && len(draftInfo.Clusters) > 0 {
			// 收集所有仓库ID
			for _, cluster := range draftInfo.Clusters {
				for _, warehouse := range cluster.Warehouses {
					supplyCreateReq := &ozonapi.DraftSupplyCreateRequest{
						DraftID:     draftInfo.DraftID,
						Timeslot:    nil, // 可能为 nil
						WarehouseID: warehouse.SupplyWarehouse.WarehouseID,
					}
					fmt.Printf("选择仓库:%s ID:%d\n", warehouse.SupplyWarehouse.Name, warehouse.SupplyWarehouse.WarehouseID)

					supplyOperationID, err := client.FBO.CreateSupplyFromDraft(supplyCreateReq)
					if err != nil {
						fmt.Printf("从草稿创建供应单失败: %v\n", err)
						continue
					}

					fmt.Printf("成功创建供应单，操作ID: %s\n", supplyOperationID)
					// 8. 获取供应单创建状态
					fmt.Printf("\n正在获取供应单创建状态...\n")
					supplyStatus, err := client.FBO.GetDraftSupplyCreateStatus(supplyOperationID)
					if err != nil {
						fmt.Printf("获取供应单创建状态失败: %v\n", err)
						continue
					}
					fmt.Printf("供应单创建状态: %s\n", supplyStatus.Status)
					if len(supplyStatus.ErrorMessages) > 0 {
						fmt.Printf("错误信息:\n")
						for i, errMsg := range supplyStatus.ErrorMessages {
							fmt.Printf("  错误 #%d: %s\n", i+1, errMsg)
						}
						continue
					}
					if len(supplyStatus.Result.OrderIDs) > 0 {
						fmt.Printf("创建的订单IDs: %v\n", supplyStatus.Result.OrderIDs)
					}
				}
			}
		}
	}
}

// TestGetDraftTimeslotInfo 测试获取草稿时间段信息
func TestGetDraftTimeslotInfo(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 使用已知的草稿ID进行测试（需要先创建一个草稿）
	draftID := int64(123456)                  // 替换为实际的草稿ID
	warehouseIDs := []int64{1020002201523000} // 替换为实际的仓库ID

	// 设置查询时间范围（从明天开始的7天内）
	now := time.Now()
	dateFrom := now.AddDate(0, 0, 1) // 明天
	dateTo := now.AddDate(0, 0, 8)   // 8天后

	fmt.Printf("测试获取草稿时间段信息\n")
	fmt.Printf("草稿ID: %d\n", draftID)
	fmt.Printf("仓库IDs: %v\n", warehouseIDs)
	fmt.Printf("查询时间范围: %s 到 %s\n",
		dateFrom.Format("2006-01-02"),
		dateTo.Format("2006-01-02"))

	// 创建时间段信息请求
	timeslotReq := &ozonapi.DraftTimeslotInfoRequest{
		DateFrom:     dateFrom,
		DateTo:       dateTo,
		DraftID:      draftID,
		WarehouseIDs: warehouseIDs,
	}

	// 调用API获取时间段信息
	timeslotInfo, err := client.FBO.GetDraftTimeslotInfo(timeslotReq)
	if err != nil {
		t.Fatalf("获取时间段信息失败: %v", err)
	}

	// 验证响应
	if timeslotInfo == nil {
		t.Fatal("时间段信息响应为空")
	}

	fmt.Printf("\n=== 时间段信息 ===\n")
	fmt.Printf("请求的时间范围: %s 到 %s\n",
		timeslotInfo.RequestedDateFrom.Format("2006-01-02"),
		timeslotInfo.RequestedDateTo.Format("2006-01-02"))
	fmt.Printf("找到 %d 个仓库的时间段信息\n", len(timeslotInfo.DropOffWarehouseTimeslots))

	// 打印每个仓库的可用时间段详情
	for i, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
		fmt.Printf("\n仓库 #%d (ID: %d):\n", i+1, warehouseTimeslot.DropOffWarehouseID)

		if len(warehouseTimeslot.Days) == 0 {
			fmt.Printf("  该仓库暂无可用时间段\n")
			continue
		}

		totalTimeslots := 0
		for j, day := range warehouseTimeslot.Days {
			fmt.Printf("  日期 #%d: %s\n", j+1, day.DateInTimezone.Format("2006-01-02 Monday"))

			if len(day.Timeslots) == 0 {
				fmt.Printf("    该日期暂无可用时间段\n")
				continue
			}

			for k, timeslot := range day.Timeslots {
				fmt.Printf("    时间段 #%d: %s - %s\n", k+1,
					timeslot.FromInTimezone.Format("15:04"),
					timeslot.ToInTimezone.Format("15:04"))
				totalTimeslots++
			}
		}
		fmt.Printf("  仓库总计可用时间段: %d 个\n", totalTimeslots)
	}

	// 验证数据完整性
	if len(timeslotInfo.DropOffWarehouseTimeslots) == 0 {
		fmt.Printf("\n注意: 未找到任何可用时间段，可能原因:\n")
		fmt.Printf("- 草稿ID不存在或已过期\n")
		fmt.Printf("- 仓库ID不正确\n")
		fmt.Printf("- 查询的时间范围内暂无可用时间段\n")
	}
}

// TestCreateSupplyFromDraftWithTimeslot 测试从草稿创建供应单（带时间段选择）
func TestCreateSupplyFromDraftWithTimeslot(t *testing.T) {
	// 创建 Ozon API 客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	// 使用已知的草稿ID进行测试（需要先创建一个草稿）
	draftID := int64(123456) // 替换为实际的草稿ID

	fmt.Printf("测试从草稿创建供应单（带时间段选择）\n")
	fmt.Printf("草稿ID: %d\n", draftID)

	// 1. 首先获取草稿信息
	fmt.Printf("\n正在获取草稿信息...\n")
	draftInfo, err := client.FBO.GetSupplyDraftInfo("operation-id-here") // 替换为实际的操作ID
	if err != nil {
		t.Fatalf("获取供应草稿信息失败: %v", err)
	}

	if draftInfo.DraftID == 0 || len(draftInfo.Clusters) == 0 {
		t.Fatal("草稿信息无效或无集群信息")
	}

	fmt.Printf("草稿ID: %d, 状态: %s\n", draftInfo.DraftID, draftInfo.Status)

	// 2. 收集所有仓库ID
	var warehouseIDs []int64
	for _, cluster := range draftInfo.Clusters {
		for _, warehouse := range cluster.Warehouses {
			warehouseIDs = append(warehouseIDs, warehouse.SupplyWarehouse.WarehouseID)
		}
	}

	if len(warehouseIDs) == 0 {
		t.Fatal("未找到可用的仓库ID")
	}

	// 3. 获取时间段信息
	now := time.Now()
	dateFrom := now.AddDate(0, 0, 1) // 明天
	dateTo := now.AddDate(0, 0, 15)  // 15天后

	timeslotReq := &ozonapi.DraftTimeslotInfoRequest{
		DateFrom:     dateFrom,
		DateTo:       dateTo,
		DraftID:      draftInfo.DraftID,
		WarehouseIDs: warehouseIDs,
	}

	fmt.Printf("\n正在获取时间段信息...\n")
	timeslotInfo, err := client.FBO.GetDraftTimeslotInfo(timeslotReq)
	if err != nil {
		t.Fatalf("获取时间段信息失败: %v", err)
	}

	// 4. 选择符合条件的仓库和时间段（至少4天后）
	var selectedWarehouseID int64
	var selectedTimeslot *ozonapi.Timeslot
	minDaysFromNow := 4

	fmt.Printf("\n正在选择合适的仓库和时间段...\n")
	fmt.Printf("要求: 时间段必须在%d天后\n", minDaysFromNow)

	for _, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
		fmt.Printf("\n检查仓库 ID: %d\n", warehouseTimeslot.DropOffWarehouseID)

		for _, day := range warehouseTimeslot.Days {
			daysFromNow := int(day.DateInTimezone.Sub(now).Hours() / 24)

			if daysFromNow < minDaysFromNow {
				fmt.Printf("  日期 %s 距今%d天，不符合要求\n",
					day.DateInTimezone.Format("2006-01-02"), daysFromNow)
				continue
			}

			if len(day.Timeslots) > 0 {
				selectedWarehouseID = warehouseTimeslot.DropOffWarehouseID
				selectedTimeslot = &day.Timeslots[0] // 选择第一个可用时间段
				fmt.Printf("  ✓ 选择日期 %s (距今%d天) 的时间段 %s-%s\n",
					day.DateInTimezone.Format("2006-01-02"), daysFromNow,
					selectedTimeslot.FromInTimezone.Format("15:04"),
					selectedTimeslot.ToInTimezone.Format("15:04"))
				break
			}
		}

		if selectedWarehouseID > 0 {
			break
		}
	}

	// 如果没有符合条件的时间段，随机选择一个仓库
	if selectedWarehouseID == 0 {
		fmt.Printf("\n未找到符合条件的时间段（需要至少%d天后），随机选择仓库创建供应单\n", minDaysFromNow)
		selectedWarehouseID = selectRandomWarehouse(warehouseIDs)
		if selectedWarehouseID > 0 {
			selectedTimeslot = nil // 不指定时间段
			fmt.Printf("随机选择的仓库ID: %d\n", selectedWarehouseID)
		} else {
			t.Fatalf("没有可用的仓库")
		}
	}

	// 5. 创建供应单
	fmt.Printf("\n正在从草稿创建供应单...\n")
	fmt.Printf("选择的仓库ID: %d\n", selectedWarehouseID)

	if selectedTimeslot != nil {
		fmt.Printf("选择的时间段: %s - %s\n",
			selectedTimeslot.FromInTimezone.Format("2006-01-02 15:04"),
			selectedTimeslot.ToInTimezone.Format("2006-01-02 15:04"))
	} else {
		fmt.Printf("未指定时间段（系统将自动分配）\n")
	}

	supplyCreateReq := &ozonapi.DraftSupplyCreateRequest{
		DraftID:     draftInfo.DraftID,
		Timeslot:    selectedTimeslot,
		WarehouseID: selectedWarehouseID,
	}

	supplyOperationID, err := client.FBO.CreateSupplyFromDraft(supplyCreateReq)
	if err != nil {
		t.Fatalf("从草稿创建供应单失败: %v", err)
	}

	fmt.Printf("成功创建供应单，操作ID: %s\n", supplyOperationID)

	// 6. 获取供应单创建状态
	fmt.Printf("\n正在获取供应单创建状态...\n")
	supplyStatus, err := client.FBO.GetDraftSupplyCreateStatus(supplyOperationID)
	if err != nil {
		t.Fatalf("获取供应单创建状态失败: %v", err)
	}

	fmt.Printf("供应单创建状态: %s\n", supplyStatus.Status)

	if len(supplyStatus.ErrorMessages) > 0 {
		fmt.Printf("错误信息:\n")
		for i, errMsg := range supplyStatus.ErrorMessages {
			fmt.Printf("  错误 #%d: %s\n", i+1, errMsg)
		}
	}

	if len(supplyStatus.Result.OrderIDs) > 0 {
		fmt.Printf("创建的订单IDs: %v\n", supplyStatus.Result.OrderIDs)
		fmt.Printf("供应单创建成功！\n")
	} else {
		fmt.Printf("注意: 未返回订单ID，请检查创建状态\n")
	}

	// 验证结果
	if supplyStatus.Status == "DraftSupplyCreateStatusSuccess" {
		fmt.Printf("\n✓ 测试通过: 供应单创建成功\n")
	} else if supplyStatus.Status == "DraftSupplyCreateStatusInProgress" {
		fmt.Printf("\n⏳ 供应单创建中，请稍后查看状态\n")
	} else {
		t.Errorf("供应单创建失败，状态: %s", supplyStatus.Status)
	}
}

// selectValidTimeslot 选择符合条件的时间段（至少指定天数后）
func selectValidTimeslot(timeslotInfo *ozonapi.DraftTimeslotInfoResponse, minDaysFromNow int) (int64, *ozonapi.Timeslot) {
	now := time.Now()

	for _, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
		for _, day := range warehouseTimeslot.Days {
			daysFromNow := int(day.DateInTimezone.Sub(now).Hours() / 24)

			// 检查是否满足最少天数要求
			if daysFromNow < minDaysFromNow {
				continue
			}

			// 选择第一个可用时间段
			if len(day.Timeslots) > 0 {
				// 随机5-9个时间段
				randomIndex := rand.Intn(len(day.Timeslots)-5) + 5
				return warehouseTimeslot.DropOffWarehouseID, &day.Timeslots[randomIndex]
			}
		}
	}

	return 0, nil
}

// selectRandomWarehouse 随机选择一个仓库（当没有符合条件的时间段时）
func selectRandomWarehouse(warehouseIDs []int64) int64 {
	if len(warehouseIDs) == 0 {
		return 0
	}

	// 简单选择第一个仓库，也可以使用随机算法
	// 如果需要真正的随机选择，可以使用 math/rand
	return warehouseIDs[0]
}

// printTimeslotInfo 打印时间段信息
func printTimeslotInfo(timeslotInfo *ozonapi.DraftTimeslotInfoResponse, minDaysFromNow int) {
	now := time.Now()

	fmt.Printf("查询时间范围: %s 到 %s\n",
		timeslotInfo.RequestedDateFrom.Format("2006-01-02"),
		timeslotInfo.RequestedDateTo.Format("2006-01-02"))
	fmt.Printf("找到 %d 个仓库的时间段信息\n", len(timeslotInfo.DropOffWarehouseTimeslots))

	for i, warehouseTimeslot := range timeslotInfo.DropOffWarehouseTimeslots {
		fmt.Printf("\n仓库 #%d (ID: %d):\n", i+1, warehouseTimeslot.DropOffWarehouseID)

		if len(warehouseTimeslot.Days) == 0 {
			fmt.Printf("  该仓库暂无可用时间段\n")
			continue
		}

		validTimeslots := 0
		for _, day := range warehouseTimeslot.Days {
			daysFromNow := int(day.DateInTimezone.Sub(now).Hours() / 24)

			if len(day.Timeslots) > 0 {
				status := "❌"
				if daysFromNow >= minDaysFromNow {
					status = "✅"
					validTimeslots += len(day.Timeslots)
				}

				fmt.Printf("  %s 日期: %s (距今%d天) - %d个时间段\n",
					status, day.DateInTimezone.Format("2006-01-02"), daysFromNow, len(day.Timeslots))

				// 只显示前3个时间段
				for j, timeslot := range day.Timeslots {
					if j >= 3 {
						fmt.Printf("    ... 还有%d个时间段\n", len(day.Timeslots)-3)
						break
					}
					fmt.Printf("    时间段: %s - %s\n",
						timeslot.FromInTimezone.Format("15:04"),
						timeslot.ToInTimezone.Format("15:04"))
				}
			}
		}

		fmt.Printf("  符合条件的时间段总数: %d 个\n", validTimeslots)
	}
}

// TestGetSupplyOrderComplete 完整的GetSupplyOrder功能测试 (适配新API格式)
func TestGetSupplyOrderComplete(t *testing.T) {
	// 创建客户端
	client := ozonapi.NewClient("f8260a04-4b24-40aa-af7c-fae8d51b3dc3", "2206838")

	fmt.Println("=== 完整GetSupplyOrder功能测试 (新API格式) ===")

	// 步骤1: 获取供应单列表
	fmt.Println("\n步骤1: 获取供应单ID列表")
	listReq := &ozonapi.SupplyOrderListRequest{
		Filter: ozonapi.SupplyOrderListFilter{
			States: []string{
				"ORDER_STATE_DATA_FILLING",
			},
		},
		Paging: ozonapi.SupplyOrderListPaging{
			FromSupplyOrderID: 0,
			Limit:             15, // 减少数量以便详细测试
		},
	}

	listResp, err := client.FBO.GetSupplyOrderList(listReq)
	if err != nil {
		t.Fatalf("获取供应单列表失败: %v", err)
	}

	fmt.Printf("获取到 %d 个供应单ID\n", len(listResp.SupplyOrderID))
	fmt.Printf("最后供应单ID: %d\n", listResp.LastSupplyOrderID)

	if len(listResp.SupplyOrderID) == 0 {
		t.Skip("没有供应单可供测试")
		return
	}

	// 显示获取到的ID列表
	fmt.Println("\n供应单ID列表:")
	for i, idStr := range listResp.SupplyOrderID {
		if i >= 5 { // 只显示前5个
			fmt.Printf("... 还有 %d 个ID\n", len(listResp.SupplyOrderID)-5)
			break
		}
		fmt.Printf("  %d. ID: %d\n", i+1, idStr)
	}

	// 步骤2: 批量获取供应单详情 (使用新的API格式)
	fmt.Println("\n步骤2: 批量获取供应单详情")
	getReq := &ozonapi.SupplyOrderGetRequest{
		OrderIDs: []int64{60863264}, // 新格式：使用字符串数组
	}

	detailResp, err := client.FBO.GetSupplyOrder(getReq)
	if err != nil {
		t.Fatalf("获取供应单详情失败: %v", err)
	}

	// 验证响应结构
	if detailResp == nil {
		t.Fatal("响应为空")
	}

	fmt.Printf("成功获取 %d 个供应单详情\n", len(detailResp.Orders))
	fmt.Printf("获取到 %d 个仓库信息\n", len(detailResp.Warehouses))

	// 步骤2.5: 获取供应单组合信息
	fmt.Println("\n步骤2.5: 获取供应单组合信息")
	bundleTestCount := 3 // 限制测试前3个供应单
	if len(listResp.SupplyOrderID) < bundleTestCount {
		bundleTestCount = len(listResp.SupplyOrderID)
	}

	for _, order := range detailResp.Orders {
		// 创建供应单组合信息请求
		bundleReq := &ozonapi.SupplyOrderBundleRequest{
			BundleIds: []string{order.Supplies[0].BundleID}, // 如果需要特定bundle ID，可以在这里指定
			IsAsc:     true,
			Limit:     100,
			//Query:     "",
			SortField: "sku",
		}

		// 调用GetSupplyOrderBundle API
		bundleResp, err := client.FBO.GetSupplyOrderBundle(bundleReq)
		if err != nil {
			fmt.Printf("  ❌ 获取供应单 %d 组合信息失败: %v\n", order.SupplyOrderId, err)
			continue
		}

		// 验证响应
		if bundleResp == nil {
			fmt.Printf("  ❌ 供应单 %d 组合信息响应为空\n", order.SupplyOrderId)
			continue
		}

		// 打印组合信息概览
		fmt.Printf("  ✅ 成功获取组合信息\n")
		fmt.Printf("  - 商品总数: %d\n", len(bundleResp.Items))
		fmt.Printf("  - 总计数量: %d\n", bundleResp.TotalCount)
		fmt.Printf("  - 是否有下一页: %t\n", bundleResp.HasNext)
		if bundleResp.LastId != "" {
			fmt.Printf("  - 最后ID: %s\n", bundleResp.LastId)
		}

		// 打印商品详情（限制显示前5个）
		if len(bundleResp.Items) > 0 {
			fmt.Printf("  \n  商品详情:\n")
			itemDisplayCount := 5
			if len(bundleResp.Items) < itemDisplayCount {
				itemDisplayCount = len(bundleResp.Items)
			}

			for j := 0; j < itemDisplayCount; j++ {
				item := bundleResp.Items[j]
				fmt.Printf("    商品 #%d:\n", j+1)
				fmt.Printf("      - SKU: %d\n", item.Sku)
				fmt.Printf("      - 唯一编码: %s\n", item.OfferId)
				fmt.Printf("      - 数量: %d\n", item.Quantity)

				// 如果有其他字段，也可以打印
				if item.Barcode != "" {
					fmt.Printf("      - 商品编号: %s\n", item.Barcode)
				}

			}

			// 如果还有更多商品，显示省略信息
			if len(bundleResp.Items) > itemDisplayCount {
				fmt.Printf("    ... 还有 %d 个商品未显示\n", len(bundleResp.Items)-itemDisplayCount)
			}
		} else {
			fmt.Printf("  - 该供应单暂无商品信息\n")
		}

	}

	// 如果跳过了一些供应单，显示提示信息
	if len(listResp.SupplyOrderID) > bundleTestCount {
		fmt.Printf("\n💡 为避免输出过多内容，跳过了剩余 %d 个供应单的组合信息查询\n",
			len(listResp.SupplyOrderID)-bundleTestCount)
	}

	// 步骤3: 获取货物规则信息
	fmt.Println("\n步骤3: 获取货物规则信息")
	cargoTestCount := 3 // 限制测试前3个供应单
	if len(detailResp.Orders) < cargoTestCount {
		cargoTestCount = len(detailResp.Orders)
	}

	for i := 0; i < cargoTestCount; i++ {
		order := detailResp.Orders[i]
		fmt.Printf("\n测试供应单 #%d (ID: %d) 的货物规则信息:\n", i+1, order.SupplyOrderId)

		// 收集该供应单的所有供应ID
		var supplyIDs []int64
		for _, supply := range order.Supplies {
			supplyIDs = append(supplyIDs, supply.SupplyID)
		}

		if len(supplyIDs) == 0 {
			fmt.Printf("  ⚠️ 供应单 %d 没有供应信息，跳过货物规则查询\n", order.SupplyOrderId)
			continue
		}

		fmt.Printf("  - 供应ID列表: %v\n", supplyIDs)

		// 调用GetCargoesRules API
		cargoRulesResp, err := client.FBO.GetCargoesRules(supplyIDs)
		if err != nil {
			fmt.Printf("  ❌ 获取供应单 %d 货物规则失败: %v\n", order.SupplyOrderId, err)
			continue
		}

		// 验证响应
		if cargoRulesResp == nil {
			fmt.Printf("  ❌ 供应单 %d 货物规则响应为空\n", order.SupplyOrderId)
			continue
		}

		// 打印货物规则信息
		fmt.Printf("  ✅ 成功获取货物规则信息\n")
		fmt.Printf("  - 供应检查列表数量: %d\n", len(cargoRulesResp.SupplyCheckLists))

		// 打印每个供应的检查状态
		if len(cargoRulesResp.SupplyCheckLists) > 0 {
			fmt.Printf("  \n  供应检查详情:\n")
			for j, checkList := range cargoRulesResp.SupplyCheckLists {
				fmt.Printf("    检查项 #%d:\n", j+1)
				fmt.Printf("      - 供应ID: %d\n", checkList.SupplyID)
				fmt.Printf("      - 检查状态: %t", checkList.Satisfied)
				if checkList.Satisfied {
					fmt.Printf(" ✅ (满足要求)\n")
				} else {
					fmt.Printf(" ❌ (不满足要求)\n")
				}
			}
		} else {
			fmt.Printf("  - 该供应单暂无检查列表信息\n")
		}

		fmt.Printf("  ✅ 供应单 #%d 货物规则验证完成\n", i+1)
	}

	// 如果跳过了一些供应单，显示提示信息
	if len(detailResp.Orders) > cargoTestCount {
		fmt.Printf("\n💡 为避免输出过多内容，跳过了剩余 %d 个供应单的货物规则查询\n",
			len(detailResp.Orders)-cargoTestCount)
	}

	// 步骤4: 验证和显示仓库信息
	fmt.Println("\n步骤4: 仓库信息")
	warehouseMap := make(map[int64]ozonapi.SupplyWarehouse)
	for _, warehouse := range detailResp.Warehouses {
		warehouseMap[warehouse.WarehouseId] = warehouse
		fmt.Printf("仓库ID: %d, 名称: %s, 地址: %s\n",
			warehouse.WarehouseId, warehouse.Name, warehouse.Address)
	}

	// 步骤5: 详细验证每个供应单
	fmt.Println("\n步骤5: 供应单详情验证")
	for i, order := range detailResp.Orders {
		fmt.Printf("\n--- 供应单 #%d ---\n", i+1)

		// 基本信息验证
		fmt.Printf("供应单ID: %d\n", order.SupplyOrderId)
		fmt.Printf("供应单号: %s\n", order.SupplyOrderNumber)
		fmt.Printf("状态: %s\n", order.State)
		fmt.Printf("创建日期: %s\n", order.CreationDate)
		fmt.Printf("数据填充截止时间: %s\n", order.DataFillingDeadlineUtc.Format("2006-01-02 15:04:05"))

		// 布尔字段验证
		fmt.Printf("可取消: %t\n", order.CanCancel)
		fmt.Printf("经济模式: %t\n", order.IsEconom)
		fmt.Printf("Super FBO: %t\n", order.IsSuperFbo)
		fmt.Printf("虚拟订单: %t\n", order.IsVirtual)
		fmt.Printf("产品Super FBO: %t\n", order.ProductSuperFbo)

		// 仓库信息验证
		fmt.Printf("发货仓库ID: %d\n", order.DropoffWarehouseId)
		if warehouse, exists := warehouseMap[order.DropoffWarehouseId]; exists {
			fmt.Printf("发货仓库名称: %s\n", warehouse.Name)
			fmt.Printf("发货仓库地址: %s\n", warehouse.Address)
		}

		// 供应信息验证
		fmt.Printf("供应数量: %d\n", len(order.Supplies))
		for j, supply := range order.Supplies {
			if j >= 3 { // 只显示前3个供应
				fmt.Printf("  ... 还有 %d 个供应\n", len(order.Supplies)-3)
				break
			}
			fmt.Printf("  供应 #%d:\n", j+1)
			fmt.Printf("    - 供应ID: %d\n", supply.SupplyID)
			fmt.Printf("    - 捆绑ID: %s\n", supply.BundleID)
			fmt.Printf("    - 存储仓库ID: %d\n", supply.StorageWarehouseID)
			fmt.Printf("    - 供应状态: %s\n", supply.SupplyState)
			//fmt.Printf("    - 标签数量: %d\n", len(supply.SupplyTags))
			//
			//// 显示供应标签
			//if len(supply.SupplyTags) > 0 {
			//	tag := supply.SupplyTags[0]
			//	fmt.Printf("    - 标签信息: ETTN=%t, EVSD=%t, 珠宝=%t, 可标记=%t, 必须标记=%t, 可追踪=%t\n",
			//		tag.IsETTNRequired, tag.IsEVSDRequired, tag.IsJewelry,
			//		tag.IsMarkingPossible, tag.IsMarkingRequired, tag.IsTraceable)
			//}
		}

		// 时间段信息验证 (修复数据结构访问错误)
		if order.Timeslot != nil {
			fmt.Printf("时间段信息:\n")
			fmt.Printf("  - 可设置: %t\n", order.Timeslot.CanSet)
			fmt.Printf("  - 必需: %t\n", order.Timeslot.IsRequired)
			if len(order.Timeslot.CanNotSetReasons) > 0 {
				fmt.Printf("  - 不能设置原因: %v\n", order.Timeslot.CanNotSetReasons)
			}
			if order.Timeslot.Value != nil {
				// 修复: Timeslot 是单个对象，不是数组
				fmt.Printf("  - 时间段信息: 已设置\n")

				// 修复: 直接访问 Timeslot 对象，不是数组
				slot := order.Timeslot.Value.Timeslot
				if !slot.From.IsZero() && !slot.To.IsZero() {
					fmt.Printf("  - 时间段: %s 到 %s\n",
						slot.From.Format("2006-01-02 15:04:05"),
						slot.To.Format("2006-01-02 15:04:05"))
				} else {
					fmt.Printf("  - 时间段: 未设置具体时间\n")
				}

				// 修复: TimezoneInfo 是单个对象，不是数组
				tz := order.Timeslot.Value.TimezoneInfo
				if tz.IanaName != "" || tz.Offset != "" {
					fmt.Printf("  - 时区: %s (偏移: %s)\n", tz.IanaName, tz.Offset)
				} else {
					fmt.Printf("  - 时区: 未设置\n")
				}
			} else {
				fmt.Printf("  - 时间段值: 未设置\n")
			}
		}

		// 车辆信息验证
		if order.Vehicle != nil {
			fmt.Printf("车辆信息:\n")
			fmt.Printf("  - 可设置: %t\n", order.Vehicle.CanSet)
			fmt.Printf("  - 必需: %t\n", order.Vehicle.IsRequired)
			if len(order.Vehicle.CanNotSetReasons) > 0 {
				fmt.Printf("  - 不能设置原因: %v\n", order.Vehicle.CanNotSetReasons)
			}
			if len(order.Vehicle.Value) > 0 {
				vehicle := order.Vehicle.Value[0]
				fmt.Printf("  - 司机姓名: %s\n", vehicle.DriverName)
				fmt.Printf("  - 司机电话: %s\n", vehicle.DriverPhone)
				fmt.Printf("  - 车辆型号: %s\n", vehicle.VehicleModel)
				fmt.Printf("  - 车牌号: %s\n", vehicle.VehicleNumber)
			}
		}

		fmt.Printf("✅ 供应单 #%d 验证完成\n", i+1)
	}

}

// TestPDFEditForFBODocuments 测试FBO相关PDF文档编辑功能
func TestPDFEditForFBODocuments(t *testing.T) {
	fmt.Println("=== FBO PDF文档编辑功能测试 ===")

	// 创建测试输出目录
	outputDir := "/Users/<USER>/GolandProjects/lens/internal/service/test"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		t.Fatalf("创建输出目录失败: %v", err)
	}

	// 步骤1: 测试现有PDF文件页码添加
	fmt.Println("\n步骤1: 测试现有PDF文件页码添加")
	if err := testAddPageNumbersToExistingPDF(outputDir); err != nil {
		t.Errorf("现有PDF文件页码添加测试失败: %v", err)
	} else {
		fmt.Println("  ✅ 现有PDF文件页码添加测试成功")
	}

	fmt.Println("\n🎉 所有PDF编辑测试完成")
}

// TestPDFTextAppend 测试在PDF中查找文本并添加新文字的功能
func TestPDFTextAppend(t *testing.T) {
	fmt.Println("=== PDF文本查找和添加功能测试 ===")

	// 创建测试输出目录
	outputDir := "/Users/<USER>/GolandProjects/lens/internal/service/test"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		t.Fatalf("创建输出目录失败: %v", err)
	}

	// 测试查找和添加文字功能
	fmt.Println("\n步骤1: 查找文本 'ID ГРУЗОВОГО МЕСТА' 并添加新文字")

	inputPath := outputDir + "/" + "2000026646108.pdf"
	outputPath := filepath.Join(outputDir, "2000026646108_with_appended_text.pdf")
	searchText := "ID ГРУЗОВОГО МЕСТА"
	appendText := " MKL-XXXXX01"

	// 检查输入文件是否存在
	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		t.Errorf("输入文件 %s 不存在，请确保文件存在后再运行测试", inputPath)
		return
	}

	// 执行文本查找和添加
	if err := findTextAndAppendInPDF(inputPath, outputPath, searchText, appendText); err != nil {
		t.Errorf("文本查找和添加失败: %v", err)
	} else {
		fmt.Printf("  ✅ 成功在文本 '%s' 后添加 '%s'\n", searchText, appendText)
		fmt.Printf("  ✅ 修改后的PDF已保存到: %s\n", outputPath)
	}

	fmt.Println("\n🎉 PDF文本添加测试完成")
}

// PackageUnit 装箱数据结构
type PackageUnit struct {
	ExternalPackageUnitId string `json:"externalPackageUnitId"`
	Type                  string `json:"type"`
	ContractorItemCode    string `json:"contractorItemCode"`
}

// TestPDFLabelProcessing 测试PDF标签处理功能
func TestPDFLabelProcessing(t *testing.T) {
	fmt.Println("=== PDF标签处理功能测试 ===")

	// 创建测试输出目录
	outputDir := "/Users/<USER>/GolandProjects/lens/internal/service/test"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		t.Fatalf("创建输出目录失败: %v", err)
	}

	// 装箱数据JSON
	packageDataJSON := `[
		{
			"externalPackageUnitId": "1022026390994000",
			"type": "PACKAGE_UNIT_TYPE_PALLET",
			"contractorItemCode": "MLN101-6"
		},
		{
			"externalPackageUnitId": "1022026398520000",
			"type": "PACKAGE_UNIT_TYPE_PALLET",
			"contractorItemCode": "MLN101-7"
		}
	]`

	fmt.Println("\n步骤1: 解析装箱数据JSON")

	// 解析JSON数据
	var packageUnits []PackageUnit
	if err := json.Unmarshal([]byte(packageDataJSON), &packageUnits); err != nil {
		t.Errorf("解析JSON数据失败: %v", err)
		return
	}

	fmt.Printf("  ✅ 成功解析 %d 个装箱项\n", len(packageUnits))
	for i, unit := range packageUnits {
		fmt.Printf("    - 装箱项 %d: ID=%s, Type=%s, Code=%s\n",
			i+1, unit.ExternalPackageUnitId, unit.Type, unit.ContractorItemCode)
	}

	// 处理PDF标签
	fmt.Println("\n步骤2: 处理PDF标签")

	inputPath := outputDir + "/2000026646108.pdf"
	outputPath := filepath.Join(outputDir, "2000026646108_with_labels.pdf")

	// 检查输入文件是否存在
	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		t.Errorf("输入文件 %s 不存在，请确保文件存在后再运行测试", inputPath)
		return
	}

	// 执行PDF标签处理
	if err := processPDFLabels(inputPath, outputPath, packageUnits); err != nil {
		t.Errorf("PDF标签处理失败: %v", err)
	} else {
		fmt.Printf("  ✅ 成功处理PDF标签\n")
		fmt.Printf("  ✅ 修改后的PDF已保存到: %s\n", outputPath)
	}

	fmt.Println("\n🎉 PDF标签处理测试完成")
}

// processPDFLabels 处理PDF标签，根据装箱数据插入标签文本
func processPDFLabels(inputPath, outputPath string, packageUnits []PackageUnit) error {
	// 打开PDF文件
	f, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("无法打开文件 %q: %w", inputPath, err)
	}
	defer f.Close()

	// 创建PDF读取器
	pdfReader, err := model.NewPdfReaderLazy(f)
	if err != nil {
		return fmt.Errorf("创建PDF读取器失败: %w", err)
	}

	// 获取页面数量
	numPages, err := pdfReader.GetNumPages()
	if err != nil {
		return fmt.Errorf("获取页面数量失败: %w", err)
	}

	fmt.Printf("    - PDF文件有 %d 页\n", numPages)

	// 为每个装箱项查找对应的页面和位置
	labelInsertions := make([]LabelInsertion, 0)

	for _, unit := range packageUnits {
		fmt.Printf("    - 处理装箱项: %s\n", unit.ExternalPackageUnitId)

		// 查找包含该ID的页面
		pageNum, err := findPageContainingID(pdfReader, unit.ExternalPackageUnitId, numPages)
		if err != nil {
			fmt.Printf("      ⚠️ 查找ID失败: %v\n", err)
			continue
		}

		if pageNum == -1 {
			fmt.Printf("      ⚠️ 未找到包含ID '%s' 的页面\n", unit.ExternalPackageUnitId)
			continue
		}

		fmt.Printf("      ✅ 在第 %d 页找到ID '%s'\n", pageNum, unit.ExternalPackageUnitId)

		// 在该页面查找 "ID ГРУЗОВОГО МЕСТА" 的位置
		targetText := "ID ГРУЗОВОГО МЕСТА"
		locations, err := findTextInSpecificPage(pdfReader, targetText, pageNum)
		if err != nil {
			fmt.Printf("      ⚠️ 查找目标文本失败: %v\n", err)
			continue
		}

		if len(locations) == 0 {
			fmt.Printf("      ⚠️ 在第 %d 页未找到文本 '%s'\n", pageNum, targetText)
			continue
		}

		// 确定要插入的文本
		insertText := getInsertTextByType(unit.Type, unit.ContractorItemCode)

		// 记录插入信息
		for _, loc := range locations {
			insertion := LabelInsertion{
				PageNum:    pageNum,
				Location:   loc,
				InsertText: insertText,
				UnitID:     unit.ExternalPackageUnitId,
			}
			labelInsertions = append(labelInsertions, insertion)
		}

		fmt.Printf("      ✅ 准备在第 %d 页插入文本: '%s'\n", pageNum, insertText)
	}

	// 创建修改后的PDF
	if len(labelInsertions) == 0 {
		return fmt.Errorf("没有找到任何可插入的位置")
	}

	fmt.Printf("    - 总共准备插入 %d 个标签\n", len(labelInsertions))

	err = createPDFWithLabels(pdfReader, labelInsertions, outputPath, numPages)
	if err != nil {
		return fmt.Errorf("创建带标签的PDF失败: %w", err)
	}

	return nil
}

// LabelInsertion 标签插入信息
type LabelInsertion struct {
	PageNum    int
	Location   TextLocationInfo
	InsertText string
	UnitID     string
}

// findPageContainingID 查找包含指定ID的页面
func findPageContainingID(pdfReader *model.PdfReader, targetID string, numPages int) (int, error) {
	for pageNum := 1; pageNum <= numPages; pageNum++ {
		page, err := pdfReader.GetPage(pageNum)
		if err != nil {
			return -1, fmt.Errorf("获取第%d页失败: %w", pageNum, err)
		}

		// 创建文本提取器
		ex, err := extractor.New(page)
		if err != nil {
			return -1, fmt.Errorf("创建文本提取器失败，页面%d: %w", pageNum, err)
		}

		// 提取页面文本
		pageText, _, _, err := ex.ExtractPageText()
		if err != nil {
			return -1, fmt.Errorf("提取页面文本失败，页面%d: %w", pageNum, err)
		}

		text := pageText.Text()

		// 检查是否包含目标ID
		if strings.Contains(text, targetID) {
			return pageNum, nil
		}
	}

	return -1, nil // 未找到
}

// findTextInSpecificPage 在指定页面查找文本位置
func findTextInSpecificPage(pdfReader *model.PdfReader, searchText string, pageNum int) ([]TextLocationInfo, error) {
	page, err := pdfReader.GetPage(pageNum)
	if err != nil {
		return nil, fmt.Errorf("获取第%d页失败: %w", pageNum, err)
	}

	// 创建文本提取器
	ex, err := extractor.New(page)
	if err != nil {
		return nil, fmt.Errorf("创建文本提取器失败，页面%d: %w", pageNum, err)
	}

	// 提取页面文本
	pageText, _, _, err := ex.ExtractPageText()
	if err != nil {
		return nil, fmt.Errorf("提取页面文本失败，页面%d: %w", pageNum, err)
	}

	text := pageText.Text()
	textMarks := pageText.Marks()

	// 查找所有匹配的文本
	matches, err := getTextMatchesInPage(text, textMarks, searchText, pageNum)
	if err != nil {
		return nil, fmt.Errorf("获取文本匹配失败，页面%d: %w", pageNum, err)
	}

	return matches, nil
}

// getInsertTextByType 根据类型确定要插入的文本
func getInsertTextByType(packageType, contractorItemCode string) string {
	switch packageType {
	case "PACKAGE_UNIT_TYPE_PALLET":
		return " " + contractorItemCode
	case "PACKAGE_UNIT_TYPE_BOXS":
		return " " + contractorItemCode
	default:
		return " " + contractorItemCode
	}
}

// createPDFWithLabels 创建带标签的PDF文件
func createPDFWithLabels(pdfReader *model.PdfReader, insertions []LabelInsertion, outputPath string, numPages int) error {
	// 创建PDF创建器
	c := creator.New()

	// 按页面分组插入信息
	pageInsertions := make(map[int][]LabelInsertion)
	for _, insertion := range insertions {
		pageInsertions[insertion.PageNum] = append(pageInsertions[insertion.PageNum], insertion)
	}

	// 处理每一页
	for pageNum := 1; pageNum <= numPages; pageNum++ {
		page, err := pdfReader.GetPage(pageNum)
		if err != nil {
			return fmt.Errorf("获取第%d页失败: %w", pageNum, err)
		}

		// 添加原始页面
		err = c.AddPage(page)
		if err != nil {
			return fmt.Errorf("添加页面失败，页面%d: %w", pageNum, err)
		}

		// 如果这一页有要插入的标签，添加标签文本
		if insertionList, exists := pageInsertions[pageNum]; exists {
			for _, insertion := range insertionList {
				err = addLabelTextToPDF(c, insertion)
				if err != nil {
					return fmt.Errorf("添加标签文本失败，页面%d: %w", pageNum, err)
				}
			}
		}
	}

	// 保存PDF文件
	err := c.WriteToFile(outputPath)
	if err != nil {
		return fmt.Errorf("保存PDF文件失败: %w", err)
	}

	return nil
}

// addLabelTextToPDF 在PDF中添加标签文本
func addLabelTextToPDF(c *creator.Creator, insertion LabelInsertion) error {
	// 使用支持俄语的字体
	font, err := loadRussianSupportedFont()
	if err != nil {
		return fmt.Errorf("无法加载支持俄语的字体: %w", err)
	}

	// 计算文本宽度以实现右对齐
	fontSize := insertion.Location.FontSize + 12
	textWidth := calculateTextWidth(insertion.InsertText, font, fontSize)

	// 计算右对齐的X坐标位置
	// 从原文本右侧开始，向左偏移文本宽度，再加上适当间距
	rightMargin := insertion.Location.BBox.Urx + 182 // 右边界位置
	xPos := rightMargin - textWidth                  // 右对齐位置
	yPos := insertion.Location.BBox.Lly - 16         // Y坐标保持不变

	// 确保文本不会超出页面左边界
	if xPos < 10 { // 最小左边距10个点
		xPos = 10
	}

	// 创建段落
	p := c.NewStyledParagraph()
	p.SetText(insertion.InsertText)
	p.SetFont(font)
	p.SetFontSize(fontSize)
	p.SetPos(xPos, yPos)

	// 绘制文本
	err = c.Draw(p)
	if err != nil {
		return fmt.Errorf("绘制文本失败: %w", err)
	}

	return nil
}

// findTextAndAppendInPDF 在PDF中查找指定文本并在其后添加新文字
func findTextAndAppendInPDF(inputPath, outputPath, searchText, appendText string) error {

	// 打开PDF文件
	f, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("无法打开文件 %q: %w", inputPath, err)
	}
	defer f.Close()

	// 创建PDF读取器
	pdfReader, err := model.NewPdfReaderLazy(f)
	if err != nil {
		return fmt.Errorf("创建PDF读取器失败: %w", err)
	}

	// 获取页面数量
	numPages, err := pdfReader.GetNumPages()
	if err != nil {
		return fmt.Errorf("获取页面数量失败: %w", err)
	}

	fmt.Printf("    - 正在搜索文本 '%s' 在 %d 页中...\n", searchText, numPages)

	// 查找文本位置
	textLocations, err := findTextLocationsInPDF(pdfReader, searchText, numPages)
	if err != nil {
		return fmt.Errorf("查找文本位置失败: %w", err)
	}

	if len(textLocations) == 0 {
		return fmt.Errorf("未找到文本 '%s'", searchText)
	}

	fmt.Printf("    - 找到 %d 个匹配的文本位置\n", len(textLocations))

	// 创建修改后的PDF
	err = createModifiedPDFWithText(pdfReader, textLocations, appendText, outputPath)
	if err != nil {
		return fmt.Errorf("创建修改后的PDF失败: %w", err)
	}

	return nil
}

// TextLocationInfo 表示找到的文本位置信息
type TextLocationInfo struct {
	Text     string
	BBox     model.PdfRectangle
	PageNum  int
	FontName string
	FontSize float64
}

// findTextLocationsInPDF 在PDF中查找指定文本的位置
func findTextLocationsInPDF(pdfReader *model.PdfReader, searchText string, numPages int) ([]TextLocationInfo, error) {
	var locations []TextLocationInfo

	for pageNum := 1; pageNum <= numPages; pageNum++ {
		page, err := pdfReader.GetPage(pageNum)
		if err != nil {
			return nil, fmt.Errorf("获取第%d页失败: %w", pageNum, err)
		}

		// 创建文本提取器
		ex, err := extractor.New(page)
		if err != nil {
			return nil, fmt.Errorf("创建文本提取器失败，页面%d: %w", pageNum, err)
		}

		// 提取页面文本
		pageText, _, _, err := ex.ExtractPageText()
		if err != nil {
			return nil, fmt.Errorf("提取页面文本失败，页面%d: %w", pageNum, err)
		}

		text := pageText.Text()
		textMarks := pageText.Marks()

		// 查找所有匹配的文本
		matches, err := getTextMatchesInPage(text, textMarks, searchText, pageNum)
		if err != nil {
			return nil, fmt.Errorf("获取文本匹配失败，页面%d: %w", pageNum, err)
		}

		locations = append(locations, matches...)
	}

	return locations, nil
}

// getTextMatchesInPage 获取页面中所有匹配的文本位置
func getTextMatchesInPage(text string, textMarks *extractor.TextMarkArray, searchText string, pageNum int) ([]TextLocationInfo, error) {
	var locations []TextLocationInfo

	// 查找所有匹配的索引
	indexes := findAllTextIndexes(text, searchText)
	if len(indexes) == 0 {
		return locations, nil
	}

	for _, start := range indexes {
		end := start + len(searchText)

		// 获取文本范围的标记
		spanMarks, err := textMarks.RangeOffset(start, end)
		if err != nil {
			return nil, fmt.Errorf("获取文本范围标记失败: %w", err)
		}

		// 获取边界框
		bbox, ok := spanMarks.BBox()
		if !ok {
			continue // 跳过没有边界框的文本
		}

		// 尝试获取字体信息
		fontSize := float64(12) // 默认字体大小
		fontName := "Helvetica" // 默认字体

		if spanMarks.Len() > 0 {
			mark := spanMarks.Elements()[0]
			if mark.Font != nil && mark.Font.FontDescriptor() != nil {
				if mark.Font.FontDescriptor().FontName != nil {
					fontName = mark.Font.FontDescriptor().FontName.String()
				}
			}
			if mark.FontSize > 0 {
				fontSize = mark.FontSize
			}
		}

		location := TextLocationInfo{
			Text:     searchText,
			BBox:     bbox,
			PageNum:  pageNum,
			FontName: fontName,
			FontSize: fontSize,
		}

		locations = append(locations, location)
	}

	return locations, nil
}

// findAllTextIndexes 查找文本中所有匹配的索引位置
func findAllTextIndexes(text, searchText string) []int {
	var indexes []int
	start := 0

	for {
		index := strings.Index(text[start:], searchText)
		if index == -1 {
			break
		}
		indexes = append(indexes, start+index)
		start += index + len(searchText)
	}

	return indexes
}

// createModifiedPDFWithText 创建修改后的PDF文件
func createModifiedPDFWithText(pdfReader *model.PdfReader, locations []TextLocationInfo, appendText, outputPath string) error {
	// 创建PDF创建器
	c := creator.New()

	// 获取页面数量
	numPages, err := pdfReader.GetNumPages()
	if err != nil {
		return fmt.Errorf("获取页面数量失败: %w", err)
	}

	// 按页面分组位置
	pageLocations := make(map[int][]TextLocationInfo)
	for _, loc := range locations {
		pageLocations[loc.PageNum] = append(pageLocations[loc.PageNum], loc)
	}

	// 处理每一页
	for pageNum := 1; pageNum <= numPages; pageNum++ {
		page, err := pdfReader.GetPage(pageNum)
		if err != nil {
			return fmt.Errorf("获取第%d页失败: %w", pageNum, err)
		}

		// 添加原始页面
		err = c.AddPage(page)
		if err != nil {
			return fmt.Errorf("添加页面失败，页面%d: %w", pageNum, err)
		}

		// 如果这一页有匹配的文本，添加新文本
		if locs, exists := pageLocations[pageNum]; exists {
			for _, loc := range locs {
				err = addTextAfterLocationInPDF(c, loc, appendText)
				if err != nil {
					return fmt.Errorf("添加文本失败，页面%d: %w", pageNum, err)
				}
			}
		}
	}

	// 保存PDF文件
	err = c.WriteToFile(outputPath)
	if err != nil {
		return fmt.Errorf("保存PDF文件失败: %w", err)
	}

	return nil
}

// addTextAfterLocationInPDF 在指定位置后添加文本（右对齐）
func addTextAfterLocationInPDF(c *creator.Creator, location TextLocationInfo, appendText string) error {
	// 使用支持俄语的字体
	font, err := loadRussianSupportedFont()
	if err != nil {
		return fmt.Errorf("无法加载支持俄语的字体: %w", err)
	}

	// 计算文本宽度以实现右对齐
	fontSize := location.FontSize
	textWidth := calculateTextWidth(appendText, font, fontSize)

	// 计算右对齐的X坐标位置
	rightMargin := location.BBox.Urx + 55 // 右边界位置
	xPos := rightMargin - textWidth       // 右对齐位置
	yPos := location.BBox.Lly             // Y坐标保持不变

	// 确保文本不会超出页面左边界
	if xPos < 10 { // 最小左边距10个点
		xPos = 10
	}

	// 创建段落
	p := c.NewStyledParagraph()
	p.SetText(appendText)
	p.SetFont(font)
	p.SetFontSize(fontSize)
	p.SetPos(xPos, yPos)

	// 绘制文本
	err = c.Draw(p)
	if err != nil {
		return fmt.Errorf("绘制文本失败: %w", err)
	}

	return nil
}

// testAddPageNumbersToExistingPDF 测试在现有PDF文件中添加页码
func testAddPageNumbersToExistingPDF(outputDir string) error {
	fmt.Println("  📄 处理现有PDF文件添加页码...")

	// 指定输入PDF文件路径
	inputPDFPath := outputDir + "/2000026646108.pdf"

	// 检查输入文件是否存在
	if _, err := os.Stat(inputPDFPath); os.IsNotExist(err) {
		return fmt.Errorf("输入PDF文件 %s 不存在，请确保文件存在后再运行测试", inputPDFPath)
	}

	// 输出文件路径
	outputPDFPath := filepath.Join(outputDir, "2000026646108_with_page_numbers.pdf")

	if err := addPageNumbersWithUniPDF(inputPDFPath, outputPDFPath); err != nil {
		return fmt.Errorf("添加页码失败: %w", err)
	}

	fmt.Printf("  ✅ 已添加页码的PDF文件保存到: %s\n", outputPDFPath)
	return nil
}

// addPageNumbersWithUniPDF 使用unipdf库在现有PDF中添加页码
func addPageNumbersWithUniPDF(inputPath, outputPath string) error {
	// 读取现有PDF文件
	f, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("打开PDF文件失败: %w", err)
	}
	defer f.Close()

	// 解析PDF文档
	pdfReader, err := model.NewPdfReader(f)
	if err != nil {
		return fmt.Errorf("解析PDF文件失败: %w", err)
	}

	// 获取页面数量
	pageCount, err := pdfReader.GetNumPages()
	if err != nil {
		return fmt.Errorf("获取页面数量失败: %w", err)
	}

	fmt.Printf("    - 检测到PDF文件有 %d 页\n", pageCount)

	// 使用creator来处理PDF页面和文本添加
	c := creator.New()

	// 处理每一页
	for i := 1; i <= pageCount; i++ {
		// 获取页面
		page, err := pdfReader.GetPage(i)
		if err != nil {
			return fmt.Errorf("获取第%d页失败: %w", i, err)
		}

		// 添加页面到creator
		err = c.AddPage(page)
		if err != nil {
			return fmt.Errorf("添加第%d页到creator失败: %w", i, err)
		}

		// 添加页码到页面
		if err := addPageNumberToPage(c, page, i, pageCount); err != nil {
			return fmt.Errorf("添加页码到第%d页失败: %w", i, err)
		}

		fmt.Printf("    - 已为第 %d 页添加页码\n", i)
	}

	// 保存修改后的PDF
	if err := c.WriteToFile(outputPath); err != nil {
		return fmt.Errorf("写入PDF文件失败: %w", err)
	}

	fmt.Printf("    - 成功为所有 %d 页添加了页码\n", pageCount)
	return nil
}

// addPageNumberToPage 在页面上添加页码 - 使用creator方法进行精确文本定位
func addPageNumberToPage(c *creator.Creator, page *model.PdfPage, pageNum, totalPages int) error {
	// 创建页码文本
	pageText := fmt.Sprintf("第%d页", pageNum)

	// 获取页面尺寸
	mediaBox, err := page.GetMediaBox()
	if err != nil {
		return fmt.Errorf("获取页面尺寸失败: %w", err)
	}

	// 计算页码位置（右下角）
	xPos := mediaBox.Urx - 80 // 距离右边80点
	yPos := mediaBox.Lly + 20 // 距离底部20点

	// 使用creator添加文本到指定位置
	if err := addTextToPageAtPosition(c, pageText, xPos, yPos); err != nil {
		return fmt.Errorf("添加页码文本失败: %w", err)
	}

	return nil
}

// addTextToPageAtPosition 在指定位置添加文本 - 基于addTextToPdf的实现
func addTextToPageAtPosition(c *creator.Creator, text string, xPos, yPos float64) error {
	// 创建样式化段落
	p := c.NewStyledParagraph()
	p.SetText(text)

	// 设置字体 - 使用Times-Bold字体（与addTextToPdf保持一致）
	timesBold, err := model.NewStandard14Font("Times-Bold")
	if err != nil {
		// 如果Times-Bold不可用，回退到Helvetica-Bold
		helveticaBold, err := model.NewStandard14Font("Helvetica-Bold")
		if err != nil {
			return fmt.Errorf("无法加载字体: %w", err)
		}
		p.SetFont(helveticaBold)
	} else {
		p.SetFont(timesBold)
	}

	// 设置字体大小
	p.SetFontSize(10)

	// 设置位置
	p.SetPos(xPos, yPos)

	// 绘制文本
	if err := c.Draw(p); err != nil {
		return fmt.Errorf("绘制文本失败: %w", err)
	}

	return nil
}

// addTextToPDFAtPosition 通用的文本添加函数 - 可以在任意位置添加文本
func addTextToPDFAtPosition(inputPath, outputPath, text string, pageNum int, xPos, yPos float64, fontSize float64) error {
	// 读取输入PDF文件
	f, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("打开PDF文件失败: %w", err)
	}
	defer f.Close()

	pdfReader, err := model.NewPdfReader(f)
	if err != nil {
		return fmt.Errorf("解析PDF文件失败: %w", err)
	}

	numPages, err := pdfReader.GetNumPages()
	if err != nil {
		return fmt.Errorf("获取页面数量失败: %w", err)
	}

	c := creator.New()

	// 加载所有页面
	for i := 0; i < numPages; i++ {
		page, err := pdfReader.GetPage(i + 1)
		if err != nil {
			return fmt.Errorf("获取第%d页失败: %w", i+1, err)
		}

		err = c.AddPage(page)
		if err != nil {
			return fmt.Errorf("添加第%d页失败: %w", i+1, err)
		}

		// 如果是指定页面或所有页面（pageNum == -1），添加文本
		if i+1 == pageNum || pageNum == -1 {
			p := c.NewStyledParagraph()
			p.SetText(text)

			// 设置字体
			timesBold, err := model.NewStandard14Font("Times-Bold")
			if err != nil {
				helveticaBold, err := model.NewStandard14Font("Helvetica-Bold")
				if err != nil {
					return fmt.Errorf("无法加载字体: %w", err)
				}
				p.SetFont(helveticaBold)
			} else {
				p.SetFont(timesBold)
			}

			// 设置字体大小
			if fontSize > 0 {
				p.SetFontSize(fontSize)
			} else {
				p.SetFontSize(10) // 默认字体大小
			}

			// 设置位置
			p.SetPos(xPos, yPos)

			// 绘制文本
			if err := c.Draw(p); err != nil {
				return fmt.Errorf("绘制文本失败: %w", err)
			}
		}
	}

	// 写入输出文件
	err = c.WriteToFile(outputPath)
	if err != nil {
		return fmt.Errorf("写入输出文件失败: %w", err)
	}

	return nil
}

// loadRussianSupportedFont 加载支持俄语的字体
func loadRussianSupportedFont() (*model.PdfFont, error) {
	// 尝试多种方法加载支持俄语的字体

	//// 方法1: 尝试加载系统中的支持俄语的字体
	//fontPaths := []string{
	//	"/System/Library/Fonts/Arial Unicode.ttf",         // macOS Arial Unicode
	//	"/System/Library/Fonts/Helvetica.ttc",             // macOS Helvetica
	//	"/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", // Linux DejaVu Sans
	//	"/Windows/Fonts/arial.ttf",                        // Windows Arial
	//	"/Windows/Fonts/calibri.ttf",                      // Windows Calibri
	//	"./fonts/DejaVuSans.ttf",                          // 项目本地字体
	//	"./fonts/arial.ttf",                               // 项目本地字体
	//}
	//
	//for _, fontPath := range fontPaths {
	//	if _, err := os.Stat(fontPath); err == nil {
	//		// 字体文件存在，尝试加载
	//		font, err := model.NewPdfFontFromTTFFile(fontPath)
	//		if err == nil {
	//			return font, nil
	//		}
	//	}
	//}

	// 方法2: 如果没有找到 TrueType 字体，尝试使用 unipdf 内置的复合字体
	// 注意：这可能需要 unipdf 的特定版本支持
	if _, err := os.Stat("/Users/<USER>/GolandProjects/lens/fonts/DejaVuSansCondensed-Bold.ttf"); err == nil {
		font, err := model.NewCompositePdfFontFromTTFFile("/Users/<USER>/GolandProjects/lens/fonts/DejaVuSansCondensed-Bold.ttf")
		if err == nil {
			return font, nil
		}
	}

	// 方法3: 回退到标准字体
	// 使用 Times-Roman，它比 Helvetica 有稍好的 Unicode 支持
	font, err := model.NewStandard14Font(model.TimesRomanName)
	if err == nil {
		return font, nil
	}

	// 方法4: 最后的回退选项 - Helvetica
	return model.NewStandard14Font(model.HelveticaName)
}

// calculateTextWidth 计算文本宽度
func calculateTextWidth(text string, font *model.PdfFont, fontSize float64) float64 {
	// 方法1: 尝试使用字体的度量信息计算文本宽度
	if font != nil {
		// 获取字体度量
		metrics, _ := font.GetFontDescriptor()
		if metrics != nil {
			// 估算文本宽度
			// 这是一个近似计算，实际宽度可能略有不同
			avgCharWidth := fontSize * 0.6 // 平均字符宽度估算
			return float64(len([]rune(text))) * avgCharWidth
		}
	}

	// 方法2: 回退到基于字符数的估算
	// 对于俄语文本，使用稍微宽一点的估算
	avgCharWidth := fontSize * 0.65 // 俄语字符通常稍宽
	runeCount := len([]rune(text))  // 正确计算Unicode字符数

	return float64(runeCount) * avgCharWidth
}
