package main

import (
	"context"
	"encoding/json"
	"fmt"
	wbhubapi "lens/internal/api/wb"
	wb_web "lens/internal/api/wb_buyer"
	wb_api "lens/internal/api/wb_seller"
	"lens/internal/api/wb_seller/supply"
	dingding "lens/internal/infrastructure/dingding"
	rocket_mq "lens/internal/infrastructure/rocketmq"
	"lens/internal/service"
	"log"
	"math/rand"
	"strings"
	"testing"
	"time"

	"github.com/apache/rocketmq-client-go/v2/primitive"
)

func TestSiteProductInfo(t *testing.T) {
	api := wb_api.Init()

	keywords, err := api.GetAllWeeklyTrendingSearches("светильник")
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(keywords)
}

func TestSiteProductInfo2(t *testing.T) {
	web := wb_web.Init()

	products, err := web.GetRecommendSearch("*********", 1)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(products)
}

func TestAdvertManager(t *testing.T) {
	// 请替换为实际的API Key
	apiKey := "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

	// 创建广告管理器
	manager, err := service.NewAdvertManager(apiKey)
	if err != nil {
		t.Fatalf("创建广告管理器失败: %v", err)
	}

	// 暂停所有运行中的广告
	if err := manager.PauseAllRunningAdverts(); err != nil {
		t.Fatalf("暂停广告失败: %v", err)
	}

	// 获取暂停的广告数量
	pausedAdverts := manager.GetPausedAdverts()
	t.Logf("成功暂停 %d 个广告", len(pausedAdverts))

	// 等待3分钟
	t.Log("等待3分钟后启动广告...")
	time.Sleep(3 * time.Minute)

	// 启动所有暂停的广告
	if err := manager.StartAllPausedAdverts(); err != nil {
		t.Fatalf("启动广告失败: %v", err)
	}

	// 验证是否所有广告都已启动
	remainingPaused := manager.GetPausedAdverts()
	if len(remainingPaused) > 0 {
		t.Errorf("仍有 %d 个广告未启动", len(remainingPaused))
	} else {
		t.Log("所有广告已成功启动")
	}
}

func TestGetStatWords(t *testing.T) {
	// 创建 API 客户端
	client := wbhubapi.NewClient("******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")

	// 获取广告词组统计数据
	advertID := 26236361
	resp, err := client.Promotion.GetStatWords(advertID)
	if err != nil {
		t.Fatalf("获取广告词组统计失败: %v", err)
	}

	// 验证响应数据
	if resp == nil {
		t.Fatal("响应数据为空")
	}

	// 打印词组信息
	t.Log("词组信息:")
	t.Logf("- 词组匹配数量: %d", len(resp.Words.Phrase))
	t.Logf("- 强匹配数量: %d", len(resp.Words.Strong))
	t.Logf("- 排除词数量: %d", len(resp.Words.Excluded))
	t.Logf("- 固定词组数量: %d", len(resp.Words.Pluse))
	t.Logf("- 关键词数量: %d", len(resp.Words.Keywords))
	t.Logf("- 是否固定关键词: %v", resp.Words.Fixed)

	// 打印统计数据
	if len(resp.Stat) > 0 {
		t.Log("\n统计数据:")
		for _, stat := range resp.Stat {
			t.Logf("关键词: %s", stat.Keyword)
			t.Logf("- 展示量: %d", stat.Views)
			t.Logf("- 点击量: %d", stat.Clicks)
			t.Logf("- 点击率: %.2f%%", stat.CTR)
			t.Logf("- 点击成本: %.2f", stat.CPC)
			t.Logf("- 开始时间: %v", stat.Begin.Format("2006-01-02 15:04:05"))
			t.Logf("- 结束时间: %v", stat.End.Format("2006-01-02 15:04:05"))
			t.Log("---")
		}
	} else {
		t.Log("没有统计数据")
	}
}

func TestGetKeywordsStats(t *testing.T) {
	// 创建 API 客户端
	client := wbhubapi.NewClient("******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")

	// 获取上周的时间范围
	now := time.Now()
	lastWeekStart := now.AddDate(0, 0, -14).Truncate(24 * time.Hour)
	lastWeekEnd := now.AddDate(0, 0, -8).Truncate(24 * time.Hour)

	// 获取广告关键词统计数据
	advertID := 23304690
	resp, err := client.Promotion.GetKeywordsStats(advertID, lastWeekStart, lastWeekEnd)
	if err != nil {
		t.Fatalf("获取广告关键词统计失败: %v", err)
	}

	// 验证响应数据
	if resp == nil {
		t.Fatal("响应数据为空")
	}

	// 打印统计数据
	t.Logf("统计时间范围: %s 至 %s", lastWeekStart.Format("2006-01-02"), lastWeekEnd.Format("2006-01-02"))

	if len(resp.Keywords) > 0 {
		var totalViews, totalClicks int
		var totalSpending float64

		for _, dayStats := range resp.Keywords {
			t.Logf("\n日期: %s", dayStats.Date)
			if len(dayStats.Stats) > 0 {
				t.Log("关键词统计:")
				for _, stat := range dayStats.Stats {
					t.Logf("- 关键词: %s", stat.Keyword)
					t.Logf("  展示量: %d", stat.Views)
					t.Logf("  点击量: %d", stat.Clicks)
					t.Logf("  点击率: %.2f%%", stat.CTR)
					t.Logf("  花费: %.2f", stat.Sum)
					t.Log("  ---")

					totalViews += stat.Views
					totalClicks += stat.Clicks
					totalSpending += stat.Sum
				}
			} else {
				t.Log("该日期没有统计数据")
			}
		}

		// 打印汇总数据
		t.Log("\n汇总数据:")
		t.Logf("总展示量: %d", totalViews)
		t.Logf("总点击量: %d", totalClicks)
		if totalViews > 0 {
			t.Logf("平均点击率: %.2f%%", float64(totalClicks)/float64(totalViews)*100)
		}
		t.Logf("总花费: %.2f", totalSpending)
		if totalClicks > 0 {
			t.Logf("平均点击成本: %.2f", totalSpending/float64(totalClicks))
		}
	} else {
		t.Log("没有统计数据")
	}
}

func TestGetFullStats(t *testing.T) {
	// 创建 API 客户端
	client := wbhubapi.NewClient("******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")

	// 获取上周的日期范围 减 5小时
	now := time.Now().Add(-5 * time.Hour)
	lastWeekStart := now.Format("2006-01-02")
	lastWeekEnd := now.Format("2006-01-02")

	// 创建请求参数 - 测试三种不同格式
	reqs := []*wbhubapi.StatsRequest{
		// // 格式1：使用日期列表
		{
			AdvertID: 26771183,
			Dates: []string{
				lastWeekStart,
				lastWeekEnd,
			},
		},
		// // 格式2：使用时间区间
		//{
		//	AdvertID: 26236361,
		//	Interval: &struct {
		//		Begin string `json:"begin"`
		//		End   string `json:"end"`
		//	}{
		//		Begin: lastWeekStart,
		//		End:   lastWeekEnd,
		//	},
		//},
		// 格式3：只使用广告ID
		//{
		//	AdvertID: 26236361,
		//},
	}
	//打印请求参数json 格式
	jsonData, err := json.Marshal(reqs)
	if err != nil {
		t.Fatalf("序列化请求参数失败: %v", err)
	}
	t.Logf("请求参数: %s", string(jsonData))
	// 获取统计数据
	resp, err := client.Promotion.GetFullStats(reqs)
	if err != nil {
		t.Fatalf("获取统计数据失败: %v", err)
	}

	// 验证响应数据
	if len(resp) == 0 {
		t.Fatal("响应数据为空")
	}

	// 打印统计信息
	t.Log("统计信息:")
	for i, stat := range resp {
		t.Logf("\n广告 #%d 统计:", i+1)
		t.Logf("- 日期: %s", stat.Dates)
		t.Logf("  展示量: %d", stat.Views)
		t.Logf("  点击量: %d", stat.Clicks)
		t.Logf("  点击率: %.2f%%", stat.Ctr)
		t.Logf("  点击成本: %.2f", stat.Cpc)
	}
}

// KeywordPerformance 用于存储关键词的累计性能数据
type KeywordStat struct {
	Keyword     string
	TotalViews  int
	TotalClicks int
	TotalSpend  float64
	CTR         float64
	CPC         float64
	ROI         float64 // 投入产出比
	CPM         float64 // 千展示成本
}

func TestAnalyzeKeywordsPerformance(t *testing.T) {
	// 创建 API 客户端
	client := wbhubapi.NewClient("******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")

	// 获取所有运行中的广告活动
	t.Log("\n=== 获取运行中的广告活动 ===")
	activeStatus := 9 // 9 表示运行中的状态

	params := &wbhubapi.CampaignQueryParams{
		Status: &activeStatus, // 只获取运行中的广告
		//Type:   &activeStatus, //手动
	}
	campaigns, _ := client.Promotion.GetCampaignsByParams(params)
	batchBids := make([]wbhubapi.BidItem, 0)
	for _, campaign := range campaigns {
		if campaign.Type != 9 {
			continue
		}
		if campaign.AdvertID == 22590989 ||
			campaign.AdvertID == 26973370 ||
			campaign.AdvertID == 22627671 ||
			campaign.AdvertID == 22692153 ||
			campaign.AdvertID == 22534626 {
			continue
		}

		nm_bids := []wbhubapi.AuctionMultibid{}

		for _, param := range campaign.UnitedParams {
			nm_bids = append(nm_bids, wbhubapi.AuctionMultibid{
				NM:  param.NMS[0],
				Bid: 250,
			})
		}
		bid := wbhubapi.BidItem{
			AdvertID: campaign.AdvertID,
			NmBids:   nm_bids,
		}
		batchBids = append(batchBids, bid)
	}
	// 批量更新出价
	if len(batchBids) > 0 {
		err := client.Promotion.UpdateBids(batchBids)
		if err != nil {
			log.Printf("批量更新广告出价失败，共 %d 个产品: %v", len(batchBids), err)
		} else {
			log.Printf("批量更新广告出价成功，共 %d 个产品", len(batchBids))
		}
	}

}

// 处理关键词的辅助函数
func processKeywords(t *testing.T, client *wbhubapi.Client, advertID int, keywords []string, keywordType string,
	noClickKeywords []string, excludeReasons map[string]string) {

	const batchSize = 1000
	var successCount, failCount int

	// 对于自动广告的无点击关键词，如果超过1000个则随机选择
	var processedKeywords []string
	if keywordType == "auto" && len(keywords) > batchSize {
		t.Logf("\n关键词数量(%d)超过1000个限制，将随机选择1000个关键词", len(keywords))
		processedKeywords = randomSelectKeywords(keywords, batchSize)
		// 记录未被选中的关键词
		t.Log("\n未被选中的关键词将在下次运行时处理")
		t.Logf("剩余未处理关键词数量: %d", len(keywords)-len(processedKeywords))
	} else {
		processedKeywords = keywords
	}

	// 计算需要处理的批次数
	for i := 0; i < len(processedKeywords); i += batchSize {
		end := i + batchSize
		if end > len(processedKeywords) {
			end = len(processedKeywords)
		}

		batch := processedKeywords[i:end]
		t.Logf("\n处理第 %d 批%s关键词 (共 %d 个)...", (i/batchSize)+1,
			getKeywordTypeDesc(keywordType), len(batch))

		// 根据关键词类型调用不同的API
		var err error
		switch keywordType {
		case "auto":
			err = client.Promotion.SetAutoExcluded(advertID, batch)
		case "strong":
			err = client.Promotion.SetStrongKeywords(advertID, batch)
		case "phrase":
			err = client.Promotion.SetPhraseKeywords(advertID, batch)
		case "excluded":
			err = client.Promotion.SetExcludedKeywords(advertID, batch)
		}

		if err != nil {
			t.Errorf("设置第 %d 批%s关键词失败: %v", (i/batchSize)+1,
				getKeywordTypeDesc(keywordType), err)
			failCount += len(batch)

			// 添加延时后重试一次
			t.Log("等待5秒后重试...")
			time.Sleep(5 * time.Second)

			err = retrySetKeywords(client, advertID, batch, keywordType)
			if err != nil {
				t.Errorf("重试设置第 %d 批%s关键词仍然失败: %v",
					(i/batchSize)+1, getKeywordTypeDesc(keywordType), err)
			} else {
				t.Logf("重试成功，第 %d 批关键词已设置", (i/batchSize)+1)
				successCount += len(batch)
				failCount -= len(batch)
			}
		} else {
			t.Logf("成功设置第 %d 批关键词", (i/batchSize)+1)
			successCount += len(batch)
		}

		// 输出本批次的关键词详情
		t.Log("本批次关键词列表:")
		for _, keyword := range batch {
			if keywordType == "auto" || keywordType == "excluded" {
				var reason string
				if contains(noClickKeywords, keyword) {
					reason = "无点击"
				} else if r, ok := excludeReasons[keyword]; ok {
					reason = r
				} else {
					reason = "性能不佳"
				}
				t.Logf("- %s (原因: %s)", keyword, reason)
			} else {
				t.Logf("- %s", keyword)
			}
		}

		// 批次间添加短暂延时
		if end < len(processedKeywords) {
			t.Log("等待1秒处理下一批...")
			time.Sleep(time.Second)
		}
	}

	// 输出总结
	t.Logf("\n%s关键词设置完成:", getKeywordTypeDesc(keywordType))
	t.Logf("- 成功数量: %d", successCount)
	t.Logf("- 失败数量: %d", failCount)
	if failCount > 0 {
		t.Log("请检查失败的关键词并手动处理")
	}
}

// 重试设置关键词
func retrySetKeywords(client *wbhubapi.Client, advertID int, keywords []string, keywordType string) error {
	switch keywordType {
	case "auto":
		return client.Promotion.SetAutoExcluded(advertID, keywords)
	case "strong":
		return client.Promotion.SetStrongKeywords(advertID, keywords)
	case "phrase":
		return client.Promotion.SetPhraseKeywords(advertID, keywords)
	case "excluded":
		return client.Promotion.SetExcludedKeywords(advertID, keywords)
	default:
		return fmt.Errorf("未知的关键词类型: %s", keywordType)
	}
}

// 获取关键词类型描述
func getKeywordTypeDesc(keywordType string) string {
	switch keywordType {
	case "auto":
		return "自动广告排除"
	case "strong":
		return "精确匹配"
	case "phrase":
		return "短语匹配"
	case "excluded":
		return "排除"
	default:
		return keywordType
	}
}

// 辅助函数：检查字符串是否在切片中
func contains(slice []string, str string) bool {
	for _, v := range slice {
		if v == str {
			return true
		}
	}
	return false
}

// 辅助函数：去重
func removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range slice {
		if _, exists := keys[entry]; !exists {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

// 随机选择指定数量的关键词
func randomSelectKeywords(keywords []string, count int) []string {
	// 创建随机数生成器
	r := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 复制原始切片，避免修改原始数据
	shuffled := make([]string, len(keywords))
	copy(shuffled, keywords)

	// Fisher-Yates 洗牌算法
	for i := len(shuffled) - 1; i > 0; i-- {
		j := r.Intn(i + 1)
		shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
	}

	// 返回前count个元素
	return shuffled[:count]
}

func TestRocketMQ(t *testing.T) {
	// 获取 RocketMQ 客户端实例
	client := rocket_mq.GetInstance()

	// 初始化生产者
	err := client.InitProducer("test_group")
	if err != nil {
		t.Fatalf("初始化 RocketMQ 生产者失败: %v", err)
	}
	defer client.Shutdown()

	// 创建测试消息
	testData := map[string]interface{}{
		"test_id":   "123",
		"timestamp": time.Now().Unix(),
		"data":      "这是一条测试消息",
	}

	// 转换为 JSON
	messageData, err := json.Marshal(testData)
	if err != nil {
		t.Fatalf("序列化消息失败: %v", err)
	}

	// 创建消息
	msg := primitive.NewMessage(
		"test",
		messageData,
	)

	// 添加消息标签和属性
	msg.WithTag("test_tag")
	msg.WithKeys([]string{"test_key"})
	msg.WithProperty("test_property", "test_value")

	// 获取生产者并发送消息
	producer := client.GetProducer()
	result, err := producer.SendSync(context.Background(), msg)
	if err != nil {
		t.Fatalf("发送消息失败: %v", err)
	}

	// 验证发送结果
	t.Logf("消息发送成功:")
	t.Logf("- 消息ID: %s", result.MsgID)
	t.Logf("- Topic: %s", msg.Topic)
	t.Logf("- Tag: %s", msg.GetTags())
	t.Logf("- Key: %v", msg.GetKeys())
	t.Logf("- Body: %s", string(messageData))
}

func TestMessageResendService(t *testing.T) {
	// 创建消息重发服务
	resendService := service.NewMessageResendService()
	if resendService == nil {
		t.Fatal("创建消息重发服务失败")
	}

	// 启动服务
	resendService.Start()

	select {}
}

func TestListSupplies(t *testing.T) {
	api := wb_api.Init()

	// 创建请求参数
	params := supply.ListParams{
		PageNumber:    1,
		PageSize:      20,
		SortBy:        "createDate",
		SortDirection: "desc",
		StatusId:      -2,
	}

	// 调用API
	resp, err := api.ListSupplies(params)
	if err != nil {
		t.Fatalf("获取供应列表失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印供应列表信息
	t.Logf("\n=== 供应列表信息 ===")
	t.Logf("总数量: %d", resp.Result.TotalCount)
	t.Logf("本页数量: %d", len(resp.Result.Data))

	if len(resp.Result.Data) > 0 {
		t.Log("\n供应详情:")
		for i, supply := range resp.Result.Data {
			t.Logf("\n[%d] 供应信息:", i+1)
			t.Logf("- 预订单ID: %d", supply.PreorderId)
			t.Logf("- 供应ID: %v", supply.SupplyId)
			t.Logf("- 箱子类型: %s (ID: %d)", supply.BoxTypeName, supply.BoxTypeId)
			t.Logf("- 创建时间: %s", supply.CreateDate.Format("2006-01-02 15:04:05"))
			t.Logf("- 修改时间: %s", supply.ChangeDate.Format("2006-01-02 15:04:05"))
			t.Logf("- 商品数量: %d", supply.DetailsQuantity)
			t.Logf("- 仓库信息: %s (ID: %d)", supply.WarehouseName, supply.WarehouseId)
			t.Logf("- 仓库地址: %s", supply.WarehouseAddress)
			t.Logf("- 状态: %s (ID: %d)", supply.StatusName, supply.StatusId)

			if supply.SupplyDate != nil {
				t.Logf("- 供应日期: %s", *supply.SupplyDate)
			}
			if supply.FactDate != nil {
				t.Logf("- 实际日期: %s", *supply.FactDate)
			}

			t.Logf("- 入库数量: %d", supply.IncomeQuantity)
			t.Logf("- 用户ID: %s", supply.UserUid)
			t.Logf("- 验收成本: %.2f", supply.AcceptanceCost)

			if supply.HasUnloadProblems {
				t.Log("- 存在卸货问题")
			}

			if supply.HasBoxes {
				t.Log("- 有箱子")
			}

			if supply.FeedbackAllowed {
				t.Log("- 允许反馈")
			}
		}
	} else {
		t.Log("\n没有找到供应记录")
	}
}

func TestGetSupplyDetails(t *testing.T) {
	api := wb_api.Init()

	// 创建请求参数
	params := supply.DetailsParams{
		PageNumber: 1,
		PageSize:   20,
		PreorderID: 42313691,
		Search:     "",
		SupplyID:   nil,
	}

	// 调用API
	resp, err := api.GetSupplyDetails(params)
	if err != nil {
		t.Fatalf("获取供应详情失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印供应详情信息
	t.Logf("\n=== 供应详情信息 ===")
	t.Logf("总数量: %d", resp.Result.TotalCount)
	t.Logf("商品总数: %d", resp.Result.ItemsQuantity)

	// 打印供应基本信息
	supply := resp.Result.Supply
	t.Log("\n供应基本信息:")
	t.Logf("- 预订单ID: %d", supply.PreorderId)
	t.Logf("- 供应ID: %v", supply.SupplyId)
	t.Logf("- 箱子类型: %s (ID: %d)", supply.BoxTypeName, supply.BoxTypeId)
	t.Logf("- 创建时间: %s", supply.CreateDate)
	t.Logf("- 修改时间: %s", supply.ChangeDate)
	t.Logf("- 商品数量: %d", supply.DetailsQuantity)
	t.Logf("- 商品种类数: %d", supply.ArticlesQuantity)
	t.Logf("- 仓库信息: %s (ID: %d)", supply.WarehouseName, supply.WarehouseId)
	t.Logf("- 仓库地址: %s", supply.WarehouseAddress)
	t.Logf("- 状态: %s (ID: %d)", supply.StatusName, supply.StatusId)

	// 打印数量统计
	t.Log("\n数量统计:")
	t.Logf("- 入库数量: %d", supply.IncomeQuantity)
	t.Logf("- 卸货数量: %d", supply.UnloadingQuantity)
	t.Logf("- 可售数量: %d", supply.ReadyForSaleQuantity)
	t.Logf("- 去人格化数量: %d", supply.DepersonalizedQuantity)

	// 打印特殊状态
	t.Log("\n特殊状态:")
	if supply.ElectronicQueueAvailable {
		t.Log("- 支持电子队列")
	}
	if supply.HasBoxes {
		t.Log("- 有箱子")
	}
	if supply.HasKIZ {
		t.Log("- 有KIZ")
	}
	if supply.HasPass {
		t.Log("- 有通行证")
	}
	if supply.HasBoxBarcodes {
		t.Log("- 有箱子条码")
	}
	if supply.HasUnloadProblems {
		t.Log("- 存在卸货问题")
	}

	// 打印商品列表
	if len(resp.Result.Data) > 0 {
		t.Log("\n商品列表:")
		for i, item := range resp.Result.Data {
			t.Logf("\n[%d] 商品信息:", i+1)
			t.Logf("- 条码: %s", item.Barcode)
			t.Logf("- 商品名称: %s", item.ImtName)
			t.Logf("- 品牌: %s", item.BrandName)
			t.Logf("- 品类: %s", item.SubjectName)
			t.Logf("- 颜色: %s", item.ColorName)
			t.Logf("- 商家编码: %s", item.Sa)
			t.Logf("- 商品ID: %d", item.NmID)
			t.Logf("- 数量: %d", item.Quantity)
			t.Logf("- 卸货数量: %d", item.UnloadingQuantity)
			t.Logf("- 可售数量: %d", item.ReadyForSaleQuantity)
			t.Logf("- 入库数量: %d", item.IncomeQuantity)
			t.Logf("- 图片链接: %s", item.ImgSrc)
		}
	} else {
		t.Log("\n没有找到商品记录")
	}
}

func TestCreateDraft(t *testing.T) {
	api := wb_api.Init()

	// 调用API创建草稿
	resp, err := api.CreateDraft()
	if err != nil {
		t.Fatalf("创建供应草稿失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印草稿信息
	t.Log("\n=== 供应草稿信息 ===")
	t.Logf("草稿ID: %s", resp.Result.DraftID)

	// 验证草稿ID格式（UUID格式）
	if len(resp.Result.DraftID) != 36 {
		t.Errorf("草稿ID格式不正确，期望36个字符的UUID，实际长度: %d", len(resp.Result.DraftID))
	}
}

func TestListGoods(t *testing.T) {
	api := wb_api.Init()

	// 测试参数
	search := "443051691"                             // 搜索特定商品
	limit := 100                                      // 每页数量
	offset := 0                                       // 起始位置
	draftID := "988a3c3a-736a-4953-8bce-2c1fc170b696" // 草稿ID

	// 调用API
	resp, err := api.ListGoods(search, limit, offset, draftID)
	if err != nil {
		t.Fatalf("获取商品列表失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印商品列表信息
	t.Logf("\n=== 商品列表信息 ===")
	t.Logf("总数量: %d", resp.Result.Total)
	t.Logf("本页商品数量: %d", len(resp.Result.Goods))

	if len(resp.Result.Goods) > 0 {
		t.Log("\n商品详情:")
		for i, item := range resp.Result.Goods {
			t.Logf("\n[%d] 商品信息:", i+1)
			t.Logf("- 商品ID: %d", item.ID)
			t.Logf("- 商品名称: %s", item.ImtName)
			t.Logf("- 品牌: %s", item.Brand)
			t.Logf("- 品类: %s", item.Subject)
			t.Logf("- 颜色: %s", item.Color)
			t.Logf("- 尺码: %s", item.Size)
			t.Logf("- 商家编码: %s", item.Article)
			t.Logf("- WB商品编号: %d", item.ArticleWB)
			t.Logf("- 条码: %s", item.Barcode)
			t.Logf("- 体积: %.2f", item.Volume)
			t.Logf("- 图片链接: %s", item.ImgSrc)
			t.Logf("- 大图链接: %s", item.ImgBigSrc)
		}
	} else {
		t.Log("\n没有找到商品")
	}
}

func TestUpdateDraftGoods(t *testing.T) {
	api := wb_api.Init()

	// 测试参数
	draftID := "988a3c3a-736a-4953-8bce-2c1fc170b696"
	barcodes := []struct {
		Barcode  string
		Quantity int
	}{
		{
			Barcode:  "MLN01-4",
			Quantity: 10,
		},
		{
			Barcode:  "MLN103-3",
			Quantity: 10,
		},
		{
			Barcode:  "MLN02-3",
			Quantity: 10,
		},
		{
			Barcode:  "MNT105-1",
			Quantity: 11,
		},
	}

	// 调用API
	resp, err := api.UpdateDraftGoods(draftID, barcodes)
	if err != nil {
		t.Fatalf("更新草稿商品失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	t.Log("\n=== 更新草稿商品成功 ===")
	t.Logf("草稿ID: %s", draftID)
	t.Log("\n更新的商品:")
	for _, item := range barcodes {
		t.Logf("- 商品条码: %s, 数量: %d", item.Barcode, item.Quantity)
	}
}

func TestGetWarehouseRecommend(t *testing.T) {
	api := wb_api.Init()

	// 使用之前创建的草稿ID
	draftID := "988a3c3a-736a-4953-8bce-2c1fc170b696"

	// 调用API
	resp, err := api.GetWarehouseRecommend(draftID)
	if err != nil {
		t.Fatalf("获取仓库推荐失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印仓库推荐信息
	t.Logf("\n=== 仓库推荐信息 ===")
	t.Logf("推荐仓库数量: %d", len(resp.Result.Warehouses))

	// 分别统计推荐和非推荐的仓库数量
	var recommendedCount, activeCount int
	for _, warehouse := range resp.Result.Warehouses {
		if warehouse.IsRecommended {
			recommendedCount++
		}
		if warehouse.IsActive {
			activeCount++
		}
	}
	t.Logf("推荐仓库数量: %d", recommendedCount)
	t.Logf("活跃仓库数量: %d", activeCount)

	// 打印推荐仓库的详细信息
	t.Log("\n推荐仓库列表:")
	for _, warehouse := range resp.Result.Warehouses {
		if warehouse.IsRecommended {
			t.Logf("\n仓库信息:")
			t.Logf("- ID: %d", warehouse.WarehouseID)
			t.Logf("- 名称: %s", warehouse.WarehouseName)
			t.Logf("- 地址: %s", warehouse.WarehouseAddress)
			t.Logf("- 地图ID: %d", warehouse.WarehouseMapID)
			t.Logf("- 是否活跃: %v", warehouse.IsActive)

			// 打印箱子类型信息
			boxType := warehouse.BoxType
			t.Log("\n箱子类型信息:")
			t.Logf("- 类型ID: %d", boxType.BoxTypeID)
			t.Logf("- 类型名称: %s", boxType.BoxTypeName)
			t.Logf("- 是否接受所有: %v", boxType.AcceptAll)
			t.Logf("- 数量: %d", boxType.Count)
			t.Logf("- 物流系数: %.2f", boxType.LogisticCoefficient)
			t.Logf("- 物流升数: %s", boxType.LogisticLiter)
			t.Logf("- 物流基数: %s", boxType.LogisticBase)
			t.Logf("- 存储升数: %s", boxType.StorageLiter)
			t.Logf("- 存储基数: %s", boxType.StorageBase)
			t.Logf("- 存储系数: %.2f", boxType.StorageCoefficient)
			t.Logf("- 是否有可用日期: %v", boxType.HasAvailableDate)
			t.Logf("- 总数量: %d", boxType.TotalCount)

			// 如果有验收系数，打印验收系数信息
			if len(boxType.AcceptanceCoefficients) > 0 {
				t.Log("\n验收系数信息:")
				for _, coef := range boxType.AcceptanceCoefficients {
					t.Logf("- 日期: %s, 系数: %.2f", coef.Date.Format("2006-01-02"), coef.Coefficient)
				}
			}
		}
	}
}

// 在文件末尾添加测试函数
func TestGetAllWarehouse(t *testing.T) {
	api := wb_api.Init()

	// 调用API获取所有仓库
	resp, err := api.GetAllWarehouse("ratingDesc")
	if err != nil {
		t.Fatalf("获取所有仓库失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印仓库信息
	t.Logf("\n=== 仓库列表信息 ===")
	t.Logf("仓库总数: %d", len(resp.Result.Resp.Data))

	// 统计莫斯科和莫斯科州的仓库数量
	var moscowFbwCount, moscowFbsCount, moscowServiceCount int
	var moscowRegionFbwCount, moscowRegionFbsCount, moscowRegionServiceCount int

	for _, warehouse := range resp.Result.Resp.Data {
		// 检查是否为莫斯科市的仓库
		if warehouse.NearCity.ID != 77 {
			if warehouse.IsFbw {
				moscowFbwCount++
			}
			if warehouse.IsFbs {
				moscowFbsCount++
			}
			if warehouse.IsService {
				moscowServiceCount++
			}
		}
		// 检查是否为莫斯科州的仓库
		if strings.Contains(warehouse.Address, "Московская") {
			if warehouse.IsFbw {
				moscowRegionFbwCount++
			}
			if warehouse.IsFbs {
				moscowRegionFbsCount++
			}
			if warehouse.IsService {
				moscowRegionServiceCount++
			}
		}
	}

	// 打印莫斯科市仓库统计
	t.Log("\n莫斯科市仓库统计:")
	t.Logf("FBW仓库数量: %d", moscowFbwCount)
	t.Logf("FBS仓库数量: %d", moscowFbsCount)
	t.Logf("服务仓库数量: %d", moscowServiceCount)

	// 打印莫斯科州仓库统计
	t.Log("\n莫斯科州仓库统计:")
	t.Logf("FBW仓库数量: %d", moscowRegionFbwCount)
	t.Logf("FBS仓库数量: %d", moscowRegionFbsCount)
	t.Logf("服务仓库数量: %d", moscowRegionServiceCount)

	// 打印仓库详细信息
	t.Log("\n仓库列表:")
	for _, warehouse := range resp.Result.Resp.Data {
		// 只打印莫斯科和莫斯科州的仓库
		if warehouse.NearCity.ID != 77 {
			continue
		}

		//t.Logf("\n仓库 #%d:", i+1)
		//t.Logf("- ID: %d (原始ID: %d)", warehouse.ID, warehouse.OrigID)
		t.Logf("- 名称: %s", warehouse.Name)
		//t.Logf("- 地址: %s", warehouse.Address)
		//t.Logf("- 工作时间: %s", warehouse.WorkTime)
		//t.Logf("- 评分: %.2f", warehouse.Rating)
		//t.Logf("- 经度: %.6f", warehouse.Longitude)
		//t.Logf("- 纬度: %.6f", warehouse.Latitude)
		//
		//if warehouse.Gates != "" {
		//	t.Logf("- 门号: %s", warehouse.Gates)
		//}
		//
		//if warehouse.PassText != "" {
		//	t.Logf("- 通行说明: %s", warehouse.PassText)
		//}
		//
		//t.Logf("- 需要通行证: %v", warehouse.IsPassNeeded)
		//t.Logf("- 邻近城市: %s (ID: %d)", warehouse.NearCity.Title, warehouse.NearCity.ID)
		//
		//if len(warehouse.Photos) > 0 {
		//	t.Logf("- 照片数量: %d", len(warehouse.Photos))
		//	for j, photo := range warehouse.Photos {
		//		t.Logf("  照片 %d: %s", j+1, photo)
		//	}
		//}
		//
		//// 打印业务类型信息
		//t.Log("- 业务类型:")
		//t.Logf("  FBW: %v", warehouse.IsFbw)
		//if warehouse.IsFbwRestrictionCargo != "" {
		//	t.Logf("  FBW限制: %s", warehouse.IsFbwRestrictionCargo)
		//}
		//t.Logf("  FBS: %v", warehouse.IsFbs)
		//if warehouse.IsFbsRestrictionCargo != "" {
		//	t.Logf("  FBS限制: %s", warehouse.IsFbsRestrictionCargo)
		//}
		//t.Logf("  配送服务: %v", warehouse.IsService)
		//if warehouse.IsServiceRestrictionCargo != "" {
		//	t.Logf("  配送限制: %s", warehouse.IsServiceRestrictionCargo)
		//}
		//
		//// 打印配送信息
		//if warehouse.DeliveryPeriodToShelf != "" {
		//	t.Logf("- 配送周期: %s", warehouse.DeliveryPeriodToShelf)
		//}
		//
		//t.Logf("- 箱子类型掩码: %d", warehouse.BoxTypeMask)
		//
		//if warehouse.OfficeTimeOffset != 0 {
		//	t.Logf("- 时区偏移: %d", warehouse.OfficeTimeOffset)
		//}
	}
}

func TestCheckSupplyPlanTime(t *testing.T) {
	api := wb_api.Init()

	// 创建请求参数 - 获取所有供应单
	params := supply.ListParams{
		PageNumber:    1,
		PageSize:      50, // 每页50条记录
		SortBy:        "createDate",
		SortDirection: "desc",
		StatusId:      -1, // 获取所有状态的供应单
	}

	// 调用API获取供应单列表
	resp, err := api.ListSupplies(params)
	if err != nil {
		t.Fatalf("获取供应列表失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	t.Logf("\n=== 供应单计划时间检查 ===")
	t.Logf("总供应单数量: %d", resp.Result.TotalCount)

	// 遍历供应单
	for _, supply := range resp.Result.Data {
		t.Logf("\n检查供应单 (预订单ID: %d)", supply.PreorderId)

		// 检查是否有计划时间
		if supply.SupplyDate == nil {
			t.Logf("- 供应单没有计划时间，检查可选时间...")

			// 获取当前时间
			now := time.Now()
			// 设置查询时间范围（当前时间往后推30天）
			dateFrom := now
			dateTo := now.AddDate(0, 0, 30)

			// 调用API获取验收成本（包含可选时间）
			costsResp, err := api.GetAcceptanceCosts(supply.PreorderId, dateFrom, dateTo)
			if err != nil {
				t.Logf("获取验收成本失败: %v", err)
				continue
			}

			if len(costsResp.Result.Costs) > 0 {
				for _, cost := range costsResp.Result.Costs {
					if cost.Coefficient == -1 {
						continue
					}
					t.Logf("- 日期: %s, 成本: %.2f, 系数: %.2f",
						cost.Date,
						cost.Cost,
						cost.Coefficient)

					// 如果有配送和存储信息，也打印出来
					ds := cost.DeliveryAndStorage
					t.Logf("  配送和存储信息:")
					t.Logf("  - 配送存储表达式: %s", ds.DeliveryAndStorageExpr)
					t.Logf("  - 存储体积截断: %s", ds.StorageVolumeCut)
					t.Logf("  - 配送基数: %s", ds.DeliveryValueBase)
					t.Logf("  - 配送升数: %s", ds.DeliveryValueLiter)
					t.Logf("  - 存储值: %s", ds.StorageValue)
					t.Logf("  - 存储升数: %s", ds.StorageLiter)
					t.Logf("  - 存储系数: %s", ds.StorageCoef)
					t.Logf("  - 配送系数: %s", ds.DeliveryCoef)
				}

			} else {
				t.Log("没有找到可选的计划时间")
			}
		} else {
			t.Logf("- 已有计划时间: %s", *supply.SupplyDate)
		}

		// 打印其他相关信息
		t.Logf("- 创建时间: %s", supply.CreateDate.Format("2006-01-02 15:04:05"))
		t.Logf("- 状态: %s (ID: %d)", supply.StatusName, supply.StatusId)
		t.Logf("- 仓库: %s", supply.WarehouseName)
		t.Logf("- 商品数量: %d", supply.DetailsQuantity)
	}
}

// 自动 查时间段
func TestCheckSupplyPlanTimeLoop(t *testing.T) {
	api := wb_api.Init()

	// 创建钉钉机器人实例
	robot, err := dingding.NewDingTalkRobot(
		"8d6dda43349d5a12d8c6fa5de3b26cb5c489f7809251d85fce3e6a2a5218bb7a",
		"SECc89fb889b0a505ff11492d69927d7bac36d644e39dcdc2cb7a4a37a2a2870c33",
	)
	if err != nil {
		t.Fatalf("创建钉钉机器人失败: %v", err)
	}

	// 创建消息构造器
	builder := dingding.MessageBuilder{}

	// 创建一个通道用于接收中断信号
	done := make(chan bool)

	t.Log("开始循环检查供应单计划时间，每30秒执行一次...")
	t.Log("按 Ctrl+C 停止执行")

	// 创建请求参数 - 获取所有供应单
	params := supply.ListParams{
		PageNumber:    1,
		PageSize:      50,
		SortBy:        "createDate",
		SortDirection: "desc",
		StatusId:      -1, // 获取所有状态的供应单
	}

	// 先获取供应单列表
	resp, err := api.ListSupplies(params)
	if err != nil {
		t.Fatalf("获取供应列表失败: %v", err)
	}

	if resp == nil {
		t.Fatal("响应为空")
	}

	t.Logf("总供应单数量: %d", resp.Result.TotalCount)

	// 在一个新的 goroutine 中执行循环检查
	go func() {
		for {
			select {
			case <-done:
				return
			default:
				// 记录本次检查的开始时间
				checkTime := time.Now()
				t.Logf("\n=== 开始检查 (时间: %s) ===", checkTime.Format("2006-01-02 15:04:05"))

				// 遍历供应单
				for _, supply := range resp.Result.Data {
					t.Logf("\n检查供应单 (预订单ID: %d 仓库: %s)", supply.PreorderId, supply.WarehouseName)

					// 检查是否有计划时间
					if supply.SupplyDate == nil {
						t.Logf("- 供应单没有计划时间，检查可选时间...")

						// 获取当前时间
						now := time.Now()
						// 设置查询时间范围（当前时间往后推30天）
						dateFrom := now
						dateTo := now.AddDate(0, 0, 30)

						// 调用API获取验收成本（包含可选时间）
						costsResp, err := api.GetAcceptanceCosts(supply.PreorderId, dateFrom, dateTo)
						if err != nil {
							t.Logf("获取验收成本失败: %v", err)
							continue
						}

						if len(costsResp.Result.Costs) > 0 {
							// 构建 Markdown 消息内容
							var availableSlots []string
							for _, cost := range costsResp.Result.Costs {
								if cost.Coefficient == -1 {
									continue
								}

								// 格式化时间槽信息
								slotInfo := fmt.Sprintf("- **日期**: %s\n  - 成本: %.2f\n  - 系数: %.2f\n  - 配送基数: %s\n  - 存储基数: %s\n",
									cost.Date,
									cost.Cost,
									cost.Coefficient,
									cost.DeliveryAndStorage.DeliveryValueBase,
									cost.DeliveryAndStorage.StorageValue)
								availableSlots = append(availableSlots, slotInfo)

								t.Logf("- 日期: %s, 成本: %.2f, 系数: %.2f",
									cost.Date,
									cost.Cost,
									cost.Coefficient)
							}

							if len(availableSlots) > 0 {
								// 构建完整的 Markdown 消息
								title := fmt.Sprintf("供应单可用时间通知")
								markdownContent := fmt.Sprintf("### 供应单信息\n"+
									"- **预订单ID**: %d\n"+
									"- **仓库**: %s\n"+
									"- **商品数量**: %d\n"+
									"- **创建时间**: %s\n\n"+
									"### 可用时间槽\n%s",
									supply.PreorderId,
									supply.WarehouseName,
									supply.DetailsQuantity,
									supply.CreateDate.Format("2006-01-02 15:04:05"),
									strings.Join(availableSlots, "\n"))

								// 发送 Markdown 消息
								markdownMsg := builder.NewMarkdownMessage(title, markdownContent)
								markdownMsg.At = &dingding.AtConfig{
									IsAtAll: true,
								}
								if err := robot.Send(markdownMsg); err != nil {
									t.Logf("发送钉钉通知失败: %v", err)
								} else {
									t.Log("已发送钉钉通知")
								}
							}
						} else {
							t.Log("没有找到可选的计划时间")
						}
					} else {
						t.Logf("- 已有计划时间: %s", *supply.SupplyDate)
					}
				}

				// 计算本次检查耗时
				elapsed := time.Since(checkTime)
				t.Logf("\n=== 检查完成 (耗时: %v) ===", elapsed)

				// 等待到下一个15秒间隔
				time.Sleep(15 * time.Second)
			}
		}
	}()

	// 主goroutine等待用户中断
	select {}
}

func TestDingTalkRobot(t *testing.T) {
	// 创建钉钉机器人实例
	robot, err := dingding.NewDingTalkRobot(
		"8d6dda43349d5a12d8c6fa5de3b26cb5c489f7809251d85fce3e6a2a5218bb7a",
		"SECc89fb889b0a505ff11492d69927d7bac36d644e39dcdc2cb7a4a37a2a2870c33",
	)
	if err != nil {
		t.Fatalf("创建钉钉机器人失败: %v", err)
	}

	// 创建消息构造器
	builder := dingding.MessageBuilder{}

	// 测试发送文本消息
	t.Log("\n=== 测试发送文本消息 ===")
	textMsg := builder.NewTextMessage("这是一条测试消息")
	textMsg.At = &dingding.AtConfig{
		IsAtAll: true,
	}
	err = robot.Send(textMsg)
	if err != nil {
		t.Errorf("发送文本消息失败: %v", err)
	} else {
		t.Log("文本消息发送成功")
	}

	// 测试发送链接消息
	t.Log("\n=== 测试发送链接消息 ===")
	linkMsg := builder.NewLinkMessage(
		"这是链接标题",
		"这是链接内容，点击查看详情",
		"https://img.alicdn.com/tfs/TB1NwmBEL9TBuNjy1zbXXXpepXa-2400-1218.png",
		"https://www.dingtalk.com",
	)
	err = robot.Send(linkMsg)
	if err != nil {
		t.Errorf("发送链接消息失败: %v", err)
	} else {
		t.Log("链接消息发送成功")
	}

	// 测试发送 Markdown 消息
	t.Log("\n=== 测试发送 Markdown 消息 ===")
	markdownContent := `### 测试标题
- 项目1
- 项目2

**加粗文本**
> 引用文本

[链接文本](https://www.example.com)`

	markdownMsg := builder.NewMarkdownMessage("Markdown测试", markdownContent)
	err = robot.Send(markdownMsg)
	if err != nil {
		t.Errorf("发送 Markdown 消息失败: %v", err)
	} else {
		t.Log("Markdown 消息发送成功")
	}

	// 测试错误情况
	t.Log("\n=== 测试错误情况 ===")

	// 测试无效的访问令牌
	invalidRobot, err := dingding.NewDingTalkRobot("invalid_token", "invalid_secret")
	if err != nil {
		t.Log("预期的错误: 创建带无效令牌的机器人失败")
	} else {
		msg := builder.NewTextMessage("这条消息不应该发送成功")
		err = invalidRobot.Send(msg)
		if err != nil {
			t.Log("预期的错误: 使用无效令牌发送消息失败")
		}
	}

	// 测试空消息
	emptyMsg := dingding.Message{
		MsgType: "text",
		Text:    &dingding.TextMessage{Content: ""},
	}
	err = robot.Send(emptyMsg)
	if err != nil {
		t.Log("预期的错误: 发送空消息失败")
	}
}

func TestCreateSupplyWithMoscowWarehouse(t *testing.T) {
	api := wb_api.Init()

	// 第一步：创建供应单草稿
	t.Log("\n=== 创建供应单草稿 ===")
	draftResp, err := api.CreateDraft()
	if err != nil {
		t.Fatalf("创建供应单草稿失败: %v", err)
	}

	draftID := draftResp.Result.DraftID
	t.Logf("草稿ID: %s", draftID)

	// 第二步：添加商品到草稿
	t.Log("\n=== 添加商品到草稿 ===")
	barcodes := []struct {
		Barcode  string
		Quantity int
	}{
		{
			Barcode:  "MLN101-1",
			Quantity: 1000,
		},
	}

	updateResp, err := api.UpdateDraftGoods(draftID, barcodes)
	if err != nil {
		t.Fatalf("添加商品到草稿失败: %v", err)
	}

	if updateResp == nil {
		t.Fatal("更新草稿响应为空")
	}

	t.Log("成功添加商品到草稿")

	// 第三步：获取仓库推荐
	t.Log("\n=== 获取仓库推荐 ===")
	warehouseResp, err := api.GetWarehouseRecommend(draftID)
	if err != nil {
		t.Fatalf("获取仓库推荐失败: %v", err)
	}

	if len(warehouseResp.Result.Warehouses) == 0 {
		t.Fatal("没有找到仓库")
	}

	t.Logf("找到 %d 个仓库", len(warehouseResp.Result.Warehouses))

	// 遍历所有仓库
	for _, warehouse := range warehouseResp.Result.Warehouses {
		// 只处理活跃的莫斯科州仓库，且箱子类型接受所有商品
		if !strings.Contains(warehouse.WarehouseAddress, "Московская") ||
			!warehouse.BoxType.AcceptAll {
			continue
		}

		t.Logf("\n=== 处理莫斯科州仓库: %s ===", warehouse.WarehouseName)
		t.Logf("- 地址: %s", warehouse.WarehouseAddress)
		t.Logf("- 箱子类型: %s (ID: %d)", warehouse.BoxType.BoxTypeName, warehouse.BoxType.BoxTypeID)
		t.Logf("- 物流系数: %.2f", warehouse.BoxType.LogisticCoefficient)
		t.Logf("- 接受所有商品: %v", warehouse.BoxType.AcceptAll)

		// 第四步：创建供应单
		t.Log("\n=== 创建供应单 ===")
		supplyResp, err := api.CreateSupply(
			draftID,
			6,
			0, // transitWarehouseId 设为0表示不使用中转仓
			warehouse.WarehouseID,
		)
		if err != nil {
			t.Logf("创建供应单失败: %v", err)
			continue
		}

		if supplyResp == nil {
			t.Logf("创建供应单响应为空")
			continue
		}

		t.Log("\n=== 供应单创建成功 ===")
		for _, id := range supplyResp.Result.Ids {
			t.Logf("供应单ID: %d", id.Id)
			t.Logf("箱子类型ID: %d", id.BoxTypeId)
			t.Logf("箱子类型名称: %s", id.BoxTypeName)
		}

		// 在每个仓库处理完成后添加分隔线
		t.Log(strings.Repeat("-", 50))
	}
}

func TestCreateSupplySpecific(t *testing.T) {
	api := wb_api.Init()
	//{"params":{"boxTypeMask":32,"draftID":"b4af68eb-eab9-4288-948f-f37156e33acb","transitWarehouseId":null,"warehouseId":120762},"jsonrpc":"2.0","id":"json-rpc_283"}
	// 测试参数
	draftID := "25be96e6-aaf1-4970-9c0d-3c710b1efde6"
	boxTypeMask := 32
	warehouseId := 120762

	// 调用 CreateSupply API
	resp, err := api.CreateSupply(
		draftID,
		boxTypeMask,
		0, // transitWarehouseId 设为 0，对应请求中的 null
		warehouseId,
	)
	if err != nil {
		t.Fatalf("创建供应单失败: %v", err)
	}

	// 验证响应
	if resp == nil {
		t.Fatal("响应为空")
	}

	// 打印响应信息
	t.Log("\n=== 供应单创建结果 ===")
	for _, id := range resp.Result.Ids {
		t.Logf("供应单ID: %d", id.Id)
		t.Logf("箱子类型ID: %d", id.BoxTypeId)
		t.Logf("箱子类型名称: %s", id.BoxTypeName)
	}
}

func TestDeleteExistingDrafts(t *testing.T) {
	api := wb_api.Init()

	// 第一步：获取草稿列表
	t.Log("\n=== 获取草稿列表 ===")
	draftsResp, err := api.ListDrafts(100, 0, -1) // 获取最新的100个草稿
	if err != nil {
		t.Fatalf("获取草稿列表失败: %v", err)
	}

	if draftsResp == nil {
		t.Fatal("草稿列表响应为空")
	}

	t.Logf("找到 %d 个草稿", len(draftsResp.Result.Drafts))

	// 如果没有草稿，提前结束测试
	if len(draftsResp.Result.Drafts) == 0 {
		t.Log("没有找到需要删除的草稿")
		return
	}

	// 第二步：遍历并删除所有草稿
	var successCount, failCount int
	for _, draft := range draftsResp.Result.Drafts {
		t.Logf("\n删除草稿 ID: %s", draft.ID)
		t.Logf("创建时间: %s", draft.CreatedAt.Format("2006-01-02 15:04:05"))

		// 打印草稿中的商品信息
		if len(draft.Items) > 0 {
			t.Log("草稿中的商品:")
			for _, item := range draft.Items {
				t.Logf("- 商品条码: %s, 数量: %d", item.Barcode, item.Quantity)
			}
		} else {
			t.Log("草稿中没有商品")
		}

		// 删除草稿
		resp, err := api.DeleteDraft(draft.ID)
		if err != nil {
			t.Logf("删除草稿失败: %v", err)
			failCount++
			continue
		}

		if resp == nil {
			t.Logf("删除草稿响应为空")
			failCount++
			continue
		}

		if resp.Result.Success {
			t.Log("草稿删除成功")
			successCount++
		} else {
			t.Log("草稿删除失败")
			failCount++
		}

		// 在每个草稿处理完成后添加分隔线
		t.Log(strings.Repeat("-", 30))

		// 添加短暂延时，避免请求过于频繁
		time.Sleep(time.Second)
	}

	// 打印删除统计
	t.Log("\n=== 删除统计 ===")
	t.Logf("总草稿数: %d", len(draftsResp.Result.Drafts))
	t.Logf("成功删除: %d", successCount)
	t.Logf("删除失败: %d", failCount)
}

func TestGetSearchInfoByKeyword(t *testing.T) {
	web := wb_web.Init()

	// 调用API获取搜索结果
	products, err := web.GetSearchInfoByKeyword("светильник светодиодный потолочный", 1)
	if err != nil {
		t.Fatalf("获取搜索信息失败: %v", err)
	}

	// 检查是否有结果返回
	if len(products) == 0 {
		t.Fatal("没有找到任何产品")
	}

	// 查找ID为253486273的产品
	found := false
	for i, product := range products {
		if product.Id == 253486273 {
			found = true
			t.Logf("\n找到目标产品:")
			t.Logf("- 产品ID: %d", product.Id)
			t.Logf("- 产品名称: %s", product.Name)
			t.Logf("- 品牌: %s", product.Brand)
			t.Logf("- 供应商: %s", product.Supplier)
			t.Logf("- 评分: %.2f", product.ReviewRating)
			t.Logf("- 评论数: %d", product.Feedbacks)

			// 输出自然排名（在当前页面中的位置，从1开始计数）
			naturalRank := i + 1
			t.Logf("- 自然排名: %d", naturalRank)

			// 检查是否是广告
			if product.Log != nil {
				t.Logf("- 广告信息:")
				t.Logf("  * 广告ID: %d", product.Log.AdvertId)
				t.Logf("  * 广告位置: %d", product.Log.Position)
				t.Logf("  * 广告类型: %s", product.Log.Tp)
				t.Logf("  * CPM: %d", product.Log.Cpm)
			} else {
				t.Log("- 该商品不是广告")
			}
			break
		}
	}

	if !found {
		t.Errorf("未找到ID为253486273的产品")
	}
}
