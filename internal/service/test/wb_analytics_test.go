package main

import (
	"fmt"
	"lens/internal/api/my_agent"
	"lens/internal/api/wb"
	"testing"
)

// TestGetNMReportDetailHistory 测试获取商品卡片历史详细报告
func TestGetNMReportDetailHistory(t *testing.T) {
	// 从环境变量获取 API Token
	apiToken := "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

	// 创建 WB API 客户端
	client := wbapi.NewClient(apiToken)

	// 构造测试请求
	req := &wbapi.NMReportDetailHistoryRequest{
		Page:       1,
		IsNextPage: false,
		Period: wbapi.Period{
			Begin: "2025-08-04",
			End:   "2025-08-04",
		},
		Aggregation: "day",
		NMIDs:       []int{*********, *********},
		// 可选：指定特定商品ID进行测试
		// NMIDs: []int{*********},
		// 可选：指定品牌进行测试
		// BrandNames: []string{"测试品牌"},
	}

	// 调用 API
	t.Logf("开始测试 GetNMReportDetailHistory...")
	result, err := client.Analytics.GetNMReportDetailHistory(req)

	if err != nil {
		t.Errorf("API 调用失败: %v", err)
		return
	}

	// 验证响应结构
	if result == nil {
		t.Error("响应结果为空")
		return
	}

	t.Logf("✅ 测试完成，数据验证通过")
}

// TestGetCampaignKeywordsAnalysis 测试获取广告活动关键词分析
func TestGetCampaignKeywordsAnalysis(t *testing.T) {
	// 创建 lens-agent-server API 客户端
	client := my_agent.NewClient("http://8.129.23.115:8002")

	// 测试广告活动ID
	campaignID := 26808239

	t.Logf("开始测试获取广告活动 %d 的关键词分析...", campaignID)

	// 调用关键词分析 API
	analysis, err := client.GetCampaignKeywordsAnalysis(campaignID)
	if err != nil {
		t.Errorf("获取关键词分析失败: %v", err)
		return
	}

	// 验证响应结构
	if analysis == nil {
		t.Error("分析结果为空")
		return
	}

	t.Logf("✅ 广告活动 %d 关键词分析完成", analysis.CampaignID)
	t.Logf("📊 关键词总数: %d", analysis.TotalKeywords)

	// 统计分析结果
	var (
		shouldExcludeCount   = 0
		orderGeneratingCount = 0
		keepCount            = 0
	)

	for _, keyword := range analysis.Keywords {
		if keyword.ShouldExclude {
			shouldExcludeCount++
		}
		if keyword.OrderGenerating {
			orderGeneratingCount++
		}
		if keyword.Keep {
			keepCount++
		}
	}

	t.Logf("🔍 分析统计:")
	t.Logf("  - 建议排除: %d 个关键词", shouldExcludeCount)
	t.Logf("  - 出单关键词: %d 个", orderGeneratingCount)
	t.Logf("  - 保持不变: %d 个", keepCount)

	// 显示前5个建议排除的关键词
	excludeCount := 0
	t.Logf("🚫 建议排除的关键词示例:")
	for _, keyword := range analysis.Keywords {
		if keyword.ShouldExclude && excludeCount < 5 {
			reason := ""
			if keyword.ExcludeIrrelevant {
				reason += "不相关 "
			}
			if keyword.ExcludeZeroClick {
				reason += "零点击 "
			}
			if keyword.ExcludeNoData {
				reason += "无数据 "
			}
			t.Logf("  - %s (原因: %s)", keyword.Keyword, reason)
			excludeCount++
		}
	}

	// 显示前5个出单关键词
	orderCount := 0
	t.Logf("💰 出单关键词示例:")
	for _, keyword := range analysis.Keywords {
		if keyword.OrderGenerating && orderCount < 5 {
			ctr := "N/A"
			cpc := "N/A"
			if keyword.CTR != nil {
				ctr = fmt.Sprintf("%.2f%%", *keyword.CTR*100)
			}
			if keyword.CPC != nil {
				cpc = fmt.Sprintf("%.2f", *keyword.CPC)
			}
			t.Logf("  - %s (CTR: %s, CPC: %s)", keyword.Keyword, ctr, cpc)
			orderCount++
		}
	}

	t.Logf("✅ 关键词分析测试完成")
}
