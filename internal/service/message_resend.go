package service

import (
	"context"
	"encoding/json"
	"lens/internal/infrastructure/redis"
	rocket_mq "lens/internal/infrastructure/rocketmq"
	"log"
	"time"

	"github.com/apache/rocketmq-client-go/v2/primitive"
)

// MessageResendService 消息重发服务
type MessageResendService struct {
	mqClient    *rocket_mq.RocketMQClient
	redisClient *redis.RedisClient
	stopChan    chan struct{}
}

// NewMessageResendService 创建消息重发服务
func NewMessageResendService() *MessageResendService {
	return &MessageResendService{
		mqClient:    rocket_mq.GetInstance(),
		redisClient: redis.GetInstance(),
		stopChan:    make(chan struct{}),
	}
}

// Start 启动重发服务
func (s *MessageResendService) Start() {
	// 初始化RocketMQ生产者
	if err := s.mqClient.InitProducer("resend_group"); err != nil {
		log.Printf("初始化重发服务RocketMQ生产者失败: %v", err)
		return
	}

	// 启动消息处理协程
	go s.processFailedMessages()

	log.Println("消息重发服务已启动")
}

// Stop 停止重发服务
func (s *MessageResendService) Stop() {
	close(s.stopChan)
	if err := s.mqClient.Shutdown(); err != nil {
		log.Printf("关闭重发服务RocketMQ生产者失败: %v", err)
	}
	log.Println("消息重发服务已停止")
}

// processFailedMessages 处理失败的消息
func (s *MessageResendService) processFailedMessages() {
	for {
		select {
		case <-s.stopChan:
			return
		default:
			// 使用阻塞式获取消息，超时时间为5秒
			result, err := s.redisClient.BLPop(context.Background(), 5*time.Second, REDIS_FAILED_QUEUE_KEY)

			if err != nil {
				// 如果是超时错误，继续下一次循环
				continue
			}

			// BLPop返回的是一个包含key和value的数组，我们需要第二个元素（value）
			if len(result) < 2 {
				continue
			}
			msgJSON := result[1]

			var failedMsg map[string]interface{}
			if err := json.Unmarshal([]byte(msgJSON), &failedMsg); err != nil {
				log.Printf("解析失败消息失败: %v", err)
				continue
			}

			// 创建新消息
			msg := primitive.NewMessage(
				failedMsg["topic"].(string),
				[]byte(failedMsg["body"].(string)),
			)
			// 设置消息Key
			msg.WithKeys([]string{failedMsg["messageKey"].(string)})

			sendResult, err := s.mqClient.GetProducer().SendSync(context.Background(), msg)

			productID := failedMsg["productId"].(string)
			keyword := failedMsg["keyword"].(string)

			if err != nil {
				log.Printf("重发消息失败 [%s], 产品ID: %s, 关键词: %s, 错误: %v",
					failedMsg["messageKey"], productID, keyword, err)

				// 更新失败记录的时间戳和错误信息
				failedMsg["timestamp"] = time.Now().Unix()
				failedMsg["error"] = err.Error()
				updatedJSON, _ := json.Marshal(failedMsg)

				// 重新推入队列
				ctx := context.Background()
				if err := s.redisClient.RPush(ctx, REDIS_FAILED_QUEUE_KEY, string(updatedJSON)); err != nil {
					log.Printf("重新推入失败消息到队列失败 [%s]: %v", failedMsg["messageKey"], err)
				}
				continue
			}

			log.Printf("消息重发成功 [%s], 新msgId: %s, 产品ID: %s, 关键词: %s",
				failedMsg["messageKey"], sendResult.MsgID, productID, keyword)
		}
	}
}
