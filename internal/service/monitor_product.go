package service

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/robfig/cron/v3"
	"lens/internal/api/my_agent"
	wbhubapi "lens/internal/api/wb"
	wb_web "lens/internal/api/wb_buyer"
	"lens/internal/api/wb_buyer/search"
	"lens/internal/infrastructure/dingding"
	"lens/internal/infrastructure/redis"
	rocket_mq "lens/internal/infrastructure/rocketmq"
	"log"
	"math/rand"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/util/gconv"

	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
)

// calculateACOS 计算 ACOS (Advertising Cost of Sales)
// ACOS = (广告花费 / 广告收入) × 100
// 返回值：ACOS 百分比，-1 表示无法计算
func calculateACOS(spending float64, revenue float64) float64 {
	// 边界情况处理
	if spending <= 0 {
		return 0.0 // 没有花费，ACOS 为 0
	}

	if revenue <= 0 {
		return -1.0 // 没有收入，无法计算 ACOS，返回 -1 表示无效值
	}

	// 计算 ACOS 百分比
	acos := (spending / revenue) * 100

	// 确保结果为合理范围（0-10000%，超过10000%可能是数据异常）
	if acos > 10000 {
		log.Printf("警告: ACOS 值异常高 (%.2f%%), 花费: %.2f, 收入: %.2f", acos, spending, revenue)
	}

	return acos
}

// getTotalOrdersFromHistoryBatch 通过 GetNMReportDetailHistory API 批量获取产品的真实总订单量
// API限制：每次最多20个NMID，每分钟最多3次调用
// 参数：productIDs - 产品ID列表，today - 查询日期（格式：YYYY-MM-DD）
// 返回：产品ID到总订单量的映射
func (s *ProductMonitorService) getTotalOrdersFromHistoryBatch(productIDs []string, today string) map[string]int {
	result := make(map[string]int)

	if len(productIDs) == 0 {
		log.Printf("警告: 产品ID列表为空")
		return result
	}

	// 将产品ID列表转换为NMID列表
	nmids := make([]int, 0, len(productIDs))
	productIDToNMID := make(map[int]string) // NMID到产品ID的映射

	for _, productID := range productIDs {
		nmid := 0
		if _, err := fmt.Sscanf(productID, "%d", &nmid); err != nil {
			log.Printf("警告: 无法将产品ID %s 转换为NMID: %v", productID, err)
			continue
		}
		nmids = append(nmids, nmid)
		productIDToNMID[nmid] = productID
	}

	if len(nmids) == 0 {
		log.Printf("警告: 没有有效的NMID可以查询")
		return result
	}

	// API限制常量
	const maxNMIDsPerRequest = 20  // 每次请求最多20个NMID
	const maxRequestsPerMinute = 3 // 每分钟最多3次请求
	const requestInterval = 20     // 请求间隔20秒（60秒/3次 = 20秒）

	// 计算需要的批次数
	totalBatches := (len(nmids) + maxNMIDsPerRequest - 1) / maxNMIDsPerRequest
	log.Printf("需要查询 %d 个产品，分 %d 批处理（每批最多%d个）", len(nmids), totalBatches, maxNMIDsPerRequest)

	// 检查是否超过每分钟限制
	if totalBatches > maxRequestsPerMinute {
		log.Printf("警告: 需要 %d 批请求，超过每分钟 %d 次的限制，将只处理前 %d 批",
			totalBatches, maxRequestsPerMinute, maxRequestsPerMinute)
		totalBatches = maxRequestsPerMinute
	}

	// 分批处理NMID列表
	for batchIndex := 0; batchIndex < totalBatches; batchIndex++ {
		// 计算当前批次的起始和结束索引
		startIndex := batchIndex * maxNMIDsPerRequest
		endIndex := startIndex + maxNMIDsPerRequest
		if endIndex > len(nmids) {
			endIndex = len(nmids)
		}

		// 获取当前批次的NMID
		batchNMIDs := nmids[startIndex:endIndex]

		log.Printf("处理第 %d/%d 批，包含 %d 个产品", batchIndex+1, totalBatches, len(batchNMIDs))

		// 如果不是第一批，需要等待以遵守速率限制
		if batchIndex > 0 {
			log.Printf("等待 %d 秒以遵守API速率限制...", requestInterval)
			time.Sleep(time.Duration(requestInterval) * time.Second)
		}

		// 构建当前批次的请求参数
		req := &wbhubapi.NMReportDetailHistoryRequest{
			Page:       1,
			IsNextPage: false,
			Period: wbhubapi.Period{
				Begin: today,
				End:   today,
			},
			OrderBy: wbhubapi.OrderBy{
				Field: "ordersCount",
				Mode:  "desc",
			},
			NMIDs:       batchNMIDs,
			Aggregation: "day",
		}

		// 处理当前批次
		batchResult := s.processBatchRequest(req, productIDToNMID, today, batchIndex+1)

		// 合并结果
		for productID, totalOrders := range batchResult {
			result[productID] = totalOrders
		}
	}

	totalProcessed := len(result)
	log.Printf("分批查询完成，成功获取 %d/%d 个产品的总订单量", totalProcessed, len(productIDs))
	return result
}

// processBatchRequest 处理单个批次的API请求
func (s *ProductMonitorService) processBatchRequest(req *wbhubapi.NMReportDetailHistoryRequest, productIDToNMID map[int]string, today string, batchNum int) map[string]int {
	result := make(map[string]int)

	// 调用API获取历史数据
	resp, err := s.wbhubClient.Analytics.GetNMReportDetailHistory(req)
	if err != nil {
		log.Printf("第 %d 批获取产品总订单量失败: %v", batchNum, err)
		return result
	}

	// 检查API响应错误
	if resp.Error {
		log.Printf("第 %d 批获取产品总订单量API返回错误: %s", batchNum, resp.ErrorText)
		return result
	}

	// 解析响应数据
	if len(resp.Data) == 0 {
		log.Printf("第 %d 批查询没有返回历史数据", batchNum)
		return result
	}

	// 解析每个产品的数据
	successCount := 0
	for _, card := range resp.Data {
		productID, exists := productIDToNMID[card.NMID]
		if !exists {
			continue
		}

		// 查找指定日期的数据
		found := false
		for _, period := range card.History {
			if period.Dt == today {
				totalOrders := period.OrdersCount
				result[productID] = totalOrders
				successCount++
				found = true
				log.Printf("第 %d 批：成功获取产品 %s 的真实总订单量: %d", batchNum, productID, totalOrders)
				break
			}
		}

		if !found {
			log.Printf("第 %d 批：未找到产品 %s 在日期 %s 的订单数据", batchNum, productID, today)
		}
	}

	log.Printf("第 %d 批处理完成，成功获取 %d/%d 个产品的总订单量", batchNum, successCount, len(req.NMIDs))
	return result
}

// MonitorProduct 监控产品结构
type MonitorProduct struct {
	Advert    []ProductAdvert `json:"-"`
	ProductID string          `json:"productId"`
	SKU       string          `json:"sku"`
	Keywords  []string        `json:"keywords"`
}

type ProductAdvert struct {
	ProductID     string    `json:"product_id"`
	SKU           string    `json:"sku"`
	AdvertID      int       `json:"advert_id"`
	AdvertType    int       `json:"advert_type"`
	CurrentBid    int       `json:"current_bid"`
	Views         int       `json:"views"`          // 展示量
	Clicks        int       `json:"clicks"`         // 点击量
	CTR           float64   `json:"ctr"`            // 点击率
	Orders        int       `json:"ad_orders"`      // 广告订单量
	OrganicOrders int       `json:"organic_orders"` // 自然流量订单量
	CR            float64   `json:"cr"`             // 转化率
	OrderSum      float64   `json:"order_sum"`      // 订单金额
	Spending      float64   `json:"spending"`       // 花费
	ACOS          float64   `json:"acos"`           // 广告成本占销售额比例 (Advertising Cost of Sales)
	CPM           float64   `json:"cpm"`            // 千次展现成本
	CPC           float64   `json:"cpc"`            // 点击成本
	CPO           float64   `json:"cpo"`            // 订单成本
	UpdateTime    time.Time `json:"update_time"`    // 更新时间
}

// MonitorProductMap 监控产品映射
type MonitorProductMap map[string]MonitorProduct

// ProductMonitorService 产品监控服务
type ProductMonitorService struct {
	products             MonitorProductMap
	mu                   sync.RWMutex
	interval             time.Duration
	stopChan             chan struct{}
	webClient            wb_web.Web
	mqClient             *rocket_mq.RocketMQClient
	restyClient          *resty.Client
	redisClient          *redis.RedisClient
	wbhubClient          *wbhubapi.Client
	config               ProductMonitorConfig
	autoBidStopChan      chan struct{}
	autoBidRunning       bool
	dingTalkRobot        *dingding.DingTalkRobot // 添加钉钉机器人客户端
	myAgentClient        *my_agent.Client
	keywordScheduler     *cron.Cron
	dailyReportScheduler *cron.Cron // 每日广告花费报告定时器
}

// ProductMonitorConfig 产品监控服务配置接口
type ProductMonitorConfig interface {
	GetWBHubApiKey() string
	GetDingTalkAccessToken() string
	GetDingTalkSecret() string
	GetMyAgentBaseURL() string
}

// NewProductMonitorService 创建产品监控服务
func NewProductMonitorService(interval time.Duration, broadcast chan []byte, cfg ProductMonitorConfig) *ProductMonitorService {
	if interval <= 0 {
		interval = 5 * time.Minute // 默认5分钟
	}

	// 获取 RocketMQ 客户端实例
	mqClient := rocket_mq.GetInstance()

	// 初始化生产者
	if err := mqClient.InitProducer("monitor_group"); err != nil {
		log.Printf("初始化 RocketMQ 生产者失败: %v", err)
		// 不要因为 RocketMQ 初始化失败就阻止服务启动
	}

	// 保存 broadcast 通道到局部变量
	globalBroadcast = broadcast

	// 初始化 resty 客户端
	restyClient := resty.New().
		SetTimeout(30*time.Second).
		SetHeader("Content-Type", "application/json").
		SetRetryCount(5).
		SetRetryWaitTime(3000 * time.Millisecond).
		SetRetryMaxWaitTime(15 * time.Second).
		AddRetryCondition(
			// 当发生网络错误或状态码不是2xx时重试
			func(response *resty.Response, err error) bool {
				return err != nil || response.StatusCode() < 200 || response.StatusCode() >= 300
			},
		)

	// 获取Redis客户端实例
	redisClient := redis.GetInstance()

	// 测试Redis连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := redisClient.Ping(ctx); err != nil {
		log.Printf("Redis连接失败: %v", err)
	}

	// 从配置获取API密钥
	apiKey := cfg.GetWBHubApiKey()
	if apiKey == "" {
		log.Printf("警告: WBHUB_API_KEY 未配置，WBHub客户端功能将不可用")
	}
	hw := wbhubapi.NewClient(apiKey)

	// 初始化钉钉机器人
	var dingTalkRobot *dingding.DingTalkRobot
	accessToken := cfg.GetDingTalkAccessToken()
	secret := cfg.GetDingTalkSecret()
	if accessToken != "" && secret != "" {
		var err error
		dingTalkRobot, err = dingding.NewDingTalkRobot(accessToken, secret)
		if err != nil {
			log.Printf("初始化钉钉机器人失败: %v", err)
		} else {
			log.Printf("钉钉机器人初始化成功")
		}
	} else {
		log.Printf("警告: 钉钉配置未完整，钉钉通知功能将不可用")
	}

	// 初始化 MyAgent 客户端
	var myAgentClient *my_agent.Client
	// 暂时使用固定地址，后续可以从配置文件读取
	myAgentBaseURL := cfg.GetMyAgentBaseURL()
	if myAgentBaseURL != "" {
		myAgentClient = my_agent.NewClient(myAgentBaseURL)
		log.Printf("MyAgent客户端初始化成功: %s", myAgentBaseURL)
	} else {
		log.Printf("警告: MyAgent配置未设置，关键词分析功能将不可用")
	}

	// 设置莫斯科时区
	moscowLocation, err := time.LoadLocation("Europe/Moscow")
	if err != nil {
		log.Printf("加载莫斯科时区失败: %v", err)
		moscowLocation = time.UTC
	}
	// 创建关键词排除定时器（莫斯科时间每天4点）
	keywordScheduler := cron.New(cron.WithLocation(moscowLocation))

	// 创建每日报告定时器（中国时间每天早上8点）
	chinaLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Printf("加载中国时区失败: %v", err)
		chinaLocation = time.UTC
	}
	dailyReportScheduler := cron.New(cron.WithLocation(chinaLocation))

	return &ProductMonitorService{
		products:             make(MonitorProductMap),
		interval:             interval,
		stopChan:             make(chan struct{}),
		webClient:            wb_web.Init(),
		wbhubClient:          hw,
		mqClient:             mqClient,
		restyClient:          restyClient,
		redisClient:          redisClient,
		config:               cfg,
		autoBidStopChan:      make(chan struct{}),
		autoBidRunning:       false,
		dingTalkRobot:        dingTalkRobot,
		myAgentClient:        myAgentClient,
		keywordScheduler:     keywordScheduler,
		dailyReportScheduler: dailyReportScheduler,
	}
}

// 定义全局 broadcast 变量用于推送
var globalBroadcast chan []byte

const (
	REDIS_MONITOR_PRODUCTS_KEY  = "monitor:products"          // Redis中存储监控产品配置的key
	REDIS_FAILED_MESSAGES_KEY   = "monitor:failed_messages"   // Redis中存储发送失败的消息
	REDIS_FAILED_QUEUE_KEY      = "monitor:failed_queue"      // Redis消息队列key
	REDIS_EXCLUDED_KEYWORDS_KEY = "monitor:excluded_keywords" // Redis中存储广告排除词的key
)

// UpdateProducts 更新监控产品列表
func (s *ProductMonitorService) UpdateProducts(data []byte) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	var newProducts MonitorProductMap
	if err := json.Unmarshal(data, &newProducts); err != nil {
		return err
	}

	s.products = newProducts
	return nil
}

// StartMonitoring 开始监控
func (s *ProductMonitorService) StartMonitoring() {
	ticker := time.NewTicker(s.interval)
	go func() {
		// 立即执行一次
		s.monitorProducts(true)
		for {
			select {
			case <-ticker.C:
				s.monitorProducts(false)
			case <-s.stopChan:
				ticker.Stop()
				return
			}
		}
	}()
}

// StopMonitoring 停止监控
func (s *ProductMonitorService) StopMonitoring() {
	close(s.stopChan)
	// 同时停止自动出价调整服务
	s.StopAutoBidAdjustment()
	// 停止关键词排除服务
	s.StopKeywordExclusionService()
	// 停止每日报告服务
	s.StopDailyReportService()
}

// UpdateInterval 更新监控间隔
func (s *ProductMonitorService) UpdateInterval(interval time.Duration) {
	if interval <= 0 {
		return
	}
	s.mu.Lock()
	defer s.mu.Unlock()
	s.interval = interval

	// 重启监控以应用新的间隔时间
	s.StopMonitoring()
	s.stopChan = make(chan struct{})
	s.StartMonitoring()
}

// GetInterval 获取当前监控间隔
func (s *ProductMonitorService) GetInterval() time.Duration {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.interval
}

// StartAutoBidAdjustment 启动自动出价调整服务
func (s *ProductMonitorService) StartAutoBidAdjustment() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.autoBidRunning {
		log.Printf("自动出价调整服务已在运行中")
		return
	}

	s.autoBidRunning = true
	log.Printf("启动自动出价调整服务...")

	go s.runAutoBidAdjustment()
}

// StopAutoBidAdjustment 停止自动出价调整服务
func (s *ProductMonitorService) StopAutoBidAdjustment() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.autoBidRunning {
		return
	}

	log.Printf("正在停止自动出价调整服务...")
	close(s.autoBidStopChan)
	s.autoBidRunning = false
	log.Printf("自动出价调整服务已停止")
}

// runAutoBidAdjustment 运行自动出价调整服务
func (s *ProductMonitorService) runAutoBidAdjustment() {
	// 计算到下一个58分的等待时间
	now := time.Now()
	nextRun := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 58, 0, 0, now.Location())
	if now.After(nextRun) {
		// 如果当前时间已经过了这个小时的58分，则等到下一个小时的58分
		nextRun = nextRun.Add(time.Hour)
	}

	log.Printf("自动出价调整服务将在 %s 首次执行", nextRun.Format("15:04:05"))

	// 等待到第一次执行时间
	timer := time.NewTimer(time.Until(nextRun))
	defer timer.Stop()

	select {
	case <-s.autoBidStopChan:
		log.Printf("收到停止信号，退出自动出价调整服务")
		return
	case <-timer.C:
		// 执行第一次调整
		s.adjustBids()
	}

	// 之后每小时的58分执行
	ticker := time.NewTicker(time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-s.autoBidStopChan:
			log.Printf("收到停止信号，退出自动出价调整服务")
			return
		case <-ticker.C:
			// 检查当前是否是58分，如果不是则等待到58分
			now := time.Now()
			if now.Minute() != 58 {
				// 计算到58分的等待时间
				target := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 58, 0, 0, now.Location())
				if now.After(target) {
					target = target.Add(time.Hour)
				}
				waitTime := time.Until(target)
				log.Printf("等待 %v 到达 %s 执行出价调整", waitTime, target.Format("15:04:05"))

				waitTimer := time.NewTimer(waitTime)
				select {
				case <-s.autoBidStopChan:
					waitTimer.Stop()
					log.Printf("收到停止信号，退出自动出价调整服务")
					return
				case <-waitTimer.C:
					s.adjustBids()
				}
				waitTimer.Stop()
			} else {
				s.adjustBids()
			}
		}
	}
}

// adjustBids 调整出价
func (s *ProductMonitorService) adjustBids() {

	// 获取所有广告 正在运行的
	status := wbhubapi.CampaignStatusRunning
	typeAuction := wbhubapi.CampaignTypeAuction
	params := &wbhubapi.CampaignQueryParams{
		Status: &status,      // 只获取运行中的广告
		Type:   &typeAuction, // 只获取搜索广告
	}
	log.Printf("正在获取运行中的广告活动...")
	campaigns, err := s.wbhubClient.Promotion.GetCampaignsByParams(params)
	if err != nil {
		log.Printf("获取广告活动失败: %v", err)
		return
	}
	log.Printf("成功获取 %d 个运行中的广告活动", len(campaigns))

	// 获取莫斯科时间
	moscowLocation, err := time.LoadLocation("Europe/Moscow")
	if err != nil {
		log.Printf("加载莫斯科时区失败: %v", err)
		return
	}

	now := time.Now().In(moscowLocation)

	// 由于在58分执行，我们需要为下一个小时设置出价系数
	// 所以使用下一个小时的时间来计算系数
	nextHour := now.Add(time.Hour)
	targetHour := nextHour.Hour()
	targetWeekday := nextHour.Weekday()

	log.Printf("开始自动出价调整 - 莫斯科时间: %s, 当前小时: %d, 目标小时: %d, 星期: %s",
		now.Format("2006-01-02 15:04:05"), now.Hour(), targetHour, targetWeekday.String())

	// 获取下一个小时的调整系数
	coefficient := s.getBidCoefficient(targetHour, targetWeekday)
	if coefficient == 0 {
		log.Printf("当前时间段无需调整出价")
		return
	}

	log.Printf("当前时间段出价调整系数: %.1f", coefficient)

	// 收集需要调整的广告
	var allBids []wbhubapi.BidItem
	adjustmentCount := 0

	//s.mu.RLock()
	//for productID, product := range s.products {
	//	for _, advert := range product.Advert {
	//		// 只调整搜索广告（类型9），跳过自动广告（类型8）
	//		if advert.AdvertType != 9 {
	//			continue
	//		}
	//
	//		// 计算新的出价
	//		baseBid := 320 // 搜索广告默认最低价320卢布
	//		newBid := int(float64(baseBid) * coefficient)
	//
	//		// 确保不低于最低价
	//		if newBid < baseBid {
	//			newBid = baseBid
	//		}
	//
	//		// 如果出价没有变化，跳过
	//		if newBid == advert.CurrentBid {
	//			continue
	//		}
	//
	//		// 将产品ID转换为整数
	//		productIDInt := 0
	//		if _, err := fmt.Sscanf(productID, "%d", &productIDInt); err != nil {
	//			log.Printf("无法将产品ID %s 转换为整数: %v", productID, err)
	//			continue
	//		}
	//		nm_bids := []wbhubapi.AuctionMultibid{{
	//			NM:  productIDInt,
	//			Bid: newBid,
	//		}}
	//		// 构造出价调整请求
	//		bid := wbhubapi.BidItem{
	//			AdvertID: advert.AdvertID,
	//			NmBids:   nm_bids,
	//		}
	//
	//		allBids = append(allBids, bid)
	//		adjustmentCount++
	//
	//		log.Printf("计划调整广告出价 - 产品ID: %s, 广告ID: %d, 当前出价: %d, 新出价: %d, 系数: %.1f",
	//			productID, advert.AdvertID, advert.CurrentBid, newBid, coefficient)
	//	}
	//}
	//s.mu.RUnlock()

	// 遍历 campaigns
	for _, campaign := range campaigns {
		if campaign.Type != 9 {
			continue
		}
		// 获得当前出价
		//currentBid := 0
		//productIDInt := 0
		bidAndProductIDMap := map[int]int{}
		for _, nmc := range campaign.AuctionMultibids {
			//currentBid = nmc.Bid
			bidAndProductIDMap[nmc.NM] = nmc.Bid
		}
		// 计算新的出价
		baseBid := 320 // 搜索广告默认最低价320卢布
		newBid := int(float64(baseBid) * coefficient)

		// 确保不低于最低价
		if newBid < baseBid {
			newBid = baseBid
		}
		nm_bids := []wbhubapi.AuctionMultibid{}
		for productIDInt, currentBid := range bidAndProductIDMap {
			// 如果出价没有变化，跳过
			if newBid == currentBid {
				continue
			}
			nm_bids = append(nm_bids, wbhubapi.AuctionMultibid{
				NM:  productIDInt,
				Bid: newBid,
			})
			log.Printf("计划调整广告出价 - 产品ID: %d, 广告ID: %d, 当前出价: %d, 新出价: %d, 系数: %.1f",
				productIDInt, campaign.AdvertID, currentBid, newBid, coefficient)
		}
		if len(nm_bids) == 0 {
			continue
		}
		bid := wbhubapi.BidItem{
			AdvertID: campaign.AdvertID,
			NmBids:   nm_bids,
		}
		allBids = append(allBids, bid)
		adjustmentCount++
	}

	// 批量更新出价（分批处理，每批最多20个）
	if len(allBids) > 0 {
		const maxBidsPerBatch = 20
		totalBatches := (len(allBids) + maxBidsPerBatch - 1) / maxBidsPerBatch
		successCount := 0
		failCount := 0
		var lastError error

		log.Printf("需要调整 %d 个广告出价，分 %d 批处理（每批最多%d个）", len(allBids), totalBatches, maxBidsPerBatch)

		for batchIndex := 0; batchIndex < totalBatches; batchIndex++ {
			// 计算当前批次的起始和结束索引
			startIndex := batchIndex * maxBidsPerBatch
			endIndex := startIndex + maxBidsPerBatch
			if endIndex > len(allBids) {
				endIndex = len(allBids)
			}

			// 获取当前批次的出价数据
			batchBids := allBids[startIndex:endIndex]

			log.Printf("处理第 %d/%d 批出价调整，包含 %d 个广告", batchIndex+1, totalBatches, len(batchBids))

			// 调用API更新当前批次
			err := s.wbhubClient.Promotion.UpdateBids(batchBids)
			if err != nil {
				log.Printf("第 %d 批出价调整失败，包含 %d 个广告: %v", batchIndex+1, len(batchBids), err)
				failCount += len(batchBids)
				lastError = err
			} else {
				log.Printf("第 %d 批出价调整成功，包含 %d 个广告", batchIndex+1, len(batchBids))
				successCount += len(batchBids)
				// 更新成功的广告的CurrentBid价格
				s.updateCurrentBidsAfterSuccess(batchBids)
			}
			// 如果不是最后一批，等待一段时间避免API限制
			if batchIndex < totalBatches-1 {
				log.Printf("等待2秒后处理下一批...")
				time.Sleep(2 * time.Second)
			}
		}

		// 发送钉钉通知
		if failCount == 0 {
			// 全部成功
			log.Printf("批量更新广告出价全部成功，共 %d 个广告", successCount)
			s.sendDingTalkNotification(now, coefficient, successCount, true, "")
		} else if successCount == 0 {
			// 全部失败
			log.Printf("批量更新广告出价全部失败，共 %d 个广告: %v", failCount, lastError)
			s.sendDingTalkNotification(now, coefficient, failCount, false, lastError.Error())
		} else {
			// 部分成功
			log.Printf("批量更新广告出价部分成功，成功 %d 个，失败 %d 个", successCount, failCount)
			errorMsg := fmt.Sprintf("部分成功：成功 %d 个，失败 %d 个。最后错误：%v", successCount, failCount, lastError)
			s.sendDingTalkNotification(now, coefficient, len(allBids), false, errorMsg)
		}
	} else {
		log.Printf("当前无需调整的广告出价")
	}
}

// updateCurrentBidsAfterSuccess 更新成功调价后的广告CurrentBid价格
func (s *ProductMonitorService) updateCurrentBidsAfterSuccess(batchBids []wbhubapi.BidItem) {
	s.mu.Lock()
	defer s.mu.Unlock()

	for _, bidItem := range batchBids {
		advertID := bidItem.AdvertID

		// 获取新的出价（取第一个NmBids的Bid值）
		if len(bidItem.NmBids) == 0 {
			continue
		}
		newBid := bidItem.NmBids[0].Bid

		// 遍历所有产品找到对应的广告并更新CurrentBid
		for productID, product := range s.products {
			for i, advert := range product.Advert {
				if advert.AdvertID == advertID {
					oldBid := advert.CurrentBid
					// 更新CurrentBid
					s.products[productID].Advert[i].CurrentBid = newBid
					log.Printf("更新广告CurrentBid - 产品ID: %s, 广告ID: %d, 旧价格: %d, 新价格: %d",
						productID, advertID, oldBid, newBid)
					break
				}
			}
		}
	}
}

// getBidCoefficient 根据时间获取出价调整系数
func (s *ProductMonitorService) getBidCoefficient(hour int, weekday time.Weekday) float64 {
	// 判断是否为工作日（周一到周五）
	isWorkday := weekday >= time.Monday && weekday <= time.Friday

	if isWorkday {
		// 工作日规则
		return s.getWorkdayCoefficient(hour)
	} else {
		// 周末规则
		return s.getWeekendCoefficient(hour, weekday)
	}
}

// getWorkdayCoefficient 获取工作日的出价调整系数
func (s *ProductMonitorService) getWorkdayCoefficient(hour int) float64 {
	var r = rand.New(rand.NewSource(time.Now().UnixNano()))
	switch {
	case hour >= 1 && hour < 8:
		return 1.0 // 深夜低谷(100%)
	case hour >= 8 && hour < 11:
		return 1.02 + r.Float64()*(1.14-1.02) // 上午预热(115%)
	case hour >= 11 && hour < 12:
		return 1.15 + r.Float64()*(1.30-1.15) // 午高峰前夕(130%)
	case hour >= 12 && hour < 14:
		return 1.31 + r.Float64()*(1.58-1.31) // 午间高峰(160%)
	case hour >= 14 && hour < 15:
		return 1.08 + r.Float64()*(1.17-1.08) // 高峰回落(120%)
	case hour >= 15 && hour < 18:
		return 1.0 + r.Float64()*(1.17-1.0) // 下午低谷(100%)
	case hour >= 18 && hour < 21:
		return 1.3 + r.Float64()*(1.5-1.3) // 晚高峰前夕(150%)
	case hour >= 21 && hour < 23:
		return 1.5 + r.Float64()*(1.8-1.5) // 晚间黄金期(220%)
	case hour >= 23 || hour == 0:
		return 1.25 + r.Float64()*(1.5-1.25) // 晚间高峰回落(160%)
	case hour == 0: // 24点到1点处理为0点
		return 1.02 + r.Float64()*(1.08-1.02) // 晚间低谷(110%)
	default:
		return 1.0 // 默认系数
	}
}

// getWeekendCoefficient 获取周末的出价调整系数（平滑调价版本）
func (s *ProductMonitorService) getWeekendCoefficient(hour int, weekday time.Weekday) float64 {
	var r = rand.New(rand.NewSource(time.Now().UnixNano()))
	switch {
	case hour >= 1 && hour < 11:
		return 1.0 // 周末清晨(100%)
	case hour >= 11 && hour < 12:
		return 1.3 + r.Float64()*(1.5-1.3) // 周末唤醒(160%)
	case hour >= 12 && hour < 22:
		return 1.5 + r.Float64()*(1.9-1.5) // 周末购物时段(200%)
	case hour >= 22 || hour == 0:
		if weekday == time.Saturday {
			return 1.5 + r.Float64()*(1.8-1.5) // 周六休息时段(180%)
		} else { // Sunday
			return 1.6 + r.Float64()*(1.9-1.6) // 周日黄金时段(200%)
		}
	default:
		return 1.0 // 默认系数
	}
}

// sendDingTalkNotification 发送钉钉通知
func (s *ProductMonitorService) sendDingTalkNotification(adjustTime time.Time, coefficient float64, adjustedCount int, success bool, errorMsg string) {
	// 检查钉钉机器人是否可用
	if s.dingTalkRobot == nil {
		log.Printf("钉钉机器人未初始化，跳过通知发送")
		return
	}

	// 构建通知消息
	var status, statusIcon string
	if success {
		status = "成功"
		statusIcon = "✅"
	} else {
		status = "失败"
		statusIcon = "❌"
	}

	// 获取时间段描述
	timeDescription := s.getTimeDescription(adjustTime.Hour(), adjustTime.Weekday())

	// 构建消息内容
	content := fmt.Sprintf(`%s 自动出价调整报告

📅 调整时间: %s
⏰ 时间段: %s
📊 调整系数: %.2f倍
🎯 调整广告数量: %d个
📈 状态: %s %s`,
		statusIcon,
		adjustTime.Format("2006-01-02 15:04:05 (MST)"),
		timeDescription,
		coefficient,
		adjustedCount,
		status,
		statusIcon)

	if !success && errorMsg != "" {
		content += fmt.Sprintf("\n❗ 错误信息: %s", errorMsg)
	}

	// 使用消息构造器创建文本消息
	builder := dingding.MessageBuilder{}
	message := builder.NewTextMessage(content)

	// 设置@所有人
	message.At = &dingding.AtConfig{
		IsAtAll: false,
	}

	// 异步发送到钉钉
	go func() {
		if err := s.dingTalkRobot.Send(message); err != nil {
			log.Printf("发送钉钉通知失败: %v", err)
		} else {
			log.Printf("钉钉通知发送成功")
		}
	}()
}

// getTimeDescription 获取时间段描述
func (s *ProductMonitorService) getTimeDescription(hour int, weekday time.Weekday) string {
	isWorkday := weekday >= time.Monday && weekday <= time.Friday

	if isWorkday {
		switch {
		case hour >= 1 && hour < 8:
			return "深夜低谷"
		case hour >= 8 && hour < 11:
			return "上午预热"
		case hour >= 11 && hour < 12:
			return "午高峰前夕"
		case hour >= 12 && hour < 14:
			return "午间高峰"
		case hour >= 14 && hour < 15:
			return "高峰回落"
		case hour >= 15 && hour < 18:
			return "下午低谷"
		case hour >= 18 && hour < 21:
			return "晚高峰前夕"
		case hour >= 21 && hour < 23:
			return "晚间黄金期"
		case hour >= 23 || hour == 0:
			return "晚间高峰回落"
		default:
			return "工作日时段"
		}
	} else {
		switch {
		case hour >= 1 && hour < 11:
			return "周末清晨"
		case hour >= 11 && hour < 12:
			return "周末唤醒"
		case hour >= 12 && hour < 22:
			return "周末购物时段"
		case hour >= 22 || hour == 0:
			if weekday == time.Saturday {
				return "周六休息时段"
			} else {
				return "周日黄金时段"
			}
		default:
			return "周末时段"
		}
	}
}

// SyncProductAdverts 同步产品广告信息
func (s *ProductMonitorService) SyncProductAdverts() {
	log.Printf("开始同步产品广告信息...")
	startTime := time.Now()

	// 获取所有广告 正在运行的
	status := wbhubapi.CampaignStatusRunning
	params := &wbhubapi.CampaignQueryParams{
		Status: &status, // 只获取运行中的广告
	}

	log.Printf("正在获取运行中的广告活动...")

	campaigns, err := s.wbhubClient.Promotion.GetCampaignsByParams(params)
	if err != nil {
		log.Printf("获取广告活动失败: %v", err)
		return
	}

	log.Printf("成功获取 %d 个运行中的广告活动", len(campaigns))

	// 创建临时映射存储产品的广告信息
	log.Printf("开始处理广告活动，创建产品广告映射...")
	productAdverts := make(map[string][]ProductAdvert)
	processedCampaigns := 0
	skippedCampaigns := 0

	for _, campaign := range campaigns {
		if campaign.AdvertID == 22590989 ||
			campaign.AdvertID == 26973370 ||
			campaign.AdvertID == 22627671 ||
			campaign.AdvertID == 22692153 ||
			campaign.AdvertID == 22534626 {
			skippedCampaigns++
			log.Printf("跳过指定的广告ID: %d", campaign.AdvertID)
			continue
		}

		productID := ""
		currentBid := 0
		advertType := ""
		if campaign.AuctionMultibids == nil {
			productID = fmt.Sprintf("%d", campaign.AutoParams.NMS[0])
			currentBid = campaign.AutoParams.NMCPM[0].CPM
			campaign.Type = 8
			advertType = "自动广告"
		} else {
			for _, param := range campaign.UnitedParams {
				if len(param.NMS) > 1 {
					for _, nmp := range param.NMS {
						log.Printf("广告ID: %d 产品ID: %d 存在多个产品", campaign.AdvertID, nmp)
						nm := fmt.Sprintf("%d", nmp)
						if _, ok := productAdverts[nm]; ok {
							continue
						}
						productID = nm
					}
				} else {
					productID = fmt.Sprintf("%d", param.NMS[0])
				}
			}
			for _, nmc := range campaign.AuctionMultibids {
				tempId := fmt.Sprintf("%d", nmc.NM)
				if tempId == productID {
					currentBid = nmc.Bid
				}
			}
			campaign.Type = 9
			advertType = "搜索广告"
		}

		processedCampaigns++
		log.Printf("处理广告 %d/%d - 广告ID: %d, 产品ID: %s, 类型: %s, 出价: %d",
			processedCampaigns, len(campaigns)-skippedCampaigns, campaign.AdvertID, productID, advertType, currentBid)

		// 创建新的广告信息
		advert := ProductAdvert{
			ProductID:     productID,
			AdvertID:      campaign.AdvertID,
			AdvertType:    campaign.Type,
			CurrentBid:    currentBid,
			Views:         campaign.Statistics.Views,
			Clicks:        campaign.Statistics.Clicks,
			CTR:           campaign.Statistics.CTR,
			Orders:        campaign.Statistics.Orders, // 广告订单量
			OrganicOrders: 0,                          // 自然流量订单量（初始化为0，后续通过API获取）
			CR:            campaign.Statistics.CR,
			OrderSum:      campaign.Statistics.SumPrice,
			Spending:      campaign.Statistics.Sum,
			ACOS:          calculateACOS(campaign.Statistics.Sum, campaign.Statistics.SumPrice),
			CPM:           0, // CPM 字段在 CampaignStatistics 中不存在，设为0
			CPC:           campaign.Statistics.CPC,
			CPO:           0, // CPO 字段在 CampaignStatistics 中不存在，设为0
			UpdateTime:    campaign.ChangeTime,
		}

		// 将广告信息添加到对应产品的广告列表中
		productAdverts[productID] = append(productAdverts[productID], advert)

		// 如果产品不存在，获取SKU并创建新产品
		if _, exists := s.products[productID]; !exists {
			log.Printf("产品 %s 不存在，正在获取产品信息...", productID)

			// 使用正确的 WB API 结构
			cardsRequest := &wbhubapi.CardsListRequest{
				Settings: wbhubapi.CardsListSettings{
					Filter: wbhubapi.CardsFilter{
						WithPhoto:  1,
						TextSearch: productID,
					},
					Cursor: wbhubapi.CardsCursor{
						Limit: 1,
					},
				},
			}
			cards, err := s.wbhubClient.Products.GetCardsList(cardsRequest, "ru")

			if err != nil {
				log.Printf("获取产品 %s 卡片信息失败: %v", productID, err)
				continue
			}

			// 获取SKU
			var sku string
			if len(cards.Cards) > 0 {
				sku = cards.Cards[0].VendorCode
				log.Printf("产品 %s 获取到SKU: %s", productID, sku)
			} else {
				log.Printf("产品 %s 未找到卡片信息，SKU为空", productID)
			}

			// 创建新产品
			s.products[productID] = MonitorProduct{
				ProductID: productID,
				SKU:       sku,
				Keywords:  []string{},
				Advert:    productAdverts[productID],
			}

			// 同步到Redis
			if err := s.syncProductToRedis(productID); err != nil {
				log.Printf("同步产品配置到Redis失败: %v", err)
			}
			continue
		}

		// 判断sku 是否为空
		if s.products[productID].SKU == "" {
			log.Printf("产品 %s 的SKU为空，正在重新获取...", productID)

			// 使用正确的 WB API 结构
			cardsRequest := &wbhubapi.CardsListRequest{
				Settings: wbhubapi.CardsListSettings{
					Filter: wbhubapi.CardsFilter{
						WithPhoto:  1,
						TextSearch: productID,
					},
					Cursor: wbhubapi.CardsCursor{
						Limit: 1,
					},
				},
			}
			cards, err := s.wbhubClient.Products.GetCardsList(cardsRequest, "ru")

			if err != nil {
				log.Printf("重新获取产品 %s 卡片信息失败: %v", productID, err)
				continue
			}

			// 获取SKU
			var sku string
			if len(cards.Cards) > 0 {
				sku = cards.Cards[0].VendorCode
				log.Printf("产品 %s 重新获取到SKU: %s", productID, sku)
			} else {
				log.Printf("产品 %s 重新获取SKU失败，未找到卡片信息", productID)
			}

			// 更新sku
			p_temp := s.products[productID]
			p_temp.SKU = sku
			s.products[productID] = p_temp
			log.Printf("产品 %s SKU更新成功", productID)

			// 同步到Redis
			if err := s.syncProductToRedis(productID); err != nil {
				log.Printf("同步产品配置到Redis失败: %v", err)
			} else {
				log.Printf("产品 %s 配置已同步到Redis", productID)
			}
			continue
		}

		// 更新现有产品的广告信息
		product := s.products[productID]
		product.Advert = productAdverts[productID]
		s.products[productID] = product
		log.Printf("产品 %s 广告信息更新完成，包含 %d 个广告", productID, len(productAdverts[productID]))
	}

	// 统计和总结日志
	totalProducts := len(s.products)
	totalAdverts := 0
	for _, product := range s.products {
		totalAdverts += len(product.Advert)
	}

	duration := time.Since(startTime)
	log.Printf("产品广告同步完成！")
	log.Printf("同步统计: 处理了 %d 个广告活动，跳过了 %d 个指定广告", processedCampaigns, skippedCampaigns)
	log.Printf("最终结果: %d 个产品，共 %d 个广告", totalProducts, totalAdverts)
	log.Printf("同步耗时: %v", duration)
}

// monitorProducts 监控产品排名
func (s *ProductMonitorService) monitorProducts(isOne bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	// 移除了批量出价更新功能

	for _, product := range s.products {
		keywordCount := len(product.Keywords)
		if keywordCount == 0 {
			continue
		}

		var wg sync.WaitGroup
		semaphore := make(chan struct{}, keywordCount)
		results := make(chan *search.SearchProduct, keywordCount)

		for _, keyword := range product.Keywords {
			wg.Add(1)
			go func(p MonitorProduct, k string) {
				defer wg.Done()
				semaphore <- struct{}{}
				defer func() { <-semaphore }()
				if result := s.processKeyword(p, k, time.Now()); result != nil {
					results <- result
				}
			}(product, keyword)
		}

		// 等待所有关键词处理完成
		go func() {
			wg.Wait()
			close(results)
		}()

		// 消费所有结果以避免goroutine泄漏
		for range results {
			// 不处理排名结果，只是消费掉
		}
	}

	log.Printf("产品监控完成，共监控 %d 个产品", len(s.products))
}

// sendWebSocketAsync 异步发送消息到WebSocket，带重试机制
func (s *ProductMonitorService) sendWebSocketAsync(record interface{}) {
	// 将记录转换为JSON
	recordJSON, err := json.Marshal(record)
	if err != nil {
		log.Printf("序列化记录失败: %v", err)
		return
	}

	go func() {
		maxRetries := 5
		retryDelay := time.Second

		for i := 0; i < maxRetries; i++ {
			select {
			case globalBroadcast <- recordJSON:
				log.Println("WebSocket 推送成功")
				return
			default:
				if i < maxRetries-1 {
					log.Printf("WebSocket 推送失败，%d秒后重试 (%d/%d)...", retryDelay/time.Second, i+1, maxRetries)
					time.Sleep(retryDelay)
					// 每次重试增加延迟
					retryDelay *= 2
				} else {
					log.Printf("WebSocket 推送失败，达到最大重试次数 (%d)，消息丢弃", maxRetries)
				}
			}
		}
	}()
}

// sendMessageToMQAsync 异步发送消息到RocketMQ
func (s *ProductMonitorService) sendMessageToMQAsync(record interface{}, productID, keyword string, rqTime time.Time) {
	go func() {
		// 生成UUID作为消息唯一ID
		messageKey := uuid.New().String()

		// 将记录转换为JSON
		recordJSON, err := json.Marshal(record)
		if err != nil {
			log.Printf("序列化记录失败: %v", err)
			return
		}

		// 创建消息
		msg := primitive.NewMessage(
			"spider_items_keyword",
			recordJSON,
		)
		msg.WithKeys([]string{messageKey}) // 设置消息Key
		result, err := s.mqClient.GetProducer().SendSync(context.Background(), msg)
		if err != nil {
			// 将失败的消息封装
			failedMsg := map[string]interface{}{
				"topic":      msg.Topic,
				"body":       string(recordJSON),
				"timestamp":  time.Now().Unix(),
				"error":      err.Error(),
				"messageKey": messageKey,
				"productId":  productID,
				"keyword":    keyword,
				"rqTime":     rqTime.Unix(),
			}
			failedMsgJSON, err := json.Marshal(failedMsg)
			if err != nil {
				log.Printf("序列化失败消息记录失败: %v", err)
				return
			}

			err = s.redisClient.RPush(context.Background(), REDIS_FAILED_QUEUE_KEY, string(failedMsgJSON))
			if err != nil {
				log.Printf("推送失败消息到Redis队列失败: %v", err)
				return
			}
			log.Printf("消息发送失败，已推送到Redis队列，消息Key: %s, 产品ID: %s, 关键词: %s", messageKey, productID, keyword)
			return
		}
		log.Printf("消息发送成功，msgId: %s, messageKey: %s, 产品ID: %s, 关键词: %s", result.MsgID, messageKey, productID, keyword)
	}()
}

// processKeyword 处理关键词搜索，带重试机制
func (s *ProductMonitorService) processKeyword(product MonitorProduct, keyword string, rqTime time.Time) *search.SearchProduct {
	const maxRetries = 3
	const retryDelay = 2 * time.Second

	var searchProducts []search.SearchProduct
	var lastErr error

	// 重试机制
	for attempt := 1; attempt <= maxRetries; attempt++ {
		searchProducts, lastErr = s.webClient.GetSearchInfoByKeyword(keyword, 1)
		if lastErr == nil {
			break
		}

		log.Printf("获取搜索结果失败 [%s] (尝试 %d/%d): %v", keyword, attempt, maxRetries, lastErr)
		if attempt < maxRetries {
			time.Sleep(retryDelay)
		}
	}

	if lastErr != nil {
		log.Printf("经过 %d 次重试后仍然失败，关键词: %s, 错误: %v", maxRetries, keyword, lastErr)
		return nil
	}

	// 创建目标产品的记录
	record := &search.SearchProduct{
		Id:          gconv.Int(product.ProductID),
		PlatformId:  "wb",
		SKU:         product.SKU,
		Keyword:     keyword,
		Rank:        0,
		InProcessAt: rqTime,
	}

	// 分析搜索结果，查找目标产品
	found := false
	// 记录前100个产品的排名和目标产品（如果在100名后）
	for i, item := range searchProducts {
		// 为每条记录生成独特的时间戳，在原始时间基础上加上毫秒级的偏移
		itemTime := rqTime.Add(time.Duration(i) * time.Millisecond)

		rank := i + 1
		// 如果是目标产品，更新目标产品的记录
		if fmt.Sprint(item.Id) == product.ProductID {
			found = true
			item.PlatformId = "wb"
			item.SKU = product.SKU
			item.Rank = rank
			item.Keyword = keyword
			item.InProcessAt = itemTime // 使用独特的时间戳
			log.Printf("找到目标产品[%s] 关键词 [%s] 在第 %d 位", product.ProductID, keyword, rank)
			// WebSocket 实时推送
			s.sendWebSocketAsync(&item)
			return &item
		}
	}

	// 如果未找到目标产品，保存1000+的记录
	if !found {
		// 使用原始时间戳，因为这是单独的记录
		record.InProcessAt = rqTime
		// WebSocket 实时推送
		s.sendWebSocketAsync(record)
		log.Printf("未找到目标产品[%s] 关键词 [%s] 在第 %d 位", product.ProductID, keyword, 0)
	}
	return record
}

// syncProductToRedis 同步单个产品配置到Redis
func (s *ProductMonitorService) syncProductToRedis(productID string) error {
	product, exists := s.products[productID]
	if !exists {
		// 如果产品不存在，从Redis中删除
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()
		return s.redisClient.HDel(ctx, REDIS_MONITOR_PRODUCTS_KEY, productID)
	}

	// 将产品配置转换为JSON
	productJSON, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("序列化产品配置失败: %v", err)
	}

	// 存储到Redis
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	return s.redisClient.HSet(ctx, REDIS_MONITOR_PRODUCTS_KEY, productID, string(productJSON))
}

// CreateProduct 创建新的监控产品
func (s *ProductMonitorService) CreateProduct(productID string, sku string, keywords []string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查产品是否已存在
	if _, exists := s.products[productID]; exists {
		return fmt.Errorf("产品ID %s 已存在", productID)
	}

	// 创建新产品
	s.products[productID] = MonitorProduct{
		ProductID: productID,
		SKU:       sku,
		Keywords:  keywords,
	}

	// 同步到Redis
	if err := s.syncProductToRedis(productID); err != nil {
		log.Printf("同步产品配置到Redis失败: %v", err)
	}

	log.Printf("已创建新产品 [%s], SKU: %s", productID, sku)
	return nil
}

// DeleteProduct 删除监控产品
func (s *ProductMonitorService) DeleteProduct(productID string) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查产品是否存在
	if _, exists := s.products[productID]; !exists {
		return fmt.Errorf("产品ID %s 不存在", productID)
	}

	// 删除产品
	delete(s.products, productID)

	// 从Redis中删除
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	if err := s.redisClient.HDel(ctx, REDIS_MONITOR_PRODUCTS_KEY, productID); err != nil {
		log.Printf("从Redis删除产品配置失败: %v", err)
	}

	log.Printf("已删除产品 [%s]", productID)
	return nil
}

// GetProduct 获取产品信息
func (s *ProductMonitorService) GetProduct(productID string) (*MonitorProduct, error) {
	//s.mu.RLock()
	//defer s.mu.RUnlock()

	product, exists := s.products[productID]
	if !exists {
		return nil, fmt.Errorf("产品ID %s 不存在", productID)
	}

	return &product, nil
}

// GetAllProducts 获取所有产品信息
func (s *ProductMonitorService) GetAllProducts() MonitorProductMap {
	//s.mu.RLock()
	//defer s.mu.RUnlock()

	// 创建一个新的 map 来避免直接返回内部 map
	products := make(MonitorProductMap)
	for k, v := range s.products {
		products[k] = v
	}

	return products
}

// UpdateProductKeywords 更新指定产品的关键词列表
func (s *ProductMonitorService) UpdateProductKeywords(productID string, keywords []string) error {
	//s.mu.Lock()
	//defer s.mu.Unlock()

	// 检查产品是否存在
	product, exists := s.products[productID]
	if !exists {
		return fmt.Errorf("产品ID %s 不存在", productID)
	}

	// 更新关键词
	product.Keywords = keywords
	s.products[productID] = product

	// 同步到Redis
	if err := s.syncProductToRedis(productID); err != nil {
		log.Printf("同步产品配置到Redis失败: %v", err)
	}

	// 记录日志
	log.Printf("已更新产品 [%s] 的关键词: %v", productID, keywords)
	return nil
}

// AppendProductKeywords 为指定产品追加关键词
func (s *ProductMonitorService) AppendProductKeywords(productID string, newKeywords []string) error {
	//s.mu.Lock()
	//defer s.mu.Unlock()

	// 检查产品是否存在
	product, exists := s.products[productID]
	if !exists {
		return fmt.Errorf("产品ID %s 不存在", productID)
	}

	// 创建一个map来检查重复的关键词
	existingKeywords := make(map[string]bool)
	for _, kw := range product.Keywords {
		existingKeywords[kw] = true
	}

	// 追加不重复的关键词
	for _, newKw := range newKeywords {
		if !existingKeywords[newKw] {
			product.Keywords = append(product.Keywords, newKw)
			existingKeywords[newKw] = true
		}
	}

	// 更新产品
	s.products[productID] = product

	// 同步到Redis
	if err := s.syncProductToRedis(productID); err != nil {
		log.Printf("同步产品配置到Redis失败: %v", err)
	}

	// 记录日志
	log.Printf("已为产品 [%s] 追加关键词，当前关键词列表: %v", productID, product.Keywords)
	return nil
}

// RemoveProductKeywords 从指定产品中删除关键词
func (s *ProductMonitorService) RemoveProductKeywords(productID string, keywordsToRemove []string) error {
	//s.mu.Lock()
	//defer s.mu.Unlock()

	// 检查产品是否存在
	product, exists := s.products[productID]
	if !exists {
		return fmt.Errorf("产品ID %s 不存在", productID)
	}

	// 创建一个map来存储要删除的关键词
	toRemove := make(map[string]bool)
	for _, kw := range keywordsToRemove {
		toRemove[kw] = true
	}

	// 创建新的关键词列表，排除要删除的关键词
	newKeywords := make([]string, 0)
	for _, kw := range product.Keywords {
		if !toRemove[kw] {
			newKeywords = append(newKeywords, kw)
		}
	}

	// 更新产品的关键词列表
	product.Keywords = newKeywords
	s.products[productID] = product

	// 同步到Redis
	if err := s.syncProductToRedis(productID); err != nil {
		log.Printf("同步产品配置到Redis失败: %v", err)
	}

	// 记录日志
	log.Printf("已从产品 [%s] 删除关键词，当前关键词列表: %v", productID, product.Keywords)
	return nil
}

// StartStatsMonitoring 开始监控广告统计数据
func (s *ProductMonitorService) StartStatsMonitoring() {
	go func() {
		// 首次启动时批量获取所有广告数据
		s.monitorStatsBatch()

		// 开始循环监听模式
		s.startContinuousStatsMonitoring()
	}()
}

// monitorStatsBatch 批量监控广告统计数据（首次启动时使用）
func (s *ProductMonitorService) monitorStatsBatch() {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 获取当前日期
	now := time.Now()
	today := now.Format("2006-01-02")

	log.Printf("首次启动：开始批量获取广告统计数据，当前时间: %s", now.Format("2006-01-02 15:04:05"))

	// 准备统计请求
	statsRequests := make([]*wbhubapi.StatsRequest, 0)
	advertIDMap := make(map[int]string) // 广告ID到产品ID的映射

	// 收集所有需要查询的广告ID
	for productID, product := range s.products {
		for _, advert := range product.Advert {
			statsRequests = append(statsRequests, &wbhubapi.StatsRequest{
				AdvertID: advert.AdvertID,
				Dates:    []string{today, today},
			})
			advertIDMap[advert.AdvertID] = productID
		}
	}

	// 如果没有广告ID，直接返回
	if len(statsRequests) == 0 {
		log.Printf("首次启动：没有找到需要监控的广告")
		return
	}

	log.Printf("首次启动：找到 %d 个广告，开始批量获取统计数据", len(statsRequests))

	// 批量获取统计数据
	stats, err := s.wbhubClient.Promotion.GetFullStats(statsRequests)
	if err != nil {
		log.Printf("首次启动：批量获取广告统计数据失败: %v", err)
		return
	}

	log.Printf("首次启动：成功获取 %d 个广告的统计数据", len(stats))

	// 处理统计数据
	updatedCount := 0
	for _, stat := range stats {
		// 从请求中获取广告ID
		advertID := stat.AdvertId
		productID := advertIDMap[advertID]

		// 更新 s.products 中的广告数据
		if product, ok := s.products[productID]; ok {
			for j, advert := range product.Advert {
				if advert.AdvertID == advertID {
					// 计算收入（从 SumPrice 字段，需要转换为 float64）
					revenue := float64(stat.SumPrice)

					// 更新广告统计数据
					product.Advert[j].Views = stat.Views
					product.Advert[j].Clicks = stat.Clicks
					product.Advert[j].CTR = stat.Ctr
					product.Advert[j].CPC = stat.Cpc
					product.Advert[j].Spending = stat.Sum
					product.Advert[j].Orders = stat.Orders                    // 广告订单量
					product.Advert[j].OrganicOrders = 0                       // 自然流量订单量（暂时设为0）
					product.Advert[j].OrderSum = revenue                      // 更新订单金额
					product.Advert[j].ACOS = calculateACOS(stat.Sum, revenue) // 计算 ACOS
					product.Advert[j].UpdateTime = now
					product.Advert[j].SKU = product.SKU
					s.products[productID] = product
					updatedCount++

					log.Printf("首次启动：更新广告 %d - 花费: %.2f, 收入: %.2f, 广告订单: %d, 自然流量订单: %d, ACOS: %.2f%%",
						advertID, stat.Sum, revenue, stat.Orders, 0, product.Advert[j].ACOS)
					break
				}
			}
		}
	}

	log.Printf("首次启动：成功更新 %d 个广告的统计数据", updatedCount)

	// 构建统计数据响应用于 WebSocket 推送
	response := map[string][]ProductAdvert{}
	for productID, product := range s.products {
		response[productID] = product.Advert
	}

	// 批量推送所有广告数据
	s.sendWebSocketAsync(response)
	log.Printf("首次启动：已推送所有广告统计数据到WebSocket客户端")
}

// startContinuousStatsMonitoring 开始持续的广告统计监控
func (s *ProductMonitorService) startContinuousStatsMonitoring() {
	log.Printf("开始持续监控模式...")

	for {
		select {
		case <-s.stopChan:
			log.Printf("收到停止信号，退出广告统计监控")
			return
		default:
			// 执行一轮完整的广告统计监控
			s.monitorStatsRound()

			// 使用固定的等待时间：1分钟
			waitTime := 1 * time.Minute
			log.Printf("本轮广告统计监控完成，等待 %v 后开始下一轮监控", waitTime)

			// 等待指定时间或收到停止信号
			select {
			case <-s.stopChan:
				log.Printf("收到停止信号，退出广告统计监控")
				return
			case <-time.After(waitTime):
				log.Printf("开始新一轮广告统计监控")
			}
		}
	}
}

// monitorStatsRound 执行一轮完整的广告统计监控
func (s *ProductMonitorService) monitorStatsRound() {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 获取当前日期
	now := time.Now()
	today := now.Format("2006-01-02")

	log.Printf("开始监控广告统计数据，当前时间: %s", now.Format("2006-01-02 15:04:05"))

	// 统计产品和广告数量
	totalProducts := len(s.products)
	totalAdverts := 0
	for _, product := range s.products {
		totalAdverts += len(product.Advert)
	}

	if totalProducts == 0 {
		log.Printf("没有找到需要监控的产品")
		return
	}

	if totalAdverts == 0 {
		log.Printf("没有找到需要监控的广告")
		return
	}

	log.Printf("找到 %d 个产品，共 %d 个广告需要监控，由于API限制（每分钟1次），预计需要 %d 分钟完成", totalProducts, totalAdverts, totalProducts)

	// 收集所有需要查询总订单量的产品ID
	productIDsForBatch := make([]string, 0, len(s.products))
	for productID := range s.products {
		productIDsForBatch = append(productIDsForBatch, productID)
	}

	// 批量获取所有产品的真实总订单量
	totalOrdersMap := s.getTotalOrdersFromHistoryBatch(productIDsForBatch, today)

	// 逐个处理每个产品的所有广告
	processedProducts := 0
	for productID, product := range s.products {
		// 检查是否收到停止信号
		select {
		case <-s.stopChan:
			log.Printf("收到停止信号，中断广告统计监控，已处理 %d/%d 个产品", processedProducts, totalProducts)
			return
		default:
		}

		processedProducts++
		log.Printf("正在处理产品 %d/%d - 产品ID: %s, 包含 %d 个广告", processedProducts, totalProducts, productID, len(product.Advert))

		// 收集当前产品的所有广告ID
		advertIDs := make([]int, 0, len(product.Advert))
		for _, advert := range product.Advert {
			advertIDs = append(advertIDs, advert.AdvertID)
		}

		if len(advertIDs) == 0 {
			log.Printf("产品 %s 没有广告，跳过", productID)
			continue
		}

		// 创建该产品所有广告的统计请求
		statsRequests := make([]*wbhubapi.StatsRequest, 0, len(advertIDs))
		for _, advertID := range advertIDs {
			statsRequests = append(statsRequests, &wbhubapi.StatsRequest{
				AdvertID: advertID,
				Dates:    []string{today, today},
			})
		}

		// 批量调用API获取该产品所有广告的统计数据
		log.Printf("批量获取产品 %s 的 %d 个广告统计数据", productID, len(statsRequests))
		stats, err := s.wbhubClient.Promotion.GetFullStats(statsRequests)
		if err != nil {
			log.Printf("获取产品 %s 广告统计数据失败: %v", productID, err)

			// 如果不是最后一个产品，等待61秒后继续处理下一个产品
			if processedProducts < totalProducts {
				log.Printf("等待61秒后处理下一个产品...")
				select {
				case <-s.stopChan:
					log.Printf("收到停止信号，中断广告统计监控")
					return
				case <-time.After(61 * time.Second):
					// 等待完成，继续处理下一个产品
				}
			}
			continue
		}

		// 处理返回的统计数据
		if len(stats) == 0 {
			log.Printf("产品 %s 没有返回广告统计数据", productID)
		} else {
			log.Printf("产品 %s 返回了 %d 个广告的统计数据", productID, len(stats))

			// 创建广告ID到统计数据的映射
			statsMap := make(map[int]*wbhubapi.StatsResponse)
			for i := range stats {
				statsMap[stats[i].AdvertId] = &stats[i]
			}

			// 获取该产品的真实总订单量，用于计算自然流量订单量
			productTotalOrders := 0
			if realTotalOrders, exists := totalOrdersMap[productID]; exists {
				productTotalOrders = realTotalOrders
				log.Printf("产品 %s 获取到真实总订单量: %d", productID, productTotalOrders)
			} else {
				log.Printf("产品 %s 无法获取真实总订单量，自然流量订单量将设为0", productID)
			}

			// 更新该产品的所有广告数据（移到外层作用域）
			updatedProduct := s.products[productID]
			updatedAdvertCount := 0
			totalAdOrders := 0 // 统计所有广告订单量总和

			if len(stats) > 0 {
				for i, advert := range updatedProduct.Advert {
					if stat, exists := statsMap[advert.AdvertID]; exists {
						// 计算收入（从 SumPrice 字段，需要转换为 float64）
						revenue := float64(stat.SumPrice)

						// 更新广告数据
						updatedProduct.Advert[i].Views = stat.Views
						updatedProduct.Advert[i].Clicks = stat.Clicks
						updatedProduct.Advert[i].CTR = stat.Ctr
						updatedProduct.Advert[i].CPC = stat.Cpc
						updatedProduct.Advert[i].Spending = stat.Sum
						updatedProduct.Advert[i].Orders = stat.Orders                    // 广告订单量
						updatedProduct.Advert[i].OrderSum = revenue                      // 更新订单金额
						updatedProduct.Advert[i].ACOS = calculateACOS(stat.Sum, revenue) // 计算 ACOS
						updatedProduct.Advert[i].UpdateTime = now
						updatedProduct.Advert[i].SKU = product.SKU

						totalAdOrders += stat.Orders
						updatedAdvertCount++

						log.Printf("更新广告 %d - 展示: %d, 点击: %d, 花费: %.2f, 收入: %.2f, 广告订单: %d, ACOS: %.2f%%",
							advert.AdvertID, stat.Views, stat.Clicks, stat.Sum, revenue, stat.Orders, updatedProduct.Advert[i].ACOS)
					} else {
						log.Printf("警告: 广告ID %d 没有返回统计数据", advert.AdvertID)
					}
				}
			}

			// 计算自然流量订单量 = 总订单量 - 广告订单量
			organicOrders := 0
			if productTotalOrders > totalAdOrders {
				organicOrders = productTotalOrders - totalAdOrders
			}

			// 为所有广告设置自然流量订单量
			for i := range updatedProduct.Advert {
				updatedProduct.Advert[i].OrganicOrders = organicOrders
			}

			// 更新到products映射中
			s.products[productID] = updatedProduct

			log.Printf("产品 %s 批量更新完成 - 成功更新 %d/%d 个广告，广告订单: %d, 总订单: %d, 自然流量订单: %d",
				productID, updatedAdvertCount, len(product.Advert), totalAdOrders, productTotalOrders, organicOrders)
		}

		// 为当前产品推送WebSocket更新
		currentProduct := s.products[productID]
		singleProductResponse := map[string][]ProductAdvert{
			productID: currentProduct.Advert,
		}
		s.sendWebSocketAsync(singleProductResponse)

		// 如果不是最后一个产品，等待61秒后处理下一个产品（遵守API限制：每分钟1次）
		if processedProducts < totalProducts {
			log.Printf("等待61秒后处理下一个产品...")
			select {
			case <-s.stopChan:
				log.Printf("收到停止信号，中断广告统计监控")
				return
			case <-time.After(61 * time.Second):
				// 等待完成，继续处理下一个产品
			}
		}
	}

	log.Printf("广告统计数据监控完成，共处理了 %d 个产品，%d 个广告", processedProducts, totalAdverts)
}

// StartKeywordExclusionService 启动关键词排除服务
func (s *ProductMonitorService) StartKeywordExclusionService() {
	if s.myAgentClient == nil {
		log.Printf("MyAgent客户端未初始化，无法启动关键词排除服务")
		return
	}

	// 每天莫斯科时间4点执行关键词排除任务
	_, err := s.keywordScheduler.AddFunc("0 4 * * *", func() {
		log.Printf("定时执行关键词排除任务...")
		s.processKeywordExclusionWithRetry()
	})

	if err != nil {
		log.Printf("添加关键词排除定时任务失败: %v", err)
		return
	}

	// 添加每小时的重试机制（如果当天任务失败）
	_, err = s.keywordScheduler.AddFunc("0 */2 * * *", func() {
		s.checkAndRetryKeywordExclusion()
	})

	if err != nil {
		log.Printf("添加关键词排除重试任务失败: %v", err)
	}

	s.keywordScheduler.Start()
	log.Printf("关键词排除服务已启动，将在每天莫斯科时间4点执行，每2小时检查重试")
}

// StopKeywordExclusionService 停止关键词排除服务
func (s *ProductMonitorService) StopKeywordExclusionService() {
	if s.keywordScheduler != nil {
		s.keywordScheduler.Stop()
		log.Printf("关键词排除服务已停止")
	}
}

// StartDailyReportService 启动每日广告花费报告服务
func (s *ProductMonitorService) StartDailyReportService() {
	if s.dailyReportScheduler == nil {
		log.Printf("每日报告调度器未初始化，无法启动服务")
		return
	}

	// 每天中国时间早上8点执行广告花费统计任务
	_, err := s.dailyReportScheduler.AddFunc("0 8 * * *", func() {
		log.Printf("定时执行每日广告花费统计任务...")
		s.generateDailyAdvertSpendReport()
	})

	if err != nil {
		log.Printf("添加每日报告定时任务失败: %v", err)
		return
	}

	s.dailyReportScheduler.Start()
	log.Printf("每日广告花费报告服务已启动，将在每天中国时间早上8点执行")
}

// StopDailyReportService 停止每日广告花费报告服务
func (s *ProductMonitorService) StopDailyReportService() {
	if s.dailyReportScheduler != nil {
		s.dailyReportScheduler.Stop()
		log.Printf("每日广告花费报告服务已停止")
	}
}

// TriggerDailyReport 手动触发每日广告花费报告
func (s *ProductMonitorService) TriggerDailyReport() {
	log.Printf("手动触发每日广告花费报告...")
	s.generateDailyAdvertSpendReport()
}

// TriggerKeywordExclusion 手动触发关键词排除任务
func (s *ProductMonitorService) TriggerKeywordExclusion() {
	if s.myAgentClient == nil {
		log.Printf("MyAgent客户端未初始化，无法执行关键词排除任务")
		return
	}

	log.Printf("手动触发关键词排除任务...")
	go s.processKeywordExclusionWithRetry()
}

// processKeywordExclusionWithRetry 带重试机制的关键词排除处理
func (s *ProductMonitorService) processKeywordExclusionWithRetry() {
	// 记录今天的执行状态
	today := time.Now().Format("2006-01-02")
	statusKey := fmt.Sprintf("keyword_exclusion_status:%s", today)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 检查今天是否已经成功执行过
	status, err := s.redisClient.Get(ctx, statusKey)
	if err == nil && status == "success" {
		log.Printf("今天的关键词排除任务已经成功执行过，跳过")
		return
	}

	// 执行关键词排除任务
	s.processKeywordExclusion()

	// 标记今天已执行成功（24小时过期）
	ctx2, cancel2 := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel2()
	s.redisClient.Set(ctx2, statusKey, "success", 24*time.Hour)
}

// checkAndRetryKeywordExclusion 检查并重试关键词排除任务
func (s *ProductMonitorService) checkAndRetryKeywordExclusion() {
	today := time.Now().Format("2006-01-02")
	statusKey := fmt.Sprintf("keyword_exclusion_status:%s", today)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 检查今天是否已经成功执行过
	status, err := s.redisClient.Get(ctx, statusKey)
	if err == nil && status == "success" {
		// 今天已经成功执行过，不需要重试
		return
	}

	// 检查当前时间是否在4点之后（确保不在定时任务执行前重试）
	moscowLocation, _ := time.LoadLocation("Europe/Moscow")
	now := time.Now().In(moscowLocation)
	if now.Hour() < 4 {
		// 还没到定时执行时间，不执行重试
		return
	}

	log.Printf("检测到今天的关键词排除任务未成功执行，开始重试...")
	s.processKeywordExclusionWithRetry()
}

// processKeywordExclusion 处理关键词排除逻辑
func (s *ProductMonitorService) processKeywordExclusion() {
	moscowLocation, _ := time.LoadLocation("Europe/Moscow")
	now := time.Now().In(moscowLocation)

	log.Printf("开始关键词排除任务 - 莫斯科时间: %s", now.Format("2006-01-02 15:04:05"))

	s.mu.RLock()
	defer s.mu.RUnlock()

	totalAdverts := 0
	processedAdverts := 0
	successCount := 0
	failCount := 0
	var errors []string

	// 统计总广告数量
	for _, product := range s.products {
		for _, advert := range product.Advert {
			// 只处理搜索广告（类型9）
			if advert.AdvertType == 9 {
				totalAdverts++
			}
		}
	}

	if totalAdverts == 0 {
		log.Printf("没有找到搜索广告，跳过关键词排除任务")
		return
	}

	log.Printf("找到 %d 个搜索广告需要处理", totalAdverts)

	// 遍历所有产品的搜索广告
	for productID, product := range s.products {
		for _, advert := range product.Advert {
			// 只处理搜索广告（类型9）
			if advert.AdvertType != 9 {
				continue
			}
			processedAdverts++
			log.Printf("处理广告 %d/%d - 产品ID: %s, 广告ID: %d",
				processedAdverts, totalAdverts, productID, advert.AdvertID)

			// 处理单个广告的关键词排除
			if err := s.processAdvertKeywordExclusion(advert.AdvertID); err != nil {
				failCount++
				errorMsg := fmt.Sprintf("广告ID %d: %v", advert.AdvertID, err)
				errors = append(errors, errorMsg)
				log.Printf("处理广告 %d 失败: %v", advert.AdvertID, err)
			} else {
				successCount++
				log.Printf("处理广告 %d 成功", advert.AdvertID)
			}

			// 每处理一个广告后等待1秒，避免API限制
			time.Sleep(1 * time.Second)
		}
	}

	// 记录最终统计结果
	log.Printf("关键词排除任务完成 - 总计: %d个广告, 成功: %d个, 失败: %d个", totalAdverts, successCount, failCount)
}

// processAdvertKeywordExclusion 处理单个广告的关键词排除
func (s *ProductMonitorService) processAdvertKeywordExclusion(advertID int) error {
	// 1. 获取广告关键词分析数据
	analysis, err := s.myAgentClient.GetCampaignKeywordsAnalysis(advertID)
	if err != nil {
		s.sendKeywordExclusionNotification(advertID, 0, 0, 0, false, fmt.Sprintf("获取关键词分析失败: %v", err), "")
		return fmt.Errorf("获取关键词分析失败: %w", err)
	}

	if analysis.TotalKeywords == 0 {
		log.Printf("广告 %d 没有关键词数据", advertID)
		s.sendKeywordExclusionNotification(advertID, 0, 0, 0, true, "没有关键词数据", "无关键词数据")
		return nil
	}

	// 2. 从Redis获取已排除的关键词
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	redisKey := fmt.Sprintf("%s:%d", REDIS_EXCLUDED_KEYWORDS_KEY, advertID)
	existingKeywordsStr, err := s.redisClient.Get(ctx, redisKey)
	var existingKeywords []string
	if err != nil {
		log.Printf("获取Redis中已排除关键词失败: %v", err)
		existingKeywords = []string{} // 如果获取失败，当作没有已排除关键词
	} else if existingKeywordsStr != "" {
		// 解析JSON格式的关键词列表
		if err := json.Unmarshal([]byte(existingKeywordsStr), &existingKeywords); err != nil {
			log.Printf("解析Redis中已排除关键词失败: %v", err)
			existingKeywords = []string{}
		}
	}

	// 转换为map便于查找
	existingKeywordMap := make(map[string]bool)
	for _, keyword := range existingKeywords {
		existingKeywordMap[strings.TrimSpace(keyword)] = true
	}

	// 3. 筛选出新的需要排除的关键词（去重处理）
	newExcludedKeywordsMap := make(map[string]bool) // 用于去重
	var newExcludedKeywords []string

	for _, keyword := range analysis.Keywords {
		trimmedKeyword := strings.TrimSpace(keyword.Keyword)
		if trimmedKeyword == "" {
			continue
		}

		// 只排除标记为应该排除的关键词
		if keyword.ShouldExclude {
			// 如果关键词不在已排除列表中，且不在当前新增列表中（去重）
			if !existingKeywordMap[trimmedKeyword] && !newExcludedKeywordsMap[trimmedKeyword] {
				newExcludedKeywords = append(newExcludedKeywords, trimmedKeyword)
				newExcludedKeywordsMap[trimmedKeyword] = true
			}
		}
	}

	if len(newExcludedKeywords) == 0 {
		log.Printf("广告 %d 没有新的关键词需要排除", advertID)
		// 发送无新增排除词的钉钉通知
		summaryText := s.formatSummary(analysis.Summary)
		s.sendKeywordExclusionNotification(advertID, analysis.TotalKeywords, 0, len(existingKeywords), true, "", summaryText)
		return nil
	}

	// 4. 检查排除词总数是否超过1000个限制
	totalExcludedCount := len(existingKeywords) + len(newExcludedKeywords)
	if totalExcludedCount > 1000 {
		// 发送超过限制的钉钉通知
		summaryText := s.formatSummary(analysis.Summary)
		s.sendKeywordExclusionNotification(advertID, analysis.TotalKeywords, len(newExcludedKeywords), totalExcludedCount, false, "排除词已经超过1000个无法执行", summaryText)
		return fmt.Errorf("排除词总数将超过1000个限制 (当前: %d, 新增: %d, 总计: %d)",
			len(existingKeywords), len(newExcludedKeywords), totalExcludedCount)
	}

	log.Printf("广告 %d 发现 %d 个新的排除关键词，当前已有 %d 个",
		advertID, len(newExcludedKeywords), len(existingKeywords))

	// 合并已有关键词和新关键词
	allExcludedKeywords := append(existingKeywords, newExcludedKeywords...)

	// 5. 调用API设置排除关键词
	if err := s.wbhubClient.Promotion.SetExcludedKeywords(advertID, allExcludedKeywords); err != nil {
		// 发送失败的钉钉通知
		summaryText := s.formatSummary(analysis.Summary)
		s.sendKeywordExclusionNotification(advertID, analysis.TotalKeywords, len(newExcludedKeywords), totalExcludedCount, false, fmt.Sprintf("设置排除关键词失败: %v", err), summaryText)
		return fmt.Errorf("设置排除关键词失败: %w", err)
	}

	// 6. 更新Redis中的排除关键词记录
	ctx2, cancel2 := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel2()

	// 序列化为JSON格式存储
	keywordsJSON, err := json.Marshal(allExcludedKeywords)
	if err != nil {
		log.Printf("序列化排除关键词失败: %v", err)
	} else {
		// 存储到Redis
		if err := s.redisClient.Set(ctx2, redisKey, string(keywordsJSON), 0); err != nil {
			log.Printf("更新Redis排除关键词失败: %v", err)
		}
	}

	log.Printf("广告 %d 成功设置 %d 个排除关键词", advertID, len(newExcludedKeywords))

	// 发送成功的钉钉通知
	summaryText := s.formatSummary(analysis.Summary)
	s.sendKeywordExclusionNotification(advertID, analysis.TotalKeywords, len(newExcludedKeywords), totalExcludedCount, true, "", summaryText)

	return nil
}

// formatSummary 格式化分析摘要为字符串
func (s *ProductMonitorService) formatSummary(summary map[string]interface{}) string {
	if summary == nil || len(summary) == 0 {
		return "无分析摘要"
	}

	var result strings.Builder

	// 基础统计信息
	if totalKeywords, ok := summary["total_keywords"]; ok {
		result.WriteString(fmt.Sprintf("总关键词数: %v\n", totalKeywords))
	}
	if orderGenerating, ok := summary["order_generating_keywords"]; ok {
		result.WriteString(fmt.Sprintf("产生订单关键词: %v个\n", orderGenerating))
	}
	if toExclude, ok := summary["keywords_to_exclude"]; ok {
		result.WriteString(fmt.Sprintf("建议排除关键词: %v个\n", toExclude))
	}
	if toKeep, ok := summary["keywords_to_keep"]; ok {
		result.WriteString(fmt.Sprintf("建议保留关键词: %v个\n", toKeep))
	}

	// 百分比信息
	if percentages, ok := summary["percentages"].(map[string]interface{}); ok {
		result.WriteString("\n📈 比例分析:\n")
		if orderPct, exists := percentages["order_generating"]; exists {
			result.WriteString(fmt.Sprintf("产生订单比例: %.1f%%\n", orderPct))
		}
		if excludePct, exists := percentages["to_exclude"]; exists {
			result.WriteString(fmt.Sprintf("建议排除比例: %.1f%%\n", excludePct))
		}
		if keepPct, exists := percentages["to_keep"]; exists {
			result.WriteString(fmt.Sprintf("建议保留比例: %.1f%%\n", keepPct))
		}
	}

	// 排除分类详情
	if excludeCategories, ok := summary["exclude_categories"].(map[string]interface{}); ok {
		result.WriteString("\n❌ 排除分类:\n")
		if irrelevant, exists := excludeCategories["irrelevant"]; exists {
			result.WriteString(fmt.Sprintf("不相关词: %v个\n", irrelevant))
		}
		if zeroClick, exists := excludeCategories["zero_click"]; exists {
			result.WriteString(fmt.Sprintf("零点击词: %v个\n", zeroClick))
		}
		if noData, exists := excludeCategories["no_data"]; exists {
			result.WriteString(fmt.Sprintf("无数据词: %v个\n", noData))
		}
	}

	// 观察分类详情
	if observeCategories, ok := summary["observe_categories"].(map[string]interface{}); ok {
		result.WriteString("\n👀 观察分类:\n")
		if lowCtr, exists := observeCategories["low_ctr"]; exists {
			result.WriteString(fmt.Sprintf("低点击率词: %v个\n", lowCtr))
		}
		if highCpc, exists := observeCategories["high_cpc"]; exists {
			result.WriteString(fmt.Sprintf("高成本词: %v个\n", highCpc))
		}
		if lowSimilarity, exists := observeCategories["low_similarity"]; exists {
			result.WriteString(fmt.Sprintf("低相似度词: %v个\n", lowSimilarity))
		}
	}

	// 优化分类详情
	if optimizeCategories, ok := summary["optimize_categories"].(map[string]interface{}); ok {
		result.WriteString("\n🔧 优化分类:\n")
		if lowPerformance, exists := optimizeCategories["low_performance"]; exists {
			result.WriteString(fmt.Sprintf("低性能词: %v个\n", lowPerformance))
		}
	}

	summaryText := result.String()
	if summaryText == "" {
		return "无分析摘要"
	}

	return strings.TrimSuffix(summaryText, "\n")
}

// sendKeywordExclusionNotification 发送关键词排除通知
func (s *ProductMonitorService) sendKeywordExclusionNotification(advertID, totalKeywords, newExcludedCount, totalExcludedCount int, success bool, errorMsg string, summary string) {
	if s.dingTalkRobot == nil {
		log.Printf("钉钉机器人未初始化，跳过通知发送")
		return
	}

	var content string
	var needAtAll bool

	if success {
		if newExcludedCount == 0 {
			// 没有新的排除词
			content = fmt.Sprintf(`✅ 关键词排除完成
广告ID：%d
此次新排除词数量：%d个
总排除关键词数量：%d个
排除执行成功（无新增排除词）

📊 分析摘要：
%s`, advertID, newExcludedCount, totalExcludedCount, summary)
		} else {
			// 有新的排除词
			content = fmt.Sprintf(`✅ 关键词排除完成
广告ID：%d
此次新排除词数量：%d个
总排除关键词数量：%d个
排除执行成功

📊 分析摘要：
%s`, advertID, newExcludedCount, totalExcludedCount, summary)
		}
		needAtAll = false
	} else {
		if totalExcludedCount > 1000 {
			// 超过1000个限制
			content = fmt.Sprintf(`❌ 关键词排除失败
广告ID：%d
此次新排除词数量：%d个
总排除关键词数量：%d个
排除词已经超过1000个无法执行

📊 分析摘要：
%s`, advertID, newExcludedCount, totalExcludedCount, summary)
		} else {
			// 其他错误
			content = fmt.Sprintf(`❌ 关键词排除失败
广告ID：%d
此次新排除词数量：%d个
总排除关键词数量：%d个
排除执行失败：%s

📊 分析摘要：
%s`, advertID, newExcludedCount, totalExcludedCount, errorMsg, summary)
		}
		needAtAll = true
	}

	// 使用消息构造器创建文本消息
	builder := dingding.MessageBuilder{}
	message := builder.NewTextMessage(content)

	// 失败时@所有人
	if needAtAll {
		message.At = &dingding.AtConfig{
			IsAtAll: true,
		}
	}

	// 异步发送到钉钉
	go func() {
		if err := s.dingTalkRobot.Send(message); err != nil {
			log.Printf("发送关键词排除通知失败: %v", err)
		} else {
			log.Printf("关键词排除通知发送成功 - 广告ID: %d", advertID)
		}
	}()
}

// generateDailyAdvertSpendReport 生成每日广告花费报告
func (s *ProductMonitorService) generateDailyAdvertSpendReport() {
	log.Printf("开始生成每日广告花费报告...")
	startTime := time.Now()

	// 加载莫斯科时区
	moscowLocation, err := time.LoadLocation("Europe/Moscow")
	if err != nil {
		log.Printf("加载莫斯科时区失败: %v", err)
		moscowLocation = time.UTC
	}

	// 计算莫斯科时间的昨天日期范围
	now := time.Now().In(moscowLocation)
	yesterday := now.AddDate(0, 0, -1)
	dateFrom := yesterday.Format("2006-01-02")
	dateTo := yesterday.Format("2006-01-02")

	log.Printf("统计莫斯科时间 %s 的广告花费数据", dateFrom)

	// 收集所有广告ID
	s.mu.RLock()
	var allAdvertIDs []int
	//productAdvertMap := make(map[int]string) // 广告ID -> 产品ID的映射

	for _, product := range s.products {
		for _, advert := range product.Advert {
			allAdvertIDs = append(allAdvertIDs, advert.AdvertID)
			//productAdvertMap[advert.AdvertID] = productID
		}
	}
	s.mu.RUnlock()

	if len(allAdvertIDs) == 0 {
		log.Printf("没有找到需要统计的广告")
		s.sendDailyReportNotification(dateFrom, 0, 0, 0, true, "")
		return
	}

	log.Printf("找到 %d 个广告需要统计花费", len(allAdvertIDs))

	// 创建统计请求
	var statsRequests []*wbhubapi.StatsRequest
	for _, advertID := range allAdvertIDs {
		statsRequests = append(statsRequests, &wbhubapi.StatsRequest{
			AdvertID: advertID,
			Dates:    []string{dateFrom, dateTo},
		})
	}

	// 批量获取广告统计数据
	stats, err := s.wbhubClient.Promotion.GetFullStats(statsRequests)
	if err != nil {
		errorMsg := fmt.Sprintf("获取广告统计数据失败: %v", err)
		log.Printf(errorMsg)
		s.sendDailyReportNotification(dateFrom, 0, 0, len(allAdvertIDs), false, errorMsg)
		return
	}

	// 计算总花费和统计信息
	var totalSpend float64
	var activeAdvertCount int
	//productSpendMap := make(map[string]float64) // 产品ID -> 花费的映射

	for _, stat := range stats {
		if stat.Sum > 0 {
			activeAdvertCount++
			spend := float64(stat.Sum)
			totalSpend += spend

			//// 记录每个产品的花费
			//if productID, exists := productAdvertMap[stat.AdvertId]; exists {
			//	productSpendMap[productID] += spend
			//}
		}
	}

	log.Printf("统计完成 - 总花费: %.2f 卢布, 活跃广告: %d/%d, 耗时: %v",
		totalSpend, activeAdvertCount, len(allAdvertIDs), time.Since(startTime))

	// 发送钉钉通知
	s.sendDailyReportNotification(dateFrom, totalSpend, activeAdvertCount, len(allAdvertIDs), true, "")
}

// sendDailyReportNotification 发送每日报告通知
func (s *ProductMonitorService) sendDailyReportNotification(date string, totalSpend float64, activeAdverts, totalAdverts int, success bool, errorMsg string) {
	if s.dingTalkRobot == nil {
		log.Printf("钉钉机器人未初始化，跳过通知发送")
		return
	}

	var content string
	var title string

	if success {
		title = "📊 每日广告花费报告"
		content = fmt.Sprintf(`**📅 日期**: %s (莫斯科时间)

**💰 总花费**: %.2f 卢布

**📈 广告统计**:
- 活跃广告: %d 个
- 总广告数: %d 个
- 活跃率: %.1f%%

**⏰ 报告时间**: %s (中国时间)

---
*此报告由系统自动生成*`,
			date,
			totalSpend,
			activeAdverts,
			totalAdverts,
			float64(activeAdverts)/float64(totalAdverts)*100,
			time.Now().In(time.FixedZone("CST", 8*3600)).Format("2006-01-02 15:04:05"))
	} else {
		title = "❌ 每日广告花费报告生成失败"
		content = fmt.Sprintf(`**📅 日期**: %s (莫斯科时间)

**❌ 错误信息**: %s

**📊 统计范围**: %d 个广告

**⏰ 报告时间**: %s (中国时间)

---
*请检查系统状态并手动重试*`,
			date,
			errorMsg,
			totalAdverts,
			time.Now().In(time.FixedZone("CST", 8*3600)).Format("2006-01-02 15:04:05"))
	}

	// 创建Markdown消息
	builder := dingding.MessageBuilder{}
	message := builder.NewMarkdownMessage(title, content)

	// 设置@配置（不@所有人，避免打扰）
	message.At = &dingding.AtConfig{
		IsAtAll: false,
	}

	// 异步发送到钉钉
	go func() {
		if err := s.dingTalkRobot.Send(message); err != nil {
			log.Printf("发送每日报告通知失败: %v", err)
		} else {
			log.Printf("每日报告通知发送成功 - 日期: %s, 总花费: %.2f 卢布", date, totalSpend)
		}
	}()
}
