package service

import (
	"context"
	"fmt"
	wbhubapi "lens/internal/api/wb"
	"log"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
)

// AdvertManager 广告管理器
type AdvertManager struct {
	client    *wbhubapi.Client
	scheduler *cron.Cron
	location  *time.Location

	// 记录被暂停的广告ID
	pausedAdverts sync.Map
}

// NewAdvertManager 创建新的广告管理器
func NewAdvertManager(apiKey string) (*AdvertManager, error) {
	// 设置莫斯科时区
	loc, err := time.LoadLocation("Europe/Moscow")
	if err != nil {
		return nil, fmt.Errorf("load moscow timezone failed: %w", err)
	}

	// 创建带时区的定时器
	scheduler := cron.New(cron.WithLocation(loc))

	return &AdvertManager{
		client:    wbhubapi.NewClient(apiKey),
		scheduler: scheduler,
		location:  loc,
	}, nil
}

// Start 启动广告管理器
func (m *AdvertManager) Start(ctx context.Context) error {
	// 每天1:00暂停广告
	if _, err := m.scheduler.AddFunc("0 1 * * *", func() {
		if err := m.PauseAllRunningAdverts(); err != nil {
			log.Printf("暂停广告失败: %v", err)
		}
	}); err != nil {
		return fmt.Errorf("添加暂停任务失败: %w", err)
	}

	// 每天8:00启动广告
	if _, err := m.scheduler.AddFunc("0 8 * * *", func() {
		if err := m.StartAllPausedAdverts(); err != nil {
			log.Printf("启动广告失败: %v", err)
		}
	}); err != nil {
		return fmt.Errorf("添加启动任务失败: %w", err)
	}

	// 启动定时器
	m.scheduler.Start()

	// 监听上下文取消
	go func() {
		<-ctx.Done()
		m.scheduler.Stop()
	}()

	return nil
}

// PauseAllRunningAdverts 暂停所有正在运行的广告
func (m *AdvertManager) PauseAllRunningAdverts() error {
	// 获取所有正在运行的广告活动
	status := wbhubapi.CampaignStatusRunning
	params := &wbhubapi.CampaignQueryParams{
		Status: &status,
	}
	campaigns, err := m.client.Promotion.GetCampaignsByParams(params)
	if err != nil {
		return fmt.Errorf("获取广告活动列表失败: %w", err)
	}

	// 遍历所有广告活动
	for _, campaign := range campaigns {
		// 暂停广告
		if err := m.client.Promotion.PauseCampaign(campaign.AdvertID); err != nil {
			log.Printf("暂停广告[%d]失败: %v", campaign.AdvertID, err)
			continue
		}
		// 记录被暂停的广告ID
		m.pausedAdverts.Store(campaign.AdvertID, time.Now())
		log.Printf("已暂停广告: %d", campaign.AdvertID)
	}

	return nil
}

// StartAllPausedAdverts 启动所有暂停的广告
func (m *AdvertManager) StartAllPausedAdverts() error {
	var successCount, failCount int
	log.Println("开始启动暂停的广告...")

	// 遍历所有被记录的暂停广告
	m.pausedAdverts.Range(func(key, value interface{}) bool {
		advertID, ok := key.(int)
		if !ok {
			log.Printf("错误: 无效的广告ID类型")
			failCount++
			return true
		}

		// 启动广告
		if err := m.client.Promotion.StartCampaign(advertID); err != nil {
			log.Printf("错误: 启动广告[%d]失败: %v", advertID, err)
			failCount++
			return true
		}

		// 从记录中移除已启动的广告ID
		m.pausedAdverts.Delete(advertID)
		log.Printf("成功: 已启动广告[%d]", advertID)
		successCount++
		return true
	})

	// 输出总结信息
	if successCount == 0 && failCount == 0 {
		log.Println("没有需要启动的广告")
	} else {
		log.Printf("广告启动完成: 成功 %d 个, 失败 %d 个", successCount, failCount)
	}

	return nil
}

// GetPausedAdverts 获取当前被暂停的广告ID列表
func (m *AdvertManager) GetPausedAdverts() []int {
	var adverts []int
	m.pausedAdverts.Range(func(key, value interface{}) bool {
		if id, ok := key.(int); ok {
			adverts = append(adverts, id)
		}
		return true
	})
	return adverts
}
