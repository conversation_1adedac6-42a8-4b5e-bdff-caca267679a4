package service

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	wb_api "lens/internal/api/wb_seller"
	"log"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
)

type CrawlerKeywordService struct {
	api         wb_api.Api
	restyClient *resty.Client
}

func NewCrawlerKeywordService(api wb_api.Api) *CrawlerKeywordService {
	// 初始化 resty 客户端
	restyClient := resty.New().
		SetTimeout(30*time.Second).
		SetHeader("Content-Type", "application/json").
		SetRetryCount(5).
		SetRetryWaitTime(3000 * time.Millisecond).
		SetRetryMaxWaitTime(15 * time.Second).
		AddRetryCondition(
			// 当发生网络错误或状态码不是2xx时重试
			func(response *resty.Response, err error) bool {
				return err != nil || response.StatusCode() < 200 || response.StatusCode() >= 300
			},
		)

	return &CrawlerKeywordService{
		api:         api,
		restyClient: restyClient,
	}
}

func (s *CrawlerKeywordService) CrawlKeywords(ctx context.Context) error {
	timestamp := time.Now()

	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
		keywords, err := s.api.GetAllWeeklyTrendingSearches("")
		if err != nil {
			log.Printf("获取关键词失败: %v\n", err)
			return err
		}

		// 处理获取到的关键词
		for _, keyword := range keywords {
			// 过滤掉无效的关键词
			if strings.TrimSpace(keyword.Text) == "" {
				continue
			}

			keyword.PlatformId = "wb"
			keyword.InProcessAt = timestamp

			// 序列化关键词数据
			recordJSON, err := json.Marshal(keyword)
			if err != nil {
				log.Printf("序列化关键词数据失败: %v", err)
				continue
			}
			// TODO: 这里可以添加数据持久化逻辑
			s.sendMessageAsync(recordJSON)
			// 例如：保存到数据库、写入文件等
			log.Printf("关键词: %s, 请求量: %d, 处理时间: %v\n",
				keyword.Text,
				keyword.RequestCount,
				keyword.InProcessAt,
			)
		}
	}

	return nil
}

// sendMessageAsync 异步发送消息到API
func (s *CrawlerKeywordService) sendMessageAsync(recordJSON []byte) {
	go func() {
		// 使用 resty 客户端发送消息
		timestamp := time.Now().UnixMilli()
		data := fmt.Sprintf("%d@Lens", timestamp)
		h := md5.New()
		h.Write([]byte(data))
		sign := hex.EncodeToString(h.Sum(nil))

		// 准备请求数据
		reqBody := map[string]string{
			"topic": "spider_trending_keyword",
			"msg":   string(recordJSON),
		}

		// 发送请求
		resp, err := s.restyClient.R().
			SetHeader("timestamp", fmt.Sprintf("%d", timestamp)).
			SetHeader("sign", sign).
			SetBody(reqBody).
			Post("https://www.cfirs.com/api/system-management/mq/addMsg")

		if err != nil {
			log.Printf("发送消息失败（已重试）: %v", err)
			return
		}

		if resp.StatusCode() != 200 {
			log.Printf("发送消息失败（已重试），状态码: %d, 响应: %s", resp.StatusCode(), resp.String())
			return
		}
	}()
}
