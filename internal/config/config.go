package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Config 通用配置结构
type Config struct {
	// Redis 配置
	RedisAddr     string
	RedisPwd      string
	RedisDatabase int

	// RocketMQ 配置
	RocketMQNameSrv string
	RocketMQGrp     string

	// API Keys
	WBApiKey    string
	WBHubApiKey string

	// 服务配置
	MonitorInterval time.Duration
	CrawlerInterval time.Duration

	// 钉钉配置
	DingTalkAccessToken string
	DingTalkSecret      string

	// MyAgent配置
	MyAgentBaseURL string

	// 日志配置
	LogLevel   string
	LogFormat  string
	ServerPort int
}

// RedisConfig Redis配置接口适配器
func (c *Config) RedisAddress() string {
	return c.RedisAddr
}

func (c *Config) RedisPassword() string {
	return c.RedisPwd
}

func (c *Config) RedisDB() int {
	return c.RedisDatabase
}

// 保持向后兼容的Get方法
func (c *Config) GetRedisAddress() string {
	return c.RedisAddr
}

func (c *Config) GetRedisPassword() string {
	return c.RedisPwd
}

func (c *Config) GetRedisDB() int {
	return c.RedisDatabase
}

// RocketMQConfig RocketMQ配置接口适配器
func (c *Config) RocketMQNameserver() string {
	return c.RocketMQNameSrv
}

func (c *Config) RocketMQGroup() string {
	return c.RocketMQGrp
}

// 保持向后兼容的Get方法
func (c *Config) GetRocketMQNameserver() string {
	return c.RocketMQNameSrv
}

func (c *Config) GetRocketMQGroup() string {
	return c.RocketMQGrp
}

// GetDingTalkAccessToken 获取钉钉访问令牌
func (c *Config) GetDingTalkAccessToken() string {
	return c.DingTalkAccessToken
}

// GetDingTalkSecret 获取钉钉密钥
func (c *Config) GetDingTalkSecret() string {
	return c.DingTalkSecret
}

// GetWBHubApiKey WBHub API密钥获取方法
func (c *Config) GetWBHubApiKey() string {
	return c.WBHubApiKey
}

// LoadFromFile 从指定文件加载配置
func LoadFromFile(filename string) (*Config, error) {
	// 加载环境变量文件
	if err := godotenv.Load(filename); err != nil {
		return nil, fmt.Errorf("加载配置文件失败: %v", err)
	}

	// 使用环境变量加载配置
	return LoadFromEnv()
}

// GetMyAgentBaseURL 获取MyAgent服务地址
func (c *Config) GetMyAgentBaseURL() string {
	return c.MyAgentBaseURL
}

// LoadFromEnv 从环境变量加载配置
func LoadFromEnv() (*Config, error) {
	cfg := &Config{}

	// Redis 配置
	cfg.RedisAddr = getEnv("REDIS_ADDRESS", "localhost:6379")
	cfg.RedisPwd = getEnv("REDIS_PASSWORD", "")
	cfg.RedisDatabase = getEnvAsInt("REDIS_DB", 0)

	// RocketMQ 配置
	cfg.RocketMQNameSrv = getEnv("ROCKETMQ_NAMESERVER", "localhost:9876")
	cfg.RocketMQGrp = getEnv("ROCKETMQ_GROUP", "lens-group")

	// API Keys
	cfg.WBApiKey = getEnv("WB_API_KEY", "")
	cfg.WBHubApiKey = getEnv("WBHUB_API_KEY", "")

	// 服务配置
	cfg.MonitorInterval = getEnvAsDuration("MONITOR_INTERVAL", "5m")
	cfg.CrawlerInterval = getEnvAsDuration("CRAWLER_INTERVAL", "1h")

	// 钉钉配置
	cfg.DingTalkAccessToken = getEnv("DINGTALK_ACCESS_TOKEN", "")
	cfg.DingTalkSecret = getEnv("DINGTALK_SECRET", "")

	// 日志配置
	cfg.LogLevel = getEnv("LOG_LEVEL", "info")
	cfg.LogFormat = getEnv("LOG_FORMAT", "json")
	cfg.ServerPort = getEnvAsInt("SERVER_PORT", 8080)
	// MyAgent配置
	cfg.MyAgentBaseURL = getEnv("MY_AGENT_BASE_URL", "")

	return cfg, nil
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(name string, defaultVal int) int {
	valueStr := getEnv(name, "")
	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultVal
}

// getEnvAsDuration 获取环境变量并转换为时间间隔
func getEnvAsDuration(name string, defaultVal string) time.Duration {
	valueStr := getEnv(name, defaultVal)
	if duration, err := time.ParseDuration(strings.ToLower(valueStr)); err == nil {
		return duration
	}
	// 如果解析失败，返回默认值
	duration, _ := time.ParseDuration(defaultVal)
	return duration
}

// getEnvAsStringSlice 获取环境变量并转换为字符串切片（逗号分隔）
func getEnvAsStringSlice(name string, defaultVal []string) []string {
	valueStr := getEnv(name, "")
	if valueStr == "" {
		return defaultVal
	}

	// 按逗号分割并去除空白
	parts := strings.Split(valueStr, ",")
	result := make([]string, 0, len(parts))
	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}

	return result
}

// Validate 验证配置是否有效
func (c *Config) Validate() error {
	if c.RedisAddr == "" {
		return fmt.Errorf("REDIS_ADDRESS 不能为空")
	}

	if c.RocketMQNameSrv == "" {
		return fmt.Errorf("ROCKETMQ_NAMESERVER 不能为空")
	}

	if c.WBHubApiKey == "" {
		return fmt.Errorf("WBHUB_API_KEY 不能为空")
	}

	return nil
}
