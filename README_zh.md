# Lens 项目

本项目提供与 Wildberries 市场交互的 API 和服务。

## 项目结构

```
lens/
├── cmd/                    # 应用程序入口点
│   ├── crawler-keyword/    # 关键词爬虫服务
│   ├── monitor-product/    # 产品监控服务
│   ├── advert-manager/     # 广告管理服务
│   └── warehouse-service/  # 仓储服务
├── internal/               # 内部包 (不导出)
│   ├── api/                # API 客户端
│   │   ├── wb_buyer/       # 买家 API 客户端 (Wildberries 买家平台)
│   │   ├── wb_seller/      # 卖家 API 客户端 (Wildberries 卖家平台)
│   │   ├── wb/             # 官方 Wildberries API 客户端
│   │   ├── ozon/           # 官方 Ozon API 客户端
│   │   ├── ozon_buyer/     # 买家 API 客户端 (Ozon 买家平台)
│   │   ├── ozon_seller/    # 卖家 API 客户端 (Ozon 卖家平台)
│   │   └── monitor/        # 对外提供的 API 服务
│   ├── service/            # 业务逻辑服务
│   └── infrastructure/     # 基础设施组件
├── pkg/                    # 公共包 (导出)
├── configs/                # 配置文件
├── docs/                   # 文档
├── go.mod                  # Go 模块定义
└── go.sum                  # Go 模块校验和
```

## 服务

### 1. 关键词爬虫 (cmd/crawler-keyword)
每周从 Wildberries 爬取热门关键词。

### 2. 产品监控 (cmd/monitor-product)
监控产品排名和性能指标。

### 3. 广告管理 (cmd/advert-manager)
管理 Wildberries 上的广告活动。

### 4. 仓储服务 (cmd/warehouse-service)
处理与仓储相关的操作。

## 安装

```bash
go mod tidy
```

## 配置

复制示例配置并根据需要修改：

```bash
cp configs/.env.example .env
# 使用您的配置编辑 .env
```

## 运行服务

每个服务都可以独立运行：

```bash
# 运行关键词爬虫
go run cmd/crawler-keyword/main.go

# 运行产品监控
go run cmd/monitor-product/main.go

# 运行广告管理器
go run cmd/advert-manager/main.go

# 运行仓储服务
go run cmd/warehouse-service/main.go
```

## API 分类说明

本项目根据不同平台和用途对API进行了分类：

1. **WB 买家 API (internal/api/wb_buyer/)**
   - 用于与Wildberries买家平台交互
   - 主要用于搜索和推荐功能
   - 模拟买家行为进行数据爬取

2. **WB 卖家 API (internal/api/wb_seller/)**
   - 用于与Wildberries卖家平台交互
   - 提供供应管理、报告、仓库等卖家功能
   - 需要卖家认证信息

3. **WB 官方 API (internal/api/wb/)**
   - Wildberries官方提供的API客户端
   - 包括推广服务和商品卡片服务
   - 使用官方API密钥进行认证

4. **Ozon API (internal/api/ozon/)**
   - 官方Ozon市场API客户端
   - 提供对Ozon商品、订单和分析服务的访问
   - 使用官方API密钥进行认证

5. **Ozon 买家 API (internal/api/ozon_buyer/)**
   - 用于与Ozon买家平台交互
   - 主要用于搜索和推荐功能
   - 模拟买家行为进行数据爬取

6. **Ozon 卖家 API (internal/api/ozon_seller/)**
   - 用于与Ozon卖家平台交互
   - 提供卖家功能，如商品管理、订单处理和报告
   - 需要卖家认证信息

7. **对外提供的 API (internal/api/monitor/)**
   - 为外部用户提供的API服务
   - 包括产品监控和关键词管理功能
   - 提供HTTP和WebSocket接口

## Docker 构建和部署

本项目支持基于 Docker 的部署，每个服务都有自己的 Dockerfile。

### 先决条件

- Docker
- Docker Compose

### 构建服务

1. 首先，构建所有服务二进制文件：
```bash
./build-docker.sh
```

2. 使用 Docker Compose 构建和运行所有服务：
```bash
docker-compose up --build
```

### 单个服务部署

每个服务都可以单独构建和运行：

```bash
# 构建特定服务
docker build -t lens/advert-manager -f cmd/advert-manager/Dockerfile .

# 运行特定服务
docker run -p 8080:8080 lens/advert-manager
```

### 时区配置

所有服务在 Docker 容器中默认配置为使用莫斯科时间 (Europe/Moscow)。