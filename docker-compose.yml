version: '3.8'

services:
  advert-manager:
    build:
      context: .
      dockerfile: cmd/advert-manager/Dockerfile
    image: lens/advert-manager:latest
    ports:
      - "8080:8080"
    networks:
      - lens-network
    depends_on:
      - redis
      - rocketmq

  crawler-keyword:
    build:
      context: .
      dockerfile: cmd/crawler-keyword/Dockerfile
    image: lens/crawler-keyword:latest
    ports:
      - "8081:8080"
    networks:
      - lens-network
    depends_on:
      - redis
      - rocketmq

  monitor-product:
    build:
      context: .
      dockerfile: cmd/monitor-product/Dockerfile
    image: lens/monitor-product:latest
    ports:
      - "8082:8081"
    networks:
      - lens-network
    depends_on:
      - redis
      - rocketmq

  warehouse-service:
    build:
      context: .
      dockerfile: cmd/warehouse-service/Dockerfile
    image: lens/warehouse-service:latest
    ports:
      - "8083:8082"
    networks:
      - lens-network
    depends_on:
      - redis
      - rocketmq

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    networks:
      - lens-network

  rocketmq:
    image: apache/rocketmq:4.9.4
    ports:
      - "9876:9876"
    networks:
      - lens-network

networks:
  lens-network:
    driver: bridge