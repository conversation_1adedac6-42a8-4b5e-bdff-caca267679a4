#!/bin/bash

# 构建所有服务的二进制文件
echo "Building all services..."

# 创建 bin 目录
mkdir -p bin

# 构建每个服务
#echo "Building advert-manager..."
#CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/advert-manager cmd/advert-manager/main.go
#
#echo "Building crawler-keyword..."
#CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/crawler-keyword cmd/crawler-keyword/main.go

echo "Building monitor-product..."
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o cmd/monitor-product/monitor-product cmd/monitor-product/main.go

#echo "Building warehouse-service..."
#CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/warehouse-service cmd/warehouse-service/main.go

echo "Build completed successfully!"
echo "Note: Each service will load its configuration from configs/{service-name}.env when running in Docker."