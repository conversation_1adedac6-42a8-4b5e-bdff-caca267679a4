# 使用官方 Go 镜像作为构建环境
FROM golang:1.24.3-alpine

# 设置工作目录
WORKDIR /app

# 复制 go mod 和 sum 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建所有服务
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /bin/advert-manager cmd/advert-manager/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /bin/crawler-keyword cmd/crawler-keyword/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /bin/monitor-product cmd/monitor-product/main.go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o /bin/warehouse-service cmd/warehouse-service/main.go

# 设置时区为莫斯科时间
ENV TZ=Europe/Moscow

# 为每个服务创建目录并复制二进制文件
RUN mkdir -p /build/advert-manager && cp /bin/advert-manager /build/advert-manager/
RUN mkdir -p /build/crawler-keyword && cp /bin/crawler-keyword /build/crawler-keyword/
RUN mkdir -p /build/monitor-product && cp /bin/monitor-product /build/monitor-product/
RUN mkdir -p /build/warehouse-service && cp /bin/warehouse-service /build/warehouse-service/