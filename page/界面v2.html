<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理器 (v1.11)</title>
    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/antd@5.19.1/dist/reset.css" />
    <style>
        body { padding: 0; background-color: #fff; }
        .ant-table-wrapper { border: 1px solid #f0f0f0; }
        .rank-cell { text-align: center; }
        .rank-line { line-height: 1.2; }
        .rank-value { font-size: 1.2em; font-weight: 500; }
        .position-value { font-size: 0.7em; color: #6c757d; margin-left: 2px; vertical-align: super; }
        .cpm-line { color: red; font-size: 0.9em; vertical-align: super;}
        .no-rank { color: #adb5bd; }
        .ant-table-expanded-row .ant-table-placeholder { padding: 20px; }
    </style>

    <!-- 外部库的 JS 脚本 -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dayjs@1.11.11/dayjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/antd@5.19.1/dist/antd.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/@ant-design/icons@4/dist/index.umd.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useRef, useCallback, useEffect } = React;
        const { Table, Button, Space, Typography, Tag, Modal, Input, message, ConfigProvider, Form, Collapse } = antd;
        const { DeleteOutlined, PlusOutlined } = icons;
        const { Title, Text } = Typography;
        const { Panel } = Collapse;

        const API_BASE_URL = 'http://************:8081';
        const WEBSOCKET_URL = 'ws://************:8081/ws';
        const MAX_TIME_COLUMNS = 10;
        const RECONNECT_DELAY = 3000;

        const formatTime = (isoString) => {
            if (!isoString) return "??:??";
            try {
                return new Date(isoString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            } catch (e) {
                return "??:??";
            }
        };

        const MonitoringApp = () => {
            const [taskList, setTaskList] = useState([]);
            const [processedData, setProcessedData] = useState({});
            const [advertData, setAdvertData] = useState({});
            const [wsStatus, setWsStatus] = useState('disconnected');
            const [createModalVisible, setCreateModalVisible] = useState(false);
            const [appendModal, setAppendModal] = useState({ visible: false, productId: null, sku: null });
            const [form] = Form.useForm();
            const ws = useRef(null);
            const reconnectionTimer = useRef(null);

            const connectWebSocket = useCallback(() => {
                if (ws.current && (ws.current.readyState === WebSocket.OPEN || ws.current.readyState === WebSocket.CONNECTING)) return;
                setWsStatus('connecting');
                ws.current = new WebSocket(WEBSOCKET_URL);
                ws.current.onopen = () => { setWsStatus('connected'); message.success('WebSocket 连接成功'); };
                ws.current.onmessage = (event) => {
                    try {
                        const rawData = JSON.parse(event.data);
                        
                        // 格式1: 广告数据
                        if (rawData && typeof rawData === 'object' && 
                            Object.keys(rawData)[0] && 
                            Array.isArray(rawData[Object.keys(rawData)[0]])) {
                            
                            // 广告数据格式
                            setAdvertData(prevData => ({
                                ...prevData,
                                ...rawData
                            }));
                            
                            // 确保为每个产品ID创建对应的任务
                            const productIds = Object.keys(rawData);
                            productIds.forEach(productId => {
                                const ads = rawData[productId];
                                if (ads && ads.length > 0) {
                                    const firstAd = ads[0];
                                    const sku = firstAd.sku || productId; // 用sku作为显示名
                                    
                                    setTaskList(currentList => {
                                        const existingTask = currentList.find(task => task.productId === productId);
                                        if (!existingTask) {
                                            const newTask = { 
                                                key: productId, 
                                                productId: productId, 
                                                sku: sku, 
                                                keywords: []
                                            };
                                            return [...currentList, newTask];
                                        }
                                        return currentList;
                                    });
                                }
                            });
                        } 
                        
                        // 格式2: 关键词排名数据
                        if (rawData.sku && rawData.keyword && rawData.id) {
                            const sku = rawData.sku;
                            const keywordText = rawData.keyword;
                            const productId = String(rawData.id);
                            const time = formatTime(rawData.in_process_at);
                            const logData = rawData.log || {};
                            const rankInfo = { promoPosition: logData.promoPosition ?? null, position: logData.position ?? null, cpm: logData.cpm ?? 0 };
                            
                            setProcessedData(currentData => {
                                const newSkuData = { ...currentData };
                                const newKeywordData = { ...(newSkuData[sku] || {}) };
                                const newTimeData = { ...(newKeywordData[keywordText] || {}) };
                                newTimeData[time] = rankInfo;
                                newKeywordData[keywordText] = newTimeData;
                                newSkuData[sku] = newKeywordData;
                                return newSkuData;
                            });
                            setTaskList(currentList => {
                                const taskIndex = currentList.findIndex(task => task.productId === productId);
                                if (taskIndex === -1) {
                                    const newTask = { key: productId, productId: productId, sku: sku, keywords: [{ key: `${productId}-${keywordText}`, keyword: keywordText }] };
                                    return [...currentList, newTask];
                                }
                                const taskToUpdate = currentList[taskIndex];
                                const keywordExists = taskToUpdate.keywords.some(kw => kw.keyword === keywordText);
                                if (!keywordExists) {
                                    const newKeyword = { key: `${productId}-${keywordText}`, keyword: keywordText };
                                    const updatedTask = { ...taskToUpdate, keywords: [...taskToUpdate.keywords, newKeyword] };
                                    const newList = [...currentList];
                                    newList[taskIndex] = updatedTask;
                                    return newList;
                                }
                                return currentList;
                            });
                        }
                    } catch (error) {
                        console.error('解析 WebSocket 数据失败:', error, '原始数据:', event.data);
                    }
                };
                ws.current.onerror = (error) => { console.error('WebSocket 连接发生错误:', error); setWsStatus('error'); message.error('WebSocket 连接发生错误'); };
                ws.current.onclose = () => { if (!ws.current) return; setWsStatus('disconnected'); message.info(`连接已断开, ${RECONNECT_DELAY / 1000}秒后尝试重连...`, 3); ws.current = null; clearTimeout(reconnectionTimer.current); reconnectionTimer.current = setTimeout(() => { connectWebSocket(); }, RECONNECT_DELAY); };
            }, []);

            useEffect(() => {
                connectWebSocket();
                return () => { if (reconnectionTimer.current) clearTimeout(reconnectionTimer.current); if (ws.current) { ws.current.onclose = null; ws.current.close(); ws.current = null; } };
            }, [connectWebSocket]);

            const apiFetch = async (endpoint, body) => {
                try {
                    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json', 'User-Agent': 'Apifox/1.0.0 (https://apifox.com)' },
                        body: JSON.stringify(body),
                    });
                    if (!response.ok) { const errorText = await response.text(); throw new Error(`HTTP error ${response.status}: ${errorText || 'Server Error'}`); }
                    if (response.status === 204) { return null; }
                    return await response.json();
                } catch (error) { message.error(`操作失败: ${error.message}`); throw error; }
            };
            
            const handleCreateTask = async (values) => { 
                const { productId, sku, keywords } = values; 
                const cleanSku = sku.trim(); 
                const cleanKeywordsArray = keywords.split(/\n+/).map(kw => kw.trim()).filter(Boolean);
                if (cleanKeywordsArray.length === 0) return message.error('关键词不能为空'); 
                try { 
                    await apiFetch('/api/monitor/product/create', { productId: String(productId), sku: cleanSku, keywords: cleanKeywordsArray }); 
                    message.success(`任务 ${cleanSku} 创建成功`); 
                    const newKeywords = cleanKeywordsArray.map(kw => ({ key: `${String(productId)}-${kw}`, keyword: kw })); 
                    setTaskList(prevData => { const taskExists = prevData.some(task => task.productId === String(productId)); if (taskExists) { return prevData; } return [...prevData, { key: String(productId), productId: String(productId), sku: cleanSku, keywords: newKeywords }]; }); 
                    setCreateModalVisible(false); 
                    form.resetFields(); 
                } catch (error) {} 
            };
            
            const handleAppendKeywords = async (values) => { 
                const { newKeywords } = values; 
                const cleanKeywordsArray = newKeywords.split(/\n+/).map(kw => kw.trim()).filter(Boolean);
                if (cleanKeywordsArray.length === 0) return message.error('关键词不能为空'); 
                try { 
                    await apiFetch('/api/monitor/product/keywords/append', { productId: String(appendModal.productId), keywords: cleanKeywordsArray }); 
                    message.success(`为 ${appendModal.sku} 追加关键词成功`); 
                    setTaskList(currentData => currentData.map(item => { if (item.productId === String(appendModal.productId)) { const existingKeywords = new Set(item.keywords.map(kw => kw.keyword)); const addedKeywords = cleanKeywordsArray.filter(kw => !existingKeywords.has(kw)).map(kw => ({ key: `${item.productId}-${kw}`, keyword: kw })); return { ...item, keywords: [...item.keywords, ...addedKeywords] }; } return item; })); 
                    setAppendModal({ visible: false, productId: null, sku: null }); 
                } catch (error) {} 
            };

            const handleDeleteKeyword = async (productId, sku, keywordToRemove) => { 
                try { 
                    await apiFetch('/api/monitor/product/keywords/remove', { productId: String(productId), keywords: [keywordToRemove] }); 
                    message.success(`关键词 "${keywordToRemove}" 已删除`); 
                    setTaskList(currentData => currentData.map(item => { if (item.productId === String(productId)) { return { ...item, keywords: item.keywords.filter(kw => kw.keyword !== keywordToRemove) }; } return item; }).filter(item => item.keywords.length > 0)); 
                } catch (error) {} 
            };

            const renderWsStatus = () => { const statusMap = { disconnected: { color: 'red', text: '未连接' }, connecting: { color: 'gold', text: '连接中...' }, connected: { color: 'green', text: '已连接' }, error: { color: 'red', text: '连接错误' }, }; const { color, text } = statusMap[wsStatus]; return <Tag color={color}>{text}</Tag>; };
            const expandedRowRender = (record) => {
                const sku = record.sku;
                const skuRankData = processedData[sku];
                const allTimes = new Set();
                
                if (skuRankData) {
                    Object.values(skuRankData).forEach(keywordHistory => {
                        Object.keys(keywordHistory).forEach(time => allTimes.add(time));
                    });
                }
                const sortedTimeColumns = Array.from(allTimes).sort((a, b) => b.localeCompare(a)).slice(0, MAX_TIME_COLUMNS);
                
                if (!record.keywords || record.keywords.length === 0) {
                    return <div style={{ padding: '20px', textAlign: 'center' }}><Text type="secondary">正在等待此任务的数据...</Text></div>;
                }
                
                const columns = sortedTimeColumns.length > 0 ? [
                    { title: '关键词', dataIndex: 'keyword', key: 'keyword', width: 200, render: (text) => (
                        <Space> 
                            <Button danger size="small" icon={<DeleteOutlined />} onClick={() => handleDeleteKeyword(record.productId, sku, text)} />
                            <Text>{text}</Text>
                        </Space>
                    ) },
                    ...sortedTimeColumns.map(time => ({
                        title: time, dataIndex: time, key: time, width: 100, align: 'center',
                        render: (data) => {
                            if (!data || data.promoPosition === null) {
                                return <span className="no-rank">-</span>;
                            }
                            return (
                                <div className="rank-cell">
                                    <div className="rank-line">
                                        <span className="rank-value">{data.promoPosition}</span>
                                        <sup className="position-value">{data.position}</sup>
                                        <div className="cpm-line">{data.cpm}</div>
                                    </div>
                                </div>
                            );
                        }
                    }))
                ] : [
                    { title: '关键词', dataIndex: 'keyword', key: 'keyword', width: 200, render: (text) => (
                        <Space>
                            <Button danger size="small" onClick={() => handleDeleteKeyword(record.productId, sku, text)} style={{ padding: '0 8px' }}>删</Button>
                            <Text>{text}</Text>
                        </Space>
                    )}
                ];
                
                const dataSource = record.keywords.map(kw => {
                    const keywordText = kw.keyword;
                    const rowData = { key: kw.key, keyword: keywordText };
                    const keywordHistory = skuRankData?.[keywordText] || {};
                    sortedTimeColumns.forEach(time => {
                        rowData[time] = keywordHistory[time];
                    });
                    return rowData;
                });
                
                return (
                    <Table 
                        columns={columns} 
                        dataSource={dataSource} 
                        pagination={false} 
                        size="small" 
                        scroll={sortedTimeColumns.length > 0 ? { x: 200 + sortedTimeColumns.length * 100 } : undefined}
                    />
                );
            };
            
            const renderCollapseItems = () => {
                return taskList.map(task => {
                    const sku = task.sku;
                    const skuRankData = processedData[sku];
                    const hasAdvertData = advertData[task.productId] && advertData[task.productId].length > 0;
                    
                    const advertHeaders = 
                        <div style={{ display: 'table', fontSize: '10px', color: '#666', marginBottom: '2px' }}>
                            <div style={{ display: 'flex', gap: 8 }}>
                                <span style={{ width: 70 }}>广告ID</span>
                                <span style={{ width: 35 }}>类型</span>
                                <span style={{ width: 40 }}>出价</span>
                                <span style={{ width: 40 }}>展示</span>
                                <span style={{ width: 35 }}>点击</span>
                                <span style={{ width: 45 }}>广告单量</span>
                                <span style={{ width: 45 }}>自然单量</span>
                                <span style={{ width: 45 }}>CTR%</span>
                                <span style={{ width: 45 }}>花费</span>
                                <span style={{ width: 45 }}>CPC</span>
                                <span style={{ width: 35 }}>ACOS</span>
                            </div>
                        </div>;

                    const AdvertRow = ({ ad }) => {
                        // 安全处理所有可能为null的数据
                        const safeNumber = (num, defaultValue = 0) => {
                            return (num !== null && num !== undefined && !isNaN(num)) ? Number(num) : defaultValue;
                        };

                        return (
                            <div style={{ display: 'flex', gap: 8, fontSize: '11px', alignItems: 'center', color: '#333' }}>
                                <span style={{ width: 70, fontWeight: 500 }}>{safeNumber(ad.advert_id)}</span>
                                <span style={{ width: 35 }}>
                                    {safeNumber(ad.advert_type, 0) === 8 ? '自动' : '手动'}
                                </span>
                                <span style={{ width: 40 }}>{safeNumber(ad.current_bid, 0).toString()}₽</span>
                                <span style={{ width: 40 }}>{safeNumber(ad.views, 0).toString()}</span>
                                <span style={{ width: 50 }}>{safeNumber(ad.clicks, 0).toString()}</span>
                                <span style={{ width: 40 }}>{safeNumber(ad.ad_orders, 0).toString()}</span>
                                <span style={{ width: 35 }}>{safeNumber(ad.organic_orders, 0).toString()}</span>
                                <span style={{ width: 45 }}>{(safeNumber(ad.ctr, 0)).toFixed(1)}%</span>
                                <span style={{ width: 45 }}>{safeNumber(ad.spending, 0).toFixed(1)}₽</span>
                                <span style={{ width: 45 }}>{safeNumber(ad.cpc, 0).toFixed(1)}₽</span>
                                <span style={{ width: 35 }}>{safeNumber(ad.acos, 0).toFixed(1)}%</span>
                            </div>
                        );
                    };

                    const renderAdvertRows = () => {
                        if (!hasAdvertData) return null;
                        const ads = advertData[task.productId];
                        return (
                            <div style={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                                {ads.slice(0, 3).map((ad, index) => (
                                    <AdvertRow key={index} ad={ad} />
                                ))}
                                {ads.length > 3 && (
                                    <Text style={{ fontSize: '10px', color: '#999', marginTop: 1 }}>
                                        还有{ads.length - 3}个广告...
                                    </Text>
                                )}
                            </div>
                        );
                    };

                    const headerContent = (
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%', minHeight: '44px' }}>
                            <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                                <Text strong>{task.sku}</Text>
                                <Text type="secondary" style={{ fontSize: '12px' }}>{task.productId}</Text>
                            </div>
                            {hasAdvertData ? (
                                <div style={{ display: 'inline-block' }}>
                                    {advertHeaders}
                                    {renderAdvertRows()}
                                </div>
                            ) : null}
                            <Space align="center" size={8}>
                                <Text type="secondary" style={{ fontSize: '14px' }}>
                                    {task.keywords?.length || 0}个词
                                </Text>
                                <Button 
                                    type="text" 
                                    icon={<PlusOutlined />} 
                                    size="small"
                                    title={`为 ${task.sku} 追加关键词`}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        setAppendModal({ visible: true, productId: task.productId, sku: task.sku })
                                    }}
                                    style={{ color: '#1890ff', fontSize: '16px' }}
                                />
                            </Space>
                        </div>
                    );
                    return (
                        <Panel header={headerContent} key={task.key} style={{ marginBottom: 0, padding: 0 }} showArrow={false}>
                            {expandedRowRender(task)}
                        </Panel>
                    );
                });
            };
            
            return (
                <div style={{ padding: '0 10px' }}>
                    <Space align="center" style={{ padding: '10px 0' }}>
                        <Title level={4} style={{ margin: 0 }}>广告监控(v1.10)</Title>
                        <Space align="center">
                            <Text>WebSocket 状态:</Text>
                            {renderWsStatus()}
                        </Space>
                    </Space>
                    <div style={{ backgroundColor: '#fafafa', padding: 10, borderRadius: 6, marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text strong style={{ fontSize: 16 }}>监控列表</Text>
                        <Button type="primary" onClick={() => setCreateModalVisible(true)}>创建监听任务</Button>
                    </div>
                    <Collapse showArrow={false}>
                        {renderCollapseItems()}
                    </Collapse>
                    <Modal title="创建新的监听任务" open={createModalVisible} onCancel={() => setCreateModalVisible(false)} footer={null}>
                        <Form form={form} layout="vertical" onFinish={handleCreateTask}>
                            <Form.Item name="productId" label="产品ID (productId)" rules={[{ required: true, message: '请输入产品ID' }]}><Input placeholder="这个ID主要用于创建任务" /></Form.Item>
                            <Form.Item name="sku" label="产品编码 (sku)" rules={[{ required: true, message: '请输入SKU' }]}><Input placeholder="例如: MLN101-3" /></Form.Item>
                            <Form.Item name="keywords" label="关键词 (每行一个)" rules={[{ required: true, message: '请输入至少一个关键词' }]}>
                                <Input.TextArea rows={4} placeholder="请每行输入一个关键词，例如：
люстра потолочная
светильник" />
                            </Form.Item>
                            <Form.Item><Button type="primary" htmlType="submit" block>确认创建</Button></Form.Item>
                        </Form>
                    </Modal>
                    <Modal title={`为 ${appendModal.sku} 追加关键词`} open={appendModal.visible} onCancel={() => setAppendModal({ visible: false, productId: null, sku: null })} footer={null}>
                        <Form onFinish={handleAppendKeywords} layout="vertical">
                            <Form.Item name="newKeywords" label="新关键词 (每行一个)" rules={[{ required: true, message: '请输入至少一个关键词' }]}>
                                <Input.TextArea rows={4} placeholder="请每行输入一个要追加的关键词" />
                            </Form.Item>
                            <Form.Item><Button type="primary" htmlType="submit" block>确认追加</Button></Form.Item>
                        </Form>
                    </Modal>
                </div>
            );
        };

        const container = document.getElementById('root');
        const root = ReactDOM.createRoot(container);
        root.render(<ConfigProvider theme={{ token: { borderRadius: 0 }, components: { Table: { headerBg: '#fafafa' }, Collapse: { contentPadding: 0,headerPadding:8 } } }}><MonitoringApp /></ConfigProvider>);
    </script>
</body>
</html>