//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package common contains common properties used by the subpackages.
package common ;import (_f "fmt";_d "io";_ee "os";_e "path/filepath";_bb "runtime";_b "time";);

// Logger is the interface used for logging in the unipdf package.
type Logger interface{Error (_a string ,_da ...interface{});Warning (_ac string ,_eg ...interface{});Notice (_c string ,_ea ...interface{});Info (_dd string ,_ag ...interface{});Debug (_cf string ,_fc ...interface{});Trace (_ca string ,_dc ...interface{});
IsLogLevel (_af LogLevel )bool ;};func _dec (_gb _d .Writer ,_fae string ,_gde string ,_bad ...interface{}){_ ,_aeb ,_ega ,_cgc :=_bb .Caller (3);if !_cgc {_aeb ="\u003f\u003f\u003f";_ega =0;}else {_aeb =_e .Base (_aeb );};_cae :=_f .Sprintf ("\u0025s\u0020\u0025\u0073\u003a\u0025\u0064 ",_fae ,_aeb ,_ega )+_gde +"\u000a";
_f .Fprintf (_gb ,_cae ,_bad ...);};

// UtcTimeFormat returns a formatted string describing a UTC timestamp.
func UtcTimeFormat (t _b .Time )string {return t .Format (_bec )+"\u0020\u0055\u0054\u0043"};

// NewWriterLogger creates new 'writer' logger.
func NewWriterLogger (logLevel LogLevel ,writer _d .Writer )*WriterLogger {_fde :=WriterLogger {Output :writer ,LogLevel :logLevel };return &_fde ;};

// WriterLogger is the logger that writes data to the Output writer
type WriterLogger struct{LogLevel LogLevel ;Output _d .Writer ;};const _gg =2025;

// NewConsoleLogger creates new console logger.
func NewConsoleLogger (logLevel LogLevel )*ConsoleLogger {return &ConsoleLogger {LogLevel :logLevel }};

// Warning logs warning message.
func (_bd ConsoleLogger )Warning (format string ,args ...interface{}){if _bd .LogLevel >=LogLevelWarning {_bbf :="\u005b\u0057\u0041\u0052\u004e\u0049\u004e\u0047\u005d\u0020";_bd .output (_ee .Stdout ,_bbf ,format ,args ...);};};const _bcg =30;var ReleasedAt =_b .Date (_gg ,_cc ,_ead ,_ccc ,_bcg ,0,0,_b .UTC );


// Debug logs debug message.
func (_ba ConsoleLogger )Debug (format string ,args ...interface{}){if _ba .LogLevel >=LogLevelDebug {_df :="\u005b\u0044\u0045\u0042\u0055\u0047\u005d\u0020";_ba .output (_ee .Stdout ,_df ,format ,args ...);};};

// IsLogLevel returns true if log level is greater or equal than `level`.
// Can be used to avoid resource intensive calls to loggers.
func (_bc WriterLogger )IsLogLevel (level LogLevel )bool {return _bc .LogLevel >=level };

// Error logs error message.
func (_eae ConsoleLogger )Error (format string ,args ...interface{}){if _eae .LogLevel >=LogLevelError {_ef :="\u005b\u0045\u0052\u0052\u004f\u0052\u005d\u0020";_eae .output (_ee .Stdout ,_ef ,format ,args ...);};};

// ConsoleLogger is a logger that writes logs to the 'os.Stdout'
type ConsoleLogger struct{LogLevel LogLevel ;};

// Notice logs notice message.
func (_ce ConsoleLogger )Notice (format string ,args ...interface{}){if _ce .LogLevel >=LogLevelNotice {_ec :="\u005bN\u004f\u0054\u0049\u0043\u0045\u005d ";_ce .output (_ee .Stdout ,_ec ,format ,args ...);};};

// DummyLogger does nothing.
type DummyLogger struct{};

// Info logs info message.
func (_efd WriterLogger )Info (format string ,args ...interface{}){if _efd .LogLevel >=LogLevelInfo {_ebc :="\u005bI\u004e\u0046\u004f\u005d\u0020";_efd .logToWriter (_efd .Output ,_ebc ,format ,args ...);};};func (_dg WriterLogger )logToWriter (_fca _d .Writer ,_ace string ,_fad string ,_ece ...interface{}){_dec (_fca ,_ace ,_fad ,_ece );
};

// SetLogger sets 'logger' to be used by the unidoc unipdf library.
func SetLogger (logger Logger ){Log =logger };

// Error logs error message.
func (_cb WriterLogger )Error (format string ,args ...interface{}){if _cb .LogLevel >=LogLevelError {_eb :="\u005b\u0045\u0052\u0052\u004f\u0052\u005d\u0020";_cb .logToWriter (_cb .Output ,_eb ,format ,args ...);};};const Version ="\u0034\u002e\u0032.\u0030";


// Notice does nothing for dummy logger.
func (DummyLogger )Notice (format string ,args ...interface{}){};

// Trace logs trace message.
func (_fce ConsoleLogger )Trace (format string ,args ...interface{}){if _fce .LogLevel >=LogLevelTrace {_cd :="\u005b\u0054\u0052\u0041\u0043\u0045\u005d\u0020";_fce .output (_ee .Stdout ,_cd ,format ,args ...);};};

// Trace does nothing for dummy logger.
func (DummyLogger )Trace (format string ,args ...interface{}){};

// Trace logs trace message.
func (_gd WriterLogger )Trace (format string ,args ...interface{}){if _gd .LogLevel >=LogLevelTrace {_fcg :="\u005b\u0054\u0052\u0041\u0043\u0045\u005d\u0020";_gd .logToWriter (_gd .Output ,_fcg ,format ,args ...);};};

// Debug does nothing for dummy logger.
func (DummyLogger )Debug (format string ,args ...interface{}){};

// Debug logs debug message.
func (_afb WriterLogger )Debug (format string ,args ...interface{}){if _afb .LogLevel >=LogLevelDebug {_ege :="\u005b\u0044\u0045\u0042\u0055\u0047\u005d\u0020";_afb .logToWriter (_afb .Output ,_ege ,format ,args ...);};};const _bec ="\u0032\u0020\u004aan\u0075\u0061\u0072\u0079\u0020\u0032\u0030\u0030\u0036\u0020\u0061\u0074\u0020\u0031\u0035\u003a\u0030\u0034";


// Info does nothing for dummy logger.
func (DummyLogger )Info (format string ,args ...interface{}){};

// Notice logs notice message.
func (_ge WriterLogger )Notice (format string ,args ...interface{}){if _ge .LogLevel >=LogLevelNotice {_cg :="\u005bN\u004f\u0054\u0049\u0043\u0045\u005d ";_ge .logToWriter (_ge .Output ,_cg ,format ,args ...);};};

// Info logs info message.
func (_de ConsoleLogger )Info (format string ,args ...interface{}){if _de .LogLevel >=LogLevelInfo {_fd :="\u005bI\u004e\u0046\u004f\u005d\u0020";_de .output (_ee .Stdout ,_fd ,format ,args ...);};};

// IsLogLevel returns true if log level is greater or equal than `level`.
// Can be used to avoid resource intensive calls to loggers.
func (_bbc ConsoleLogger )IsLogLevel (level LogLevel )bool {return _bbc .LogLevel >=level };const _cc =7;const (LogLevelTrace LogLevel =5;LogLevelDebug LogLevel =4;LogLevelInfo LogLevel =3;LogLevelNotice LogLevel =2;LogLevelWarning LogLevel =1;LogLevelError LogLevel =0;
);const _ead =26;

// Error does nothing for dummy logger.
func (DummyLogger )Error (format string ,args ...interface{}){};func (_aff ConsoleLogger )output (_ab _d .Writer ,_afd string ,_bda string ,_fa ...interface{}){_dec (_ab ,_afd ,_bda ,_fa ...);};

// IsLogLevel returns true from dummy logger.
func (DummyLogger )IsLogLevel (level LogLevel )bool {return true };

// Warning logs warning message.
func (_bdf WriterLogger )Warning (format string ,args ...interface{}){if _bdf .LogLevel >=LogLevelWarning {_be :="\u005b\u0057\u0041\u0052\u004e\u0049\u004e\u0047\u005d\u0020";_bdf .logToWriter (_bdf .Output ,_be ,format ,args ...);};};

// Warning does nothing for dummy logger.
func (DummyLogger )Warning (format string ,args ...interface{}){};const _ccc =15;

// LogLevel is the verbosity level for logging.
type LogLevel int ;var Log Logger =DummyLogger {};