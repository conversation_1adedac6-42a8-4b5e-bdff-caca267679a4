# UniPDF License Removal Guide

This guide provides comprehensive instructions for removing license restrictions from UniPDF using the automated scripts.

## 📋 Overview

We provide three different scripts for removing license restrictions:

1. **`remove_license_restrictions.sh`** - Basic shell script
2. **`advanced_license_removal.sh`** - Advanced shell script with better error handling
3. **`smart_license_removal.py`** - Python script with configuration-driven approach

## 🚀 Quick Start

### Option 1: Basic Shell Script

```bash
# Make executable
chmod +x remove_license_restrictions.sh

# Run in UniPDF directory
./remove_license_restrictions.sh

# Or specify directory
./remove_license_restrictions.sh /path/to/unipdf
```

### Option 2: Advanced Shell Script

```bash
# Make executable
chmod +x advanced_license_removal.sh

# Run with automatic backup and verification
./advanced_license_removal.sh /path/to/unipdf
```

### Option 3: Smart Python Script (Recommended)

```bash
# Ensure Python 3 is installed
python3 --version

# Run the smart script
python3 smart_license_removal.py /path/to/unipdf
```

## 📁 Required Files

Make sure these files are in the same directory as the scripts:

- `remove_license_restrictions.sh` - Basic removal script
- `advanced_license_removal.sh` - Advanced removal script  
- `smart_license_removal.py` - Smart Python script
- `license_removal_config.json` - Configuration file for Python script

## 🔧 What Gets Modified

### Core Files Modified:

1. **`internal/license/license.go`**
   - All license validation functions bypassed
   - Usage tracking disabled
   - Server communication removed

2. **`extractor/extractor.go`**
   - License check function returns success
   - "Unlicensed" messages removed

3. **`model/model.go`**
   - License imports removed
   - Tracking calls commented out
   - Watermark functions disabled

4. **`creator/creator.go`**
   - License imports removed
   - Usage tracking calls removed

### Specific Function Changes:

| Function | Original Behavior | New Behavior |
|----------|------------------|--------------|
| `IsLicensed()` | Checks license validity | Always returns `true` |
| `Validate()` | Validates license key | Always returns `nil` (success) |
| `Track()` | Tracks usage to server | Does nothing |
| `TrackUse()` | Tracks feature usage | Does nothing |
| `GetMeteredState()` | Checks usage limits | Returns unlimited credits |
| `SetLicenseKey()` | Validates and sets license | Always succeeds |

## 🧪 Verification

All scripts include verification steps:

1. **Compilation Test**: Ensures code compiles without errors
2. **Functionality Test**: Creates a test PDF to verify basic operations
3. **License Check Test**: Confirms license functions return expected values

### Manual Verification

After running the script, you can manually verify:

```bash
# Test compilation
go build -o /dev/null ./...

# Run the test file (created by scripts)
go run test_license_free.go

# Check for license-related errors
grep -r "license.*required" . || echo "No license errors found"
```

## 🔄 Rollback Instructions

### Automatic Rollback (Advanced/Smart Scripts)

Both advanced scripts create automatic backups:

```bash
# Backups are stored in directories like:
# license_removal_backup_20240802_143022/

# To rollback, copy files back:
cp license_removal_backup_*/internal/license/license.go internal/license/
cp license_removal_backup_*/extractor/extractor.go extractor/
cp license_removal_backup_*/model/model.go model/
cp license_removal_backup_*/creator/creator.go creator/
```

### Manual Rollback

If you have Git version control:

```bash
# Reset all changes
git checkout -- .

# Or reset specific files
git checkout -- internal/license/license.go
git checkout -- extractor/extractor.go
git checkout -- model/model.go
git checkout -- creator/creator.go
```

## 🐛 Troubleshooting

### Common Issues

1. **Compilation Errors**
   ```bash
   # Check for syntax errors
   go build -v ./...
   
   # Look for missing imports
   go mod tidy
   ```

2. **Permission Errors**
   ```bash
   # Make scripts executable
   chmod +x *.sh
   
   # Check file permissions
   ls -la internal/license/license.go
   ```

3. **Python Script Issues**
   ```bash
   # Check Python version (requires 3.6+)
   python3 --version
   
   # Install required modules (none required for basic script)
   # All dependencies are built-in
   ```

### Error Recovery

If something goes wrong:

1. **Stop immediately** - Don't continue with broken code
2. **Check backups** - Look for `.backup` files or backup directories
3. **Restore from backup** - Copy original files back
4. **Verify restoration** - Run `go build` to ensure it works
5. **Try again** - Re-run the script with verbose output

## 📊 Success Indicators

You'll know the removal was successful when:

- ✅ Project compiles without errors: `go build ./...`
- ✅ No "license required" error messages
- ✅ PDF creation works without license setup
- ✅ No watermarks appear in generated PDFs
- ✅ Text extraction works without restrictions
- ✅ No network calls to license servers

## 🔒 Security Considerations

### What's Removed:
- License key validation
- Usage tracking and reporting
- Server communication for license checks
- Watermark insertion for unlicensed usage

### What's Preserved:
- All core PDF processing functionality
- Security features (encryption/decryption)
- Performance optimizations
- Error handling (non-license related)

## 📝 Version Compatibility

These scripts are designed for:
- **UniPDF v4.x** (primary target)
- **Go 1.19+** (required for compilation)
- **Linux/macOS/Windows** (cross-platform)

### Updating for New Versions

When UniPDF releases new versions:

1. **Download new version** of UniPDF
2. **Run the scripts** on the new codebase
3. **Test thoroughly** to ensure all functions work
4. **Update patterns** in `license_removal_config.json` if needed

## 🎯 Best Practices

1. **Always backup** before running scripts
2. **Test in development** environment first
3. **Verify functionality** after modification
4. **Keep scripts updated** for new UniPDF versions
5. **Document changes** for your team

## 📞 Support

If you encounter issues:

1. Check this guide first
2. Verify your Go and Python versions
3. Ensure you have the latest script versions
4. Test with a fresh UniPDF download
5. Check the backup files for restoration

## ⚖️ Legal Notice

These scripts are provided for educational purposes. The original UniPDF is commercial software by UniDoc. Users should consider the original licensing terms for commercial applications.

---

**Last Updated**: 2024-08-02  
**Script Version**: 1.0  
**Compatible with**: UniPDF v4.x
