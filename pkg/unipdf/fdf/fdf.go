//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package fdf provides support for loading form field data from Form Field Data (FDF) files.
package fdf ;import (_gb "bufio";_ge "bytes";_fc "encoding/hex";_e "errors";_ad "fmt";_c "github.com/unidoc/unipdf/v4/common";_fa "github.com/unidoc/unipdf/v4/core";_dd "io";_d "os";_ag "regexp";_b "sort";_g "strconv";_a "strings";);func (_ggg *fdfParser )parseObject ()(_fa .PdfObject ,error ){_c .Log .Trace ("\u0052e\u0061d\u0020\u0064\u0069\u0072\u0065c\u0074\u0020o\u0062\u006a\u0065\u0063\u0074");
_ggg .skipSpaces ();for {_egfc ,_gbc :=_ggg ._eec .Peek (2);if _gbc !=nil {return nil ,_gbc ;};_c .Log .Trace ("\u0050e\u0065k\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u003a\u0020\u0025\u0073",string (_egfc ));if _egfc [0]=='/'{_gead ,_acb :=_ggg .parseName ();
_c .Log .Trace ("\u002d\u003e\u004ea\u006d\u0065\u003a\u0020\u0027\u0025\u0073\u0027",_gead );return &_gead ,_acb ;}else if _egfc [0]=='('{_c .Log .Trace ("\u002d>\u0053\u0074\u0072\u0069\u006e\u0067!");return _ggg .parseString ();}else if _egfc [0]=='['{_c .Log .Trace ("\u002d\u003e\u0041\u0072\u0072\u0061\u0079\u0021");
return _ggg .parseArray ();}else if (_egfc [0]=='<')&&(_egfc [1]=='<'){_c .Log .Trace ("\u002d>\u0044\u0069\u0063\u0074\u0021");return _ggg .parseDict ();}else if _egfc [0]=='<'{_c .Log .Trace ("\u002d\u003e\u0048\u0065\u0078\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0021");
return _ggg .parseHexString ();}else if _egfc [0]=='%'{_ggg .readComment ();_ggg .skipSpaces ();}else {_c .Log .Trace ("\u002d\u003eN\u0075\u006d\u0062e\u0072\u0020\u006f\u0072\u0020\u0072\u0065\u0066\u003f");_egfc ,_ =_ggg ._eec .Peek (15);_gbf :=string (_egfc );
_c .Log .Trace ("\u0050\u0065\u0065k\u0020\u0073\u0074\u0072\u003a\u0020\u0025\u0073",_gbf );if (len (_gbf )> 3)&&(_gbf [:4]=="\u006e\u0075\u006c\u006c"){_ggb ,_cdc :=_ggg .parseNull ();return &_ggb ,_cdc ;}else if (len (_gbf )> 4)&&(_gbf [:5]=="\u0066\u0061\u006cs\u0065"){_cgg ,_bgc :=_ggg .parseBool ();
return &_cgg ,_bgc ;}else if (len (_gbf )> 3)&&(_gbf [:4]=="\u0074\u0072\u0075\u0065"){_cff ,_ggf :=_ggg .parseBool ();return &_cff ,_ggf ;};_fbe :=_gae .FindStringSubmatch (_gbf );if len (_fbe )> 1{_egfc ,_ =_ggg ._eec .ReadBytes ('R');_c .Log .Trace ("\u002d\u003e\u0020\u0021\u0052\u0065\u0066\u003a\u0020\u0027\u0025\u0073\u0027",string (_egfc [:]));
_ddfe ,_dcb :=_eea (string (_egfc ));return &_ddfe ,_dcb ;};_abe :=_eg .FindStringSubmatch (_gbf );if len (_abe )> 1{_c .Log .Trace ("\u002d\u003e\u0020\u004e\u0075\u006d\u0062\u0065\u0072\u0021");return _ggg .parseNumber ();};_abe =_gfd .FindStringSubmatch (_gbf );
if len (_abe )> 1{_c .Log .Trace ("\u002d\u003e\u0020\u0045xp\u006f\u006e\u0065\u006e\u0074\u0069\u0061\u006c\u0020\u004e\u0075\u006d\u0062\u0065r\u0021");_c .Log .Trace ("\u0025\u0020\u0073",_abe );return _ggg .parseNumber ();};_c .Log .Debug ("\u0045R\u0052\u004f\u0052\u0020U\u006e\u006b\u006e\u006f\u0077n\u0020(\u0070e\u0065\u006b\u0020\u0022\u0025\u0073\u0022)",_gbf );
return nil ,_e .New ("\u006f\u0062\u006a\u0065\u0063t\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0065\u0072\u0072\u006fr\u0020\u002d\u0020\u0075\u006e\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0070\u0061\u0074\u0074\u0065\u0072\u006e");
};};};func (_cae *fdfParser )parseDict ()(*_fa .PdfObjectDictionary ,error ){_c .Log .Trace ("\u0052\u0065\u0061\u0064\u0069\u006e\u0067\u0020\u0046\u0044\u0046\u0020D\u0069\u0063\u0074\u0021");_dde :=_fa .MakeDict ();_deb ,_ :=_cae ._eec .ReadByte ();
if _deb !='<'{return nil ,_e .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0069\u0063\u0074");};_deb ,_ =_cae ._eec .ReadByte ();if _deb !='<'{return nil ,_e .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0069\u0063\u0074");};for {_cae .skipSpaces ();
_cae .skipComments ();_afab ,_fgg :=_cae ._eec .Peek (2);if _fgg !=nil {return nil ,_fgg ;};_c .Log .Trace ("D\u0069c\u0074\u0020\u0070\u0065\u0065\u006b\u003a\u0020%\u0073\u0020\u0028\u0025 x\u0029\u0021",string (_afab ),string (_afab ));if (_afab [0]=='>')&&(_afab [1]=='>'){_c .Log .Trace ("\u0045\u004f\u0046\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079");
_cae ._eec .ReadByte ();_cae ._eec .ReadByte ();break ;};_c .Log .Trace ("\u0050a\u0072s\u0065\u0020\u0074\u0068\u0065\u0020\u006e\u0061\u006d\u0065\u0021");_eef ,_fgg :=_cae .parseName ();_c .Log .Trace ("\u004be\u0079\u003a\u0020\u0025\u0073",_eef );
if _fgg !=nil {_c .Log .Debug ("E\u0052\u0052\u004f\u0052\u0020\u0052e\u0074\u0075\u0072\u006e\u0069\u006e\u0067\u0020\u006ea\u006d\u0065\u0020e\u0072r\u0020\u0025\u0073",_fgg );return nil ,_fgg ;};if len (_eef )> 4&&_eef [len (_eef )-4:]=="\u006e\u0075\u006c\u006c"{_bbg :=_eef [0:len (_eef )-4];
_c .Log .Debug ("\u0054\u0061\u006b\u0069n\u0067\u0020\u0063\u0061\u0072\u0065\u0020\u006f\u0066\u0020n\u0075l\u006c\u0020\u0062\u0075\u0067\u0020\u0028%\u0073\u0029",_eef );_c .Log .Debug ("\u004e\u0065\u0077\u0020ke\u0079\u0020\u0022\u0025\u0073\u0022\u0020\u003d\u0020\u006e\u0075\u006c\u006c",_bbg );
_cae .skipSpaces ();_ecd ,_ :=_cae ._eec .Peek (1);if _ecd [0]=='/'{_dde .Set (_bbg ,_fa .MakeNull ());continue ;};};_cae .skipSpaces ();_geb ,_fgg :=_cae .parseObject ();if _fgg !=nil {return nil ,_fgg ;};_dde .Set (_eef ,_geb );_c .Log .Trace ("\u0064\u0069\u0063\u0074\u005b\u0025\u0073\u005d\u0020\u003d\u0020\u0025\u0073",_eef ,_geb .String ());
};_c .Log .Trace ("\u0072\u0065\u0074\u0075rn\u0069\u006e\u0067\u0020\u0046\u0044\u0046\u0020\u0044\u0069\u0063\u0074\u0021");return _dde ,nil ;};var _cf =_ag .MustCompile ("\u0025\u0025\u0045O\u0046");func (_egf *fdfParser )parseBool ()(_fa .PdfObjectBool ,error ){_feg ,_fdg :=_egf ._eec .Peek (4);
if _fdg !=nil {return _fa .PdfObjectBool (false ),_fdg ;};if (len (_feg )>=4)&&(string (_feg [:4])=="\u0074\u0072\u0075\u0065"){_egf ._eec .Discard (4);return _fa .PdfObjectBool (true ),nil ;};_feg ,_fdg =_egf ._eec .Peek (5);if _fdg !=nil {return _fa .PdfObjectBool (false ),_fdg ;
};if (len (_feg )>=5)&&(string (_feg [:5])=="\u0066\u0061\u006cs\u0065"){_egf ._eec .Discard (5);return _fa .PdfObjectBool (false ),nil ;};return _fa .PdfObjectBool (false ),_e .New ("\u0075n\u0065\u0078\u0070\u0065c\u0074\u0065\u0064\u0020\u0062o\u006fl\u0065a\u006e\u0020\u0073\u0074\u0072\u0069\u006eg");
};func (_aa *fdfParser )parseNull ()(_fa .PdfObjectNull ,error ){_ ,_daf :=_aa ._eec .Discard (4);return _fa .PdfObjectNull {},_daf ;};func (_dg *fdfParser )parseName ()(_fa .PdfObjectName ,error ){var _caf _ge .Buffer ;_ddd :=false ;for {_bce ,_gea :=_dg ._eec .Peek (1);
if _gea ==_dd .EOF {break ;};if _gea !=nil {return _fa .PdfObjectName (_caf .String ()),_gea ;};if !_ddd {if _bce [0]=='/'{_ddd =true ;_dg ._eec .ReadByte ();}else if _bce [0]=='%'{_dg .readComment ();_dg .skipSpaces ();}else {_c .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020N\u0061\u006d\u0065\u0020\u0073\u0074\u0061\u0072\u0074\u0069\u006e\u0067\u0020w\u0069\u0074\u0068\u0020\u0025\u0073\u0020(\u0025\u0020\u0078\u0029",_bce ,_bce );
return _fa .PdfObjectName (_caf .String ()),_ad .Errorf ("\u0069n\u0076a\u006c\u0069\u0064\u0020\u006ea\u006d\u0065:\u0020\u0028\u0025\u0063\u0029",_bce [0]);};}else {if _fa .IsWhiteSpace (_bce [0]){break ;}else if (_bce [0]=='/')||(_bce [0]=='[')||(_bce [0]=='(')||(_bce [0]==']')||(_bce [0]=='<')||(_bce [0]=='>'){break ;
}else if _bce [0]=='#'{_ddf ,_adb :=_dg ._eec .Peek (3);if _adb !=nil {return _fa .PdfObjectName (_caf .String ()),_adb ;};_dg ._eec .Discard (3);_dcf ,_adb :=_fc .DecodeString (string (_ddf [1:3]));if _adb !=nil {return _fa .PdfObjectName (_caf .String ()),_adb ;
};_caf .Write (_dcf );}else {_gd ,_ :=_dg ._eec .ReadByte ();_caf .WriteByte (_gd );};};};return _fa .PdfObjectName (_caf .String ()),nil ;};func (_dbb *fdfParser )readTextLine ()(string ,error ){var _fe _ge .Buffer ;for {_ca ,_agd :=_dbb ._eec .Peek (1);
if _agd !=nil {_c .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0025\u0073",_agd .Error ());return _fe .String (),_agd ;};if (_ca [0]!='\r')&&(_ca [0]!='\n'){_edb ,_ :=_dbb ._eec .ReadByte ();_fe .WriteByte (_edb );}else {break ;};};return _fe .String (),nil ;
};func _aga (_fbb string )(*fdfParser ,error ){_fgc :=fdfParser {};_fega :=[]byte (_fbb );_bda :=_ge .NewReader (_fega );_fgc ._egc =_bda ;_fgc ._dba =map[int64 ]_fa .PdfObject {};_dbg :=_gb .NewReader (_bda );_fgc ._eec =_dbg ;_fgc ._abg =int64 (len (_fbb ));
return &_fgc ,_fgc .parse ();};

// LoadFromPath loads FDF form data from file path `fdfPath`.
func LoadFromPath (fdfPath string )(*Data ,error ){_ec ,_af :=_d .Open (fdfPath );if _af !=nil {return nil ,_af ;};defer _ec .Close ();return Load (_ec );};func (_ef *fdfParser )skipComments ()error {if _ ,_abb :=_ef .skipSpaces ();_abb !=nil {return _abb ;
};_acg :=true ;for {_eag ,_bgf :=_ef ._eec .Peek (1);if _bgf !=nil {_c .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0025\u0073",_bgf .Error ());return _bgf ;};if _acg &&_eag [0]!='%'{return nil ;};_acg =false ;if (_eag [0]!='\r')&&(_eag [0]!='\n'){_ef ._eec .ReadByte ();
}else {break ;};};return _ef .skipComments ();};var _gae =_ag .MustCompile ("^\u005c\u0073\u002a\u0028\\d\u002b)\u005c\u0073\u002b\u0028\u005cd\u002b\u0029\u005c\u0073\u002b\u0052");

// Data represents forms data format (FDF) file data.
type Data struct{_bb *_fa .PdfObjectDictionary ;_ce *_fa .PdfObjectArray ;};var _fg =_ag .MustCompile ("\u0028\u005c\u0064\u002b)\\\u0073\u002b\u0028\u005c\u0064\u002b\u0029\u005c\u0073\u002b\u006f\u0062\u006a");

// Root returns the Root of the FDF document.
func (_afg *fdfParser )Root ()(*_fa .PdfObjectDictionary ,error ){if _afg ._gge !=nil {if _eee ,_agdg :=_afg .trace (_afg ._gge .Get ("\u0052\u006f\u006f\u0074")).(*_fa .PdfObjectDictionary );_agdg {if _fcg ,_dcc :=_afg .trace (_eee .Get ("\u0046\u0044\u0046")).(*_fa .PdfObjectDictionary );
_dcc {return _fcg ,nil ;};};};var _cfa []int64 ;for _fda :=range _afg ._dba {_cfa =append (_cfa ,_fda );};_b .Slice (_cfa ,func (_bbbg ,_abad int )bool {return _cfa [_bbbg ]< _cfa [_abad ]});for _ ,_cfb :=range _cfa {_aea :=_afg ._dba [_cfb ];if _aaf ,_ecfd :=_afg .trace (_aea ).(*_fa .PdfObjectDictionary );
_ecfd {if _ebb ,_cdfa :=_afg .trace (_aaf .Get ("\u0046\u0044\u0046")).(*_fa .PdfObjectDictionary );_cdfa {return _ebb ,nil ;};};};return nil ,_e .New ("\u0046\u0044\u0046\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");};func (_cgf *fdfParser )seekFdfVersionTopDown ()(int ,int ,error ){_cgf ._egc .Seek (0,_dd .SeekStart );
_cgf ._eec =_gb .NewReader (_cgf ._egc );_bgcc :=20;_dda :=make ([]byte ,_bgcc );for {_aeb ,_fde :=_cgf ._eec .ReadByte ();if _fde !=nil {if _fde ==_dd .EOF {break ;}else {return 0,0,_fde ;};};if _fa .IsDecimalDigit (_aeb )&&_dda [_bgcc -1]=='.'&&_fa .IsDecimalDigit (_dda [_bgcc -2])&&_dda [_bgcc -3]=='-'&&_dda [_bgcc -4]=='F'&&_dda [_bgcc -5]=='D'&&_dda [_bgcc -6]=='P'{_gecc :=int (_dda [_bgcc -2]-'0');
_bada :=int (_aeb -'0');return _gecc ,_bada ,nil ;};_dda =append (_dda [1:_bgcc ],_aeb );};return 0,0,_e .New ("\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};func (_gbd *fdfParser )parseHexString ()(*_fa .PdfObjectString ,error ){_gbd ._eec .ReadByte ();
var _bcg _ge .Buffer ;for {_cg ,_bbf :=_gbd ._eec .Peek (1);if _bbf !=nil {return _fa .MakeHexString (""),_bbf ;};if _cg [0]=='>'{_gbd ._eec .ReadByte ();break ;};_gbac ,_ :=_gbd ._eec .ReadByte ();if !_fa .IsWhiteSpace (_gbac ){_bcg .WriteByte (_gbac );
};};if _bcg .Len ()%2==1{_bcg .WriteRune ('0');};_eaa ,_bad :=_fc .DecodeString (_bcg .String ());if _bad !=nil {_c .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u0020\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0068\u0065\u0078\u0020\u0073\u0074r\u0069\u006e\u0067\u003a\u0020\u0027\u0025\u0073\u0027 \u002d\u0020\u0072\u0065\u0074\u0075\u0072\u006e\u0069\u006e\u0067\u0020\u0061n\u0020\u0065\u006d\u0070\u0074\u0079 \u0073\u0074\u0072i\u006e\u0067",_bcg .String ());
return _fa .MakeHexString (""),nil ;};return _fa .MakeHexString (string (_eaa )),nil ;};func (_ac *fdfParser )getFileOffset ()int64 {_ea ,_ :=_ac ._egc .Seek (0,_dd .SeekCurrent );_ea -=int64 (_ac ._eec .Buffered ());return _ea ;};func _fac (_aeab _dd .ReadSeeker )(*fdfParser ,error ){_dgd :=&fdfParser {};
_dgd ._egc =_aeab ;_dgd ._dba =map[int64 ]_fa .PdfObject {};_deg ,_cb ,_dff :=_dgd .parseFdfVersion ();if _dff !=nil {_c .Log .Error ("U\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0070\u0061\u0072\u0073\u0065\u0020\u0076e\u0072\u0073\u0069o\u006e:\u0020\u0025\u0076",_dff );
return nil ,_dff ;};_dgd ._gg =_deg ;_dgd ._aba =_cb ;_dff =_dgd .parse ();return _dgd ,_dff ;};func (_gaa *fdfParser )setFileOffset (_ada int64 ){_gaa ._egc .Seek (_ada ,_dd .SeekStart );_gaa ._eec =_gb .NewReader (_gaa ._egc );};var _gfd =_ag .MustCompile ("\u005e\u005b\u005c+-\u002e\u005d\u002a\u0028\u005b\u0030\u002d\u0039\u002e]\u002b)\u0065[\u005c+\u002d\u002e\u005d\u002a\u0028\u005b\u0030\u002d\u0039\u002e\u005d\u002b\u0029");


// FieldDictionaries returns a map of field names to field dictionaries.
func (fdf *Data )FieldDictionaries ()(map[string ]*_fa .PdfObjectDictionary ,error ){_ba :=map[string ]*_fa .PdfObjectDictionary {};for _ga :=0;_ga < fdf ._ce .Len ();_ga ++{_ddcd ,_fab :=_fa .GetDict (fdf ._ce .Get (_ga ));if _fab {_ff ,_ :=_fa .GetString (_ddcd .Get ("\u0054"));
if _ff !=nil {_ba [_ff .Str ()]=_ddcd ;};};};return _ba ,nil ;};func (_baf *fdfParser )skipSpaces ()(int ,error ){_bba :=0;for {_dea ,_gc :=_baf ._eec .ReadByte ();if _gc !=nil {return 0,_gc ;};if _fa .IsWhiteSpace (_dea ){_bba ++;}else {_baf ._eec .UnreadByte ();
break ;};};return _bba ,nil ;};func (_gbce *fdfParser )seekToEOFMarker (_cdg int64 )error {_bee :=int64 (0);_dac :=int64 (1000);for _bee < _cdg {if _cdg <=(_dac +_bee ){_dac =_cdg -_bee ;};_ ,_age :=_gbce ._egc .Seek (-_bee -_dac ,_dd .SeekEnd );if _age !=nil {return _age ;
};_badc :=make ([]byte ,_dac );_gbce ._egc .Read (_badc );_c .Log .Trace ("\u004c\u006f\u006f\u006bi\u006e\u0067\u0020\u0066\u006f\u0072\u0020\u0045\u004f\u0046 \u006da\u0072\u006b\u0065\u0072\u003a\u0020\u0022%\u0073\u0022",string (_badc ));_agf :=_cf .FindAllStringIndex (string (_badc ),-1);
if _agf !=nil {_gbg :=_agf [len (_agf )-1];_c .Log .Trace ("\u0049\u006e\u0064\u003a\u0020\u0025\u0020\u0064",_agf );_gbce ._egc .Seek (-_bee -_dac +int64 (_gbg [0]),_dd .SeekEnd );return nil ;};_c .Log .Debug ("\u0057\u0061\u0072\u006e\u0069\u006eg\u003a\u0020\u0045\u004f\u0046\u0020\u006d\u0061\u0072\u006b\u0065\u0072\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075n\u0064\u0021\u0020\u002d\u0020\u0063\u006f\u006e\u0074\u0069\u006e\u0075\u0065\u0020s\u0065e\u006b\u0069\u006e\u0067");
_bee +=_dac ;};_c .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u003a\u0020\u0045\u004f\u0046\u0020\u006d\u0061\u0072\u006be\u0072 \u0077\u0061\u0073\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e");return _e .New ("\u0045\u004f\u0046\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
};func (_bcd *fdfParser )parseNumber ()(_fa .PdfObject ,error ){return _fa .ParseNumber (_bcd ._eec )};func (_fdc *fdfParser )parseString ()(*_fa .PdfObjectString ,error ){_fdc ._eec .ReadByte ();var _bebb _ge .Buffer ;_bab :=1;for {_daa ,_aee :=_fdc ._eec .Peek (1);
if _aee !=nil {return _fa .MakeString (_bebb .String ()),_aee ;};if _daa [0]=='\\'{_fdc ._eec .ReadByte ();_bgfd ,_gga :=_fdc ._eec .ReadByte ();if _gga !=nil {return _fa .MakeString (_bebb .String ()),_gga ;};if _fa .IsOctalDigit (_bgfd ){_fcc ,_fb :=_fdc ._eec .Peek (2);
if _fb !=nil {return _fa .MakeString (_bebb .String ()),_fb ;};var _ecg []byte ;_ecg =append (_ecg ,_bgfd );for _ ,_gba :=range _fcc {if _fa .IsOctalDigit (_gba ){_ecg =append (_ecg ,_gba );}else {break ;};};_fdc ._eec .Discard (len (_ecg )-1);_c .Log .Trace ("\u004e\u0075\u006d\u0065ri\u0063\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0022\u0025\u0073\u0022",_ecg );
_bd ,_fb :=_g .ParseUint (string (_ecg ),8,32);if _fb !=nil {return _fa .MakeString (_bebb .String ()),_fb ;};_bebb .WriteByte (byte (_bd ));continue ;};switch _bgfd {case 'n':_bebb .WriteRune ('\n');case 'r':_bebb .WriteRune ('\r');case 't':_bebb .WriteRune ('\t');
case 'b':_bebb .WriteRune ('\b');case 'f':_bebb .WriteRune ('\f');case '(':_bebb .WriteRune ('(');case ')':_bebb .WriteRune (')');case '\\':_bebb .WriteRune ('\\');};continue ;}else if _daa [0]=='('{_bab ++;}else if _daa [0]==')'{_bab --;if _bab ==0{_fdc ._eec .ReadByte ();
break ;};};_adc ,_ :=_fdc ._eec .ReadByte ();_bebb .WriteByte (_adc );};return _fa .MakeString (_bebb .String ()),nil ;};type fdfParser struct{_gg int ;_aba int ;_dba map[int64 ]_fa .PdfObject ;_egc _dd .ReadSeeker ;_eec *_gb .Reader ;_abg int64 ;_gge *_fa .PdfObjectDictionary ;
};func (_cfd *fdfParser )parseFdfVersion ()(int ,int ,error ){_cfd ._egc .Seek (0,_dd .SeekStart );_ade :=20;_aeec :=make ([]byte ,_ade );_cfd ._egc .Read (_aeec );_gceb :=_dab .FindStringSubmatch (string (_aeec ));if len (_gceb )< 3{_bbd ,_ecf ,_gfc :=_cfd .seekFdfVersionTopDown ();
if _gfc !=nil {_c .Log .Debug ("F\u0061\u0069\u006c\u0065\u0064\u0020\u0072\u0065\u0063\u006f\u0076\u0065\u0072\u0079\u0020\u002d\u0020\u0075n\u0061\u0062\u006c\u0065\u0020\u0074\u006f\u0020\u0066\u0069nd\u0020\u0076\u0065r\u0073i\u006f\u006e");return 0,0,_gfc ;
};return _bbd ,_ecf ,nil ;};_fecg ,_fea :=_g .Atoi (_gceb [1]);if _fea !=nil {return 0,0,_fea ;};_fecd ,_fea :=_g .Atoi (_gceb [2]);if _fea !=nil {return 0,0,_fea ;};_c .Log .Debug ("\u0046\u0064\u0066\u0020\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u0020%\u0064\u002e\u0025\u0064",_fecg ,_fecd );
return _fecg ,_fecd ,nil ;};var _eg =_ag .MustCompile ("\u005e\u005b\u005c\u002b\u002d\u002e\u005d\u002a\u0028\u005b\u0030\u002d9\u002e\u005d\u002b\u0029");func (_fgf *fdfParser )parseIndirectObject ()(_fa .PdfObject ,error ){_bead :=_fa .PdfIndirectObject {};
_c .Log .Trace ("\u002dR\u0065a\u0064\u0020\u0069\u006e\u0064i\u0072\u0065c\u0074\u0020\u006f\u0062\u006a");_bcf ,_dafb :=_fgf ._eec .Peek (20);if _dafb !=nil {_c .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0046\u0061\u0069\u006c\u0020\u0074\u006f\u0020r\u0065a\u0064\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a");
return &_bead ,_dafb ;};_c .Log .Trace ("\u0028\u0069\u006edi\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0020\u0070\u0065\u0065\u006b\u0020\u0022\u0025\u0073\u0022",string (_bcf ));_eagg :=_fg .FindStringSubmatchIndex (string (_bcf ));if len (_eagg )< 6{_c .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0061\u0062l\u0065\u0020\u0074\u006f \u0066\u0069\u006e\u0064\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u0020\u0028\u0025\u0073\u0029",string (_bcf ));
return &_bead ,_e .New ("\u0075\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0064\u0065\u0074\u0065\u0063\u0074\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020s\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065");
};_fgf ._eec .Discard (_eagg [0]);_c .Log .Trace ("O\u0066\u0066\u0073\u0065\u0074\u0073\u0020\u0025\u0020\u0064",_eagg );_cec :=_eagg [1]-_eagg [0];_acf :=make ([]byte ,_cec );_ ,_dafb =_fgf .readAtLeast (_acf ,_cec );if _dafb !=nil {_c .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0075\u006e\u0061\u0062l\u0065\u0020\u0074\u006f\u0020\u0072\u0065\u0061\u0064\u0020-\u0020\u0025\u0073",_dafb );
return nil ,_dafb ;};_c .Log .Trace ("\u0074\u0065\u0078t\u006c\u0069\u006e\u0065\u003a\u0020\u0025\u0073",_acf );_fecgb :=_fg .FindStringSubmatch (string (_acf ));if len (_fecgb )< 3{_c .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0061\u0062l\u0065\u0020\u0074\u006f \u0066\u0069\u006e\u0064\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u0020\u0028\u0025\u0073\u0029",string (_acf ));
return &_bead ,_e .New ("\u0075\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0064\u0065\u0074\u0065\u0063\u0074\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020s\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065");
};_add ,_ :=_g .Atoi (_fecgb [1]);_ceb ,_ :=_g .Atoi (_fecgb [2]);_bead .ObjectNumber =int64 (_add );_bead .GenerationNumber =int64 (_ceb );for {_cab ,_abc :=_fgf ._eec .Peek (2);if _abc !=nil {return &_bead ,_abc ;};_c .Log .Trace ("I\u006ed\u002e\u0020\u0070\u0065\u0065\u006b\u003a\u0020%\u0073\u0020\u0028\u0025 x\u0029\u0021",string (_cab ),string (_cab ));
if _fa .IsWhiteSpace (_cab [0]){_fgf .skipSpaces ();}else if _cab [0]=='%'{_fgf .skipComments ();}else if (_cab [0]=='<')&&(_cab [1]=='<'){_c .Log .Trace ("\u0043\u0061\u006c\u006c\u0020\u0050\u0061\u0072\u0073e\u0044\u0069\u0063\u0074");_bead .PdfObject ,_abc =_fgf .parseDict ();
_c .Log .Trace ("\u0045\u004f\u0046\u0020Ca\u006c\u006c\u0020\u0050\u0061\u0072\u0073\u0065\u0044\u0069\u0063\u0074\u003a\u0020%\u0076",_abc );if _abc !=nil {return &_bead ,_abc ;};_c .Log .Trace ("\u0050\u0061\u0072\u0073\u0065\u0064\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002e.\u002e\u0020\u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u002e");
}else if (_cab [0]=='/')||(_cab [0]=='(')||(_cab [0]=='[')||(_cab [0]=='<'){_bead .PdfObject ,_abc =_fgf .parseObject ();if _abc !=nil {return &_bead ,_abc ;};_c .Log .Trace ("P\u0061\u0072\u0073\u0065\u0064\u0020o\u0062\u006a\u0065\u0063\u0074\u0020\u002e\u002e\u002e \u0066\u0069\u006ei\u0073h\u0065\u0064\u002e");
}else {if _cab [0]=='e'{_afd ,_cabc :=_fgf .readTextLine ();if _cabc !=nil {return nil ,_cabc ;};if len (_afd )>=6&&_afd [0:6]=="\u0065\u006e\u0064\u006f\u0062\u006a"{break ;};}else if _cab [0]=='s'{_cab ,_ =_fgf ._eec .Peek (10);if string (_cab [:6])=="\u0073\u0074\u0072\u0065\u0061\u006d"{_cfe :=6;
if len (_cab )> 6{if _fa .IsWhiteSpace (_cab [_cfe ])&&_cab [_cfe ]!='\r'&&_cab [_cfe ]!='\n'{_c .Log .Debug ("\u004e\u006fn\u002d\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0074\u0020\u0046\u0044\u0046\u0020\u006e\u006f\u0074 \u0065\u006e\u0064\u0069\u006e\u0067 \u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006c\u0069\u006e\u0065\u0020\u0070\u0072o\u0070\u0065r\u006c\u0079\u0020\u0077i\u0074\u0068\u0020\u0045\u004fL\u0020\u006d\u0061\u0072\u006b\u0065\u0072");
_cfe ++;};if _cab [_cfe ]=='\r'{_cfe ++;if _cab [_cfe ]=='\n'{_cfe ++;};}else if _cab [_cfe ]=='\n'{_cfe ++;};};_fgf ._eec .Discard (_cfe );_fabf ,_gfa :=_bead .PdfObject .(*_fa .PdfObjectDictionary );if !_gfa {return nil ,_e .New ("\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u006di\u0073s\u0069\u006e\u0067\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079");
};_c .Log .Trace ("\u0053\u0074\u0072\u0065\u0061\u006d\u0020\u0064\u0069c\u0074\u0020\u0025\u0073",_fabf );_eae ,_fgb :=_fabf .Get ("\u004c\u0065\u006e\u0067\u0074\u0068").(*_fa .PdfObjectInteger );if !_fgb {return nil ,_e .New ("\u0073\u0074re\u0061\u006d\u0020l\u0065\u006e\u0067\u0074h n\u0065ed\u0073\u0020\u0074\u006f\u0020\u0062\u0065 a\u006e\u0020\u0069\u006e\u0074\u0065\u0067e\u0072");
};_egcb :=*_eae ;if _egcb < 0{return nil ,_e .New ("\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006e\u0065\u0065\u0064\u0073\u0020\u0074\u006f \u0062e\u0020\u006c\u006f\u006e\u0067\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0030");};if int64 (_egcb )> _fgf ._abg {_c .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0053t\u0072\u0065\u0061\u006d\u0020l\u0065\u006e\u0067\u0074\u0068\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u006c\u0061\u0072\u0067\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0069\u007a\u0065");
return nil ,_e .New ("\u0069n\u0076\u0061l\u0069\u0064\u0020\u0073t\u0072\u0065\u0061m\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u002c\u0020la\u0072\u0067\u0065r\u0020\u0074h\u0061\u006e\u0020\u0066\u0069\u006ce\u0020\u0073i\u007a\u0065");};_ebg :=make ([]byte ,_egcb );
_ ,_abc =_fgf .readAtLeast (_ebg ,int (_egcb ));if _abc !=nil {_c .Log .Debug ("E\u0052\u0052\u004f\u0052 s\u0074r\u0065\u0061\u006d\u0020\u0028%\u0064\u0029\u003a\u0020\u0025\u0058",len (_ebg ),_ebg );_c .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_abc );
return nil ,_abc ;};_gfgf :=_fa .PdfObjectStream {};_gfgf .Stream =_ebg ;_gfgf .PdfObjectDictionary =_bead .PdfObject .(*_fa .PdfObjectDictionary );_gfgf .ObjectNumber =_bead .ObjectNumber ;_gfgf .GenerationNumber =_bead .GenerationNumber ;_fgf .skipSpaces ();
_fgf ._eec .Discard (9);_fgf .skipSpaces ();return &_gfgf ,nil ;};};_bead .PdfObject ,_abc =_fgf .parseObject ();return &_bead ,_abc ;};};_c .Log .Trace ("\u0052\u0065\u0074\u0075rn\u0069\u006e\u0067\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0021");
return &_bead ,nil ;};func _eea (_eeb string )(_fa .PdfObjectReference ,error ){_fec :=_fa .PdfObjectReference {};_geeb :=_gae .FindStringSubmatch (_eeb );if len (_geeb )< 3{_c .Log .Debug ("\u0045\u0072\u0072or\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065");
return _fec ,_e .New ("\u0075n\u0061\u0062\u006c\u0065 \u0074\u006f\u0020\u0070\u0061r\u0073e\u0020r\u0065\u0066\u0065\u0072\u0065\u006e\u0063e");};_dead ,_dcgd :=_g .Atoi (_geeb [1]);if _dcgd !=nil {_c .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0070a\u0072\u0073\u0069n\u0067\u0020\u006fb\u006a\u0065c\u0074\u0020\u006e\u0075\u006d\u0062e\u0072 '\u0025\u0073\u0027\u0020\u002d\u0020\u0055\u0073\u0069\u006e\u0067\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u006e\u0075\u006d\u0020\u003d\u0020\u0030",_geeb [1]);
return _fec ,nil ;};_fec .ObjectNumber =int64 (_dead );_fegc ,_dcgd :=_g .Atoi (_geeb [2]);if _dcgd !=nil {_c .Log .Debug ("\u0045\u0072r\u006f\u0072\u0020\u0070\u0061r\u0073\u0069\u006e\u0067\u0020g\u0065\u006e\u0065\u0072\u0061\u0074\u0069\u006f\u006e\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0027\u0025\u0073\u0027\u0020\u002d\u0020\u0055\u0073\u0069\u006e\u0067\u0020\u0067\u0065\u006e\u0020\u003d\u0020\u0030",_geeb [2]);
return _fec ,nil ;};_fec .GenerationNumber =int64 (_fegc );return _fec ,nil ;};

// FieldValues implements interface model.FieldValueProvider.
// Returns a map of field names to values (PdfObjects).
func (fdf *Data )FieldValues ()(map[string ]_fa .PdfObject ,error ){_bag ,_df :=fdf .FieldDictionaries ();if _df !=nil {return nil ,_df ;};var _dc []string ;for _def :=range _bag {_dc =append (_dc ,_def );};_b .Strings (_dc );_ee :=map[string ]_fa .PdfObject {};
for _ ,_bbe :=range _dc {_ed :=_bag [_bbe ];_da :=_fa .TraceToDirectObject (_ed .Get ("\u0056"));_ee [_bbe ]=_da ;};return _ee ,nil ;};func (_dbd *fdfParser )trace (_dbgg _fa .PdfObject )_fa .PdfObject {switch _fcgd :=_dbgg .(type ){case *_fa .PdfObjectReference :_ebc ,_cda :=_dbd ._dba [_fcgd .ObjectNumber ].(*_fa .PdfIndirectObject );
if _cda {return _ebc .PdfObject ;};_c .Log .Debug ("\u0054\u0079\u0070\u0065\u0020\u0065\u0072\u0072\u006f\u0072");return nil ;case *_fa .PdfIndirectObject :return _fcgd .PdfObject ;};return _dbgg ;};func (_gec *fdfParser )readAtLeast (_bg []byte ,_gee int )(int ,error ){_dcg :=_gee ;
_geg :=0;_afa :=0;for _dcg > 0{_gf ,_fcb :=_gec ._eec .Read (_bg [_geg :]);if _fcb !=nil {_c .Log .Debug ("\u0045\u0052\u0052O\u0052\u0020\u0046\u0061i\u006c\u0065\u0064\u0020\u0072\u0065\u0061d\u0069\u006e\u0067\u0020\u0028\u0025\u0064\u003b\u0025\u0064\u0029\u0020\u0025\u0073",_gf ,_afa ,_fcb .Error ());
return _geg ,_e .New ("\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0072\u0065a\u0064\u0069\u006e\u0067");};_afa ++;_geg +=_gf ;_dcg -=_gf ;};return _geg ,nil ;};func (_gcb *fdfParser )parse ()error {_gcb ._egc .Seek (0,_dd .SeekStart );_gcb ._eec =_gb .NewReader (_gcb ._egc );
for {_gcb .skipComments ();_ccc ,_abf :=_gcb ._eec .Peek (20);if _abf !=nil {_c .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0046\u0061\u0069\u006c\u0020\u0074\u006f\u0020r\u0065a\u0064\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a");
return _abf ;};if _a .HasPrefix (string (_ccc ),"\u0074r\u0061\u0069\u006c\u0065\u0072"){_gcb ._eec .Discard (7);_gcb .skipSpaces ();_gcb .skipComments ();_edd ,_ :=_gcb .parseDict ();_gcb ._gge =_edd ;break ;};_adf :=_fg .FindStringSubmatchIndex (string (_ccc ));
if len (_adf )< 6{_c .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0061\u0062l\u0065\u0020\u0074\u006f \u0066\u0069\u006e\u0064\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u0020\u0028\u0025\u0073\u0029",string (_ccc ));
return _e .New ("\u0075\u006e\u0061b\u006c\u0065\u0020\u0074\u006f\u0020\u0064\u0065\u0074\u0065\u0063\u0074\u0020\u0069\u006e\u0064\u0069\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020s\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065");
};_ecdb ,_abf :=_gcb .parseIndirectObject ();if _abf !=nil {return _abf ;};switch _ded :=_ecdb .(type ){case *_fa .PdfIndirectObject :_gcb ._dba [_ded .ObjectNumber ]=_ded ;case *_fa .PdfObjectStream :_gcb ._dba [_ded .ObjectNumber ]=_ded ;default:return _e .New ("\u0074\u0079\u0070\u0065\u0020\u0065\u0072\u0072\u006f\u0072");
};};return nil ;};

// Load loads FDF form data from `r`.
func Load (r _dd .ReadSeeker )(*Data ,error ){_de ,_db :=_fac (r );if _db !=nil {return nil ,_db ;};_ab ,_db :=_de .Root ();if _db !=nil {return nil ,_db ;};_be ,_ddc :=_fa .GetArray (_ab .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));if !_ddc {return nil ,_e .New ("\u0066\u0069\u0065\u006c\u0064\u0073\u0020\u006d\u0069s\u0073\u0069\u006e\u0067");
};return &Data {_ce :_be ,_bb :_ab },nil ;};func (_edc *fdfParser )readComment ()(string ,error ){var _acd _ge .Buffer ;_ ,_ae :=_edc .skipSpaces ();if _ae !=nil {return _acd .String (),_ae ;};_gac :=true ;for {_bc ,_fff :=_edc ._eec .Peek (1);if _fff !=nil {_c .Log .Debug ("\u0045\u0072\u0072\u006f\u0072\u0020\u0025\u0073",_fff .Error ());
return _acd .String (),_fff ;};if _gac &&_bc [0]!='%'{return _acd .String (),_e .New ("c\u006f\u006d\u006d\u0065\u006e\u0074 \u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0073\u0074a\u0072\u0074\u0020w\u0069t\u0068\u0020\u0025");};_gac =false ;if (_bc [0]!='\r')&&(_bc [0]!='\n'){_efd ,_ :=_edc ._eec .ReadByte ();
_acd .WriteByte (_efd );}else {break ;};};return _acd .String (),nil ;};func (_dbc *fdfParser )parseArray ()(*_fa .PdfObjectArray ,error ){_edbc :=_fa .MakeArray ();_dbc ._eec .ReadByte ();for {_dbc .skipSpaces ();_gacd ,_gcg :=_dbc ._eec .Peek (1);if _gcg !=nil {return _edbc ,_gcg ;
};if _gacd [0]==']'{_dbc ._eec .ReadByte ();break ;};_ceg ,_gcg :=_dbc .parseObject ();if _gcg !=nil {return _edbc ,_gcg ;};_edbc .Append (_ceg );};return _edbc ,nil ;};var _dab =_ag .MustCompile ("\u0025F\u0044F\u002d\u0028\u005c\u0064\u0029\u005c\u002e\u0028\u005c\u0064\u0029");
