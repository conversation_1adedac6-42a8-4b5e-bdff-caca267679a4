//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package pdfutil ;import (_c "errors";_ca "github.com/unidoc/unipdf/v4/common";_g "github.com/unidoc/unipdf/v4/contentstream";_ag "github.com/unidoc/unipdf/v4/contentstream/draw";_b "github.com/unidoc/unipdf/v4/core";_cf "github.com/unidoc/unipdf/v4/model";
_e "github.com/unidoc/unipdf/v4/ps";);func _ge (_ed string ,_be *_cf .PdfPageResources )([]byte ,error ){_gd :=_g .NewContentStreamParser (_ed );_fd ,_d :=_gd .Parse ();if _d !=nil {return nil ,_d ;};_gb :=&_g .ContentStreamOperations {};_ga :=map[_b .PdfObjectName ]bool {};
_eg :=map[_b .PdfObjectName ]bool {};_ac :=_g .NewContentStreamProcessor (*_fd );_ac .AddHandler (_g .HandlerConditionEnumAllOperands ,"",func (_ce *_g .ContentStreamOperation ,_ad _g .GraphicsState ,_gc *_cf .PdfPageResources )error {_fc :=_ce .Operand ;
switch _fc {case "\u0043\u0053":if _fe (_ad .ColorspaceStroking ){_dc :=_ce .Params [0].(*_b .PdfObjectName );if *_dc !="\u0050a\u0074\u0074\u0065\u0072\u006e"{_fb ,_dg :=_gc .GetColorspaceByName (*_dc );if !_dg {return _c .New ("\u0043\u006f\u006c\u006frs\u0070\u0061\u0063\u0065\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006ee\u0064");
};_aa ,_dg :=_fb .(*_cf .PdfColorspaceSpecialPattern );if !_dg {return _c .New ("\u0054\u0079\u0070\u0065\u0020\u0065\u0072\u0072\u006f\u0072");};if _aa .UnderlyingCS !=nil {_aa .UnderlyingCS =_cf .NewPdfColorspaceDeviceGray ();};_d =_gc .SetColorspaceByName (*_dc ,_aa );
if _d !=nil {return _d ;};};*_gb =append (*_gb ,_ce );return nil ;};_ab :=_g .ContentStreamOperation {};_ab .Operand =_fc ;_ab .Params =[]_b .PdfObject {_b .MakeName ("\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079")};*_gb =append (*_gb ,&_ab );
return nil ;case "\u0063\u0073":if _fe (_ad .ColorspaceNonStroking ){_gf :=_ce .Params [0].(*_b .PdfObjectName );if *_gf !="\u0050a\u0074\u0074\u0065\u0072\u006e"{_dcc ,_gg :=_gc .GetColorspaceByName (*_gf );if !_gg {return _c .New ("\u0043\u006f\u006c\u006frs\u0070\u0061\u0063\u0065\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006ee\u0064");
};_eb ,_gg :=_dcc .(*_cf .PdfColorspaceSpecialPattern );if !_gg {return _c .New ("\u0054\u0079\u0070\u0065\u0020\u0065\u0072\u0072\u006f\u0072");};if _eb .UnderlyingCS !=nil {_eb .UnderlyingCS =_cf .NewPdfColorspaceDeviceGray ();};if _aca :=_gc .SetColorspaceByName (*_gf ,_eb );
_aca !=nil {return _aca ;};};*_gb =append (*_gb ,_ce );return nil ;};_dd :=_g .ContentStreamOperation {};_dd .Operand =_fc ;_dd .Params =[]_b .PdfObject {_b .MakeName ("\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079")};*_gb =append (*_gb ,&_dd );
return nil ;case "\u0053\u0043","\u0053\u0043\u004e":if _fe (_ad .ColorspaceStroking ){_db :=_g .ContentStreamOperation {};_db .Operand =_fc ;_db .Params =[]_b .PdfObject {};_fcf ,_df :=_ad .ColorStroking .(*_cf .PdfColorPattern );if !_df {return _c .New ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0073\u0074\u0072\u006f\u006b\u0069\u006e\u0067\u0020c\u006f\u006c\u006fr\u0020t\u0079\u0070\u0065");
};if _fcf .Color !=nil {_gge ,_cg :=_ad .ColorspaceStroking .ColorToRGB (_fcf .Color );if _cg !=nil {return _cg ;};_fg :=_gge .(*_cf .PdfColorDeviceRGB );_fbd :=_fg .ToGray ();_db .Params =append (_db .Params ,_b .MakeFloat (_fbd .Val ()));};if _ ,_ef :=_ga [_fcf .PatternName ];
_ef {_db .Params =append (_db .Params ,_b .MakeName (string (_fcf .PatternName )));*_gb =append (*_gb ,&_db );return nil ;};_ga [_fcf .PatternName ]=true ;_eff ,_fgf :=_gc .GetPatternByName (_fcf .PatternName );if !_fgf {return _c .New ("\u0055\u006e\u0064\u0065fi\u006e\u0065\u0064\u0020\u0070\u0061\u0074\u0074\u0065\u0072\u006e\u0020\u006e\u0061m\u0065");
};_agb ,_egb :=_ddbc (_eff );if _egb !=nil {return _egb ;};_egb =_gc .SetPatternByName (_fcf .PatternName ,_agb .ToPdfObject ());if _egb !=nil {return _egb ;};_db .Params =append (_db .Params ,_b .MakeName (string (_fcf .PatternName )));*_gb =append (*_gb ,&_db );
}else {_cfd ,_gcc :=_ad .ColorspaceStroking .ColorToRGB (_ad .ColorStroking );if _gcc !=nil {return _gcc ;};_ddc :=_cfd .(*_cf .PdfColorDeviceRGB );_ba :=_ddc .ToGray ();_eba :=_g .ContentStreamOperation {};_eba .Operand =_fc ;_eba .Params =[]_b .PdfObject {_b .MakeFloat (_ba .Val ())};
*_gb =append (*_gb ,&_eba );};return nil ;case "\u0073\u0063","\u0073\u0063\u006e":if _fe (_ad .ColorspaceNonStroking ){_cc :=_g .ContentStreamOperation {};_cc .Operand =_fc ;_cc .Params =[]_b .PdfObject {};_fed ,_edb :=_ad .ColorNonStroking .(*_cf .PdfColorPattern );
if !_edb {return _c .New ("I\u006e\u0076\u0061\u006c\u0069\u0064 \u0073\u0074\u0072\u006f\u006b\u0069\u006e\u0067\u0020c\u006f\u006c\u006fr\u0020t\u0079\u0070\u0065");};if _fed .Color !=nil {_ebc ,_baa :=_ad .ColorspaceNonStroking .ColorToRGB (_fed .Color );
if _baa !=nil {return _baa ;};_bd :=_ebc .(*_cf .PdfColorDeviceRGB );_adf :=_bd .ToGray ();_cc .Params =append (_cc .Params ,_b .MakeFloat (_adf .Val ()));};if _ ,_ddb :=_ga [_fed .PatternName ];_ddb {_cc .Params =append (_cc .Params ,_b .MakeName (string (_fed .PatternName )));
*_gb =append (*_gb ,&_cc );return nil ;};_ga [_fed .PatternName ]=true ;_edf ,_dbb :=_gc .GetPatternByName (_fed .PatternName );if !_dbb {return _c .New ("\u0055\u006e\u0064\u0065fi\u006e\u0065\u0064\u0020\u0070\u0061\u0074\u0074\u0065\u0072\u006e\u0020\u006e\u0061m\u0065");
};_gcg ,_gee :=_ddbc (_edf );if _gee !=nil {return _gee ;};_gee =_gc .SetPatternByName (_fed .PatternName ,_gcg .ToPdfObject ());if _gee !=nil {return _gee ;};_cc .Params =append (_cc .Params ,_b .MakeName (string (_fed .PatternName )));*_gb =append (*_gb ,&_cc );
}else {_dcce ,_bef :=_ad .ColorspaceNonStroking .ColorToRGB (_ad .ColorNonStroking );if _bef !=nil {return _bef ;};_cb :=_dcce .(*_cf .PdfColorDeviceRGB );_de :=_cb .ToGray ();_ega :=_g .ContentStreamOperation {};_ega .Operand =_fc ;_ega .Params =[]_b .PdfObject {_b .MakeFloat (_de .Val ())};
*_gb =append (*_gb ,&_ega );};return nil ;case "\u0052\u0047","\u004b":_ebd ,_geb :=_ad .ColorspaceStroking .ColorToRGB (_ad .ColorStroking );if _geb !=nil {return _geb ;};_bc :=_ebd .(*_cf .PdfColorDeviceRGB );_eda :=_bc .ToGray ();_cce :=_g .ContentStreamOperation {};
_cce .Operand ="\u0047";_cce .Params =[]_b .PdfObject {_b .MakeFloat (_eda .Val ())};*_gb =append (*_gb ,&_cce );return nil ;case "\u0072\u0067","\u006b":_dbd ,_aac :=_ad .ColorspaceNonStroking .ColorToRGB (_ad .ColorNonStroking );if _aac !=nil {return _aac ;
};_ebb :=_dbd .(*_cf .PdfColorDeviceRGB );_fff :=_ebb .ToGray ();_gebg :=_g .ContentStreamOperation {};_gebg .Operand ="\u0067";_gebg .Params =[]_b .PdfObject {_b .MakeFloat (_fff .Val ())};*_gb =append (*_gb ,&_gebg );return nil ;case "\u0073\u0068":if len (_ce .Params )!=1{return _c .New ("\u0050\u0061\u0072\u0061\u006d\u0073 \u0074\u006f\u0020\u0073\u0068\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006fr\u0020\u0073\u0068\u006f\u0075\u006c\u0064 \u0062\u0065\u0020\u0031");
};_ae ,_beg :=_ce .Params [0].(*_b .PdfObjectName );if !_beg {return _c .New ("\u0073\u0068 \u0070\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0061\u0020na\u006d\u0065");};if _ ,_ccb :=_eg [*_ae ];
_ccb {*_gb =append (*_gb ,_ce );return nil ;};_eg [*_ae ]=true ;_cafa ,_gebd :=_gc .GetShadingByName (*_ae );if !_gebd {return _c .New ("\u0053\u0068\u0061\u0064\u0069\u006e\u0067\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066i\u006ee\u0064\u0020\u0069\u006e\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0073");
};_ggf ,_ggfe :=_agg (_cafa );if _ggfe !=nil {return _ggfe ;};_ggfe =_gc .SetShadingByName (*_ae ,_ggf .GetContext ().ToPdfObject ());if _ggfe !=nil {return _ggfe ;};};*_gb =append (*_gb ,_ce );return nil ;});_ac .AddHandler (_g .HandlerConditionEnumOperand ,"\u0042\u0049",func (_cbd *_g .ContentStreamOperation ,_acb _g .GraphicsState ,_acc *_cf .PdfPageResources )error {if len (_cbd .Params )!=1{return _c .New ("\u0069\u006e\u0076\u0061l\u0069\u0064\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020o\u0066 \u0070\u0061\u0072\u0061\u006d\u0065\u0074e\u0072\u0073");
};_agf ,_edfa :=_cbd .Params [0].(*_g .ContentStreamInlineImage );if !_edfa {return _c .New ("\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006el\u0069\u006e\u0065\u0020\u0069\u006d\u0061g\u0065\u0020\u0070\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072");
};_ec ,_fbe :=_agf .ToImage (_acc );if _fbe !=nil {return _fbe ;};_def ,_fbe :=_agf .GetColorSpace (_acc );if _fbe !=nil {return _fbe ;};_gea ,_fbe :=_def .ImageToRGB (*_ec );if _fbe !=nil {return _fbe ;};_eca :=_cf .NewPdfColorspaceDeviceRGB ();_gce ,_fbe :=_eca .ImageToGray (_gea );
if _fbe !=nil {return _fbe ;};_ecd ,_fbe :=_agf .GetEncoder ();if _fbe !=nil {return _fbe ;};if _egaa ,_gcca :=_ecd .(*_b .DCTEncoder );_gcca {_egaa .ColorComponents =1;};_bed ,_fbe :=_g .NewInlineImageFromImage (_gce ,_ecd );if _fbe !=nil {if _fbe ==_b .ErrUnsupportedEncodingParameters {_ecd =_b .NewFlateEncoder ();
};_bed ,_fbe =_g .NewInlineImageFromImage (_gce ,_ecd );if _fbe !=nil {return _fbe ;};};_cgd :=_g .ContentStreamOperation {};_cgd .Operand ="\u0042\u0049";_cgd .Params =[]_b .PdfObject {_bed };*_gb =append (*_gb ,&_cgd );return nil ;});_cd :=map[string ]bool {};
_ac .AddHandler (_g .HandlerConditionEnumOperand ,"\u0044\u006f",func (_defa *_g .ContentStreamOperation ,_edbb _g .GraphicsState ,_ee *_cf .PdfPageResources )error {if len (_defa .Params )< 1{return _c .New ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0070\u0061\u0072\u0061\u006ds\u0020\u0066\u006f\u0072\u0020\u0044\u006f\u0020\u006f\u0062\u006a\u0065\u0063t\u003a\u0020\u0052\u0061\u006e\u0067e\u0020\u0063\u0068e\u0063\u006b");
};_cbdb :=_defa .Params [0].(*_b .PdfObjectName );_ ,_cea :=_cd [string (*_cbdb )];if _cea {return nil ;};_cd [string (*_cbdb )]=true ;_ ,_dff :=_ee .GetXObjectByName (*_cbdb );if _dff ==_cf .XObjectTypeImage {_fbg ,_fef :=_ee .GetXObjectImageByName (*_cbdb );
if _fef !=nil {return _fef ;};_fee ,_fef :=_fbg .ToImage ();if _fef !=nil {return _fef ;};_da ,_fef :=_fbg .ColorSpace .ImageToRGB (*_fee );if _fef !=nil {return _fef ;};_bg :=_cf .NewPdfColorspaceDeviceRGB ();_egbc ,_fef :=_bg .ImageToGray (_da );if _fef !=nil {return _fef ;
};_cgdg :=_fbg .Filter ;if _gac ,_egf :=_cgdg .(*_b .DCTEncoder );_egf {_gac .ColorComponents =1;};_ecg ,_fef :=_cf .NewXObjectImageFromImage (&_egbc ,nil ,_cgdg );if _fef !=nil {if _fef ==_b .ErrUnsupportedEncodingParameters {_cgdg =_b .NewFlateEncoder ();
};_ecg ,_fef =_cf .NewXObjectImageFromImage (&_egbc ,nil ,_cgdg );if _fef !=nil {return _fef ;};};_fef =_ee .SetXObjectImageByName (*_cbdb ,_ecg );if _fef !=nil {return _fef ;};}else if _dff ==_cf .XObjectTypeForm {_cff ,_dgg :=_ee .GetXObjectFormByName (*_cbdb );
if _dgg !=nil {return _dgg ;};_afa ,_dgg :=_cff .GetContentStream ();if _dgg !=nil {return _dgg ;};_fce :=_cff .Resources ;if _fce ==nil {_fce =_ee ;};_feb ,_dgg :=_ge (string (_afa ),_fce );if _dgg !=nil {return _dgg ;};_dgg =_cff .SetContentStream (_feb ,nil );
if _dgg !=nil {return _dgg ;};_dgg =_ee .SetXObjectFormByName (*_cbdb ,_cff );if _dgg !=nil {return _dgg ;};};return nil ;});_d =_ac .Process (_be );if _d !=nil {return nil ,_d ;};return _gb .Bytes (),nil ;};

// ConvertPageToGrayscale() replaces color objects on the page with grayscale ones.  Also references XObject Images and Forms
// to convert those to grayscale.
func ConvertPageToGrayscale (page *_cf .PdfPage )error {_ea ,_eab :=page .GetAllContentStreams ();if _eab !=nil {return _eab ;};_f ,_eab :=_ge (_ea ,page .Resources );if _eab !=nil {return _eab ;};_eab =page .SetContentStreams ([]string {string (_f )},_b .NewFlateEncoder ());
if _eab !=nil {return _eab ;};return nil ;};

// NormalizePage performs the following operations on the passed in page:
//   - Normalize the page rotation.
//     Rotates the contents of the page according to the Rotate entry, thus
//     flattening the rotation. The Rotate entry of the page is set to nil.
//   - Normalize the media box.
//     If the media box of the page is offsetted (Llx != 0 or Lly != 0),
//     the contents of the page are translated to (-Llx, -Lly). After
//     normalization, the media box is updated (Llx and Lly are set to 0 and
//     Urx and Ury are updated accordingly).
//   - Normalize the crop box.
//     The crop box of the page is updated based on the previous operations.
//
// After normalization, the page should look the same if openend using a
// PDF viewer.
// NOTE: This function does not normalize annotations, outlines other parts
// that are not part of the basic geometry and page content streams.
func NormalizePage (page *_cf .PdfPage )error {_ccf ,_eeb :=page .GetMediaBox ();if _eeb !=nil {return _eeb ;};_aaa ,_eeb :=page .GetRotate ();if _eeb !=nil {_ca .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0025\u0073\u0020\u002d\u0020\u0069\u0067\u006e\u006f\u0072\u0069\u006e\u0067\u0020\u0061\u006e\u0064\u0020\u0061\u0073\u0073\u0075\u006d\u0069\u006e\u0067\u0020\u006e\u006f\u0020\u0072\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u000a",_eeb .Error ());
};_fca :=_aaa %360!=0&&_aaa %90==0;_ccf .Normalize ();_bcf ,_ced ,_efb ,_bad :=_ccf .Llx ,_ccf .Lly ,_ccf .Width (),_ccf .Height ();_gde :=_bcf !=0||_ced !=0;if !_fca &&!_gde {return nil ;};_ecdf :=func (_ede ,_bbc ,_ddac float64 )_ag .BoundingBox {return _ag .Path {Points :[]_ag .Point {_ag .NewPoint (0,0).Rotate (_ddac ),_ag .NewPoint (_ede ,0).Rotate (_ddac ),_ag .NewPoint (0,_bbc ).Rotate (_ddac ),_ag .NewPoint (_ede ,_bbc ).Rotate (_ddac )}}.GetBoundingBox ();
};_ecde :=_g .NewContentCreator ();var _bdc float64 ;if _fca {_bdc =-float64 (_aaa );_fgfe :=_ecdf (_efb ,_bad ,_bdc );_ecde .Translate ((_fgfe .Width -_efb )/2+_efb /2,(_fgfe .Height -_bad )/2+_bad /2);_ecde .RotateDeg (_bdc );_ecde .Translate (-_efb /2,-_bad /2);
_efb ,_bad =_fgfe .Width ,_fgfe .Height ;};if _gde {_ecde .Translate (-_bcf ,-_ced );};_fdf :=_ecde .Operations ();_gbb ,_eeb :=_b .MakeStream (_fdf .Bytes (),_b .NewFlateEncoder ());if _eeb !=nil {return _eeb ;};_fga :=_b .MakeArray (_gbb );_fga .Append (page .GetContentStreamObjs ()...);
*_ccf =_cf .PdfRectangle {Urx :_efb ,Ury :_bad };if _edfb :=page .CropBox ;_edfb !=nil {_edfb .Normalize ();_ecc ,_dbg ,_ebgb ,_ggeg :=_edfb .Llx -_bcf ,_edfb .Lly -_ced ,_edfb .Width (),_edfb .Height ();if _fca {_dbf :=_ecdf (_ebgb ,_ggeg ,_bdc );_ebgb ,_ggeg =_dbf .Width ,_dbf .Height ;
};*_edfb =_cf .PdfRectangle {Llx :_ecc ,Lly :_dbg ,Urx :_ecc +_ebgb ,Ury :_dbg +_ggeg };};_ca .Log .Debug ("\u0052\u006f\u0074\u0061\u0074\u0065\u003d\u0025\u0066\u00b0\u0020\u004f\u0070\u0073\u003d%\u0071 \u004d\u0065\u0064\u0069\u0061\u0042\u006f\u0078\u003d\u0025\u002e\u0032\u0066",_bdc ,_fdf ,_ccf );
page .Contents =_fga ;page .Rotate =nil ;return nil ;};func _agg (_gcf *_cf .PdfShading )(*_cf .PdfShading ,error ){_edad :=_gcf .ColorSpace ;if _edad .GetNumComponents ()==1{return _gcf ,nil ;}else if _edad .GetNumComponents ()==3{_dfb :=&_cf .PdfFunctionType4 {};
_dfb .Domain =[]float64 {0,1,0,1,0,1};_dfb .Range =[]float64 {0,1};_fbf :=_e .NewPSProgram ();_fbf .Append (_e .MakeReal (0.11));_fbf .Append (_e .MakeOperand ("\u006d\u0075\u006c"));_fbf .Append (_e .MakeOperand ("\u0065\u0078\u0063\u0068"));_fbf .Append (_e .MakeReal (0.59));
_fbf .Append (_e .MakeOperand ("\u006d\u0075\u006c"));_fbf .Append (_e .MakeOperand ("\u0061\u0064\u0064"));_fbf .Append (_e .MakeOperand ("\u0065\u0078\u0063\u0068"));_fbf .Append (_e .MakeReal (0.3));_fbf .Append (_e .MakeOperand ("\u006d\u0075\u006c"));
_fbf .Append (_e .MakeOperand ("\u0061\u0064\u0064"));_dfb .Program =_fbf ;_dda :=_cf .NewPdfColorspaceDeviceN ();_dda .AlternateSpace =_cf .NewPdfColorspaceDeviceGray ();_dda .ColorantNames =_b .MakeArray (_b .MakeName ("\u0052"),_b .MakeName ("\u0047"),_b .MakeName ("\u0042"));
_dda .TintTransform =_dfb ;_gcf .ColorSpace =_dda ;return _gcf ,nil ;}else if _edad .GetNumComponents ()==4{_dce :=&_cf .PdfFunctionType4 {};_dce .Domain =[]float64 {0,1,0,1,0,1,0,1};_dce .Range =[]float64 {0,1};_abb :=_e .NewPSProgram ();_abb .Append (_e .MakeOperand ("\u0065\u0078\u0063\u0068"));
_abb .Append (_e .MakeReal (0.11));_abb .Append (_e .MakeOperand ("\u006d\u0075\u006c"));_abb .Append (_e .MakeOperand ("\u0061\u0064\u0064"));_abb .Append (_e .MakeOperand ("\u0065\u0078\u0063\u0068"));_abb .Append (_e .MakeReal (0.59));_abb .Append (_e .MakeOperand ("\u006d\u0075\u006c"));
_abb .Append (_e .MakeOperand ("\u0061\u0064\u0064"));_abb .Append (_e .MakeOperand ("\u0065\u0078\u0063\u0068"));_abb .Append (_e .MakeReal (0.30));_abb .Append (_e .MakeOperand ("\u006d\u0075\u006c"));_abb .Append (_e .MakeOperand ("\u0061\u0064\u0064"));
_abb .Append (_e .MakeOperand ("\u0064\u0075\u0070"));_abb .Append (_e .MakeReal (1.0));_abb .Append (_e .MakeOperand ("\u0067\u0065"));_ebe :=_e .NewPSProgram ();_ebe .Append (_e .MakeOperand ("\u0070\u006f\u0070"));_ebe .Append (_e .MakeReal (1.0));_abb .Append (_ebe );
_abb .Append (_e .MakeOperand ("\u0069\u0066"));_dce .Program =_abb ;_ccc :=_cf .NewPdfColorspaceDeviceN ();_ccc .AlternateSpace =_cf .NewPdfColorspaceDeviceGray ();_ccc .ColorantNames =_b .MakeArray (_b .MakeName ("\u0043"),_b .MakeName ("\u004d"),_b .MakeName ("\u0059"),_b .MakeName ("\u004b"));
_ccc .TintTransform =_dce ;_gcf .ColorSpace =_ccc ;return _gcf ,nil ;}else {return nil ,_c .New ("\u0055\u006e\u0073\u0075\u0070\u0070\u006fr\u0074\u0065\u0064 \u0070\u0061\u0074t\u0065\u0072n\u0020\u0063\u006f\u006c\u006f\u0072s\u0070ac\u0065\u0020\u0066\u006f\u0072\u0020\u0067\u0072\u0061\u0079\u0073\u0063\u0061\u006c\u0065\u0020\u0063\u006f\u006e\u0076\u0065\u0072\u0073\u0069\u006f\u006e");
};};func _fe (_af _cf .PdfColorspace )bool {_ ,_caf :=_af .(*_cf .PdfColorspaceSpecialPattern );return _caf };func _ddbc (_dee *_cf .PdfPattern )(*_cf .PdfPattern ,error ){if _dee .IsTiling (){_dab :=_dee .GetAsTilingPattern ();if _dab .IsColored (){_bb ,_aad :=_dab .GetContentStream ();
if _aad !=nil {return nil ,_aad ;};_ecf ,_aad :=_ge (string (_bb ),_dab .Resources );if _aad !=nil {return nil ,_aad ;};_aad =_dab .SetContentStream (_ecf ,nil );if _aad !=nil {return nil ,_aad ;};_ =_dab .ToPdfObject ();};}else if _dee .IsShading (){_ecfb :=_dee .GetAsShadingPattern ();
_defag ,_ebg :=_agg (_ecfb .Shading );if _ebg !=nil {return nil ,_ebg ;};_ecfb .Shading =_defag ;_ =_ecfb .ToPdfObject ();};return _dee ,nil ;};