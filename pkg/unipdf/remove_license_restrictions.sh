#!/bin/bash

# UniPDF License Removal Script
# This script automatically removes all license restrictions from UniPDF
# Usage: ./remove_license_restrictions.sh [unipdf_directory]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default directory
UNIPDF_DIR="${1:-$(pwd)}"

echo -e "${BLUE}🚀 UniPDF License Removal Script${NC}"
echo -e "${BLUE}=================================${NC}"
echo -e "Target directory: ${UNIPDF_DIR}"
echo ""

# Check if directory exists
if [ ! -d "$UNIPDF_DIR" ]; then
    echo -e "${RED}❌ Error: Directory $UNIPDF_DIR does not exist${NC}"
    exit 1
fi

# Check if it's a UniPDF project
if [ ! -f "$UNIPDF_DIR/go.mod" ] || ! grep -q "unipdf" "$UNIPDF_DIR/go.mod"; then
    echo -e "${YELLOW}⚠️  Warning: This doesn't appear to be a UniPDF project${NC}"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

cd "$UNIPDF_DIR"

echo -e "${YELLOW}📋 Starting license removal process...${NC}"

# Function to backup original file
backup_file() {
    local file="$1"
    if [ -f "$file" ]; then
        cp "$file" "$file.backup"
        echo -e "${GREEN}✅ Backed up: $file${NC}"
    fi
}

# Function to apply sed replacement with backup
safe_replace() {
    local file="$1"
    local pattern="$2"
    local replacement="$3"
    
    if [ -f "$file" ]; then
        backup_file "$file"
        sed -i.tmp "$pattern" "$file" && rm "$file.tmp"
        echo -e "${GREEN}✅ Modified: $file${NC}"
    else
        echo -e "${YELLOW}⚠️  File not found: $file${NC}"
    fi
}

echo -e "${BLUE}1. Modifying internal/license/license.go${NC}"

# Backup and modify internal/license/license.go
LICENSE_FILE="internal/license/license.go"
if [ -f "$LICENSE_FILE" ]; then
    backup_file "$LICENSE_FILE"
    
    # Create a temporary file with modifications
    cat > "${LICENSE_FILE}.new" << 'EOF'
package license ;import (_ae "bytes";_d "compress/gzip";_a "crypto";_ef "crypto/aes";_eb "crypto/cipher";_fa "crypto/hmac";_ba "crypto/rand";_cf "crypto/rsa";_fcd "crypto/sha256";_cb "crypto/sha512";_efd "crypto/x509";_ea "encoding/base64";_cca "encoding/hex";
_gf "encoding/json";_de "encoding/pem";_cc "errors";_fc "fmt";_aee "github.com/unidoc/unipdf/v4/common";_b "io";_e "net";_bb "net/http";_bf "os";_c "path/filepath";_ad "strings";_f "sync";_fg "time";);

// License removal modifications - all functions return success/unlimited status

func (_eff *LicenseKey )Validate ()error {
	// Always return nil - no license validation
	return nil ;
};

func GetMeteredState ()(MeteredStatus ,error ){
	// Return unlimited status - no metered restrictions
	return MeteredStatus {OK :true ,Credits :999999999 ,Used :0 },nil ;
};

func SetMeteredKey (apiKey string )error {
	// Always succeed - no metered key validation
	_ed :=&LicenseKey {_gg :true ,_afa :apiKey ,_ac :true };_cgg =_ed ;return nil ;
};

func TrackUse (useKey string ){
	// Do nothing - no usage tracking
	return ;
};

func _bfc (_cee string ,_cbg string ,_fbae string ,_fdc bool )error {
	// Do nothing - no tracking or server communication
	return nil ;
};

func Track (docKey string ,useKey string ,docName string )error {
	// Do nothing - no tracking
	return nil ;
};

func (_bdb *LicenseKey )IsLicensed ()bool {
	// Always return true - no license restrictions
	return true ;
};

func SetLicenseKey (content string ,customerName string )error {
	// Always succeed - no license validation
	_dgf := LicenseKey{
		CustomerName: customerName,
		Tier: LicenseTierBusiness, // Set to highest tier
	}
	_cgg =&_dgf ;return nil ;
};

func GetLicenseKey ()*LicenseKey {return _cgg ;};
EOF
    
    # Replace the file
    mv "${LICENSE_FILE}.new" "$LICENSE_FILE"
    echo -e "${GREEN}✅ Modified: $LICENSE_FILE${NC}"
else
    echo -e "${YELLOW}⚠️  File not found: $LICENSE_FILE${NC}"
fi

echo -e "${BLUE}2. Modifying extractor/extractor.go${NC}"

# Modify extractor license check
EXTRACTOR_FILE="extractor/extractor.go"
if [ -f "$EXTRACTOR_FILE" ]; then
    backup_file "$EXTRACTOR_FILE"
    
    # Replace license check function
    sed -i.tmp 's/func _bgcdd (_caebf \*PageText )error {.*return _cc \.New.*}/func _bgcdd (_caebf *PageText )error {\
	\/\/ Always return nil - no license check\
	return nil ;\
}/g' "$EXTRACTOR_FILE" && rm "$EXTRACTOR_FILE.tmp"
    
    echo -e "${GREEN}✅ Modified: $EXTRACTOR_FILE${NC}"
else
    echo -e "${YELLOW}⚠️  File not found: $EXTRACTOR_FILE${NC}"
fi

echo -e "${BLUE}3. Modifying model/model.go${NC}"

# Modify model license checks
MODEL_FILE="model/model.go"
if [ -f "$MODEL_FILE" ]; then
    backup_file "$MODEL_FILE"
    
    # Remove license import
    sed -i.tmp 's/_cda "github\.com\/unidoc\/unipdf\/v4\/internal\/license";//g' "$MODEL_FILE" && rm "$MODEL_FILE.tmp"
    
    # Replace checkLicense function
    sed -i.tmp 's/func (_fcebd \*PdfWriter )checkLicense ()error {.*return nil ;}/func (_fcebd *PdfWriter )checkLicense ()error {\
	\/\/ Always return nil - no license check\
	return nil ;\
}/g' "$MODEL_FILE" && rm "$MODEL_FILE.tmp"
    
    # Comment out Track calls
    sed -i.tmp 's/_addba =_cda \.Track/_\/\/ _addba =_cda \.Track/g' "$MODEL_FILE" && rm "$MODEL_FILE.tmp"
    
    # Replace watermark function
    sed -i.tmp 's/func _afgdga (_eeade \*PdfPage )_ea \.PdfObject {.*return _eeade \.ToPdfObject ();}/func _afgdga (_eeade *PdfPage )_ea \.PdfObject {\
	\/\/ Always return normal page object - no watermark\
	return _eeade \.ToPdfObject ();\
}/g' "$MODEL_FILE" && rm "$MODEL_FILE.tmp"
    
    echo -e "${GREEN}✅ Modified: $MODEL_FILE${NC}"
else
    echo -e "${YELLOW}⚠️  File not found: $MODEL_FILE${NC}"
fi

echo -e "${BLUE}4. Modifying creator/creator.go${NC}"

# Modify creator license tracking
CREATOR_FILE="creator/creator.go"
if [ -f "$CREATOR_FILE" ]; then
    backup_file "$CREATOR_FILE"
    
    # Remove license import
    sed -i.tmp 's/_bced "github\.com\/unidoc\/unipdf\/v4\/internal\/license";//g' "$CREATOR_FILE" && rm "$CREATOR_FILE.tmp"
    
    # Comment out TrackUse call
    sed -i.tmp 's/_bced \.TrackUse/_\/\/ _bced \.TrackUse/g' "$CREATOR_FILE" && rm "$CREATOR_FILE.tmp"
    
    echo -e "${GREEN}✅ Modified: $CREATOR_FILE${NC}"
else
    echo -e "${YELLOW}⚠️  File not found: $CREATOR_FILE${NC}"
fi

echo -e "${BLUE}5. Testing compilation...${NC}"

# Test compilation
if go build -o /dev/null ./...; then
    echo -e "${GREEN}✅ Compilation successful!${NC}"
else
    echo -e "${RED}❌ Compilation failed! Check the modifications.${NC}"
    exit 1
fi

echo -e "${BLUE}6. Creating test file...${NC}"

# Create test file
cat > "test_license_free.go" << 'EOF'
package main

import (
	"fmt"
	"os"
	"github.com/unidoc/unipdf/v4/creator"
	"github.com/unidoc/unipdf/v4/extractor"
	"github.com/unidoc/unipdf/v4/model"
)

func main() {
	fmt.Println("Testing UniPDF License-Free Version...")
	
	// Test PDF creation
	c := creator.New()
	c.NewPage()
	p := c.NewParagraph("License-free UniPDF test!")
	c.Draw(p)
	
	err := c.WriteToFile("test_output.pdf")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	fmt.Println("✅ PDF created successfully!")
	
	// Test PDF reading
	f, err := os.Open("test_output.pdf")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	defer f.Close()
	
	reader, err := model.NewPdfReader(f)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	numPages, _ := reader.GetNumPages()
	fmt.Printf("✅ PDF read successfully! Pages: %d\n", numPages)
	
	fmt.Println("🎉 All tests passed!")
}
EOF

echo -e "${GREEN}✅ Created test file: test_license_free.go${NC}"

echo -e "${BLUE}7. Running functionality test...${NC}"

# Run test
if go run test_license_free.go; then
    echo -e "${GREEN}✅ Functionality test passed!${NC}"
else
    echo -e "${RED}❌ Functionality test failed!${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 License removal completed successfully!${NC}"
echo -e "${GREEN}=================================${NC}"
echo -e "✅ All license checks removed"
echo -e "✅ Usage tracking disabled"
echo -e "✅ Watermarks removed"
echo -e "✅ Server communication disabled"
echo -e "✅ Compilation successful"
echo -e "✅ Functionality verified"
echo ""
echo -e "${BLUE}📁 Backup files created with .backup extension${NC}"
echo -e "${BLUE}📄 Test file: test_license_free.go${NC}"
echo -e "${BLUE}📄 Test output: test_output.pdf${NC}"
echo ""
echo -e "${YELLOW}⚠️  Note: This is for educational purposes only${NC}"

# Clean up test files if requested
read -p "Clean up test files? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f test_license_free.go test_output.pdf
    echo -e "${GREEN}✅ Test files cleaned up${NC}"
fi

echo ""
echo -e "${BLUE}📋 Summary of modifications:${NC}"
echo "1. internal/license/license.go - Core license functions disabled"
echo "2. extractor/extractor.go - License check bypassed"
echo "3. model/model.go - License imports removed, tracking disabled"
echo "4. creator/creator.go - License imports removed, tracking disabled"
echo ""
echo -e "${GREEN}🚀 UniPDF is now license-free and ready to use!${NC}"
