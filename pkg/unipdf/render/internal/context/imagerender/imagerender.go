//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package imagerender ;import (_g "errors";_f "fmt";_fcc "github.com/unidoc/freetype/raster";_fd "github.com/unidoc/unipdf/v4/common";_b "github.com/unidoc/unipdf/v4/internal/precision";_gd "github.com/unidoc/unipdf/v4/internal/transform";_fa "github.com/unidoc/unipdf/v4/render/internal/context";
_fdf "golang.org/x/image/draw";_fc "golang.org/x/image/font";_bc "golang.org/x/image/math/f64";_eb "golang.org/x/image/math/fixed";_ce "image";_da "image/color";_e "image/draw";_a "math";_c "sort";_de "strings";);type circle struct{_fadg ,_adg ,_eaag float64 };
func (_eeaa *surfacePattern )ColorAt (x ,y int )_da .Color {_baef :=_eeaa ._ege .Bounds ();switch _eeaa ._dbae {case _eega :if y >=_baef .Dy (){return _da .Transparent ;};case _dbef :if x >=_baef .Dx (){return _da .Transparent ;};case _efdf :if x >=_baef .Dx ()||y >=_baef .Dy (){return _da .Transparent ;
};};x =x %_baef .Dx ()+_baef .Min .X ;y =y %_baef .Dy ()+_baef .Min .Y ;return _eeaa ._ege .At (x ,y );};func (_ggg *Context )FillPattern ()_fa .Pattern {return _ggg ._add };func (_cdg *Context )DrawImageAnchored (im _ce .Image ,x ,y int ,ax ,ay float64 ){_bcd :=im .Bounds ().Size ();
x -=int (ax *float64 (_bcd .X ));y -=int (ay *float64 (_bcd .Y ));_beb :=_fdf .BiLinear ;_fgc :=_cdg ._cddc .Clone ().Translate (float64 (x ),float64 (y ));_gcbb :=_bc .Aff3 {_fgc [0],_fgc [3],_fgc [6],_fgc [1],_fgc [4],_fgc [7]};if _cdg ._fafb ==nil {_beb .Transform (_cdg ._ffd ,_gcbb ,im ,im .Bounds (),_fdf .Over ,nil );
}else {_beb .Transform (_cdg ._ffd ,_gcbb ,im ,im .Bounds (),_fdf .Over ,&_fdf .Options {DstMask :_cdg ._fafb ,DstMaskP :_ce .Point {}});};};func (_aaae *Context )ShearAbout (sx ,sy ,x ,y float64 ){_aaae .Translate (x ,y );_aaae .Shear (sx ,sy );_aaae .Translate (-x ,-y );
};func (_gcb *Context )Height ()int {return _gcb ._ea };func NewContextForImage (im _ce .Image )*Context {return NewContextForRGBA (_acge (im ))};func (_dabe *Context )Matrix ()_gd .Matrix {return _dabe ._cddc };func (_gff *Context )Rotate (angle float64 ){_gff ._cddc =_gff ._cddc .Rotate (angle )};
func (_fac *Context )DrawEllipse (x ,y ,rx ,ry float64 ){_fac .NewSubPath ();_fac .DrawEllipticalArc (x ,y ,rx ,ry ,0,2*_a .Pi );_fac .ClosePath ();};type Context struct{_aeb int ;_ea int ;_eae *_fcc .Rasterizer ;_ffd *_ce .RGBA ;_fafb *_ce .Alpha ;_dcf _da .Color ;
_add _fa .Pattern ;_gcee _fa .Pattern ;_gcg _fcc .Path ;_cag _fcc .Path ;_eebf _gd .Point ;_cab _gd .Point ;_cgc bool ;_bb []float64 ;_efd float64 ;_cbc float64 ;_fcd _fa .LineCap ;_acg _fa .LineJoin ;_daa _fa .FillRule ;_cddc _gd .Matrix ;_fcdb _fa .TextState ;
_bcbc []*Context ;};func (_adcf *Context )ClipPreserve (){_daf :=_ce .NewAlpha (_ce .Rect (0,0,_adcf ._aeb ,_adcf ._ea ));_gfa :=_fcc .NewAlphaOverPainter (_daf );_adcf .fill (_gfa );if _adcf ._fafb ==nil {_adcf ._fafb =_daf ;}else {_gdee :=_ce .NewAlpha (_ce .Rect (0,0,_adcf ._aeb ,_adcf ._ea ));
_fdf .DrawMask (_gdee ,_gdee .Bounds (),_daf ,_ce .Point {},_adcf ._fafb ,_ce .Point {},_fdf .Over );_adcf ._fafb =_gdee ;};};func (_fdd *Context )SetLineJoin (lineJoin _fa .LineJoin ){_fdd ._acg =lineJoin };func (_cddb *Context )ClosePath (){if _cddb ._cgc {_afb :=_aaaa (_cddb ._eebf );
_cddb ._gcg .Add1 (_afb );_cddb ._cag .Add1 (_afb );_cddb ._cab =_cddb ._eebf ;};};func _efcb (_bff float64 )float64 {return _bff *_a .Pi /180};func (_gab *Context )DrawLine (x1 ,y1 ,x2 ,y2 float64 ){_gab .MoveTo (x1 ,y1 );_gab .LineTo (x2 ,y2 )};func (_babg *Context )Stroke (){_babg .StrokePreserve ();
_babg .ClearPath ()};func (_agd *Context )MeasureString (s string ,face _fc .Face )(_cff ,_abcde float64 ){_ecac :=&_fc .Drawer {Face :face };_aad :=_ecac .MeasureString (s );return float64 (_aad >>6),_agd ._fcdb .Tf .Size ;};func (_bgf *Context )SetStrokeRGBA (r ,g ,b ,a float64 ){_efc :=_da .NRGBA {uint8 (r *255),uint8 (g *255),uint8 (b *255),uint8 (a *255)};
_bgf ._gcee =_adaa (_efc );};func (_gac *patternPainter )Paint (ss []_fcc .Span ,done bool ){_cacf :=_gac ._edeag .Bounds ();for _ ,_eabf :=range ss {if _eabf .Y < _cacf .Min .Y {continue ;};if _eabf .Y >=_cacf .Max .Y {return ;};if _eabf .X0 < _cacf .Min .X {_eabf .X0 =_cacf .Min .X ;
};if _eabf .X1 > _cacf .Max .X {_eabf .X1 =_cacf .Max .X ;};if _eabf .X0 >=_eabf .X1 {continue ;};const _acc =1<<16-1;_adgc :=_eabf .Y -_gac ._edeag .Rect .Min .Y ;_ecc :=_eabf .X0 -_gac ._edeag .Rect .Min .X ;_fce :=(_eabf .Y -_gac ._edeag .Rect .Min .Y )*_gac ._edeag .Stride +(_eabf .X0 -_gac ._edeag .Rect .Min .X )*4;
_caee :=_fce +(_eabf .X1 -_eabf .X0 )*4;for _ffc ,_bgg :=_fce ,_ecc ;_ffc < _caee ;_ffc ,_bgg =_ffc +4,_bgg +1{_dccc :=_eabf .Alpha ;if _gac ._cde !=nil {_dccc =_dccc *uint32 (_gac ._cde .AlphaAt (_bgg ,_adgc ).A )/255;if _dccc ==0{continue ;};};_gffg :=_gac ._eab .ColorAt (_bgg ,_adgc );
_cdf ,_addb ,_cafa ,_ffe :=_gffg .RGBA ();_edfb :=uint32 (_gac ._edeag .Pix [_ffc +0]);_bbgc :=uint32 (_gac ._edeag .Pix [_ffc +1]);_cfbe :=uint32 (_gac ._edeag .Pix [_ffc +2]);_cebg :=uint32 (_gac ._edeag .Pix [_ffc +3]);_edba :=(_acc -(_ffe *_dccc /_acc ))*0x101;
_gac ._edeag .Pix [_ffc +0]=uint8 ((_edfb *_edba +_cdf *_dccc )/_acc >>8);_gac ._edeag .Pix [_ffc +1]=uint8 ((_bbgc *_edba +_addb *_dccc )/_acc >>8);_gac ._edeag .Pix [_ffc +2]=uint8 ((_cfbe *_edba +_cafa *_dccc )/_acc >>8);_gac ._edeag .Pix [_ffc +3]=uint8 ((_cebg *_edba +_ffe *_dccc )/_acc >>8);
};};};func _dgge (_ebdd float64 )_eb .Int26_6 {_ebdd =_b .RoundDefault (_ebdd );return _eb .Int26_6 (_ebdd *64)};func _defdf (_gdga float64 ,_dgbb stops )_da .Color {if _gdga <=0.0||len (_dgbb )==1{return _dgbb [0]._aecgg ;};_cggg :=_dgbb [len (_dgbb )-1];
if _gdga >=_cggg ._dbag {return _cggg ._aecgg ;};for _ebd ,_badd :=range _dgbb [1:]{if _gdga < _badd ._dbag {_gdga =(_gdga -_dgbb [_ebd ]._dbag )/(_badd ._dbag -_dgbb [_ebd ]._dbag );return _dbce (_dgbb [_ebd ]._aecgg ,_badd ._aecgg ,_gdga );};};return _cggg ._aecgg ;
};func (_bac *Context )SetRGBA255 (r ,g ,b ,a int ){_bac ._dcf =_da .NRGBA {uint8 (r ),uint8 (g ),uint8 (b ),uint8 (a )};_bac .setFillAndStrokeColor (_bac ._dcf );};func (_dag *Context )ScaleAbout (sx ,sy ,x ,y float64 ){_dag .Translate (x ,y );_dag .Scale (sx ,sy );
_dag .Translate (-x ,-y );};func (_gcce stops )Swap (i ,j int ){_gcce [i ],_gcce [j ]=_gcce [j ],_gcce [i ]};func (_gde *Context )SetStrokeStyle (pattern _fa .Pattern ){_gde ._gcee =pattern };func NewContextForRGBA (im *_ce .RGBA )*Context {_cgcd :=im .Bounds ().Size ().X ;
_ebf :=im .Bounds ().Size ().Y ;return &Context {_aeb :_cgcd ,_ea :_ebf ,_eae :_fcc .NewRasterizer (_cgcd ,_ebf ),_ffd :im ,_dcf :_da .Transparent ,_add :_gdae ,_gcee :_eg ,_cbc :1,_daa :_fa .FillRuleWinding ,_cddc :_gd .IdentityMatrix (),_fcdb :_fa .NewTextState ()};
};func (_afa *Context )SetDash (dashes ...float64 ){_afa ._bb =dashes };func (_caf *linearGradient )ColorAt (x ,y int )_da .Color {if len (_caf ._agaf )==0{return _da .Transparent ;};_gaee ,_dfc :=float64 (x ),float64 (y );_bba ,_gfbc ,_ada ,_beba :=_caf ._ggd ,_caf ._fgf ,_caf ._fad ,_caf ._gee ;
_cae ,_fcfd :=_ada -_bba ,_beba -_gfbc ;if _fcfd ==0&&_cae !=0{return _defdf ((_gaee -_bba )/_cae ,_caf ._agaf );};if _cae ==0&&_fcfd !=0{return _defdf ((_dfc -_gfbc )/_fcfd ,_caf ._agaf );};_agf :=_cae *(_gaee -_bba )+_fcfd *(_dfc -_gfbc );if _agf < 0{return _caf ._agaf [0]._aecgg ;
};_cda :=_a .Hypot (_cae ,_fcfd );_fccc :=((_gaee -_bba )*-_fcfd +(_dfc -_gfbc )*_cae )/(_cda *_cda );_edb ,_cdgb :=_bba +_fccc *-_fcfd ,_gfbc +_fccc *_cae ;_cagb :=_a .Hypot (_gaee -_edb ,_dfc -_cdgb )/_cda ;return _defdf (_cagb ,_caf ._agaf );};func (_cba *Context )LineTo (x ,y float64 ){if !_cba ._cgc {_cba .MoveTo (x ,y );
}else {x ,y =_cba .Transform (x ,y );_bdg :=_gd .NewPoint (x ,y );_fe :=_aaaa (_bdg );_cba ._gcg .Add1 (_fe );_cba ._cag .Add1 (_fe );_cba ._cab =_bdg ;};};func (_dec *Context )drawString (_adbf string ,_acb _fc .Face ,_cbe ,_badc float64 ){_egf :=&_fc .Drawer {Src :_ce .NewUniform (_dec ._dcf ),Face :_acb ,Dot :_aaaa (_gd .NewPoint (_cbe ,_badc ))};
_dge :=rune (-1);for _ ,_bbb :=range _adbf {if _dge >=0{_egf .Dot .X +=_egf .Face .Kern (_dge ,_bbb );};_gbg ,_fbg ,_ecf ,_fgcd ,_egg :=_egf .Face .Glyph (_egf .Dot ,_bbb );if !_egg {continue ;};_ccd :=_gbg .Sub (_gbg .Min );_fed :=_ce .NewRGBA (_ccd );
_fdf .DrawMask (_fed ,_ccd ,_egf .Src ,_ce .Point {},_fbg ,_ecf ,_fdf .Over );var _cgbd *_fdf .Options ;if _dec ._fafb !=nil {_cgbd =&_fdf .Options {DstMask :_dec ._fafb ,DstMaskP :_ce .Point {}};};_dee :=_dec ._cddc .Clone ().Translate (float64 (_gbg .Min .X ),float64 (_gbg .Min .Y ));
_gfab :=_bc .Aff3 {_dee [0],_dee [3],_dee [6],_dee [1],_dee [4],_dee [7]};_fdf .BiLinear .Transform (_dec ._ffd ,_gfab ,_fed ,_ccd ,_fdf .Over ,_cgbd );_egf .Dot .X +=_fgcd ;_dge =_bbb ;};};func (_eea *Context )DrawRectangle (x ,y ,w ,h float64 ){_eea .NewSubPath ();
_eea .MoveTo (x ,y );_eea .LineTo (x +w ,y );_eea .LineTo (x +w ,y +h );_eea .LineTo (x ,y +h );_eea .ClosePath ();};func (_ccg *Context )StrokePreserve (){var _cgb _fcc .Painter ;if _ccg ._fafb ==nil {if _gbb ,_fccb :=_ccg ._gcee .(*solidPattern );_fccb {_ddc :=_fcc .NewRGBAPainter (_ccg ._ffd );
_ddc .SetColor (_gbb ._eceb );_cgb =_ddc ;};};if _cgb ==nil {_cgb =_bgac (_ccg ._ffd ,_ccg ._fafb ,_ccg ._gcee );};_ccg .stroke (_cgb );};func (_eed *Context )Shear (x ,y float64 ){_eed ._cddc .Shear (x ,y )};func (_bfe *Context )DrawString (s string ,face _fc .Face ,x ,y float64 ){_bfe .DrawStringAnchored (s ,face ,x ,y ,0,0);
};func (_abcd *Context )SetRGB (r ,g ,b float64 ){_abcd .SetRGBA (r ,g ,b ,1)};func (_cgg *Context )SetHexColor (x string ){_dfa ,_dgg ,_dcd ,_fcfa :=_fbbe (x );_cgg .SetRGBA255 (_dfa ,_dgg ,_dcd ,_fcfa );};func (_dfaf *Context )DrawArc (x ,y ,r ,angle1 ,angle2 float64 ){_dfaf .DrawEllipticalArc (x ,y ,r ,r ,angle1 ,angle2 );
};func (_afae *Context )MoveTo (x ,y float64 ){if _afae ._cgc {_afae ._cag .Add1 (_aaaa (_afae ._eebf ));};x ,y =_afae .Transform (x ,y );_aab :=_gd .NewPoint (x ,y );_ggf :=_aaaa (_aab );_afae ._gcg .Start (_ggf );_afae ._cag .Start (_ggf );_afae ._eebf =_aab ;
_afae ._cab =_aab ;_afae ._cgc =true ;};func (_eda *Context )Pop (){_dda :=*_eda ;_ffge :=_eda ._bcbc ;_fccg :=_ffge [len (_ffge )-1];*_eda =*_fccg ;_eda ._gcg =_dda ._gcg ;_eda ._cag =_dda ._cag ;_eda ._eebf =_dda ._eebf ;_eda ._cab =_dda ._cab ;_eda ._cgc =_dda ._cgc ;
};func (_bge *Context )TextState ()*_fa .TextState {return &_bge ._fcdb };func (_ccee *Context )SetFillRule (fillRule _fa .FillRule ){_ccee ._daa =fillRule };func (_efe *Context )SetRGB255 (r ,g ,b int ){_efe .SetRGBA255 (r ,g ,b ,255)};func (_gef stops )Less (i ,j int )bool {return _gef [i ]._dbag < _gef [j ]._dbag };
func (_bdgb *Context )DrawEllipticalArc (x ,y ,rx ,ry ,angle1 ,angle2 float64 ){const _afaa =16;for _bcc :=0;_bcc < _afaa ;_bcc ++{_cfeg :=float64 (_bcc +0)/_afaa ;_efff :=float64 (_bcc +1)/_afaa ;_dgc :=angle1 +(angle2 -angle1 )*_cfeg ;_feg :=angle1 +(angle2 -angle1 )*_efff ;
_fcaa :=x +rx *_a .Cos (_dgc );_ffgb :=y +ry *_a .Sin (_dgc );_agee :=x +rx *_a .Cos ((_dgc +_feg )/2);_cfd :=y +ry *_a .Sin ((_dgc +_feg )/2);_edea :=x +rx *_a .Cos (_feg );_agg :=y +ry *_a .Sin (_feg );_edeg :=2*_agee -_fcaa /2-_edea /2;_degd :=2*_cfd -_ffgb /2-_agg /2;
if _bcc ==0{if _bdgb ._cgc {_bdgb .LineTo (_fcaa ,_ffgb );}else {_bdgb .MoveTo (_fcaa ,_ffgb );};};_bdgb .QuadraticTo (_edeg ,_degd ,_edea ,_agg );};};func _eefg (_dgec _ce .Image ,_gaa repeatOp )_fa .Pattern {return &surfacePattern {_ege :_dgec ,_dbae :_gaa };
};func (_fgg *Context )DrawImage (im _ce .Image ,x ,y int ){_fgg .DrawImageAnchored (im ,x ,y ,0,0)};func (_faa *Context )StrokePattern ()_fa .Pattern {return _faa ._gcee };func NewContext (width ,height int )*Context {return NewContextForRGBA (_ce .NewRGBA (_ce .Rect (0,0,width ,height )));
};func (_gdc *Context )DrawRoundedRectangle (x ,y ,w ,h ,r float64 ){_aaba ,_dgf ,_gbf ,_deb :=x ,x +r ,x +w -r ,x +w ;_afef ,_dfge ,_cbf ,_dab :=y ,y +r ,y +h -r ,y +h ;_gdc .NewSubPath ();_gdc .MoveTo (_dgf ,_afef );_gdc .LineTo (_gbf ,_afef );_gdc .DrawArc (_gbf ,_dfge ,r ,_efcb (270),_efcb (360));
_gdc .LineTo (_deb ,_cbf );_gdc .DrawArc (_gbf ,_cbf ,r ,_efcb (0),_efcb (90));_gdc .LineTo (_dgf ,_dab );_gdc .DrawArc (_dgf ,_cbf ,r ,_efcb (90),_efcb (180));_gdc .LineTo (_aaba ,_dfge );_gdc .DrawArc (_dgf ,_dfge ,r ,_efcb (180),_efcb (270));_gdc .ClosePath ();
};func (_bfdg *Context )DrawPoint (x ,y ,r float64 ){_bfdg .Push ();_def ,_fg :=_bfdg .Transform (x ,y );_bfdg .Identity ();_bfdg .DrawCircle (_def ,_fg ,r );_bfdg .Pop ();};func (_ceb *Context )Clear (){_dcaf :=_ce .NewUniform (_ceb ._dcf );_fdf .Draw (_ceb ._ffd ,_ceb ._ffd .Bounds (),_dcaf ,_ce .Point {},_fdf .Src );
};func (_ffg *Context )Clip (){_ffg .ClipPreserve ();_ffg .ClearPath ()};func _bgb (_afe ,_gca ,_fca ,_cc ,_gce ,_dfb ,_bgd ,_dba float64 )[]_gd .Point {_dfg :=(_a .Hypot (_fca -_afe ,_cc -_gca )+_a .Hypot (_gce -_fca ,_dfb -_cc )+_a .Hypot (_bgd -_gce ,_dba -_dfb ));
_cg :=int (_dfg +0.5);if _cg < 4{_cg =4;};_ac :=float64 (_cg )-1;_gcc :=make ([]_gd .Point ,_cg );for _aef :=0;_aef < _cg ;_aef ++{_ec :=float64 (_aef )/_ac ;_cce ,_dca :=_bcbb (_afe ,_gca ,_fca ,_cc ,_gce ,_dfb ,_bgd ,_dba ,_ec );_gcc [_aef ]=_gd .NewPoint (_cce ,_dca );
};return _gcc ;};func (_afbc stops )Len ()int {return len (_afbc )};func _cd (_ca ,_gg ,_cb ,_ba ,_bcb ,_dc ,_gc float64 )(_ee ,_dd float64 ){_fcf :=1-_gc ;_ad :=_fcf *_fcf ;_bd :=2*_fcf *_gc ;_gf :=_gc *_gc ;_ee =_ad *_ca +_bd *_cb +_gf *_bcb ;_dd =_ad *_gg +_bd *_ba +_gf *_dc ;
return ;};func (_gcbg *Context )SetColor (c _da .Color ){_gcbg .setFillAndStrokeColor (c )};func NewRadialGradient (x0 ,y0 ,r0 ,x1 ,y1 ,r1 float64 )_fa .Gradient {_ade :=circle {x0 ,y0 ,r0 };_fbc :=circle {x1 ,y1 ,r1 };_dgb :=circle {x1 -x0 ,y1 -y0 ,r1 -r0 };
_ece :=_bfg (_dgb ._fadg ,_dgb ._adg ,-_dgb ._eaag ,_dgb ._fadg ,_dgb ._adg ,_dgb ._eaag );var _fgce float64 ;if _ece !=0{_fgce =1.0/_ece ;};_gaea :=-_ade ._eaag ;_eceg :=&radialGradient {_debc :_ade ,_fbe :_fbc ,_cagg :_dgb ,_defd :_ece ,_fbd :_fgce ,_ggfa :_gaea };
return _eceg ;};func NewLinearGradient (x0 ,y0 ,x1 ,y1 float64 )_fa .Gradient {_fefd :=&linearGradient {_ggd :x0 ,_fgf :y0 ,_fad :x1 ,_gee :y1 };return _fefd ;};func (_bab *Context )SetFillRGBA (r ,g ,b ,a float64 ){_ ,_ ,_ ,_gfc :=_bab ._dcf .RGBA ();
if _gfc > 0&&_gfc !=65535&&a ==1{a =float64 (_gfc )/65535;};_adc :=_da .NRGBA {uint8 (r *255),uint8 (g *255),uint8 (b *255),uint8 (a *255)};_bab ._dcf =_adc ;_bab ._add =_adaa (_adc );};func (_cca *Context )SetFillStyle (pattern _fa .Pattern ){if _gcd ,_cad :=pattern .(*solidPattern );
_cad {_cca ._dcf =_gcd ._eceb ;};_cca ._add =pattern ;};func _eee (_acd _fcc .Path ,_fcdc []float64 ,_bae float64 )_fcc .Path {return _fdde (_eegg (_cfge (_acd ),_fcdc ,_bae ));};func (_gae *Context )RotateAbout (angle ,x ,y float64 ){_gae .Translate (x ,y );
_gae .Rotate (angle );_gae .Translate (-x ,-y );};func (_fcb *Context )setFillAndStrokeColor (_dfe _da .Color ){_fcb ._dcf =_dfe ;_fcb ._add =_adaa (_dfe );_fcb ._gcee =_adaa (_dfe );};func _acge (_ebdg _ce .Image )*_ce .RGBA {_agae :=_ebdg .Bounds ();
_feb :=_ce .NewRGBA (_agae );_e .Draw (_feb ,_agae ,_ebdg ,_agae .Min ,_e .Src );return _feb ;};func (_dccg *Context )Push (){_eeg :=*_dccg ;_dccg ._bcbc =append (_dccg ._bcbc ,&_eeg )};func (_ede *Context )InvertMask (){if _ede ._fafb ==nil {_ede ._fafb =_ce .NewAlpha (_ede ._ffd .Bounds ());
}else {for _acfa ,_fcbc :=range _ede ._fafb .Pix {_ede ._fafb .Pix [_acfa ]=255-_fcbc ;};};};func (_fafa *Context )fill (_eefc _fcc .Painter ){_fef :=_fafa ._cag ;if _fafa ._cgc {_fef =make (_fcc .Path ,len (_fafa ._cag ));copy (_fef ,_fafa ._cag );_fef .Add1 (_aaaa (_fafa ._eebf ));
};_aaa :=_fafa ._eae ;_aaa .UseNonZeroWinding =_fafa ._daa ==_fa .FillRuleWinding ;_aaa .Clear ();_aaa .AddPath (_fef );_aaa .Rasterize (_eefc );};func (_cef *Context )Transform (x ,y float64 )(_cfed ,_eded float64 ){return _cef ._cddc .Transform (x ,y );
};func (_fea *Context )CubicTo (x1 ,y1 ,x2 ,y2 ,x3 ,y3 float64 ){if !_fea ._cgc {_fea .MoveTo (x1 ,y1 );};_ebe ,_eaa :=_fea ._cab .X ,_fea ._cab .Y ;x1 ,y1 =_fea .Transform (x1 ,y1 );x2 ,y2 =_fea .Transform (x2 ,y2 );x3 ,y3 =_fea .Transform (x3 ,y3 );_fddf :=_bgb (_ebe ,_eaa ,x1 ,y1 ,x2 ,y2 ,x3 ,y3 );
_eca :=_aaaa (_fea ._cab );for _ ,_fdg :=range _fddf [1:]{_ggef :=_aaaa (_fdg );if _ggef ==_eca {continue ;};_eca =_ggef ;_fea ._gcg .Add1 (_ggef );_fea ._cag .Add1 (_ggef );_fea ._cab =_fdg ;};};func (_aea *Context )ClearPath (){_aea ._gcg .Clear ();_aea ._cag .Clear ();
_aea ._cgc =false };type patternPainter struct{_edeag *_ce .RGBA ;_cde *_ce .Alpha ;_eab _fa .Pattern ;};func _bfg (_efg ,_fcfb ,_dgcf ,_dcg ,_ccea ,_fcgc float64 )float64 {return _efg *_dcg +_fcfb *_ccea +_dgcf *_fcgc ;};func _adaa (_dgbg _da .Color )_fa .Pattern {return &solidPattern {_eceb :_dgbg }};
type radialGradient struct{_debc ,_fbe ,_cagg circle ;_defd ,_fbd float64 ;_ggfa float64 ;_dbf stops ;};func (_fcg *Context )Scale (x ,y float64 ){_fcg ._cddc =_fcg ._cddc .Scale (x ,y )};func (_be *Context )Width ()int {return _be ._aeb };func _cfge (_gfag _fcc .Path )[][]_gd .Point {var _dcb [][]_gd .Point ;
var _ebff []_gd .Point ;var _eac ,_afff float64 ;for _egaf :=0;_egaf < len (_gfag );{switch _gfag [_egaf ]{case 0:if len (_ebff )> 0{_dcb =append (_dcb ,_ebff );_ebff =nil ;};_cebe :=_ded (_gfag [_egaf +1]);_dae :=_ded (_gfag [_egaf +2]);_ebff =append (_ebff ,_gd .NewPoint (_cebe ,_dae ));
_eac ,_afff =_cebe ,_dae ;_egaf +=4;case 1:_cbd :=_ded (_gfag [_egaf +1]);_adde :=_ded (_gfag [_egaf +2]);_ebff =append (_ebff ,_gd .NewPoint (_cbd ,_adde ));_eac ,_afff =_cbd ,_adde ;_egaf +=4;case 2:_afac :=_ded (_gfag [_egaf +1]);_fgb :=_ded (_gfag [_egaf +2]);
_ggac :=_ded (_gfag [_egaf +3]);_ccaf :=_ded (_gfag [_egaf +4]);_cfgga :=_gge (_eac ,_afff ,_afac ,_fgb ,_ggac ,_ccaf );_ebff =append (_ebff ,_cfgga ...);_eac ,_afff =_ggac ,_ccaf ;_egaf +=6;case 3:_bdf :=_ded (_gfag [_egaf +1]);_gefe :=_ded (_gfag [_egaf +2]);
_fbb :=_ded (_gfag [_egaf +3]);_caad :=_ded (_gfag [_egaf +4]);_fdc :=_ded (_gfag [_egaf +5]);_bag :=_ded (_gfag [_egaf +6]);_aggag :=_bgb (_eac ,_afff ,_bdf ,_gefe ,_fbb ,_caad ,_fdc ,_bag );_ebff =append (_ebff ,_aggag ...);_eac ,_afff =_fdc ,_bag ;_egaf +=8;
default:_fd .Log .Debug ("\u0057\u0041\u0052\u004e: \u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0070\u0061\u0074\u0068\u003a\u0020%\u0076",_gfag );return _dcb ;};};if len (_ebff )> 0{_dcb =append (_dcb ,_ebff );};return _dcb ;};type stops []stop ;
func _fbbe (_dgcd string )(_gced ,_faff ,_fdea ,_eccc int ){_dgcd =_de .TrimPrefix (_dgcd ,"\u0023");_eccc =255;if len (_dgcd )==3{_aac :="\u00251\u0078\u0025\u0031\u0078\u0025\u0031x";_f .Sscanf (_dgcd ,_aac ,&_gced ,&_faff ,&_fdea );_gced |=_gced <<4;
_faff |=_faff <<4;_fdea |=_fdea <<4;};if len (_dgcd )==6{_gbd :="\u0025\u0030\u0032x\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078";_f .Sscanf (_dgcd ,_gbd ,&_gced ,&_faff ,&_fdea );};if len (_dgcd )==8{_gag :="\u0025\u00302\u0078\u0025\u00302\u0078\u0025\u0030\u0032\u0078\u0025\u0030\u0032\u0078";
_f .Sscanf (_dgcd ,_gag ,&_gced ,&_faff ,&_fdea ,&_eccc );};return ;};func _aaaa (_ddf _gd .Point )_eb .Point26_6 {return _eb .Point26_6 {X :_dgge (_ddf .X ),Y :_dgge (_ddf .Y )}};func (_adb *Context )Fill (){_adb .FillPreserve ();_adb .ClearPath ()};type linearGradient struct{_ggd ,_fgf ,_fad ,_gee float64 ;
_agaf stops ;};func (_ace *Context )ResetClip (){_ace ._fafb =nil };func (_cdcd *Context )Identity (){_cdcd ._cddc =_gd .IdentityMatrix ()};func _dbce (_abd ,_aed _da .Color ,_abcg float64 )_da .Color {_bgba ,_cee ,_bfa ,_cebd :=_abd .RGBA ();_bace ,_bcad ,_fda ,_edad :=_aed .RGBA ();
return _da .RGBA {_cge (_bgba ,_bace ,_abcg ),_cge (_cee ,_bcad ,_abcg ),_cge (_bfa ,_fda ,_abcg ),_cge (_cebd ,_edad ,_abcg )};};func _fdde (_bfc [][]_gd .Point )_fcc .Path {var _debcf _fcc .Path ;for _ ,_eggb :=range _bfc {var _dfab _eb .Point26_6 ;for _cfbg ,_efab :=range _eggb {_gfba :=_aaaa (_efab );
if _cfbg ==0{_debcf .Start (_gfba );}else {_gcbbb :=_gfba .X -_dfab .X ;_ebb :=_gfba .Y -_dfab .Y ;if _gcbbb < 0{_gcbbb =-_gcbbb ;};if _ebb < 0{_ebb =-_ebb ;};if _gcbbb +_ebb > 8{_debcf .Add1 (_gfba );};};_dfab =_gfba ;};};return _debcf ;};func (_dbaa *Context )Translate (x ,y float64 ){_dbaa ._cddc =_dbaa ._cddc .Translate (x ,y )};
func (_abc *Context )LineWidth ()float64 {return _abc ._cbc };func (_ecd *radialGradient )AddColorStop (offset float64 ,color _da .Color ){_ecd ._dbf =append (_ecd ._dbf ,stop {_dbag :offset ,_aecgg :color });_c .Sort (_ecd ._dbf );};var (_gdae =_adaa (_da .White );
_eg =_adaa (_da .Black ););func _cge (_afc ,_gaf uint32 ,_cced float64 )uint8 {return uint8 (int32 (float64 (_afc )*(1.0-_cced )+float64 (_gaf )*_cced )>>8);};func (_dbcg *linearGradient )AddColorStop (offset float64 ,color _da .Color ){_dbcg ._agaf =append (_dbcg ._agaf ,stop {_dbag :offset ,_aecgg :color });
_c .Sort (_dbcg ._agaf );};type stop struct{_dbag float64 ;_aecgg _da .Color ;};func (_bca *Context )NewSubPath (){if _bca ._cgc {_bca ._cag .Add1 (_aaaa (_bca ._eebf ));};_bca ._cgc =false ;};func (_cdc *Context )DrawCircle (x ,y ,r float64 ){_cdc .NewSubPath ();
_cdc .DrawEllipticalArc (x ,y ,r ,r ,0,2*_a .Pi );_cdc .ClosePath ();};func (_edf *Context )joiner ()_fcc .Joiner {switch _edf ._acg {case _fa .LineJoinBevel :return _fcc .BevelJoiner ;case _fa .LineJoinRound :return _fcc .RoundJoiner ;};return nil ;};
func (_ega *Context )SetPixel (x ,y int ){_ega ._ffd .Set (x ,y ,_ega ._dcf )};const (_bfcd repeatOp =iota ;_eega ;_dbef ;_efdf ;);func (_deg *Context )SetRGBA (r ,g ,b ,a float64 ){_ ,_ ,_ ,_fab :=_deg ._dcf .RGBA ();if _fab > 0&&_fab !=65535&&a ==1{a =float64 (_fab )/65535;
};_deg ._dcf =_da .NRGBA {uint8 (r *255),uint8 (g *255),uint8 (b *255),uint8 (a *255)};_deg .setFillAndStrokeColor (_deg ._dcf );};func _bcbb (_dde ,_fb ,_dbe ,_gb ,_aff ,_ff ,_gdg ,_bf ,_ga float64 )(_aec ,_eeb float64 ){_ab :=1-_ga ;_ge :=_ab *_ab *_ab ;
_eff :=3*_ab *_ab *_ga ;_caa :=3*_ab *_ga *_ga ;_gfd :=_ga *_ga *_ga ;_aec =_ge *_dde +_eff *_dbe +_caa *_aff +_gfd *_gdg ;_eeb =_ge *_fb +_eff *_gb +_caa *_ff +_gfd *_bf ;return ;};func (_edd *Context )FillPreserve (){var _fde _fcc .Painter ;if _edd ._fafb ==nil {if _cdb ,_cfee :=_edd ._add .(*solidPattern );
_cfee {_aag :=_fcc .NewRGBAPainter (_edd ._ffd );_aag .SetColor (_cdb ._eceb );_fde =_aag ;};};if _fde ==nil {_fde =_bgac (_edd ._ffd ,_edd ._fafb ,_edd ._add );};_edd .fill (_fde );};func (_dg *Context )SetLineWidth (lineWidth float64 ){_dg ._cbc =lineWidth };
func _ded (_deda _eb .Int26_6 )float64 {const _bdgg ,_bdca =6,1<<6-1;if _deda >=0{return float64 (_deda >>_bdgg )+float64 (_deda &_bdca )/64;};_deda =-_deda ;if _deda >=0{return -(float64 (_deda >>_bdgg )+float64 (_deda &_bdca )/64);};return 0;};func (_cfe *Context )stroke (_dcc _fcc .Painter ){_dbg :=_cfe ._gcg ;
if len (_cfe ._bb )> 0{_dbg =_eee (_dbg ,_cfe ._bb ,_cfe ._efd );}else {_dbg =_fdde (_cfge (_dbg ));};_gdb :=_cfe ._eae ;_gdb .UseNonZeroWinding =true ;_gdb .Clear ();_bdc :=(_cfe ._cddc .ScalingFactorX ()+_cfe ._cddc .ScalingFactorY ())/2;_gdb .AddStroke (_dbg ,_dgge (_cfe ._cbc *_bdc ),_cfe .capper (),_cfe .joiner ());
_gdb .Rasterize (_dcc );};func _eegg (_fcfba [][]_gd .Point ,_bccc []float64 ,_bgaa float64 )[][]_gd .Point {var _cfb [][]_gd .Point ;if len (_bccc )==0{return _fcfba ;};if len (_bccc )==1{_bccc =append (_bccc ,_bccc [0]);};for _ ,_faaf :=range _fcfba {if len (_faaf )< 2{continue ;
};_cbea :=_faaf [0];_ffad :=1;_dgbd :=0;_gdea :=0.0;if _bgaa !=0{var _bdff float64 ;for _ ,_cgea :=range _bccc {_bdff +=_cgea ;};_bgaa =_a .Mod (_bgaa ,_bdff );if _bgaa < 0{_bgaa +=_bdff ;};for _gec ,_ggefc :=range _bccc {_bgaa -=_ggefc ;if _bgaa < 0{_dgbd =_gec ;
_gdea =_ggefc +_bgaa ;break ;};};};var _baf []_gd .Point ;_baf =append (_baf ,_cbea );for _ffad < len (_faaf ){_edde :=_bccc [_dgbd ];_gcgf :=_faaf [_ffad ];_bbbe :=_cbea .Distance (_gcgf );_fdfg :=_edde -_gdea ;if _bbbe > _fdfg {_bfbc :=_fdfg /_bbbe ;
_ggfac :=_cbea .Interpolate (_gcgf ,_bfbc );_baf =append (_baf ,_ggfac );if _dgbd %2==0&&len (_baf )> 1{_cfb =append (_cfb ,_baf );};_baf =nil ;_baf =append (_baf ,_ggfac );_gdea =0;_cbea =_ggfac ;_dgbd =(_dgbd +1)%len (_bccc );}else {_baf =append (_baf ,_gcgf );
_cbea =_gcgf ;_gdea +=_bbbe ;_ffad ++;};};if _dgbd %2==0&&len (_baf )> 1{_cfb =append (_cfb ,_baf );};};return _cfb ;};func (_faca *solidPattern )ColorAt (x ,y int )_da .Color {return _faca ._eceb };func _bgac (_cgba *_ce .RGBA ,_dcaa *_ce .Alpha ,_cbb _fa .Pattern )*patternPainter {return &patternPainter {_cgba ,_dcaa ,_cbb };
};func (_fdfa *Context )QuadraticTo (x1 ,y1 ,x2 ,y2 float64 ){if !_fdfa ._cgc {_fdfa .MoveTo (x1 ,y1 );};x1 ,y1 =_fdfa .Transform (x1 ,y1 );x2 ,y2 =_fdfa .Transform (x2 ,y2 );_afg :=_gd .NewPoint (x1 ,y1 );_bfd :=_gd .NewPoint (x2 ,y2 );_baa :=_aaaa (_afg );
_gfb :=_aaaa (_bfd );_fdfa ._gcg .Add2 (_baa ,_gfb );_fdfa ._cag .Add2 (_baa ,_gfb );_fdfa ._cab =_bfd ;};func (_gdf *Context )SetLineCap (lineCap _fa .LineCap ){_gdf ._fcd =lineCap };func (_fba *Context )DrawStringAnchored (s string ,face _fc .Face ,x ,y ,ax ,ay float64 ){_ecg ,_aecg :=_fba .MeasureString (s ,face );
_fba .drawString (s ,face ,x -ax *_ecg ,y +ay *_aecg );};type solidPattern struct{_eceb _da .Color };func (_bfb *Context )SetDashOffset (offset float64 ){_bfb ._efd =offset };type repeatOp int ;type surfacePattern struct{_ege _ce .Image ;_dbae repeatOp ;
};func _gge (_eef ,_cdd ,_bdd ,_db ,_af ,_ae float64 )[]_gd .Point {_gda :=(_a .Hypot (_bdd -_eef ,_db -_cdd )+_a .Hypot (_af -_bdd ,_ae -_db ));_df :=int (_gda +0.5);if _df < 4{_df =4;};_ef :=float64 (_df )-1;_faf :=make ([]_gd .Point ,_df );for _cf :=0;
_cf < _df ;_cf ++{_bg :=float64 (_cf )/_ef ;_ag ,_agb :=_cd (_eef ,_cdd ,_bdd ,_db ,_af ,_ae ,_bg );_faf [_cf ]=_gd .NewPoint (_ag ,_agb );};return _faf ;};func (_aga *Context )AsMask ()*_ce .Alpha {_aebf :=_ce .NewAlpha (_aga ._ffd .Bounds ());_fdf .Draw (_aebf ,_aga ._ffd .Bounds (),_aga ._ffd ,_ce .Point {},_fdf .Src );
return _aebf ;};func (_acf *Context )SetMask (mask *_ce .Alpha )error {if mask .Bounds ().Size ()!=_acf ._ffd .Bounds ().Size (){return _g .New ("\u006d\u0061\u0073\u006b\u0020\u0073i\u007a\u0065\u0020\u006d\u0075\u0073\u0074\u0020\u006d\u0061\u0074\u0063\u0068 \u0063\u006f\u006e\u0074\u0065\u0078\u0074 \u0073\u0069\u007a\u0065");
};_acf ._fafb =mask ;return nil ;};func (_ffa *Context )drawRegularPolygon (_fdgd int ,_cfg ,_efce ,_abcc ,_ccf float64 ){_dbc :=2*_a .Pi /float64 (_fdgd );_ccf -=_a .Pi /2;if _fdgd %2==0{_ccf +=_dbc /2;};_ffa .NewSubPath ();for _bad :=0;_bad < _fdgd ;
_bad ++{_cabc :=_ccf +_dbc *float64 (_bad );_ffa .LineTo (_cfg +_abcc *_a .Cos (_cabc ),_efce +_abcc *_a .Sin (_cabc ));};_ffa .ClosePath ();};func (_bfed *Context )SetMatrix (m _gd .Matrix ){_bfed ._cddc =m };func (_bddb *Context )Image ()_ce .Image {return _bddb ._ffd };
func (_gga *radialGradient )ColorAt (x ,y int )_da .Color {if len (_gga ._dbf )==0{return _da .Transparent ;};_ebg ,_fbae :=float64 (x )+0.5-_gga ._debc ._fadg ,float64 (y )+0.5-_gga ._debc ._adg ;_agga :=_bfg (_ebg ,_fbae ,_gga ._debc ._eaag ,_gga ._cagg ._fadg ,_gga ._cagg ._adg ,_gga ._cagg ._eaag );
_dggf :=_bfg (_ebg ,_fbae ,-_gga ._debc ._eaag ,_ebg ,_fbae ,_gga ._debc ._eaag );if _gga ._defd ==0{if _agga ==0{return _da .Transparent ;};_cfgg :=0.5*_dggf /_agga ;if _cfgg *_gga ._cagg ._eaag >=_gga ._ggfa {return _defdf (_cfgg ,_gga ._dbf );};return _da .Transparent ;
};_cffg :=_bfg (_agga ,_gga ._defd ,0,_agga ,-_dggf ,0);if _cffg >=0{_eec :=_a .Sqrt (_cffg );_gggb :=(_agga +_eec )*_gga ._fbd ;_bbg :=(_agga -_eec )*_gga ._fbd ;if _gggb *_gga ._cagg ._eaag >=_gga ._ggfa {return _defdf (_gggb ,_gga ._dbf );}else if _bbg *_gga ._cagg ._eaag >=_gga ._ggfa {return _defdf (_bbg ,_gga ._dbf );
};};return _da .Transparent ;};func (_feac *Context )capper ()_fcc .Capper {switch _feac ._fcd {case _fa .LineCapButt :return _fcc .ButtCapper ;case _fa .LineCapRound :return _fcc .RoundCapper ;case _fa .LineCapSquare :return _fcc .SquareCapper ;};return nil ;
};