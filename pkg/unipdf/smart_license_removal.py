#!/usr/bin/env python3
"""
Smart UniPDF License Removal <PERSON>ript
This script uses configuration-driven approach to remove license restrictions
Usage: python3 smart_license_removal.py [unipdf_directory]
"""

import os
import sys
import json
import re
import shutil
import subprocess
from datetime import datetime
from pathlib import Path

class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

class LicenseRemover:
    def __init__(self, unipdf_dir=None, config_file="license_removal_config.json"):
        self.unipdf_dir = Path(unipdf_dir or os.getcwd())
        self.config_file = config_file
        self.config = self.load_config()
        self.backup_dir = None
        
    def load_config(self):
        """Load configuration from JSON file"""
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"{Colors.RED}❌ Config file not found: {self.config_file}{Colors.NC}")
            sys.exit(1)
        except json.JSONDecodeError as e:
            print(f"{Colors.RED}❌ Invalid JSON in config file: {e}{Colors.NC}")
            sys.exit(1)
    
    def print_header(self):
        """Print script header"""
        print(f"{Colors.PURPLE}🤖 Smart UniPDF License Removal Script{Colors.NC}")
        print(f"{Colors.PURPLE}======================================={Colors.NC}")
        print(f"Target: {self.unipdf_dir}")
        print(f"Config: {self.config_file}")
        print(f"Version: {self.config.get('version', 'unknown')}")
        print()
    
    def validate_environment(self):
        """Validate the environment and target directory"""
        if not self.unipdf_dir.exists():
            print(f"{Colors.RED}❌ Directory not found: {self.unipdf_dir}{Colors.NC}")
            return False
        
        go_mod = self.unipdf_dir / "go.mod"
        if not go_mod.exists():
            print(f"{Colors.YELLOW}⚠️  No go.mod found. This may not be a Go project.{Colors.NC}")
        
        # Check if it looks like UniPDF
        if go_mod.exists():
            with open(go_mod, 'r') as f:
                content = f.read()
                if 'unipdf' not in content.lower():
                    print(f"{Colors.YELLOW}⚠️  This doesn't appear to be a UniPDF project.{Colors.NC}")
                    response = input("Continue anyway? (y/N): ")
                    if response.lower() != 'y':
                        return False
        
        return True
    
    def create_backup(self):
        """Create backup directory and backup files"""
        if not self.config.get('backup_enabled', True):
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_pattern = self.config.get('rollback', {}).get('backup_directory_pattern', 'backup_{timestamp}')
        backup_name = backup_pattern.format(timestamp=timestamp)
        
        self.backup_dir = self.unipdf_dir / backup_name
        self.backup_dir.mkdir(exist_ok=True)
        
        print(f"{Colors.BLUE}📁 Created backup directory: {self.backup_dir}{Colors.NC}")
        
        # Backup files that will be modified
        for mod in self.config.get('modifications', []):
            file_path = self.unipdf_dir / mod['file']
            if file_path.exists():
                backup_path = self.backup_dir / mod['file']
                backup_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(file_path, backup_path)
                print(f"{Colors.GREEN}✅ Backed up: {mod['file']}{Colors.NC}")
    
    def apply_modifications(self):
        """Apply all modifications from config"""
        print(f"{Colors.BLUE}🔧 Applying modifications...{Colors.NC}")
        
        for i, mod in enumerate(self.config.get('modifications', []), 1):
            print(f"{Colors.CYAN}{i}. {mod['description']}{Colors.NC}")
            
            file_path = self.unipdf_dir / mod['file']
            
            if not file_path.exists():
                print(f"{Colors.YELLOW}⚠️  File not found: {mod['file']}{Colors.NC}")
                continue
            
            if mod['type'] == 'complete_replacement':
                self.apply_complete_replacement(file_path, mod)
            elif mod['type'] == 'function_replacement':
                self.apply_pattern_replacements(file_path, mod.get('patterns', []))
            elif mod['type'] == 'multiple_replacements':
                self.apply_pattern_replacements(file_path, mod.get('patterns', []))
            else:
                print(f"{Colors.YELLOW}⚠️  Unknown modification type: {mod['type']}{Colors.NC}")
    
    def apply_complete_replacement(self, file_path, mod):
        """Replace entire file content"""
        if mod['file'] == 'internal/license/license.go':
            # Special handling for license.go
            new_content = self.get_license_bypass_content()
            with open(file_path, 'w') as f:
                f.write(new_content)
            print(f"{Colors.GREEN}✅ Completely replaced: {file_path.name}{Colors.NC}")
    
    def apply_pattern_replacements(self, file_path, patterns):
        """Apply pattern-based replacements"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        for pattern in patterns:
            search = pattern['search']
            replace = pattern['replace']
            flags = pattern.get('flags', [])
            
            # Convert flags to re flags
            re_flags = 0
            if 'DOTALL' in flags:
                re_flags |= re.DOTALL
            if 'MULTILINE' in flags:
                re_flags |= re.MULTILINE
            if 'IGNORECASE' in flags:
                re_flags |= re.IGNORECASE
            
            new_content = re.sub(search, replace, content, flags=re_flags)
            if new_content != content:
                content = new_content
                changes_made += 1
                desc = pattern.get('description', 'Pattern replacement')
                print(f"  ✅ {desc}")
        
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"{Colors.GREEN}✅ Modified {file_path.name} ({changes_made} changes){Colors.NC}")
        else:
            print(f"{Colors.YELLOW}⚠️  No changes made to {file_path.name}{Colors.NC}")
    
    def get_license_bypass_content(self):
        """Get the content for license bypass implementation"""
        return '''package license ;import (_ae "bytes";_d "compress/gzip";_a "crypto";_ef "crypto/aes";_eb "crypto/cipher";_fa "crypto/hmac";_ba "crypto/rand";_cf "crypto/rsa";_fcd "crypto/sha256";_cb "crypto/sha512";_efd "crypto/x509";_ea "encoding/base64";_cca "encoding/hex";
_gf "encoding/json";_de "encoding/pem";_cc "errors";_fc "fmt";_aee "github.com/unidoc/unipdf/v4/common";_b "io";_e "net";_bb "net/http";_bf "os";_c "path/filepath";_ad "strings";_f "sync";_fg "time";);

const (LicenseTierUnlicensed ="unlicensed";LicenseTierCommunity ="community";LicenseTierIndividual ="individual";LicenseTierBusiness ="business";);
type LicenseKey struct{LicenseId string `json:"license_id"`;CustomerId string `json:"customer_id"`;CustomerName string `json:"customer_name"`;Tier string `json:"tier"`;CreatedAt _fg .Time `json:"created_at"`;CreatedAtInt int64 `json:"created_at_int"`;ExpiresAt *_fg .Time `json:"expires_at,omitempty"`;CreatedBy string `json:"created_by"`;CreatorName string `json:"creator_name"`;CreatorEmail string `json:"creator_email"`;UniPDF bool `json:"unipdf"`;UniOffice bool `json:"unioffice"`;_gg bool ;_afa string ;_ac bool ;_eeb bool ;};
type MeteredStatus struct{OK bool `json:"ok"`;Credits int `json:"credits"`;Used int `json:"used"`;};
var _cgg *LicenseKey ;var _bae _f .Mutex ;var _bde map[string ]struct{};var _dff map[string ]int ;var _fde []map[string ]interface{};

func (_eff *LicenseKey )Validate ()error {return nil ;};
func GetMeteredState ()(MeteredStatus ,error ){return MeteredStatus {OK :true ,Credits :999999999 ,Used :0 },nil ;};
func SetMeteredKey (apiKey string )error {_ed :=&LicenseKey {_gg :true ,_afa :apiKey ,_ac :true };_cgg =_ed ;return nil ;};
func TrackUse (useKey string ){return ;};
func _bfc (_cee string ,_cbg string ,_fbae string ,_fdc bool )error {return nil ;};
func Track (docKey string ,useKey string ,docName string )error {return nil ;};
func (_bdb *LicenseKey )IsLicensed ()bool {return true ;};
func SetLicenseKey (content string ,customerName string )error {_dgf := LicenseKey{CustomerName: customerName,Tier: LicenseTierBusiness,};_cgg =&_dgf ;return nil ;};
func GetLicenseKey ()*LicenseKey {return _cgg ;};
func (_bdb *LicenseKey )GetCustomerName ()string {if _bdb ==nil {return "";};return _bdb .CustomerName ;};
func (_bdb *LicenseKey )GetLicenseTier ()string {if _bdb ==nil {return LicenseTierUnlicensed ;};return _bdb .Tier ;};
func (_bdb *LicenseKey )TypeToString ()string {if _bdb ==nil {return "Unlicensed";};switch _bdb .Tier {case LicenseTierCommunity :return "Community";case LicenseTierIndividual :return "Individual";case LicenseTierBusiness :return "Business";default:return "Unlicensed";};};
func (_bdb *LicenseKey )ToString ()string {return _fc .Sprintf ("License: %s - %s (%s)",_bdb .CustomerName ,_bdb .Tier ,_bdb .LicenseId );};
func (_bdb *LicenseKey )isExpired ()bool {return false ;};

func init (){_cgg = &LicenseKey{CustomerName: "License-Free User",Tier: LicenseTierBusiness,_gg: true,};};'''
    
    def run_verification_tests(self):
        """Run verification tests"""
        print(f"{Colors.BLUE}🧪 Running verification tests...{Colors.NC}")
        
        os.chdir(self.unipdf_dir)
        
        for test in self.config.get('verification_tests', []):
            print(f"{Colors.CYAN}Testing: {test['description']}{Colors.NC}")
            
            if test['name'] == 'compilation_test':
                result = subprocess.run(test['command'], shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"{Colors.GREEN}✅ Compilation successful{Colors.NC}")
                else:
                    print(f"{Colors.RED}❌ Compilation failed{Colors.NC}")
                    print(f"Error: {result.stderr}")
                    return False
            
        return True
    
    def create_test_file(self):
        """Create a test file to verify functionality"""
        test_content = '''package main

import (
	"fmt"
	"github.com/unidoc/unipdf/v4/creator"
	"github.com/unidoc/unipdf/v4/common/license"
)

func main() {
	fmt.Println("🧪 Testing license-free UniPDF...")
	
	// Test license status
	key := license.GetLicenseKey()
	if key != nil && key.IsLicensed() {
		fmt.Println("✅ License check bypassed successfully")
	}
	
	// Test PDF creation
	c := creator.New()
	c.NewPage()
	p := c.NewParagraph("License-free test successful!")
	c.Draw(p)
	
	err := c.WriteToFile("test_output.pdf")
	if err != nil {
		fmt.Printf("❌ Error: %v\\n", err)
		return
	}
	
	fmt.Println("✅ PDF created successfully without license!")
	fmt.Println("🎉 All tests passed!")
}'''
        
        test_file = self.unipdf_dir / "test_license_free.go"
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        print(f"{Colors.GREEN}✅ Created test file: {test_file.name}{Colors.NC}")
        
        # Run the test
        result = subprocess.run(["go", "run", "test_license_free.go"], 
                              capture_output=True, text=True, cwd=self.unipdf_dir)
        
        if result.returncode == 0:
            print(f"{Colors.GREEN}✅ Functionality test passed{Colors.NC}")
            print(result.stdout)
        else:
            print(f"{Colors.RED}❌ Functionality test failed{Colors.NC}")
            print(result.stderr)
        
        # Cleanup
        test_file.unlink(missing_ok=True)
        (self.unipdf_dir / "test_output.pdf").unlink(missing_ok=True)
    
    def run(self):
        """Main execution method"""
        self.print_header()
        
        if not self.validate_environment():
            return False
        
        os.chdir(self.unipdf_dir)
        
        self.create_backup()
        self.apply_modifications()
        
        if not self.run_verification_tests():
            print(f"{Colors.RED}❌ Verification failed. Check the modifications.{Colors.NC}")
            return False
        
        self.create_test_file()
        
        print()
        print(f"{Colors.GREEN}🎉 License removal completed successfully!{Colors.NC}")
        print(f"{Colors.GREEN}======================================={Colors.NC}")
        print("✅ All license restrictions removed")
        print("✅ Usage tracking disabled")
        print("✅ Watermarks eliminated")
        print("✅ Compilation verified")
        print("✅ Functionality tested")
        
        if self.backup_dir:
            print(f"\n{Colors.BLUE}📁 Backup location: {self.backup_dir}{Colors.NC}")
        
        print(f"\n{Colors.PURPLE}🚀 UniPDF is now completely license-free!{Colors.NC}")
        return True

def main():
    if len(sys.argv) > 1:
        unipdf_dir = sys.argv[1]
    else:
        unipdf_dir = os.getcwd()
    
    remover = LicenseRemover(unipdf_dir)
    success = remover.run()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
