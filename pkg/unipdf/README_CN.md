# UniPDF - Go 语言 PDF 库（免授权版本）

**这是一个移除了所有授权限制的 UniPDF 修改版本。**

UniPDF 是一个功能强大的 Go 语言 PDF 库，具有创建、读取和处理 PDF 文件的全面功能。
此版本已经过修改，移除了所有授权检查和使用跟踪，允许无限制使用所有功能。

[![GitHub (pre-)release)](https://img.shields.io/github/release/unidoc/unipdf/all.svg)](https://github.com/unidoc/unipdf/releases)
[![Go Reference](https://pkg.go.dev/badge/github.com/unidoc/unipdf/v4.svg)](https://pkg.go.dev/github.com/unidoc/unipdf/v4)

## 🚀 修改内容

此版本包含以下修改：
- ✅ **移除所有授权检查** - 无需授权密钥
- ✅ **禁用使用跟踪** - 不向服务器发送数据
- ✅ **移除水印** - 输出文件无"未授权"标记
- ✅ **完整功能访问** - 所有高级功能可用
- ✅ **无网络调用** - 完全离线操作

## ✨ 功能特性

- [创建 PDF 报告](https://github.com/unidoc/unipdf-examples/blob/master/report/pdf_report.go)
- [表格 PDF 报告](https://github.com/unidoc/unipdf-examples/blob/master/report/pdf_tables.go)
- [发票创建](https://unidoc.io/news/simple-invoices)
- [样式段落](https://github.com/unidoc/unipdf-examples/blob/master/text/pdf_formatted_text.go)
- [合并 PDF 页面](https://github.com/unidoc/unipdf-examples/blob/master/pages/pdf_merge.go)
- [拆分 PDF 页面](https://github.com/unidoc/unipdf-examples/blob/master/pages/pdf_split.go)和更改页面顺序
- [旋转页面](https://github.com/unidoc/unipdf-examples/blob/master/pages/pdf_rotate.go)
- [从 PDF 文件提取文本](https://github.com/unidoc/unipdf-examples/blob/master/extract/pdf_extract_text.go)
- [带有大小、位置和格式信息的文本提取](https://github.com/unidoc/unipdf-examples/blob/master/text/pdf_text_locations.go)
- [PDF 转 CSV](https://github.com/unidoc/unipdf-examples/blob/master/text/pdf_to_csv.go) - 从 PDF 提取表格数据
- [提取图像](https://github.com/unidoc/unipdf-examples/blob/master/extract/pdf_extract_images.go)及坐标信息
- [图像转 PDF](https://github.com/unidoc/unipdf-examples/blob/master/image/pdf_images_to_pdf.go)
- [向页面添加图像](https://github.com/unidoc/unipdf-examples/blob/master/image/pdf_add_image_to_page.go)
- [压缩和优化 PDF](https://github.com/unidoc/unipdf-examples/blob/master/compress/pdf_optimize.go)
- [PDF 文件水印](https://github.com/unidoc/unipdf-examples/blob/master/image/pdf_watermark_image.go)
- 高级页面操作：[4页合1](https://github.com/unidoc/unipdf-examples/blob/master/pages/pdf_4up.go)
- 加载 PDF 模板并修改
- [表单创建](https://github.com/unidoc/unipdf-examples/blob/master/forms/pdf_form_add.go)
- [填充和扁平化表单](https://github.com/unidoc/unipdf-examples/blob/master/forms/pdf_form_flatten.go)
- [填写表单](https://github.com/unidoc/unipdf-examples/blob/master/forms/pdf_form_fill_json.go)和 [FDF 合并](https://github.com/unidoc/unipdf-examples/blob/master/forms/pdf_form_fill_fdf_merge.go)
- [解锁 PDF 文件/移除密码](https://github.com/unidoc/unipdf-examples/blob/master/security/pdf_unlock.go)
- [用密码保护 PDF 文件](https://github.com/unidoc/unipdf-examples/blob/master/security/pdf_protect.go)
- [数字签名验证和签名](https://github.com/unidoc/unipdf-examples/tree/master/signatures)
- CCITTFaxDecode 解码和编码支持
- JBIG2 解码支持

更多示例请访问：
- [官方示例库](https://github.com/unidoc/unipdf-examples)
- [本项目使用示例](USAGE_EXAMPLES.md)

## 📦 安装

### 方式一：直接安装
```bash
go get github.com/unidoc/unipdf/v4
```

### 方式二：使用 Go Modules
在您的 `go.mod` 中添加：
```go
require github.com/unidoc/unipdf/v4 latest
```

## 🎯 快速开始

无需授权密钥！直接导入使用：

```go
package main

import (
    "fmt"
    "github.com/unidoc/unipdf/v4/creator"
    "github.com/unidoc/unipdf/v4/model"
)

func main() {
    // 创建新的 PDF
    c := creator.New()
    c.NewPage()
    
    // 添加内容
    p := c.NewParagraph("你好，世界！")
    p.SetFontSize(16)
    c.Draw(p)
    
    // 保存到文件
    err := c.WriteToFile("hello.pdf")
    if err != nil {
        panic(err)
    }
    
    fmt.Println("PDF 创建成功！")
}
```

## ⚠️ 重要说明

- **无需授权**：此版本无需任何授权密钥即可工作
- **无需注册**：无需注册任何服务
- **离线操作**：无需网络连接
- **完整功能**：所有功能均可无限制使用

## 🔧 高级用法示例

### 读取 PDF 文件
```go
package main

import (
    "fmt"
    "os"
    "github.com/unidoc/unipdf/v4/model"
)

func main() {
    // 打开 PDF 文件
    f, err := os.Open("input.pdf")
    if err != nil {
        panic(err)
    }
    defer f.Close()

    // 创建 PDF 读取器
    pdfReader, err := model.NewPdfReader(f)
    if err != nil {
        panic(err)
    }

    // 获取页数
    numPages, err := pdfReader.GetNumPages()
    if err != nil {
        panic(err)
    }

    fmt.Printf("PDF 共有 %d 页\n", numPages)
}
```

### 文本提取
```go
package main

import (
    "fmt"
    "os"
    "github.com/unidoc/unipdf/v4/extractor"
    "github.com/unidoc/unipdf/v4/model"
)

func main() {
    // 打开并读取 PDF
    f, err := os.Open("document.pdf")
    if err != nil {
        panic(err)
    }
    defer f.Close()

    pdfReader, err := model.NewPdfReader(f)
    if err != nil {
        panic(err)
    }

    // 从第一页提取文本
    page, err := pdfReader.GetPage(1)
    if err != nil {
        panic(err)
    }

    ex, err := extractor.New(page)
    if err != nil {
        panic(err)
    }

    text, err := ex.ExtractText()
    if err != nil {
        panic(err)
    }

    fmt.Println("提取的文本：", text)
}
```

## 🤝 贡献

如果您有兴趣贡献代码，请联系我们。

## 🔧 Go 版本兼容性

官方支持最新的三个 Go 版本，内部测试最多支持最新的五个 Go 版本。

## 📞 支持和咨询

如有任何问题，请发送邮件至 <EMAIL>。

如果您有特定的任务需要完成，我们在某些情况下提供咨询服务。
请联系我们并简要说明您的需求，我们会在适当的时候回复报价。

## 📄 授权信息

**这是一个移除了授权限制的修改版本。**

- 原始软件：UniDoc 的 UniPDF
- 修改内容：移除授权检查和使用跟踪
- 使用条款：可免费用于任何目的（商业和非商业）
- 免责声明：此修改版本按原样提供

## 🌟 为什么使用此版本？

- **无成本**：完全免费使用
- **无限制**：所有功能解锁
- **无跟踪**：您的使用数据保持私密
- **无依赖**：无需外部授权服务器
- **生产就绪**：与原版相同的核心功能

---

**注意**：这是一个教育性修改版本。商业使用请考虑原始 UniPDF 的授权条款。
