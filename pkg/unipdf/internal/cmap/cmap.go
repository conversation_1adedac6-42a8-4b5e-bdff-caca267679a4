//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package cmap ;import (_eb "bufio";_a "bytes";_gef "encoding/hex";_b "errors";_ge "fmt";_d "github.com/unidoc/unipdf/v4/common";_cf "github.com/unidoc/unipdf/v4/core";_ab "github.com/unidoc/unipdf/v4/internal/cmap/bcmaps";_f "io";_ca "sort";_e "strconv";
_bd "strings";_c "unicode/utf16";);func (cmap *CMap )CharcodeBytesToUnicode (data []byte )(string ,int ){_ebdd ,_ege :=cmap .BytesToCharcodes (data );if !_ege {_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0043\u0068\u0061\u0072\u0063\u006f\u0064\u0065\u0042\u0079\u0074\u0065s\u0054\u006f\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u002e\u0020\u004e\u006f\u0074\u0020\u0069n\u0020\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0073\u002e\u0020\u0064\u0061\u0074\u0061\u003d\u005b\u0025\u0020\u0030\u0032\u0078]\u0020\u0063\u006d\u0061\u0070=\u0025\u0073",data ,cmap );
return "",0;};_ggf :=make ([]string ,len (_ebdd ));var _dagf []CharCode ;for _edc ,_gad :=range _ebdd {_cca ,_abg :=cmap ._ccc [_gad ];if !_abg {_dagf =append (_dagf ,_gad );_cca =MissingCodeString ;};_ggf [_edc ]=_cca ;};_gea :=_bd .Join (_ggf ,"");if len (_dagf )> 0{_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020C\u0068\u0061\u0072c\u006f\u0064\u0065\u0042y\u0074\u0065\u0073\u0054\u006f\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u002e\u0020\u004e\u006f\u0074\u0020\u0069\u006e\u0020\u006d\u0061\u0070\u002e\u000a"+"\u0009d\u0061t\u0061\u003d\u005b\u0025\u00200\u0032\u0078]\u003d\u0025\u0023\u0071\u000a"+"\u0009\u0063h\u0061\u0072\u0063o\u0064\u0065\u0073\u003d\u0025\u0030\u0032\u0078\u000a"+"\u0009\u006d\u0069\u0073\u0073\u0069\u006e\u0067\u003d\u0025\u0064\u0020%\u0030\u0032\u0078\u000a"+"\u0009\u0075\u006e\u0069\u0063\u006f\u0064\u0065\u003d`\u0025\u0073\u0060\u000a"+"\u0009\u0063\u006d\u0061\u0070\u003d\u0025\u0073",data ,string (data ),_ebdd ,len (_dagf ),_dagf ,_gea ,cmap );
};return _gea ,len (_dagf );};func IsPredefinedCMap (name string )bool {return _ab .AssetExists (name )};type cmapArray struct{Array []cmapObject ;};func (_gbdf *cMapParser )parseDict ()(cmapDict ,error ){_d .Log .Trace ("\u0052\u0065\u0061\u0064\u0069\u006e\u0067\u0020\u0050\u0044\u0046\u0020D\u0069\u0063\u0074\u0021");
_aee :=_gaag ();_bdbb ,_ :=_gbdf ._edb .ReadByte ();if _bdbb !='<'{return _aee ,ErrBadCMapDict ;};_bdbb ,_ =_gbdf ._edb .ReadByte ();if _bdbb !='<'{return _aee ,ErrBadCMapDict ;};for {_gbdf .skipSpaces ();_bcg ,_ccbd :=_gbdf ._edb .Peek (2);if _ccbd !=nil {return _aee ,_ccbd ;
};if (_bcg [0]=='>')&&(_bcg [1]=='>'){_gbdf ._edb .ReadByte ();_gbdf ._edb .ReadByte ();break ;};_aggc ,_ccbd :=_gbdf .parseName ();_d .Log .Trace ("\u004be\u0079\u003a\u0020\u0025\u0073",_aggc .Name );if _ccbd !=nil {_d .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u0052\u0065\u0074\u0075\u0072\u006e\u0069\u006e\u0067\u0020\u006e\u0061\u006d\u0065\u002e\u0020\u0065\u0072r=\u0025\u0076",_ccbd );
return _aee ,_ccbd ;};_gbdf .skipSpaces ();_bddb ,_ccbd :=_gbdf .parseObject ();if _ccbd !=nil {return _aee ,_ccbd ;};_aee .Dict [_aggc .Name ]=_bddb ;_gbdf .skipSpaces ();_bcg ,_ccbd =_gbdf ._edb .Peek (3);if _ccbd !=nil {return _aee ,_ccbd ;};if string (_bcg )=="\u0064\u0065\u0066"{_gbdf ._edb .Discard (3);
};};return _aee ,nil ;};func (_fbc *cMapParser )parseHexString ()(cmapHexString ,error ){_fbc ._edb .ReadByte ();_fag :=[]byte ("\u0030\u0031\u0032\u003345\u0036\u0037\u0038\u0039\u0061\u0062\u0063\u0064\u0065\u0066\u0041\u0042\u0043\u0044E\u0046");_acaf :=_a .Buffer {};
for {_fbc .skipSpaces ();_bbc ,_dcee :=_fbc ._edb .Peek (1);if _dcee !=nil {return cmapHexString {},_dcee ;};if _bbc [0]=='>'{_fbc ._edb .ReadByte ();break ;};_cbba ,_ :=_fbc ._edb .ReadByte ();if _a .IndexByte (_fag ,_cbba )>=0{_acaf .WriteByte (_cbba );
};};if _acaf .Len ()%2==1{_d .Log .Debug ("\u0070\u0061rs\u0065\u0048\u0065x\u0053\u0074\u0072\u0069ng:\u0020ap\u0070\u0065\u006e\u0064\u0069\u006e\u0067 '\u0030\u0027\u0020\u0074\u006f\u0020\u0025#\u0071",_acaf .String ());_acaf .WriteByte ('0');};_ecfe :=_acaf .Len ()/2;
_ageg ,_ :=_gef .DecodeString (_acaf .String ());return cmapHexString {_fcga :_ecfe ,_ceae :_ageg },nil ;};func (cmap *CMap )toBfData ()string {if len (cmap ._ccc )==0{return "";};_ecc :=make ([]CharCode ,0,len (cmap ._ccc ));for _adec :=range cmap ._ccc {_ecc =append (_ecc ,_adec );
};_ca .Slice (_ecc ,func (_bfc ,_fb int )bool {return _ecc [_bfc ]< _ecc [_fb ]});var _ffg []charRange ;_agf :=charRange {_ecc [0],_ecc [0]};_cbf :=cmap ._ccc [_ecc [0]];for _ ,_fdc :=range _ecc [1:]{_beb :=cmap ._ccc [_fdc ];if _fdc ==_agf ._ee +1&&_ded (_beb )==_ded (_cbf )+1{_agf ._ee =_fdc ;
}else {_ffg =append (_ffg ,_agf );_agf ._cc ,_agf ._ee =_fdc ,_fdc ;};_cbf =_beb ;};_ffg =append (_ffg ,_agf );var _fcc []CharCode ;var _fa []fbRange ;for _ ,_egg :=range _ffg {if _egg ._cc ==_egg ._ee {_fcc =append (_fcc ,_egg ._cc );}else {_fa =append (_fa ,fbRange {_fd :_egg ._cc ,_bb :_egg ._ee ,_dad :cmap ._ccc [_egg ._cc ]});
};};_d .Log .Trace ("\u0063\u0068ar\u0052\u0061\u006eg\u0065\u0073\u003d\u0025d f\u0062Ch\u0061\u0072\u0073\u003d\u0025\u0064\u0020fb\u0052\u0061\u006e\u0067\u0065\u0073\u003d%\u0064",len (_ffg ),len (_fcc ),len (_fa ));var _fccg []string ;if len (_fcc )> 0{_fcag :=(len (_fcc )+_bab -1)/_bab ;
for _cad :=0;_cad < _fcag ;_cad ++{_abf :=_cef (len (_fcc )-_cad *_bab ,_bab );_fccg =append (_fccg ,_ge .Sprintf ("\u0025\u0064\u0020\u0062\u0065\u0067\u0069\u006e\u0062f\u0063\u0068\u0061\u0072",_abf ));for _bdg :=0;_bdg < _abf ;_bdg ++{_fdg :=_fcc [_cad *_bab +_bdg ];
_bbg :=cmap ._ccc [_fdg ];_fccg =append (_fccg ,_ge .Sprintf ("\u003c%\u0030\u0034\u0078\u003e\u0020\u0025s",_fdg ,_ac (_bbg )));};_fccg =append (_fccg ,"\u0065n\u0064\u0062\u0066\u0063\u0068\u0061r");};};if len (_fa )> 0{_eacb :=(len (_fa )+_bab -1)/_bab ;
for _cd :=0;_cd < _eacb ;_cd ++{_eef :=_cef (len (_fa )-_cd *_bab ,_bab );_fccg =append (_fccg ,_ge .Sprintf ("\u0025d\u0020b\u0065\u0067\u0069\u006e\u0062\u0066\u0072\u0061\u006e\u0067\u0065",_eef ));for _dac :=0;_dac < _eef ;_dac ++{_bfd :=_fa [_cd *_bab +_dac ];
_fccg =append (_fccg ,_ge .Sprintf ("\u003c%\u00304\u0078\u003e\u003c\u0025\u0030\u0034\u0078\u003e\u0020\u0025\u0073",_bfd ._fd ,_bfd ._bb ,_ac (_bfd ._dad )));};_fccg =append (_fccg ,"\u0065\u006e\u0064\u0062\u0066\u0072\u0061\u006e\u0067\u0065");};};
return _bd .Join (_fccg ,"\u000a");};type CMap struct{*cMapParser ;_cae string ;_dd int ;_gd int ;_cb string ;_def string ;_af CIDSystemInfo ;_bdf []Codespace ;_abe map[CharCode ]CharCode ;_eg map[CharCode ]CharCode ;_ccc map[CharCode ]string ;_gb map[string ]CharCode ;
_ec []byte ;_ddf *_cf .PdfObjectStream ;_geb integer ;};type cmapString struct{String string ;};type cmapObject interface{};func (cmap *CMap )CIDSystemInfo ()CIDSystemInfo {return cmap ._af };func (cmap *CMap )parseVersion ()error {_dbf :="";_aaee :=false ;
for _gcgc :=0;_gcgc < 3&&!_aaee ;_gcgc ++{_gbdd ,_fcbe :=cmap .parseObject ();if _fcbe !=nil {return _fcbe ;};switch _fbef :=_gbdd .(type ){case cmapOperand :switch _fbef .Operand {case "\u0064\u0065\u0066":_aaee =true ;default:_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0070\u0061\u0072\u0073\u0065\u0056e\u0072\u0073\u0069\u006f\u006e\u003a \u0073\u0074\u0061\u0074\u0065\u0020\u0065\u0072\u0072\u006f\u0072\u002e\u0020o\u003d\u0025\u0023\u0076",_gbdd );
return ErrBadCMap ;};case cmapInt :_dbf =_ge .Sprintf ("\u0025\u0064",_fbef ._fbag );case cmapFloat :_dbf =_ge .Sprintf ("\u0025\u0066",_fbef ._aed );case cmapString :_dbf =_fbef .String ;default:_d .Log .Debug ("\u0045\u0052RO\u0052\u003a\u0020p\u0061\u0072\u0073\u0065Ver\u0073io\u006e\u003a\u0020\u0042\u0061\u0064\u0020ty\u0070\u0065\u002e\u0020\u006f\u003d\u0025#\u0076",_gbdd );
};};cmap ._cb =_dbf ;return nil ;};func (cmap *CMap )parseBfrange ()error {for {var _bafg CharCode ;_fad ,_bdb :=cmap .parseObject ();if _bdb !=nil {if _bdb ==_f .EOF {break ;};return _bdb ;};switch _cffed :=_fad .(type ){case cmapOperand :if _cffed .Operand ==_dbfd {return nil ;
};return _b .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065d\u0020\u006fp\u0065\u0072\u0061\u006e\u0064");case cmapHexString :_bafg =_acfb (_cffed );default:return _b .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0074\u0079\u0070\u0065");
};var _eaca CharCode ;_fad ,_bdb =cmap .parseObject ();if _bdb !=nil {if _bdb ==_f .EOF {break ;};return _bdb ;};switch _gfgc :=_fad .(type ){case cmapOperand :_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0049\u006e\u0063\u006f\u006d\u0070\u006c\u0065\u0074\u0065\u0020\u0062\u0066r\u0061\u006e\u0067\u0065\u0020\u0074\u0072i\u0070\u006c\u0065\u0074");
return ErrBadCMap ;case cmapHexString :_eaca =_acfb (_gfgc );if _eaca > 0xffff{_eaca =0xffff;};default:_d .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a \u0055\u006e\u0065\u0078\u0070e\u0063t\u0065d\u0020\u0074\u0079\u0070\u0065\u0020\u0025T",_fad );return ErrBadCMap ;
};_fad ,_bdb =cmap .parseObject ();if _bdb !=nil {if _bdb ==_f .EOF {break ;};return _bdb ;};switch _bgd :=_fad .(type ){case cmapArray :if len (_bgd .Array )!=int (_eaca -_bafg )+1{_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0049\u006e\u0076\u0061\u006c\u0069d\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0069\u0074\u0065\u006d\u0073\u0020\u0069\u006e\u0020a\u0072\u0072\u0061\u0079");
return ErrBadCMap ;};for _cgf :=_bafg ;_cgf <=_eaca ;_cgf ++{_fbd :=_bgd .Array [_cgf -_bafg ];_gffc ,_acf :=_fbd .(cmapHexString );if !_acf {return _b .New ("\u006e\u006f\u006e-h\u0065\u0078\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0069\u006e\u0020\u0061\u0072\u0072\u0061\u0079");
};_deg :=_dbe (_gffc );cmap ._ccc [_cgf ]=string (_deg );};case cmapHexString :_fdb :=_dbe (_bgd );_cbd :=len (_fdb );for _fce :=_bafg ;_fce <=_eaca ;_fce ++{cmap ._ccc [_fce ]=string (_fdb );if _cbd > 0{_fdb [_cbd -1]++;}else {_d .Log .Debug ("\u004e\u006f\u0020c\u006d\u0061\u0070\u0020\u0074\u0061\u0072\u0067\u0065\u0074\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0069\u0065d\u0020\u0066\u006f\u0072\u0020\u0025\u0023\u0076",_fce );
};if _fce ==1<<32-1{break ;};};default:_d .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a \u0055\u006e\u0065\u0078\u0070e\u0063t\u0065d\u0020\u0074\u0079\u0070\u0065\u0020\u0025T",_fad );return ErrBadCMap ;};};return nil ;};func (cmap *CMap )Type ()int {return cmap ._gd };
func _bg (_ga string )(*CMap ,error ){_gga ,_gbd :=_ab .Asset (_ga );if _gbd !=nil {return nil ,_gbd ;};return LoadCmapFromDataCID (_gga );};func (cmap *CMap )inCodespace (_daa CharCode ,_gcg int )bool {for _ ,_egf :=range cmap ._bdf {if _egf .Low <=_daa &&_daa <=_egf .High &&_gcg ==_egf .NumBytes {return true ;
};};return false ;};type cmapDict struct{Dict map[string ]cmapObject ;};type Codespace struct{NumBytes int ;Low CharCode ;High CharCode ;};type charRange struct{_cc CharCode ;_ee CharCode ;};const (_cfgc ="\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f";
_dgb ="\u0062e\u0067\u0069\u006e\u0063\u006d\u0061p";_eae ="\u0065n\u0064\u0063\u006d\u0061\u0070";_gfe ="\u0062\u0065\u0067\u0069nc\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0072\u0061\u006e\u0067\u0065";_fcf ="\u0065\u006e\u0064\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065r\u0061\u006e\u0067\u0065";
_gfb ="b\u0065\u0067\u0069\u006e\u0062\u0066\u0063\u0068\u0061\u0072";_gca ="\u0065n\u0064\u0062\u0066\u0063\u0068\u0061r";_dgf ="\u0062\u0065\u0067i\u006e\u0062\u0066\u0072\u0061\u006e\u0067\u0065";_dbfd ="\u0065\u006e\u0064\u0062\u0066\u0072\u0061\u006e\u0067\u0065";
_eafc ="\u0062\u0065\u0067\u0069\u006e\u0063\u0069\u0064\u0072\u0061\u006e\u0067\u0065";_egga ="e\u006e\u0064\u0063\u0069\u0064\u0072\u0061\u006e\u0067\u0065";_caa ="\u0075s\u0065\u0063\u006d\u0061\u0070";_dbfe ="\u0057\u004d\u006fd\u0065";_fga ="\u0043\u004d\u0061\u0070\u004e\u0061\u006d\u0065";
_ddaf ="\u0043\u004d\u0061\u0070\u0054\u0079\u0070\u0065";_dgg ="C\u004d\u0061\u0070\u0056\u0065\u0072\u0073\u0069\u006f\u006e";);func (_ag *CIDSystemInfo )String ()string {return _ge .Sprintf ("\u0025\u0073\u002d\u0025\u0073\u002d\u0025\u0030\u0033\u0064",_ag .Registry ,_ag .Ordering ,_ag .Supplement );
};func (cmap *CMap )CharcodeToUnicode (code CharCode )(string ,bool ){if _afd ,_eee :=cmap ._ccc [code ];_eee {return _afd ,true ;};return MissingCodeString ,false ;};func (_ffc *cMapParser )parseObject ()(cmapObject ,error ){_ffc .skipSpaces ();for {_acad ,_eceb :=_ffc ._edb .Peek (2);
if _eceb !=nil {return nil ,_eceb ;};if _acad [0]=='%'{_ffc .parseComment ();_ffc .skipSpaces ();continue ;}else if _acad [0]=='/'{_add ,_fcg :=_ffc .parseName ();return _add ,_fcg ;}else if _acad [0]=='('{_cgd ,_bed :=_ffc .parseString ();return _cgd ,_bed ;
}else if _acad [0]=='['{_ebfd ,_ffgg :=_ffc .parseArray ();return _ebfd ,_ffgg ;}else if (_acad [0]=='<')&&(_acad [1]=='<'){_cbbb ,_dba :=_ffc .parseDict ();return _cbbb ,_dba ;}else if _acad [0]=='<'{_befc ,_bdd :=_ffc .parseHexString ();return _befc ,_bdd ;
}else if _cf .IsDecimalDigit (_acad [0])||(_acad [0]=='-'&&_cf .IsDecimalDigit (_acad [1])){_egbf ,_efd :=_ffc .parseNumber ();if _efd !=nil {return nil ,_efd ;};return _egbf ,nil ;}else {_bffd ,_ebfde :=_ffc .parseOperand ();if _ebfde !=nil {return nil ,_ebfde ;
};return _bffd ,nil ;};};};func (cmap *CMap )parseBfchar ()error {for {_fdf ,_cfc :=cmap .parseObject ();if _cfc !=nil {if _cfc ==_f .EOF {break ;};return _cfc ;};var _efe CharCode ;switch _bbgd :=_fdf .(type ){case cmapOperand :if _bbgd .Operand ==_gca {return nil ;
};return _b .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065d\u0020\u006fp\u0065\u0072\u0061\u006e\u0064");case cmapHexString :_efe =_acfb (_bbgd );default:return _b .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0074\u0079\u0070\u0065");
};_fdf ,_cfc =cmap .parseObject ();if _cfc !=nil {if _cfc ==_f .EOF {break ;};return _cfc ;};var _gcd []rune ;switch _fffb :=_fdf .(type ){case cmapOperand :if _fffb .Operand ==_gca {return nil ;};_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0055\u006e\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020o\u0070\u0065\u0072\u0061\u006e\u0064\u002e\u0020\u0025\u0023\u0076",_fffb );
return ErrBadCMap ;case cmapHexString :_gcd =_dbe (_fffb );case cmapName :_d .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064 \u006e\u0061\u006de\u002e \u0025\u0023\u0076",_fffb );_gcd =[]rune {MissingCodeRune };
default:_d .Log .Debug ("E\u0052\u0052\u004f\u0052\u003a\u0020U\u006e\u0065\u0078\u0070\u0065\u0063\u0074\u0065\u0064 \u0074\u0079\u0070e\u002e \u0025\u0023\u0076",_fdf );return ErrBadCMap ;};cmap ._ccc [_efe ]=string (_gcd );};return nil ;};type cMapParser struct{_edb *_eb .Reader };
func (cmap *CMap )matchCode (_dce []byte )(_dea CharCode ,_gadg int ,_dabd bool ){for _bbd :=0;_bbd < _da ;_bbd ++{if _bbd < len (_dce ){_dea =_dea <<8|CharCode (_dce [_bbd ]);_gadg ++;};_dabd =cmap .inCodespace (_dea ,_bbd +1);if _dabd {return _dea ,_gadg ,true ;
};};_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0063o\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0020m\u0061t\u0063\u0068\u0065\u0073\u0020\u0062\u0079\u0074\u0065\u0073\u003d\u005b\u0025\u0020\u0030\u0032\u0078\u005d=\u0025\u0023\u0071\u0020\u0063\u006d\u0061\u0070\u003d\u0025\u0073",_dce ,string (_dce ),cmap );
return 0,0,false ;};func (cmap *CMap )parseWMode ()error {var _egb int ;_eab :=false ;for _bca :=0;_bca < 3&&!_eab ;_bca ++{_fff ,_babf :=cmap .parseObject ();if _babf !=nil {return _babf ;};switch _ggbg :=_fff .(type ){case cmapOperand :switch _ggbg .Operand {case "\u0064\u0065\u0066":_eab =true ;
default:_d .Log .Error ("\u0070\u0061\u0072\u0073\u0065\u0057\u004d\u006f\u0064\u0065:\u0020\u0073\u0074\u0061\u0074\u0065\u0020e\u0072\u0072\u006f\u0072\u002e\u0020\u006f\u003d\u0025\u0023\u0076",_fff );return ErrBadCMap ;};case cmapInt :_egb =int (_ggbg ._fbag );
};};cmap ._geb =integer {_beba :true ,_dcdgf :_egb };return nil ;};func LoadPredefinedCMap (name string )(*CMap ,error ){cmap ,_ad :=_bg (name );if _ad !=nil {return nil ,_ad ;};if cmap ._def ==""{cmap .computeInverseMappings ();return cmap ,nil ;};_abc ,_ad :=_bg (cmap ._def );
if _ad !=nil {return nil ,_ad ;};for _bbf ,_afc :=range _abc ._abe {if _ ,_adb :=cmap ._abe [_bbf ];!_adb {cmap ._abe [_bbf ]=_afc ;};};cmap ._bdf =append (cmap ._bdf ,_abc ._bdf ...);cmap .computeInverseMappings ();return cmap ,nil ;};func (cmap *CMap )parseSystemInfo ()error {_cee :=false ;
_acd :=false ;_dga :="";_eda :=false ;_afca :=CIDSystemInfo {};for _fdag :=0;_fdag < 50&&!_eda ;_fdag ++{_fba ,_cdgd :=cmap .parseObject ();if _cdgd !=nil {return _cdgd ;};switch _adfe :=_fba .(type ){case cmapDict :_eeb :=_adfe .Dict ;_gfg ,_cea :=_eeb ["\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079"];
if !_cea {_d .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");return ErrBadCMap ;};_faf ,_cea :=_gfg .(cmapString );if !_cea {_d .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");
return ErrBadCMap ;};_afca .Registry =_faf .String ;_gfg ,_cea =_eeb ["\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067"];if !_cea {_d .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");
return ErrBadCMap ;};_faf ,_cea =_gfg .(cmapString );if !_cea {_d .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");return ErrBadCMap ;};_afca .Ordering =_faf .String ;
_geg ,_cea :=_eeb ["\u0053\u0075\u0070\u0070\u006c\u0065\u006d\u0065\u006e\u0074"];if !_cea {_d .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");return ErrBadCMap ;};_bae ,_cea :=_geg .(cmapInt );
if !_cea {_d .Log .Debug ("\u0045\u0052\u0052\u004fR:\u0020\u0042\u0061\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006ef\u006f");return ErrBadCMap ;};_afca .Supplement =int (_bae ._fbag );_eda =true ;case cmapOperand :switch _adfe .Operand {case "\u0062\u0065\u0067i\u006e":_cee =true ;
case "\u0065\u006e\u0064":_eda =true ;case "\u0064\u0065\u0066":_acd =false ;};case cmapName :if _cee {_dga =_adfe .Name ;_acd =true ;};case cmapString :if _acd {switch _dga {case "\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079":_afca .Registry =_adfe .String ;
case "\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067":_afca .Ordering =_adfe .String ;};};case cmapInt :if _acd {switch _dga {case "\u0053\u0075\u0070\u0070\u006c\u0065\u006d\u0065\u006e\u0074":_afca .Supplement =int (_adfe ._fbag );};};};};if !_eda {_d .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0050\u0061\u0072\u0073\u0065\u0064\u0020\u0053\u0079\u0073\u0074\u0065\u006d\u0020\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074\u0020\u0069\u006ec\u006f\u0072\u0072\u0065\u0063\u0074\u006c\u0079");
return ErrBadCMap ;};cmap ._af =_afca ;return nil ;};const (_bab =100;_ebda ="\u000a\u002f\u0043\u0049\u0044\u0049\u006e\u0069\u0074\u0020\u002f\u0050\u0072\u006fc\u0053\u0065\u0074\u0020\u0066\u0069\u006e\u0064\u0072es\u006fu\u0072c\u0065 \u0062\u0065\u0067\u0069\u006e\u000a\u0031\u0032\u0020\u0064\u0069\u0063\u0074\u0020\u0062\u0065\u0067\u0069n\u000a\u0062\u0065\u0067\u0069\u006e\u0063\u006d\u0061\u0070\n\u002f\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065m\u0049\u006e\u0066\u006f\u0020\u003c\u003c\u0020\u002f\u0052\u0065\u0067\u0069\u0073t\u0072\u0079\u0020\u0028\u0041\u0064\u006f\u0062\u0065\u0029\u0020\u002f\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u0028\u0055\u0043\u0053)\u0020\u002f\u0053\u0075\u0070p\u006c\u0065\u006d\u0065\u006et\u0020\u0030\u0020\u003e\u003e\u0020\u0064\u0065\u0066\u000a\u002f\u0043\u004d\u0061\u0070\u004e\u0061\u006d\u0065\u0020\u002f\u0041\u0064\u006f\u0062\u0065-\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0055\u0043\u0053\u0020\u0064\u0065\u0066\u000a\u002fC\u004d\u0061\u0070\u0054\u0079\u0070\u0065\u0020\u0032\u0020\u0064\u0065\u0066\u000a\u0031\u0020\u0062\u0065\u0067\u0069\u006e\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063e\u0072\u0061n\u0067\u0065\n\u003c\u0030\u0030\u0030\u0030\u003e\u0020<\u0046\u0046\u0046\u0046\u003e\u000a\u0065\u006e\u0064\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065r\u0061\u006e\u0067\u0065\u000a";
_dge ="\u0065\u006e\u0064\u0063\u006d\u0061\u0070\u000a\u0043\u004d\u0061\u0070\u004e\u0061\u006d\u0065\u0020\u0063ur\u0072e\u006e\u0074\u0064\u0069\u0063\u0074\u0020\u002f\u0043\u004d\u0061\u0070 \u0064\u0065\u0066\u0069\u006e\u0065\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0020\u0070\u006fp\u000a\u0065\u006e\u0064\u000a\u0065\u006e\u0064\u000a";
);func _ac (_bfde string )string {_egd :=[]rune (_bfde );_egee :=make ([]string ,len (_egd ));for _cbc ,_bef :=range _egd {_egee [_cbc ]=_ge .Sprintf ("\u0025\u0030\u0034\u0078",_bef );};return _ge .Sprintf ("\u003c\u0025\u0073\u003e",_bd .Join (_egee ,""));
};func _ded (_gbb string )rune {_eaf :=[]rune (_gbb );return _eaf [len (_eaf )-1]};const (_da =4;MissingCodeRune ='\ufffd';MissingCodeString =string (MissingCodeRune ););type CIDSystemInfo struct{Registry string ;Ordering string ;Supplement int ;};func (_gcb *cMapParser )skipSpaces ()(int ,error ){_ggad :=0;
for {_bcf ,_acc :=_gcb ._edb .Peek (1);if _acc !=nil {return 0,_acc ;};if _cf .IsWhiteSpace (_bcf [0]){_gcb ._edb .ReadByte ();_ggad ++;}else {break ;};};return _ggad ,nil ;};func (cmap *CMap )CIDToCharcode (cid CharCode )(CharCode ,bool ){_ba ,_ecg :=cmap ._eg [cid ];
return _ba ,_ecg ;};func LoadCmapFromData (data []byte ,isSimple bool )(*CMap ,error ){_d .Log .Trace ("\u004c\u006fa\u0064\u0043\u006d\u0061\u0070\u0046\u0072\u006f\u006d\u0044\u0061\u0074\u0061\u003a\u0020\u0069\u0073\u0053\u0069\u006d\u0070\u006ce=\u0025\u0074",isSimple );
cmap :=_ddd (isSimple );cmap .cMapParser =_ecce (data );_ef :=cmap .parse ();if _ef !=nil {return nil ,_ef ;};if len (cmap ._bdf )==0{if cmap ._def !=""{return cmap ,nil ;};_d .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0073\u002e\u0020\u0063\u006d\u0061p=\u0025\u0073",cmap );
};cmap .computeInverseMappings ();return cmap ,nil ;};func _dcg (_gcf cmapHexString )rune {_fcde :=_dbe (_gcf );if _dfga :=len (_fcde );_dfga ==0{_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0068\u0065\u0078\u0054o\u0052\u0075\u006e\u0065\u002e\u0020\u0045\u0078p\u0065c\u0074\u0065\u0064\u0020\u0061\u0074\u0020\u006c\u0065\u0061\u0073\u0074\u0020\u006f\u006e\u0065\u0020\u0072u\u006e\u0065\u0020\u0073\u0068\u0065\u0078\u003d\u0025\u0023\u0076",_gcf );
return MissingCodeRune ;};if len (_fcde )> 1{_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0068\u0065\u0078\u0054\u006f\u0052\u0075\u006e\u0065\u002e\u0020\u0045\u0078p\u0065\u0063\u0074\u0065\u0064\u0020\u0065\u0078\u0061\u0063\u0074\u006c\u0079\u0020\u006f\u006e\u0065\u0020\u0072\u0075\u006e\u0065\u0020\u0073\u0068\u0065\u0078\u003d\u0025\u0023v\u0020\u002d\u003e\u0020\u0025#\u0076",_gcf ,_fcde );
};return _fcde [0];};func (_gde *cMapParser )parseComment ()(string ,error ){var _fcbb _a .Buffer ;_ ,_cegb :=_gde .skipSpaces ();if _cegb !=nil {return _fcbb .String (),_cegb ;};_agc :=true ;for {_gfd ,_bbbd :=_gde ._edb .Peek (1);if _bbbd !=nil {_d .Log .Debug ("p\u0061r\u0073\u0065\u0043\u006f\u006d\u006d\u0065\u006et\u003a\u0020\u0065\u0072r=\u0025\u0076",_bbbd );
return _fcbb .String (),_bbbd ;};if _agc &&_gfd [0]!='%'{return _fcbb .String (),ErrBadCMapComment ;};_agc =false ;if (_gfd [0]!='\r')&&(_gfd [0]!='\n'){_gaf ,_ :=_gde ._edb .ReadByte ();_fcbb .WriteByte (_gaf );}else {break ;};};return _fcbb .String (),nil ;
};func (cmap *CMap )parse ()error {var _cdg cmapObject ;for {_adf ,_edg :=cmap .parseObject ();if _edg !=nil {if _edg ==_f .EOF {break ;};_d .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0043\u004d\u0061\u0070\u003a\u0020\u0025\u0076",_edg );
return _edg ;};switch _fbe :=_adf .(type ){case cmapOperand :_ebe :=_fbe ;switch _ebe .Operand {case _gfe :_aga :=cmap .parseCodespaceRange ();if _aga !=nil {return _aga ;};case _eafc :_acb :=cmap .parseCIDRange ();if _acb !=nil {return _acb ;};case _gfb :_afgb :=cmap .parseBfchar ();
if _afgb !=nil {return _afgb ;};case _dgf :_ggd :=cmap .parseBfrange ();if _ggd !=nil {return _ggd ;};case _caa :if _cdg ==nil {_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0075\u0073\u0065\u0063m\u0061\u0070\u0020\u0077\u0069\u0074\u0068\u0020\u006e\u006f \u0061\u0072\u0067");
return ErrBadCMap ;};_fda ,_cge :=_cdg .(cmapName );if !_cge {_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0075\u0073\u0065\u0063\u006d\u0061\u0070\u0020\u0061\u0072\u0067\u0020\u006eo\u0074\u0020\u0061\u0020\u006e\u0061\u006de\u0020\u0025\u0023\u0076",_cdg );
return ErrBadCMap ;};cmap ._def =_fda .Name ;case _cfgc :_bfdb :=cmap .parseSystemInfo ();if _bfdb !=nil {return _bfdb ;};};case cmapName :_age :=_fbe ;switch _age .Name {case _cfgc :_cfg :=cmap .parseSystemInfo ();if _cfg !=nil {return _cfg ;};case _fga :_bad :=cmap .parseName ();
if _bad !=nil {return _bad ;};case _ddaf :_agaa :=cmap .parseType ();if _agaa !=nil {return _agaa ;};case _dgg :_dbd :=cmap .parseVersion ();if _dbd !=nil {return _dbd ;};case _dbfe :if _edg =cmap .parseWMode ();_edg !=nil {return _edg ;};};};_cdg =_adf ;
};return nil ;};func (_bgf *cMapParser )parseName ()(cmapName ,error ){_gfc :="";_agg :=false ;for {_bcfc ,_egeea :=_bgf ._edb .Peek (1);if _egeea ==_f .EOF {break ;};if _egeea !=nil {return cmapName {_gfc },_egeea ;};if !_agg {if _bcfc [0]=='/'{_agg =true ;
_bgf ._edb .ReadByte ();}else {_d .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u004e\u0061\u006d\u0065\u0020\u0073\u0074a\u0072t\u0069n\u0067 \u0077\u0069\u0074\u0068\u0020\u0025\u0073\u0020\u0028\u0025\u0020\u0078\u0029",_bcfc ,_bcfc );return cmapName {_gfc },_ge .Errorf ("\u0069n\u0076a\u006c\u0069\u0064\u0020\u006ea\u006d\u0065:\u0020\u0028\u0025\u0063\u0029",_bcfc [0]);
};}else {if _cf .IsWhiteSpace (_bcfc [0]){break ;}else if (_bcfc [0]=='/')||(_bcfc [0]=='[')||(_bcfc [0]=='(')||(_bcfc [0]==']')||(_bcfc [0]=='<')||(_bcfc [0]=='>'){break ;}else if _bcfc [0]=='#'{_fbaa ,_gda :=_bgf ._edb .Peek (3);if _gda !=nil {return cmapName {_gfc },_gda ;
};_bgf ._edb .Discard (3);_aaeb ,_gda :=_gef .DecodeString (string (_fbaa [1:3]));if _gda !=nil {return cmapName {_gfc },_gda ;};_gfc +=string (_aaeb );}else {_bbbb ,_ :=_bgf ._edb .ReadByte ();_gfc +=string (_bbbb );};};};return cmapName {_gfc },nil ;
};func NewCIDSystemInfo (obj _cf .PdfObject )(_ccb CIDSystemInfo ,_ff error ){_dadf ,_de :=_cf .GetDict (obj );if !_de {return CIDSystemInfo {},_cf .ErrTypeError ;};_ea ,_de :=_cf .GetStringVal (_dadf .Get ("\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079"));
if !_de {return CIDSystemInfo {},_cf .ErrTypeError ;};_dag ,_de :=_cf .GetStringVal (_dadf .Get ("\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067"));if !_de {return CIDSystemInfo {},_cf .ErrTypeError ;};_gc ,_de :=_cf .GetIntVal (_dadf .Get ("\u0053\u0075\u0070\u0070\u006c\u0065\u006d\u0065\u006e\u0074"));
if !_de {return CIDSystemInfo {},_cf .ErrTypeError ;};return CIDSystemInfo {Registry :_ea ,Ordering :_dag ,Supplement :_gc },nil ;};func (cmap *CMap )computeInverseMappings (){for _ece ,_abcc :=range cmap ._abe {if _gf ,_eea :=cmap ._eg [_abcc ];!_eea ||(_eea &&_gf > _ece ){cmap ._eg [_abcc ]=_ece ;
};};for _fca ,_eac :=range cmap ._ccc {if _df ,_dc :=cmap ._gb [_eac ];!_dc ||(_dc &&_df > _fca ){cmap ._gb [_eac ]=_fca ;};};_ca .Slice (cmap ._bdf ,func (_afg ,_bda int )bool {return cmap ._bdf [_afg ].Low < cmap ._bdf [_bda ].Low });};func _dbe (_gead cmapHexString )[]rune {if len (_gead ._ceae )==1{return []rune {rune (_gead ._ceae [0])};
};_eccee :=_gead ._ceae ;if len (_eccee )%2!=0{_eccee =append (_eccee ,0);_d .Log .Debug ("\u0045\u0052\u0052O\u0052\u003a\u0020\u0068\u0065\u0078\u0054\u006f\u0052\u0075\u006e\u0065\u0073\u002e\u0020\u0050\u0061\u0064\u0064\u0069\u006e\u0067\u0020\u0073\u0068\u0065\u0078\u003d\u0025#\u0076\u0020\u0074\u006f\u0020\u0025\u002b\u0076",_gead ,_eccee );
};_fcgd :=len (_eccee )>>1;_cade :=make ([]uint16 ,_fcgd );for _cfda :=0;_cfda < _fcgd ;_cfda ++{_cade [_cfda ]=uint16 (_eccee [_cfda <<1])<<8+uint16 (_eccee [_cfda <<1+1]);};_abee :=_c .Decode (_cade );return _abee ;};func _ddd (_ebd bool )*CMap {_bf :=16;
if _ebd {_bf =8;};return &CMap {_dd :_bf ,_abe :make (map[CharCode ]CharCode ),_eg :make (map[CharCode ]CharCode ),_ccc :make (map[CharCode ]string ),_gb :make (map[string ]CharCode )};};func (cmap *CMap )parseType ()error {_bcd :=0;_aaa :=false ;for _aefe :=0;
_aefe < 3&&!_aaa ;_aefe ++{_ccf ,_cff :=cmap .parseObject ();if _cff !=nil {return _cff ;};switch _aae :=_ccf .(type ){case cmapOperand :switch _aae .Operand {case "\u0064\u0065\u0066":_aaa =true ;default:_d .Log .Error ("\u0070\u0061r\u0073\u0065\u0054\u0079\u0070\u0065\u003a\u0020\u0073\u0074\u0061\u0074\u0065\u0020\u0065\u0072\u0072\u006f\u0072\u002e\u0020\u006f=%\u0023\u0076",_ccf );
return ErrBadCMap ;};case cmapInt :_bcd =int (_aae ._fbag );};};cmap ._gd =_bcd ;return nil ;};type CharCode uint32 ;func (_bace *cMapParser )parseOperand ()(cmapOperand ,error ){_bde :=cmapOperand {};_gefb :=_a .Buffer {};for {_bebe ,_gffcd :=_bace ._edb .Peek (1);
if _gffcd !=nil {if _gffcd ==_f .EOF {break ;};return _bde ,_gffcd ;};if _cf .IsDelimiter (_bebe [0]){break ;};if _cf .IsWhiteSpace (_bebe [0]){break ;};_dcdg ,_ :=_bace ._edb .ReadByte ();_gefb .WriteByte (_dcdg );};if _gefb .Len ()==0{return _bde ,_ge .Errorf ("\u0069\u006e\u0076al\u0069\u0064\u0020\u006f\u0070\u0065\u0072\u0061\u006e\u0064\u0020\u0028\u0065\u006d\u0070\u0074\u0079\u0029");
};_bde .Operand =_gefb .String ();return _bde ,nil ;};func _acfb (_fffbg cmapHexString )CharCode {_caaa :=CharCode (0);for _ ,_dcc :=range _fffbg ._ceae {_caaa <<=8;_caaa |=CharCode (_dcc );};return _caaa ;};func (_bea *cMapParser )parseString ()(cmapString ,error ){_bea ._edb .ReadByte ();
_ecd :=_a .Buffer {};_eag :=1;for {_efc ,_cced :=_bea ._edb .Peek (1);if _cced !=nil {return cmapString {_ecd .String ()},_cced ;};if _efc [0]=='\\'{_bea ._edb .ReadByte ();_dfc ,_cagc :=_bea ._edb .ReadByte ();if _cagc !=nil {return cmapString {_ecd .String ()},_cagc ;
};if _cf .IsOctalDigit (_dfc ){_dgfe ,_bddd :=_bea ._edb .Peek (2);if _bddd !=nil {return cmapString {_ecd .String ()},_bddd ;};var _eebc []byte ;_eebc =append (_eebc ,_dfc );for _ ,_ddae :=range _dgfe {if _cf .IsOctalDigit (_ddae ){_eebc =append (_eebc ,_ddae );
}else {break ;};};_bea ._edb .Discard (len (_eebc )-1);_d .Log .Trace ("\u004e\u0075\u006d\u0065ri\u0063\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0020\u0022\u0025\u0073\u0022",_eebc );_aaeg ,_bddd :=_e .ParseUint (string (_eebc ),8,32);if _bddd !=nil {return cmapString {_ecd .String ()},_bddd ;
};_ecd .WriteByte (byte (_aaeg ));continue ;};switch _dfc {case 'n':_ecd .WriteByte ('\n');case 'r':_ecd .WriteByte ('\r');case 't':_ecd .WriteByte ('\t');case 'b':_ecd .WriteByte ('\b');case 'f':_ecd .WriteByte ('\f');case '(':_ecd .WriteByte ('(');case ')':_ecd .WriteByte (')');
case '\\':_ecd .WriteByte ('\\');};continue ;}else if _efc [0]=='('{_eag ++;}else if _efc [0]==')'{_eag --;if _eag ==0{_bea ._edb .ReadByte ();break ;};};_ffb ,_ :=_bea ._edb .ReadByte ();_ecd .WriteByte (_ffb );};return cmapString {_ecd .String ()},nil ;
};func (_ddaeg *cMapParser )parseNumber ()(cmapObject ,error ){_gaa ,_cfcd :=_cf .ParseNumber (_ddaeg ._edb );if _cfcd !=nil {return nil ,_cfcd ;};switch _ccff :=_gaa .(type ){case *_cf .PdfObjectFloat :return cmapFloat {float64 (*_ccff )},nil ;case *_cf .PdfObjectInteger :return cmapInt {int64 (*_ccff )},nil ;
};return nil ,_ge .Errorf ("\u0075n\u0068\u0061\u006e\u0064\u006c\u0065\u0064\u0020\u006e\u0075\u006db\u0065\u0072\u0020\u0074\u0079\u0070\u0065\u0020\u0025\u0054",_gaa );};type cmapOperand struct{Operand string ;};func (cmap *CMap )NBits ()int {return cmap ._dd };
type integer struct{_beba bool ;_dcdgf int ;};func (cmap *CMap )Bytes ()[]byte {_d .Log .Trace ("\u0063\u006d\u0061\u0070.B\u0079\u0074\u0065\u0073\u003a\u0020\u0063\u006d\u0061\u0070\u003d\u0025\u0073",cmap .String ());if len (cmap ._ec )> 0{return cmap ._ec ;
};cmap ._ec =[]byte (_bd .Join ([]string {_ebda ,cmap .toBfData (),_dge },"\u000a"));return cmap ._ec ;};func (cmap *CMap )BytesToCharcodes (data []byte )([]CharCode ,bool ){var _eca []CharCode ;if cmap ._dd ==8{for _ ,_bac :=range data {_eca =append (_eca ,CharCode (_bac ));
};return _eca ,true ;};for _afa :=0;_afa < len (data );{_ggfe ,_dadb ,_gedb :=cmap .matchCode (data [_afa :]);if !_gedb {_d .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0063\u006f\u0064\u0065\u0020\u006d\u0061\u0074\u0063\u0068\u0020\u0061\u0074\u0020\u0069\u003d\u0025\u0064\u0020\u0062\u0079\u0074\u0065\u0073\u003d\u005b\u0025\u0020\u0030\u0032\u0078\u005d\u003d\u0025\u0023\u0071",_afa ,data ,string (data ));
return _eca ,false ;};_eca =append (_eca ,_ggfe );_afa +=_dadb ;};return _eca ,true ;};func NewToUnicodeCMap (codeToRune map[CharCode ]rune )*CMap {_ebb :=make (map[CharCode ]string ,len (codeToRune ));for _fg ,_ae :=range codeToRune {_ebb [_fg ]=string (_ae );
};cmap :=&CMap {_cae :"\u0041d\u006fb\u0065\u002d\u0049\u0064\u0065n\u0074\u0069t\u0079\u002d\u0055\u0043\u0053",_gd :2,_dd :16,_af :CIDSystemInfo {Registry :"\u0041\u0064\u006fb\u0065",Ordering :"\u0055\u0043\u0053",Supplement :0},_bdf :[]Codespace {{Low :0,High :0xffff}},_ccc :_ebb ,_gb :make (map[string ]CharCode ,len (codeToRune )),_abe :make (map[CharCode ]CharCode ,len (codeToRune )),_eg :make (map[CharCode ]CharCode ,len (codeToRune ))};
cmap .computeInverseMappings ();return cmap ;};var (ErrBadCMap =_b .New ("\u0062\u0061\u0064\u0020\u0063\u006d\u0061\u0070");ErrBadCMapComment =_b .New ("c\u006f\u006d\u006d\u0065\u006e\u0074 \u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0073\u0074a\u0072\u0074\u0020w\u0069t\u0068\u0020\u0025");
ErrBadCMapDict =_b .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0069\u0063\u0074"););func (_gafg *cMapParser )parseArray ()(cmapArray ,error ){_fgf :=cmapArray {};_fgf .Array =[]cmapObject {};_gafg ._edb .ReadByte ();for {_gafg .skipSpaces ();
_eadb ,_cgb :=_gafg ._edb .Peek (1);if _cgb !=nil {return _fgf ,_cgb ;};if _eadb [0]==']'{_gafg ._edb .ReadByte ();break ;};_cggc ,_cgb :=_gafg .parseObject ();if _cgb !=nil {return _fgf ,_cgb ;};_fgf .Array =append (_fgf .Array ,_cggc );};return _fgf ,nil ;
};func _gaag ()cmapDict {return cmapDict {Dict :map[string ]cmapObject {}}};func (cmap *CMap )CharcodeToCID (code CharCode )(CharCode ,bool ){_bc ,_dg :=cmap ._abe [code ];return _bc ,_dg ;};func _ecce (_abb []byte )*cMapParser {_ceg :=cMapParser {};_fdbe :=_a .NewBuffer (_abb );
_ceg ._edb =_eb .NewReader (_fdbe );return &_ceg ;};func (cmap *CMap )parseCIDRange ()error {for {_cccc ,_ead :=cmap .parseObject ();if _ead !=nil {if _ead ==_f .EOF {break ;};return _ead ;};_bgg ,_cbfg :=_cccc .(cmapHexString );if !_cbfg {if _daf ,_ace :=_cccc .(cmapOperand );
_ace {if _daf .Operand ==_egga {return nil ;};return _b .New ("\u0063\u0069\u0064\u0020\u0069\u006e\u0074\u0065\u0072\u0076\u0061\u006c\u0020s\u0074\u0061\u0072\u0074\u0020\u006du\u0073\u0074\u0020\u0062\u0065\u0020\u0061\u0020\u0068\u0065\u0078\u0020\u0073t\u0072\u0069\u006e\u0067");
};};_daga :=_acfb (_bgg );_cccc ,_ead =cmap .parseObject ();if _ead !=nil {if _ead ==_f .EOF {break ;};return _ead ;};_ebdb ,_cbfg :=_cccc .(cmapHexString );if !_cbfg {return _b .New ("\u0063\u0069d\u0020\u0069\u006e\u0074e\u0072\u0076a\u006c\u0020\u0065\u006e\u0064\u0020\u006d\u0075s\u0074\u0020\u0062\u0065\u0020\u0061\u0020\u0068\u0065\u0078\u0020\u0073t\u0072\u0069\u006e\u0067");
};if len (_bgg ._ceae )!=len (_ebdb ._ceae ){return _b .New ("\u0075\u006e\u0065\u0071\u0075\u0061\u006c\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020o\u0066 \u0062\u0079\u0074\u0065\u0073\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065");};_eba :=_acfb (_ebdb );
if _daga > _eba {_d .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0043\u0049\u0044\u0020\u0072\u0061\u006e\u0067\u0065\u002e\u0020\u0073t\u0061\u0072\u0074\u003d\u0030\u0078\u0025\u0030\u0032\u0078\u0020\u0065\u006e\u0064=\u0030x\u0025\u0030\u0032\u0078",_daga ,_eba );
return ErrBadCMap ;};_cccc ,_ead =cmap .parseObject ();if _ead !=nil {if _ead ==_f .EOF {break ;};return _ead ;};_cbg ,_cbfg :=_cccc .(cmapInt );if !_cbfg {return _b .New ("\u0063\u0069\u0064\u0020\u0073t\u0061\u0072\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0064\u0065\u0063\u0069\u006d\u0061\u006c\u0020\u006e\u0075\u006d\u0062\u0065\u0072");
};if _cbg ._fbag < 0{return _b .New ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0063\u0069\u0064\u0020\u0073\u0074\u0061\u0072\u0074\u0020\u0076\u0061\u006c\u0075\u0065");};_fed :=_cbg ._fbag ;for _dda :=_daga ;_dda <=_eba ;_dda ++{cmap ._abe [_dda ]=CharCode (_fed );
_fed ++;};_d .Log .Trace ("C\u0049\u0044\u0020\u0072\u0061\u006eg\u0065\u003a\u0020\u003c\u0030\u0078\u0025\u0058\u003e \u003c\u0030\u0078%\u0058>\u0020\u0025\u0064",_daga ,_eba ,_cbg ._fbag );};return nil ;};type cmapHexString struct{_fcga int ;_ceae []byte ;
};func (cmap *CMap )parseCodespaceRange ()error {for {_dgad ,_fe :=cmap .parseObject ();if _fe !=nil {if _fe ==_f .EOF {break ;};return _fe ;};_cffe ,_dddg :=_dgad .(cmapHexString );if !_dddg {if _eefd ,_ggc :=_dgad .(cmapOperand );_ggc {if _eefd .Operand ==_fcf {return nil ;
};return _b .New ("\u0075n\u0065x\u0070\u0065\u0063\u0074\u0065d\u0020\u006fp\u0065\u0072\u0061\u006e\u0064");};};_dgad ,_fe =cmap .parseObject ();if _fe !=nil {if _fe ==_f .EOF {break ;};return _fe ;};_cce ,_dddg :=_dgad .(cmapHexString );if !_dddg {return _b .New ("\u006e\u006f\u006e-\u0068\u0065\u0078\u0020\u0068\u0069\u0067\u0068");
};if len (_cffe ._ceae )!=len (_cce ._ceae ){return _b .New ("\u0075\u006e\u0065\u0071\u0075\u0061\u006c\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020o\u0066 \u0062\u0079\u0074\u0065\u0073\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065");};_dfd :=_acfb (_cffe );
_fge :=_acfb (_cce );if _fge < _dfd {_d .Log .Debug ("\u0045R\u0052\u004fR\u003a\u0020\u0042\u0061d\u0020\u0063\u006fd\u0065\u0073\u0070\u0061\u0063\u0065\u002e\u0020\u006cow\u003d\u0030\u0078%\u0030\u0032x\u0020\u0068\u0069\u0067\u0068\u003d0\u0078\u00250\u0032\u0078",_dfd ,_fge );
return ErrBadCMap ;};_aff :=_cce ._fcga ;_bbbc :=Codespace {NumBytes :_aff ,Low :_dfd ,High :_fge };cmap ._bdf =append (cmap ._bdf ,_bbbc );_d .Log .Trace ("\u0043\u006f\u0064e\u0073\u0070\u0061\u0063e\u0020\u006c\u006f\u0077\u003a\u0020\u0030x\u0025\u0058\u002c\u0020\u0068\u0069\u0067\u0068\u003a\u0020\u0030\u0078\u0025\u0058",_dfd ,_fge );
};if len (cmap ._bdf )==0{_d .Log .Debug ("\u0045\u0052R\u004f\u0052\u003a\u0020\u004e\u006f\u0020\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0073\u0020\u0069\u006e\u0020\u0063ma\u0070\u002e");return ErrBadCMap ;};return nil ;};func LoadCmapFromDataCID (data []byte )(*CMap ,error ){return LoadCmapFromData (data ,false )};
type cmapInt struct{_fbag int64 };func (cmap *CMap )StringToCID (s string )(CharCode ,bool ){_ebf ,_gff :=cmap ._gb [s ];return _ebf ,_gff ;};func (cmap *CMap )parseName ()error {_bfcf :="";_aca :=false ;for _aef :=0;_aef < 20&&!_aca ;_aef ++{_bbag ,_cfea :=cmap .parseObject ();
if _cfea !=nil {return _cfea ;};switch _dfg :=_bbag .(type ){case cmapOperand :switch _dfg .Operand {case "\u0064\u0065\u0066":_aca =true ;default:_d .Log .Debug ("\u0070\u0061\u0072\u0073\u0065\u004e\u0061\u006d\u0065\u003a\u0020\u0053\u0074\u0061\u0074\u0065\u0020\u0065\u0072\u0072\u006f\u0072\u002e\u0020o\u003d\u0025\u0023\u0076\u0020n\u0061\u006de\u003d\u0025\u0023\u0071",_bbag ,_bfcf );
if _bfcf !=""{_bfcf =_ge .Sprintf ("\u0025\u0073\u0020%\u0073",_bfcf ,_dfg .Operand );};_d .Log .Debug ("\u0070\u0061\u0072\u0073\u0065\u004e\u0061\u006d\u0065\u003a \u0052\u0065\u0063\u006f\u0076\u0065\u0072e\u0064\u002e\u0020\u006e\u0061\u006d\u0065\u003d\u0025\u0023\u0071",_bfcf );
};case cmapName :_bfcf =_dfg .Name ;};};if !_aca {_d .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a \u0070\u0061\u0072\u0073\u0065N\u0061m\u0065:\u0020\u004e\u006f\u0020\u0064\u0065\u0066 ");return ErrBadCMap ;};cmap ._cae =_bfcf ;return nil ;};type cmapName struct{Name string ;
};func (cmap *CMap )Stream ()(*_cf .PdfObjectStream ,error ){if cmap ._ddf !=nil {return cmap ._ddf ,nil ;};_bge ,_bacg :=_cf .MakeStream (cmap .Bytes (),_cf .NewFlateEncoder ());if _bacg !=nil {return nil ,_bacg ;};cmap ._ddf =_bge ;return cmap ._ddf ,nil ;
};func (cmap *CMap )WMode ()(int ,bool ){return cmap ._geb ._dcdgf ,cmap ._geb ._beba };type cmapFloat struct{_aed float64 };type fbRange struct{_fd CharCode ;_bb CharCode ;_dad string ;};func (cmap *CMap )String ()string {_caec :=cmap ._af ;_fgg :=[]string {_ge .Sprintf ("\u006e\u0062\u0069\u0074\u0073\u003a\u0025\u0064",cmap ._dd ),_ge .Sprintf ("\u0074y\u0070\u0065\u003a\u0025\u0064",cmap ._gd )};
if cmap ._cb !=""{_fgg =append (_fgg ,_ge .Sprintf ("\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u003a\u0025\u0073",cmap ._cb ));};if cmap ._def !=""{_fgg =append (_fgg ,_ge .Sprintf ("u\u0073\u0065\u0063\u006d\u0061\u0070\u003a\u0025\u0023\u0071",cmap ._def ));
};_fgg =append (_fgg ,_ge .Sprintf ("\u0073\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f\u003a\u0025\u0073",_caec .String ()));if len (cmap ._bdf )> 0{_fgg =append (_fgg ,_ge .Sprintf ("\u0063\u006f\u0064\u0065\u0073\u0070\u0061\u0063\u0065\u0073\u003a\u0025\u0064",len (cmap ._bdf )));
};if len (cmap ._ccc )> 0{_fgg =append (_fgg ,_ge .Sprintf ("\u0063\u006fd\u0065\u0054\u006fU\u006e\u0069\u0063\u006f\u0064\u0065\u003a\u0025\u0064",len (cmap ._ccc )));};return _ge .Sprintf ("\u0043\u004d\u0041P\u007b\u0025\u0023\u0071\u0020\u0025\u0073\u007d",cmap ._cae ,_bd .Join (_fgg ,"\u0020"));
};func _cef (_bec ,_gbbg int )int {if _bec < _gbbg {return _bec ;};return _gbbg ;};func (cmap *CMap )Name ()string {return cmap ._cae };