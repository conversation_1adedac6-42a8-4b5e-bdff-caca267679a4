//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package testutils ;import (_b "crypto/md5";_fe "encoding/hex";_dc "errors";_gg "fmt";_a "github.com/unidoc/unipdf/v4/common";_da "github.com/unidoc/unipdf/v4/core";_fg "image";_dd "image/png";_f "io";_ca "os";_d "os/exec";_e "path/filepath";_ba "strings";
_c "testing";);func CompareImages (img1 ,img2 _fg .Image )(bool ,error ){_ge :=img1 .Bounds ();_gc :=0;for _ef :=0;_ef < _ge .Size ().X ;_ef ++{for _af :=0;_af < _ge .Size ().Y ;_af ++{_bab ,_ag ,_bb ,_ :=img1 .At (_ef ,_af ).RGBA ();_gea ,_ga ,_caac ,_ :=img2 .At (_ef ,_af ).RGBA ();
if _bab !=_gea ||_ag !=_ga ||_bb !=_caac {_gc ++;};};};_gf :=float64 (_gc )/float64 (_ge .Dx ()*_ge .Dy ());if _gf > 0.0001{_gg .Printf ("\u0064\u0069\u0066f \u0066\u0072\u0061\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0076\u0020\u0028\u0025\u0064\u0029\u000a",_gf ,_gc );
return false ,nil ;};return true ,nil ;};func ParseIndirectObjects (rawpdf string )(map[int64 ]_da .PdfObject ,error ){_agb :=_da .NewParserFromString (rawpdf );_eec :=map[int64 ]_da .PdfObject {};for {_gbdc ,_gad :=_agb .ParseIndirectObject ();if _gad !=nil {if _gad ==_f .EOF {break ;
};return nil ,_gad ;};switch _gd :=_gbdc .(type ){case *_da .PdfIndirectObject :_eec [_gd .ObjectNumber ]=_gbdc ;case *_da .PdfObjectStream :_eec [_gd .ObjectNumber ]=_gbdc ;};};for _ ,_ccb :=range _eec {_caf (_ccb ,_eec );};return _eec ,nil ;};func ReadPNG (file string )(_fg .Image ,error ){_cb ,_ae :=_ca .Open (file );
if _ae !=nil {return nil ,_ae ;};defer _cb .Close ();return _dd .Decode (_cb );};func _caf (_gbe _da .PdfObject ,_feg map[int64 ]_da .PdfObject )error {switch _fegb :=_gbe .(type ){case *_da .PdfIndirectObject :_baf :=_fegb ;_caf (_baf .PdfObject ,_feg );
case *_da .PdfObjectDictionary :_agbg :=_fegb ;for _ ,_fbc :=range _agbg .Keys (){_ecd :=_agbg .Get (_fbc );if _bc ,_ega :=_ecd .(*_da .PdfObjectReference );_ega {_bbc ,_gec :=_feg [_bc .ObjectNumber ];if !_gec {return _dc .New ("r\u0065\u0066\u0065\u0072\u0065\u006ec\u0065\u0020\u0074\u006f\u0020\u006f\u0075\u0074\u0073i\u0064\u0065\u0020o\u0062j\u0065\u0063\u0074");
};_agbg .Set (_fbc ,_bbc );}else {_caf (_ecd ,_feg );};};case *_da .PdfObjectArray :_bae :=_fegb ;for _cf ,_egc :=range _bae .Elements (){if _gfb ,_cad :=_egc .(*_da .PdfObjectReference );_cad {_baff ,_eef :=_feg [_gfb .ObjectNumber ];if !_eef {return _dc .New ("r\u0065\u0066\u0065\u0072\u0065\u006ec\u0065\u0020\u0074\u006f\u0020\u006f\u0075\u0074\u0073i\u0064\u0065\u0020o\u0062j\u0065\u0063\u0074");
};_bae .Set (_cf ,_baff );}else {_caf (_egc ,_feg );};};};return nil ;};func ComparePNGFiles (file1 ,file2 string )(bool ,error ){_fc ,_df :=HashFile (file1 );if _df !=nil {return false ,_df ;};_ab ,_df :=HashFile (file2 );if _df !=nil {return false ,_df ;
};if _fc ==_ab {return true ,nil ;};_ea ,_df :=ReadPNG (file1 );if _df !=nil {return false ,_df ;};_gbf ,_df :=ReadPNG (file2 );if _df !=nil {return false ,_df ;};if _ea .Bounds ()!=_gbf .Bounds (){return false ,nil ;};return CompareImages (_ea ,_gbf );
};func RenderPDFToPNGs (pdfPath string ,dpi int ,outpathTpl string )error {if dpi <=0{dpi =100;};if _ ,_cg :=_d .LookPath ("\u0067\u0073");_cg !=nil {return ErrRenderNotSupported ;};return _d .Command ("\u0067\u0073","\u002d\u0073\u0044\u0045\u0056\u0049\u0043\u0045\u003d\u0070\u006e\u0067a\u006c\u0070\u0068\u0061","\u002d\u006f",outpathTpl ,_gg .Sprintf ("\u002d\u0072\u0025\u0064",dpi ),pdfPath ).Run ();
};func RunRenderTest (t *_c .T ,pdfPath ,outputDir ,baselineRenderPath string ,saveBaseline bool ){_ccf :=_ba .TrimSuffix (_e .Base (pdfPath ),_e .Ext (pdfPath ));t .Run ("\u0072\u0065\u006e\u0064\u0065\u0072",func (_aa *_c .T ){_geaf :=_e .Join (outputDir ,_ccf );
_gcd :=_geaf +"\u002d%\u0064\u002e\u0070\u006e\u0067";if _abf :=RenderPDFToPNGs (pdfPath ,0,_gcd );_abf !=nil {_aa .Skip (_abf );};for _bg :=1;true ;_bg ++{_bba :=_gg .Sprintf ("\u0025s\u002d\u0025\u0064\u002e\u0070\u006eg",_geaf ,_bg );_fcd :=_e .Join (baselineRenderPath ,_gg .Sprintf ("\u0025\u0073\u002d\u0025\u0064\u005f\u0065\u0078\u0070\u002e\u0070\u006e\u0067",_ccf ,_bg ));
if _ ,_dfb :=_ca .Stat (_bba );_dfb !=nil {break ;};_aa .Logf ("\u0025\u0073",_fcd );if saveBaseline {_aa .Logf ("\u0043\u006fp\u0079\u0069\u006eg\u0020\u0025\u0073\u0020\u002d\u003e\u0020\u0025\u0073",_bba ,_fcd );_ee :=CopyFile (_bba ,_fcd );if _ee !=nil {_aa .Fatalf ("\u0045\u0052\u0052OR\u0020\u0063\u006f\u0070\u0079\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0025\u0073\u003a\u0020\u0025\u0076",_fcd ,_ee );
};continue ;};_aa .Run (_gg .Sprintf ("\u0070\u0061\u0067\u0065\u0025\u0064",_bg ),func (_ec *_c .T ){_ec .Logf ("\u0043o\u006dp\u0061\u0072\u0069\u006e\u0067 \u0025\u0073 \u0076\u0073\u0020\u0025\u0073",_bba ,_fcd );_gcc ,_be :=ComparePNGFiles (_bba ,_fcd );
if _ca .IsNotExist (_be ){_ec .Fatal ("\u0069m\u0061g\u0065\u0020\u0066\u0069\u006ce\u0020\u006di\u0073\u0073\u0069\u006e\u0067");}else if !_gcc {_ec .Fatal ("\u0077\u0072\u006f\u006eg \u0070\u0061\u0067\u0065\u0020\u0072\u0065\u006e\u0064\u0065\u0072\u0065\u0064");
};});};});};func HashFile (file string )(string ,error ){_gb ,_caa :=_ca .Open (file );if _caa !=nil {return "",_caa ;};defer _gb .Close ();_ff :=_b .New ();if _ ,_caa =_f .Copy (_ff ,_gb );_caa !=nil {return "",_caa ;};return _fe .EncodeToString (_ff .Sum (nil )),nil ;
};var (ErrRenderNotSupported =_dc .New ("\u0072\u0065\u006e\u0064\u0065r\u0069\u006e\u0067\u0020\u0050\u0044\u0046\u0020\u0066\u0069\u006c\u0065\u0073 \u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0075\u0070\u0070\u006f\u0072\u0074\u0065\u0064\u0020\u006f\u006e\u0020\u0074\u0068\u0069\u0073\u0020\u0073\u0079\u0073\u0074\u0065m");
);func CompareDictionariesDeep (d1 ,d2 *_da .PdfObjectDictionary )bool {if len (d1 .Keys ())!=len (d2 .Keys ()){_a .Log .Debug ("\u0044\u0069\u0063\u0074\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u006d\u0069s\u006da\u0074\u0063\u0068\u0020\u0028\u0025\u0064\u0020\u0021\u003d\u0020\u0025\u0064\u0029",len (d1 .Keys ()),len (d2 .Keys ()));
_a .Log .Debug ("\u0057\u0061s\u0020\u0027\u0025s\u0027\u0020\u0076\u0073\u0020\u0027\u0025\u0073\u0027",d1 .Write (),d2 .Write ());return false ;};for _ ,_cea :=range d1 .Keys (){if _cea =="\u0050\u0061\u0072\u0065\u006e\u0074"{continue ;};_ad :=_da .TraceToDirectObject (d1 .Get (_cea ));
_bd :=_da .TraceToDirectObject (d2 .Get (_cea ));if _ad ==nil {_a .Log .Debug ("\u00761\u0020\u0069\u0073\u0020\u006e\u0069l");return false ;};if _bd ==nil {_a .Log .Debug ("\u00762\u0020\u0069\u0073\u0020\u006e\u0069l");return false ;};switch _ged :=_ad .(type ){case *_da .PdfObjectDictionary :_ccc ,_abe :=_bd .(*_da .PdfObjectDictionary );
if !_abe {_a .Log .Debug ("\u0054\u0079\u0070\u0065 m\u0069\u0073\u006d\u0061\u0074\u0063\u0068\u0020\u0025\u0054\u0020\u0076\u0073\u0020%\u0054",_ad ,_bd );return false ;};if !CompareDictionariesDeep (_ged ,_ccc ){return false ;};continue ;case *_da .PdfObjectArray :_dg ,_dfbg :=_bd .(*_da .PdfObjectArray );
if !_dfbg {_a .Log .Debug ("\u00762\u0020n\u006f\u0074\u0020\u0061\u006e\u0020\u0061\u0072\u0072\u0061\u0079");return false ;};if _ged .Len ()!=_dg .Len (){_a .Log .Debug ("\u0061\u0072\u0072\u0061\u0079\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u0020\u006d\u0069s\u006da\u0074\u0063\u0068\u0020\u0028\u0025\u0064\u0020\u0021\u003d\u0020\u0025\u0064\u0029",_ged .Len (),_dg .Len ());
return false ;};for _aeb :=0;_aeb < _ged .Len ();_aeb ++{_agbgc :=_da .TraceToDirectObject (_ged .Get (_aeb ));_gdca :=_da .TraceToDirectObject (_dg .Get (_aeb ));if _bca ,_fbb :=_agbgc .(*_da .PdfObjectDictionary );_fbb {_ed ,_efe :=_gdca .(*_da .PdfObjectDictionary );
if !_efe {return false ;};if !CompareDictionariesDeep (_bca ,_ed ){return false ;};}else {_eee :=_agbgc .Write ();_bge :=_gdca .Write ();if string (_eee )!=string (_bge ){_a .Log .Debug ("M\u0069\u0073\u006d\u0061tc\u0068 \u0027\u0025\u0073\u0027\u0020!\u003d\u0020\u0027\u0025\u0073\u0027",_eee ,_bge );
return false ;};};};continue ;};if _ad .String ()!=_bd .String (){_a .Log .Debug ("\u006b\u0065y\u003d\u0025\u0073\u0020\u004d\u0069\u0073\u006d\u0061\u0074\u0063\u0068\u0021\u0020\u0027\u0025\u0073\u0027\u0020\u0021\u003d\u0020'%\u0073\u0027",_cea ,_ad .String (),_bd .String ());
_a .Log .Debug ("\u0046o\u0072 \u0027\u0025\u0054\u0027\u0020\u002d\u0020\u0027\u0025\u0054\u0027",_ad ,_bd );_a .Log .Debug ("\u0046\u006f\u0072\u0020\u0027\u0025\u002b\u0076\u0027\u0020\u002d\u0020'\u0025\u002b\u0076\u0027",_ad ,_bd );return false ;};
};return true ;};func CopyFile (src ,dst string )error {_fb ,_bac :=_ca .Open (src );if _bac !=nil {return _bac ;};defer _fb .Close ();_cc ,_bac :=_ca .Create (dst );if _bac !=nil {return _bac ;};defer _cc .Close ();_ ,_bac =_f .Copy (_cc ,_fb );return _bac ;
};