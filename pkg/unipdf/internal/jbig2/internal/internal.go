//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package internal ;import _ag "errors";var ErrOOB =_ag .New ("o\u0075\u0074\u0020\u006f\u0066\u0020\u0062\u0061\u006e\u0064");