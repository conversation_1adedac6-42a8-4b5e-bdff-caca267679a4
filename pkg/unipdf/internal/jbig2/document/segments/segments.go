//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package segments ;import (_dg "encoding/binary";_gb "errors";_e "fmt";_ge "github.com/unidoc/unipdf/v4/common";_ed "github.com/unidoc/unipdf/v4/internal/bitwise";_db "github.com/unidoc/unipdf/v4/internal/jbig2/basic";_gf "github.com/unidoc/unipdf/v4/internal/jbig2/bitmap";
_ae "github.com/unidoc/unipdf/v4/internal/jbig2/decoder/arithmetic";_aee "github.com/unidoc/unipdf/v4/internal/jbig2/decoder/huffman";_ac "github.com/unidoc/unipdf/v4/internal/jbig2/decoder/mmr";_fff "github.com/unidoc/unipdf/v4/internal/jbig2/encoder/arithmetic";
_ff "github.com/unidoc/unipdf/v4/internal/jbig2/errors";_b "github.com/unidoc/unipdf/v4/internal/jbig2/internal";_fa "image";_d "io";_g "math";_a "strings";_c "time";);func (_ceea *GenericRefinementRegion )readAtPixels ()error {_ceea .GrAtX =make ([]int8 ,2);
_ceea .GrAtY =make ([]int8 ,2);_ccd ,_daa :=_ceea ._ebe .ReadByte ();if _daa !=nil {return _daa ;};_ceea .GrAtX [0]=int8 (_ccd );_ccd ,_daa =_ceea ._ebe .ReadByte ();if _daa !=nil {return _daa ;};_ceea .GrAtY [0]=int8 (_ccd );_ccd ,_daa =_ceea ._ebe .ReadByte ();
if _daa !=nil {return _daa ;};_ceea .GrAtX [1]=int8 (_ccd );_ccd ,_daa =_ceea ._ebe .ReadByte ();if _daa !=nil {return _daa ;};_ceea .GrAtY [1]=int8 (_ccd );return nil ;};type templater interface{form (_cgf ,_cef ,_ccc ,_bce ,_aba int16 )int16 ;setIndex (_gece *_ae .DecoderStats );
};func (_cefbc *TableSegment )parseHeader ()error {var (_adde int ;_ebcc uint64 ;_agbd error ;);_adde ,_agbd =_cefbc ._gdcb .ReadBit ();if _agbd !=nil {return _agbd ;};if _adde ==1{return _e .Errorf ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0074\u0061\u0062\u006c\u0065 \u0073\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0064e\u0066\u0069\u006e\u0069\u0074\u0069\u006f\u006e\u002e\u0020\u0042\u002e\u0032\u002e1\u0020\u0043\u006f\u0064\u0065\u0020\u0054\u0061\u0062\u006c\u0065\u0020\u0066\u006c\u0061\u0067\u0073\u003a\u0020\u0042\u0069\u0074\u0020\u0037\u0020\u006d\u0075\u0073\u0074\u0020b\u0065\u0020\u007a\u0065\u0072\u006f\u002e\u0020\u0057a\u0073\u003a \u0025\u0064",_adde );
};if _ebcc ,_agbd =_cefbc ._gdcb .ReadBits (3);_agbd !=nil {return _agbd ;};_cefbc ._caab =(int32 (_ebcc )+1)&0xf;if _ebcc ,_agbd =_cefbc ._gdcb .ReadBits (3);_agbd !=nil {return _agbd ;};_cefbc ._efgef =(int32 (_ebcc )+1)&0xf;if _ebcc ,_agbd =_cefbc ._gdcb .ReadBits (32);
_agbd !=nil {return _agbd ;};_cefbc ._fggcd =int32 (_ebcc &_g .MaxInt32 );if _ebcc ,_agbd =_cefbc ._gdcb .ReadBits (32);_agbd !=nil {return _agbd ;};_cefbc ._bdbeg =int32 (_ebcc &_g .MaxInt32 );return nil ;};func (_fcge *SymbolDictionary )decodeRefinedSymbol (_ffgf ,_bace uint32 )error {var (_gbgf int ;
_aegd ,_eecgc int32 ;);if _fcge .IsHuffmanEncoded {_cdcf ,_dafa :=_fcge ._gaeb .ReadBits (byte (_fcge ._dbce ));if _dafa !=nil {return _dafa ;};_gbgf =int (_cdcf );_afce ,_dafa :=_aee .GetStandardTable (15);if _dafa !=nil {return _dafa ;};_dcefd ,_dafa :=_afce .Decode (_fcge ._gaeb );
if _dafa !=nil {return _dafa ;};_aegd =int32 (_dcefd );_dcefd ,_dafa =_afce .Decode (_fcge ._gaeb );if _dafa !=nil {return _dafa ;};_eecgc =int32 (_dcefd );_afce ,_dafa =_aee .GetStandardTable (1);if _dafa !=nil {return _dafa ;};if _ ,_dafa =_afce .Decode (_fcge ._gaeb );
_dafa !=nil {return _dafa ;};_fcge ._gaeb .Align ();}else {_fbgad ,_dfddb :=_fcge ._daed .DecodeIAID (uint64 (_fcge ._dbce ),_fcge ._efg );if _dfddb !=nil {return _dfddb ;};_gbgf =int (_fbgad );_aegd ,_dfddb =_fcge ._daed .DecodeInt (_fcge ._fag );if _dfddb !=nil {return _dfddb ;
};_eecgc ,_dfddb =_fcge ._daed .DecodeInt (_fcge ._cddc );if _dfddb !=nil {return _dfddb ;};};if _abfgf :=_fcge .setSymbolsArray ();_abfgf !=nil {return _abfgf ;};_gfcba :=_fcge ._dfe [_gbgf ];if _eced :=_fcge .decodeNewSymbols (_ffgf ,_bace ,_gfcba ,_aegd ,_eecgc );
_eced !=nil {return _eced ;};if _fcge .IsHuffmanEncoded {_fcge ._gaeb .Align ();};return nil ;};func (_bgag *PageInformationSegment )checkInput ()error {if _bgag .PageBMHeight ==_g .MaxInt32 {if !_bgag .IsStripe {_ge .Log .Debug ("P\u0061\u0067\u0065\u0049\u006e\u0066\u006f\u0072\u006da\u0074\u0069\u006f\u006e\u0053\u0065\u0067me\u006e\u0074\u002e\u0049s\u0053\u0074\u0072\u0069\u0070\u0065\u0020\u0073\u0068ou\u006c\u0064 \u0062\u0065\u0020\u0074\u0072\u0075\u0065\u002e");
};};return nil ;};func (_acbg *GenericRegion )computeSegmentDataStructure ()error {_acbg .DataOffset =_acbg ._fddcc .AbsolutePosition ();_acbg .DataHeaderLength =_acbg .DataOffset -_acbg .DataHeaderOffset ;_acbg .DataLength =int64 (_acbg ._fddcc .AbsoluteLength ())-_acbg .DataHeaderLength ;
return nil ;};type template1 struct{};func (_dd *GenericRefinementRegion )decodeTypicalPredictedLine (_cg ,_bf ,_cc ,_dcc ,_gdb ,_cce int )error {_beb :=_cg -int (_dd .ReferenceDY );_dbf :=_dd .ReferenceBitmap .GetByteIndex (0,_beb );_gbc :=_dd .RegionBitmap .GetByteIndex (0,_cg );
var _gg error ;switch _dd .TemplateID {case 0:_gg =_dd .decodeTypicalPredictedLineTemplate0 (_cg ,_bf ,_cc ,_dcc ,_gdb ,_cce ,_gbc ,_beb ,_dbf );case 1:_gg =_dd .decodeTypicalPredictedLineTemplate1 (_cg ,_bf ,_cc ,_dcc ,_gdb ,_cce ,_gbc ,_beb ,_dbf );};
return _gg ;};func (_gecc *template1 )form (_fbg ,_bcbb ,_fad ,_df ,_fee int16 )int16 {return ((_fbg &0x02)<<8)|(_bcbb <<6)|((_fad &0x03)<<4)|(_df <<1)|_fee ;};func (_gc *EndOfStripe )parseHeader ()error {_dgb ,_fb :=_gc ._dgd .ReadBits (32);if _fb !=nil {return _fb ;
};_gc ._dbc =int (_dgb &_g .MaxInt32 );return nil ;};func (_cacc *PatternDictionary )extractPatterns (_dbca *_gf .Bitmap )error {var _eafad int ;_geae :=make ([]*_gf .Bitmap ,_cacc .GrayMax +1);for _eafad <=int (_cacc .GrayMax ){_acgea :=int (_cacc .HdpWidth )*_eafad ;
_cfgc :=_fa .Rect (_acgea ,0,_acgea +int (_cacc .HdpWidth ),int (_cacc .HdpHeight ));_faf ,_ffae :=_gf .Extract (_cfgc ,_dbca );if _ffae !=nil {return _ffae ;};_geae [_eafad ]=_faf ;_eafad ++;};_cacc .Patterns =_geae ;return nil ;};func (_abbb *GenericRegion )setParametersWithAt (_egc bool ,_cfaa byte ,_eabc ,_afg bool ,_dcf ,_fcf []int8 ,_eba ,_fdde uint32 ,_gegg *_ae .DecoderStats ,_gee *_ae .Decoder ){_abbb .IsMMREncoded =_egc ;
_abbb .GBTemplate =_cfaa ;_abbb .IsTPGDon =_eabc ;_abbb .GBAtX =_dcf ;_abbb .GBAtY =_fcf ;_abbb .RegionSegment .BitmapHeight =_fdde ;_abbb .RegionSegment .BitmapWidth =_eba ;_abbb ._gbce =nil ;_abbb .Bitmap =nil ;if _gegg !=nil {_abbb ._cfb =_gegg ;};if _gee !=nil {_abbb ._bde =_gee ;
};_ge .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0047\u0049O\u004e\u005d\u0020\u0073\u0065\u0074P\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073\u0020\u0053\u0044\u0041t\u003a\u0020\u0025\u0073",_abbb );};func (_cccb *PatternDictionary )parseHeader ()error {_ge .Log .Trace ("\u005b\u0050\u0041\u0054\u0054\u0045\u0052\u004e\u002d\u0044\u0049\u0043\u0054I\u004f\u004e\u0041\u0052\u0059\u005d[\u0070\u0061\u0072\u0073\u0065\u0048\u0065\u0061\u0064\u0065\u0072\u005d\u0020b\u0065\u0067\u0069\u006e");
defer func (){_ge .Log .Trace ("\u005b\u0050\u0041T\u0054\u0045\u0052\u004e\u002d\u0044\u0049\u0043\u0054\u0049\u004f\u004e\u0041\u0052\u0059\u005d\u005b\u0070\u0061\u0072\u0073\u0065\u0048\u0065\u0061\u0064\u0065\u0072\u005d \u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064");
}();_ ,_fabe :=_cccb ._dface .ReadBits (5);if _fabe !=nil {return _fabe ;};if _fabe =_cccb .readTemplate ();_fabe !=nil {return _fabe ;};if _fabe =_cccb .readIsMMREncoded ();_fabe !=nil {return _fabe ;};if _fabe =_cccb .readPatternWidthAndHeight ();_fabe !=nil {return _fabe ;
};if _fabe =_cccb .readGrayMax ();_fabe !=nil {return _fabe ;};if _fabe =_cccb .computeSegmentDataStructure ();_fabe !=nil {return _fabe ;};return _cccb .checkInput ();};func (_cbfe *GenericRegion )overrideAtTemplate1 (_aaef ,_cbb ,_eafa ,_dfaa ,_eab int )int {_aaef &=0x1FF7;
if _cbfe .GBAtY [0]==0&&_cbfe .GBAtX [0]>=-int8 (_eab ){_aaef |=(_dfaa >>uint (7-(int8 (_eab )+_cbfe .GBAtX [0]))&0x1)<<3;}else {_aaef |=int (_cbfe .getPixel (_cbb +int (_cbfe .GBAtX [0]),_eafa +int (_cbfe .GBAtY [0])))<<3;};return _aaef ;};type PageInformationSegment struct{_dcaf *_ed .Reader ;
PageBMHeight int ;PageBMWidth int ;ResolutionX int ;ResolutionY int ;_fgeb bool ;_bae _gf .CombinationOperator ;_efff bool ;DefaultPixelValue uint8 ;_afcf bool ;IsLossless bool ;IsStripe bool ;MaxStripeSize uint16 ;};func (_egaab *TableSegment )HtOOB ()int32 {return _egaab ._bbgc };
func (_fdcc *TextRegion )computeSymbolCodeLength ()error {if _fdcc .IsHuffmanEncoded {return _fdcc .symbolIDCodeLengths ();};_fdcc ._eebg =int8 (_g .Ceil (_g .Log (float64 (_fdcc .NumberOfSymbols ))/_g .Log (2)));return nil ;};func (_adgf *Header )writeReferredToSegments (_cddb _ed .BinaryWriter )(_begdb int ,_ccae error ){const _adbd ="\u0077\u0072\u0069te\u0052\u0065\u0066\u0065\u0072\u0072\u0065\u0064\u0054\u006f\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0073";
var (_aabg uint16 ;_eecc uint32 ;);_ebbfa :=_adgf .referenceSize ();_bgaa :=1;_bagd :=make ([]byte ,_ebbfa );for _ ,_aabe :=range _adgf .RTSNumbers {switch _ebbfa {case 4:_eecc =uint32 (_aabe );_dg .BigEndian .PutUint32 (_bagd ,_eecc );_bgaa ,_ccae =_cddb .Write (_bagd );
if _ccae !=nil {return 0,_ff .Wrap (_ccae ,_adbd ,"u\u0069\u006e\u0074\u0033\u0032\u0020\u0073\u0069\u007a\u0065");};case 2:_aabg =uint16 (_aabe );_dg .BigEndian .PutUint16 (_bagd ,_aabg );_bgaa ,_ccae =_cddb .Write (_bagd );if _ccae !=nil {return 0,_ff .Wrap (_ccae ,_adbd ,"\u0075\u0069\u006e\u0074\u0031\u0036");
};default:if _ccae =_cddb .WriteByte (byte (_aabe ));_ccae !=nil {return 0,_ff .Wrap (_ccae ,_adbd ,"\u0075\u0069\u006et\u0038");};};_begdb +=_bgaa ;};return _begdb ,nil ;};func (_fbde *PatternDictionary )readPatternWidthAndHeight ()error {_feeb ,_abfg :=_fbde ._dface .ReadByte ();
if _abfg !=nil {return _abfg ;};_fbde .HdpWidth =_feeb ;_feeb ,_abfg =_fbde ._dface .ReadByte ();if _abfg !=nil {return _abfg ;};_fbde .HdpHeight =_feeb ;return nil ;};func (_ffbg *Header )writeSegmentNumber (_cegg _ed .BinaryWriter )(_gedb int ,_dbad error ){_acge :=make ([]byte ,4);
_dg .BigEndian .PutUint32 (_acge ,_ffbg .SegmentNumber );if _gedb ,_dbad =_cegg .Write (_acge );_dbad !=nil {return 0,_ff .Wrap (_dbad ,"\u0048e\u0061\u0064\u0065\u0072.\u0077\u0072\u0069\u0074\u0065S\u0065g\u006de\u006e\u0074\u004e\u0075\u006d\u0062\u0065r","");
};return _gedb ,nil ;};func (_beab *TextRegion )decodeSymInRefSize ()(int64 ,error ){const _bebgc ="\u0064e\u0063o\u0064\u0065\u0053\u0079\u006dI\u006e\u0052e\u0066\u0053\u0069\u007a\u0065";if _beab .SbHuffRSize ==0{_cdbf ,_agee :=_aee .GetStandardTable (1);
if _agee !=nil {return 0,_ff .Wrap (_agee ,_bebgc ,"");};return _cdbf .Decode (_beab ._cgbc );};if _beab ._bcgd ==nil {var (_babb int ;_gegc error ;);if _beab .SbHuffFS ==3{_babb ++;};if _beab .SbHuffDS ==3{_babb ++;};if _beab .SbHuffDT ==3{_babb ++;};
if _beab .SbHuffRDWidth ==3{_babb ++;};if _beab .SbHuffRDHeight ==3{_babb ++;};if _beab .SbHuffRDX ==3{_babb ++;};if _beab .SbHuffRDY ==3{_babb ++;};_beab ._bcgd ,_gegc =_beab .getUserTable (_babb );if _gegc !=nil {return 0,_ff .Wrap (_gegc ,_bebgc ,"");
};};_gacc ,_fgcd :=_beab ._bcgd .Decode (_beab ._cgbc );if _fgcd !=nil {return 0,_ff .Wrap (_fgcd ,_bebgc ,"");};return _gacc ,nil ;};func (_ebdc *Header )String ()string {_ebdg :=&_a .Builder {};_ebdg .WriteString ("\u000a[\u0053E\u0047\u004d\u0045\u004e\u0054-\u0048\u0045A\u0044\u0045\u0052\u005d\u000a");
_ebdg .WriteString (_e .Sprintf ("\t\u002d\u0020\u0053\u0065gm\u0065n\u0074\u004e\u0075\u006d\u0062e\u0072\u003a\u0020\u0025\u0076\u000a",_ebdc .SegmentNumber ));_ebdg .WriteString (_e .Sprintf ("\u0009\u002d\u0020T\u0079\u0070\u0065\u003a\u0020\u0025\u0076\u000a",_ebdc .Type ));
_ebdg .WriteString (_e .Sprintf ("\u0009-\u0020R\u0065\u0074\u0061\u0069\u006eF\u006c\u0061g\u003a\u0020\u0025\u0076\u000a",_ebdc .RetainFlag ));_ebdg .WriteString (_e .Sprintf ("\u0009\u002d\u0020Pa\u0067\u0065\u0041\u0073\u0073\u006f\u0063\u0069\u0061\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0076\u000a",_ebdc .PageAssociation ));
_ebdg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0050\u0061\u0067\u0065\u0041\u0073\u0073\u006f\u0063\u0069\u0061\u0074i\u006fn\u0046\u0069\u0065\u006c\u0064\u0053\u0069\u007a\u0065\u003a\u0020\u0025\u0076\u000a",_ebdc .PageAssociationFieldSize ));
_ebdg .WriteString ("\u0009-\u0020R\u0054\u0053\u0045\u0047\u004d\u0045\u004e\u0054\u0053\u003a\u000a");for _ ,_bgea :=range _ebdc .RTSNumbers {_ebdg .WriteString (_e .Sprintf ("\u0009\t\u002d\u0020\u0025\u0064\u000a",_bgea ));};_ebdg .WriteString (_e .Sprintf ("\t\u002d \u0048\u0065\u0061\u0064\u0065\u0072\u004c\u0065n\u0067\u0074\u0068\u003a %\u0076\u000a",_ebdc .HeaderLength ));
_ebdg .WriteString (_e .Sprintf ("\u0009-\u0020\u0053\u0065\u0067m\u0065\u006e\u0074\u0044\u0061t\u0061L\u0065n\u0067\u0074\u0068\u003a\u0020\u0025\u0076\n",_ebdc .SegmentDataLength ));_ebdg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074D\u0061\u0074\u0061\u0053\u0074\u0061\u0072t\u004f\u0066\u0066\u0073\u0065\u0074\u003a\u0020\u0025\u0076\u000a",_ebdc .SegmentDataStartOffset ));
return _ebdg .String ();};func (_ead *SymbolDictionary )decodeHeightClassBitmap (_geaaga *_gf .Bitmap ,_fddg int64 ,_eaff int ,_dgfc []int )error {for _gabc :=_fddg ;_gabc < int64 (_ead ._dcbgd );_gabc ++{var _dbd int ;for _cecf :=_fddg ;_cecf <=_gabc -1;
_cecf ++{_dbd +=_dgfc [_cecf ];};_gdec :=_fa .Rect (_dbd ,0,_dbd +_dgfc [_gabc ],_eaff );_ffge ,_caff :=_gf .Extract (_gdec ,_geaaga );if _caff !=nil {return _caff ;};_ead ._bfbfa [_gabc ]=_ffge ;_ead ._dfe =append (_ead ._dfe ,_ffge );};return nil ;};
func (_afdcc *TextRegion )decodeRdh ()(int64 ,error ){const _cbab ="\u0064e\u0063\u006f\u0064\u0065\u0052\u0064h";if _afdcc .IsHuffmanEncoded {if _afdcc .SbHuffRDHeight ==3{if _afdcc ._cgac ==nil {var (_dgba int ;_cadc error ;);if _afdcc .SbHuffFS ==3{_dgba ++;
};if _afdcc .SbHuffDS ==3{_dgba ++;};if _afdcc .SbHuffDT ==3{_dgba ++;};if _afdcc .SbHuffRDWidth ==3{_dgba ++;};_afdcc ._cgac ,_cadc =_afdcc .getUserTable (_dgba );if _cadc !=nil {return 0,_ff .Wrap (_cadc ,_cbab ,"");};};return _afdcc ._cgac .Decode (_afdcc ._cgbc );
};_ggea ,_bbfa :=_aee .GetStandardTable (14+int (_afdcc .SbHuffRDHeight ));if _bbfa !=nil {return 0,_ff .Wrap (_bbfa ,_cbab ,"");};return _ggea .Decode (_afdcc ._cgbc );};_cbdd ,_dgca :=_afdcc ._afef .DecodeInt (_afdcc ._abcgd );if _dgca !=nil {return 0,_ff .Wrap (_dgca ,_cbab ,"");
};return int64 (_cbdd ),nil ;};func (_cea *GenericRefinementRegion )GetRegionBitmap ()(*_gf .Bitmap ,error ){var _cfe error ;_ge .Log .Trace ("\u005b\u0047E\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0047\u0065\u0074\u0052\u0065\u0067\u0069\u006f\u006e\u0042\u0069\u0074\u006d\u0061\u0070\u0020\u0062\u0065\u0067\u0069\u006e\u0073\u002e\u002e\u002e");
defer func (){if _cfe !=nil {_ge .Log .Trace ("[\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052E\u0046\u002d\u0052\u0045\u0047\u0049\u004fN]\u0020\u0047\u0065\u0074R\u0065\u0067\u0069\u006f\u006e\u0042\u0069\u0074\u006dap\u0020\u0066a\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0076",_cfe );
}else {_ge .Log .Trace ("\u005b\u0047E\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0047\u0065\u0074\u0052\u0065\u0067\u0069\u006f\u006e\u0042\u0069\u0074\u006d\u0061\u0070\u0020\u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u002e");
};}();if _cea .RegionBitmap !=nil {return _cea .RegionBitmap ,nil ;};_gd :=0;if _cea .ReferenceBitmap ==nil {_cea .ReferenceBitmap ,_cfe =_cea .getGrReference ();if _cfe !=nil {return nil ,_cfe ;};};if _cea ._ce ==nil {_cea ._ce ,_cfe =_ae .New (_cea ._ebe );
if _cfe !=nil {return nil ,_cfe ;};};if _cea ._ebb ==nil {_cea ._ebb =_ae .NewStats (8192,1);};_cea .RegionBitmap =_gf .New (int (_cea .RegionInfo .BitmapWidth ),int (_cea .RegionInfo .BitmapHeight ));if _cea .TemplateID ==0{if _cfe =_cea .updateOverride ();
_cfe !=nil {return nil ,_cfe ;};};_ad :=(_cea .RegionBitmap .Width +7)&-8;var _af int ;if _cea .IsTPGROn {_af =int (-_cea .ReferenceDY )*_cea .ReferenceBitmap .RowStride ;};_fc :=_af +1;for _fed :=0;_fed < _cea .RegionBitmap .Height ;_fed ++{if _cea .IsTPGROn {_bg ,_edf :=_cea .decodeSLTP ();
if _edf !=nil {return nil ,_edf ;};_gd ^=_bg ;};if _gd ==0{_cfe =_cea .decodeOptimized (_fed ,_cea .RegionBitmap .Width ,_cea .RegionBitmap .RowStride ,_cea .ReferenceBitmap .RowStride ,_ad ,_af ,_fc );if _cfe !=nil {return nil ,_cfe ;};}else {_cfe =_cea .decodeTypicalPredictedLine (_fed ,_cea .RegionBitmap .Width ,_cea .RegionBitmap .RowStride ,_cea .ReferenceBitmap .RowStride ,_ad ,_af );
if _cfe !=nil {return nil ,_cfe ;};};};return _cea .RegionBitmap ,nil ;};func (_fbbef *Header )readSegmentNumber (_ccge *_ed .Reader )error {const _daf ="\u0072\u0065\u0061\u0064\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u004eu\u006d\u0062\u0065\u0072";
_bdgcd :=make ([]byte ,4);_ ,_ffca :=_ccge .Read (_bdgcd );if _ffca !=nil {return _ff .Wrap (_ffca ,_daf ,"");};_fbbef .SegmentNumber =_dg .BigEndian .Uint32 (_bdgcd );return nil ;};func (_fga *SymbolDictionary )decodeHeightClassCollectiveBitmap (_gac int64 ,_dgbbec ,_eegf uint32 )(*_gf .Bitmap ,error ){if _gac ==0{_bgac :=_gf .New (int (_eegf ),int (_dgbbec ));
var (_efcc byte ;_ecbd error ;);for _bagc :=0;_bagc < len (_bgac .Data );_bagc ++{_efcc ,_ecbd =_fga ._gaeb .ReadByte ();if _ecbd !=nil {return nil ,_ecbd ;};if _ecbd =_bgac .SetByte (_bagc ,_efcc );_ecbd !=nil {return nil ,_ecbd ;};};return _bgac ,nil ;
};if _fga ._bafd ==nil {_fga ._bafd =NewGenericRegion (_fga ._gaeb );};_fga ._bafd .setParameters (true ,_fga ._gaeb .AbsolutePosition (),_gac ,_dgbbec ,_eegf );_gadc ,_geag :=_fga ._bafd .GetRegionBitmap ();if _geag !=nil {return nil ,_geag ;};return _gadc ,nil ;
};func (_cag *SymbolDictionary )encodeATFlags (_eeaeb _ed .BinaryWriter )(_ecec int ,_aeaa error ){const _dceb ="\u0065\u006e\u0063\u006f\u0064\u0065\u0041\u0054\u0046\u006c\u0061\u0067\u0073";if _cag .IsHuffmanEncoded ||_cag .SdTemplate !=0{return 0,nil ;
};_dbbdb :=4;if _cag .SdTemplate !=0{_dbbdb =1;};for _cge :=0;_cge < _dbbdb ;_cge ++{if _aeaa =_eeaeb .WriteByte (byte (_cag .SdATX [_cge ]));_aeaa !=nil {return _ecec ,_ff .Wrapf (_aeaa ,_dceb ,"\u0053d\u0041\u0054\u0058\u005b\u0025\u0064]",_cge );};_ecec ++;
if _aeaa =_eeaeb .WriteByte (byte (_cag .SdATY [_cge ]));_aeaa !=nil {return _ecec ,_ff .Wrapf (_aeaa ,_dceb ,"\u0053d\u0041\u0054\u0059\u005b\u0025\u0064]",_cge );};_ecec ++;};return _ecec ,nil ;};func (_gag *PageInformationSegment )readDefaultPixelValue ()error {_dgda ,_dfdd :=_gag ._dcaf .ReadBit ();
if _dfdd !=nil {return _dfdd ;};_gag .DefaultPixelValue =uint8 (_dgda &0xf);return nil ;};func (_bdgdb *PageInformationSegment )readRequiresAuxiliaryBuffer ()error {_abae ,_ecbf :=_bdgdb ._dcaf .ReadBit ();if _ecbf !=nil {return _ecbf ;};if _abae ==1{_bdgdb ._efff =true ;
};return nil ;};type Documenter interface{GetPage (int )(Pager ,error );GetGlobalSegment (int )(*Header ,error );};func (_bee *TextRegion )blit (_ceaa *_gf .Bitmap ,_fbdaf int64 )error {if _bee .IsTransposed ==0&&(_bee .ReferenceCorner ==2||_bee .ReferenceCorner ==3){_bee ._dea +=int64 (_ceaa .Width -1);
}else if _bee .IsTransposed ==1&&(_bee .ReferenceCorner ==0||_bee .ReferenceCorner ==2){_bee ._dea +=int64 (_ceaa .Height -1);};_fgbf :=_bee ._dea ;if _bee .IsTransposed ==1{_fgbf ,_fbdaf =_fbdaf ,_fgbf ;};switch _bee .ReferenceCorner {case 0:_fbdaf -=int64 (_ceaa .Height -1);
case 2:_fbdaf -=int64 (_ceaa .Height -1);_fgbf -=int64 (_ceaa .Width -1);case 3:_fgbf -=int64 (_ceaa .Width -1);};_gdecc :=_gf .Blit (_ceaa ,_bee .RegionBitmap ,int (_fgbf ),int (_fbdaf ),_bee .CombinationOperator );if _gdecc !=nil {return _gdecc ;};if _bee .IsTransposed ==0&&(_bee .ReferenceCorner ==0||_bee .ReferenceCorner ==1){_bee ._dea +=int64 (_ceaa .Width -1);
};if _bee .IsTransposed ==1&&(_bee .ReferenceCorner ==1||_bee .ReferenceCorner ==3){_bee ._dea +=int64 (_ceaa .Height -1);};return nil ;};func (_adab *RegionSegment )Size ()int {return 17};func (_bgd *GenericRegion )setParameters (_ffded bool ,_ggaa ,_gdg int64 ,_cgbba ,_fccg uint32 ){_bgd .IsMMREncoded =_ffded ;
_bgd .DataOffset =_ggaa ;_bgd .DataLength =_gdg ;_bgd .RegionSegment .BitmapHeight =_cgbba ;_bgd .RegionSegment .BitmapWidth =_fccg ;_bgd ._gbce =nil ;_bgd .Bitmap =nil ;};type HalftoneRegion struct{_acfg *_ed .Reader ;_fcee *Header ;DataHeaderOffset int64 ;
DataHeaderLength int64 ;DataOffset int64 ;DataLength int64 ;RegionSegment *RegionSegment ;HDefaultPixel int8 ;CombinationOperator _gf .CombinationOperator ;HSkipEnabled bool ;HTemplate byte ;IsMMREncoded bool ;HGridWidth uint32 ;HGridHeight uint32 ;HGridX int32 ;
HGridY int32 ;HRegionX uint16 ;HRegionY uint16 ;HalftoneRegionBitmap *_gf .Bitmap ;Patterns []*_gf .Bitmap ;};func (_cca *HalftoneRegion )GetPatterns ()([]*_gf .Bitmap ,error ){var (_bdb []*_gf .Bitmap ;_abbg error ;);for _ ,_gbaf :=range _cca ._fcee .RTSegments {var _gffg Segmenter ;
_gffg ,_abbg =_gbaf .GetSegmentData ();if _abbg !=nil {_ge .Log .Debug ("\u0047e\u0074\u0053\u0065\u0067m\u0065\u006e\u0074\u0044\u0061t\u0061 \u0066a\u0069\u006c\u0065\u0064\u003a\u0020\u0025v",_abbg );return nil ,_abbg ;};_aeeb ,_bfdb :=_gffg .(*PatternDictionary );
if !_bfdb {_abbg =_e .Errorf ("\u0072e\u006c\u0061t\u0065\u0064\u0020\u0073e\u0067\u006d\u0065n\u0074\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0070at\u0074\u0065\u0072n\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u003a \u0025\u0054",_gffg );
return nil ,_abbg ;};var _gcdf []*_gf .Bitmap ;_gcdf ,_abbg =_aeeb .GetDictionary ();if _abbg !=nil {_ge .Log .Debug ("\u0070\u0061\u0074\u0074\u0065\u0072\u006e\u0020\u0047\u0065\u0074\u0044\u0069\u0063\u0074i\u006fn\u0061\u0072\u0079\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_abbg );
return nil ,_abbg ;};_bdb =append (_bdb ,_gcdf ...);};return _bdb ,nil ;};func (_ced *TableSegment )HtRS ()int32 {return _ced ._caab };const (ORandom OrganizationType =iota ;OSequential ;);func (_fceef *PatternDictionary )readIsMMREncoded ()error {_feeaab ,_bced :=_fceef ._dface .ReadBit ();
if _bced !=nil {return _bced ;};if _feeaab !=0{_fceef .IsMMREncoded =true ;};return nil ;};func (_dgfa *RegionSegment )String ()string {_dbeb :=&_a .Builder {};_dbeb .WriteString ("\u0009[\u0052E\u0047\u0049\u004f\u004e\u0020S\u0045\u0047M\u0045\u004e\u0054\u005d\u000a");
_dbeb .WriteString (_e .Sprintf ("\t\u0009\u002d\u0020\u0042\u0069\u0074m\u0061\u0070\u0020\u0028\u0077\u0069d\u0074\u0068\u002c\u0020\u0068\u0065\u0069g\u0068\u0074\u0029\u0020\u005b\u0025\u0064\u0078\u0025\u0064]\u000a",_dgfa .BitmapWidth ,_dgfa .BitmapHeight ));
_dbeb .WriteString (_e .Sprintf ("\u0009\u0009\u002d\u0020L\u006f\u0063\u0061\u0074\u0069\u006f\u006e\u0020\u0028\u0078,\u0079)\u003a\u0020\u005b\u0025\u0064\u002c\u0025d\u005d\u000a",_dgfa .XLocation ,_dgfa .YLocation ));_dbeb .WriteString (_e .Sprintf ("\t\u0009\u002d\u0020\u0043\u006f\u006db\u0069\u006e\u0061\u0074\u0069\u006f\u006e\u004f\u0070e\u0072\u0061\u0074o\u0072:\u0020\u0025\u0073",_dgfa .CombinaionOperator ));
return _dbeb .String ();};func (_bef *GenericRefinementRegion )String ()string {_ef :=&_a .Builder {};_ef .WriteString ("\u000a[\u0047E\u004e\u0045\u0052\u0049\u0043 \u0052\u0045G\u0049\u004f\u004e\u005d\u000a");_ef .WriteString (_bef .RegionInfo .String ()+"\u000a");
_ef .WriteString (_e .Sprintf ("\u0009\u002d \u0049\u0073\u0054P\u0047\u0052\u006f\u006e\u003a\u0020\u0025\u0076\u000a",_bef .IsTPGROn ));_ef .WriteString (_e .Sprintf ("\u0009-\u0020T\u0065\u006d\u0070\u006c\u0061t\u0065\u0049D\u003a\u0020\u0025\u0076\u000a",_bef .TemplateID ));
_ef .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0047\u0072\u0041\u0074\u0058\u003a\u0020\u0025\u0076\u000a",_bef .GrAtX ));_ef .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0047\u0072\u0041\u0074\u0059\u003a\u0020\u0025\u0076\u000a",_bef .GrAtY ));
_ef .WriteString (_e .Sprintf ("\u0009-\u0020R\u0065\u0066\u0065\u0072\u0065n\u0063\u0065D\u0058\u0020\u0025\u0076\u000a",_bef .ReferenceDX ));_ef .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0052ef\u0065\u0072\u0065\u006e\u0063\u0044\u0065\u0059\u003a\u0020\u0025\u0076\u000a",_bef .ReferenceDY ));
return _ef .String ();};func (_aa *GenericRefinementRegion )Init (header *Header ,r *_ed .Reader )error {_aa ._ga =header ;_aa ._ebe =r ;_aa .RegionInfo =NewRegionSegment (r );return _aa .parseHeader ();};func (_daeb *PageInformationSegment )Size ()int {return 19};
func (_afbf *SymbolDictionary )InitEncode (symbols *_gf .Bitmaps ,symbolList []int ,symbolMap map[int ]int ,unborderSymbols bool )error {const _effe ="S\u0079\u006d\u0062\u006f\u006c\u0044i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002eI\u006e\u0069\u0074E\u006ec\u006f\u0064\u0065";
_afbf .SdATX =[]int8 {3,-3,2,-2};_afbf .SdATY =[]int8 {-1,-1,-2,-2};_afbf ._egbc =symbols ;_afbf ._ccbg =make ([]int ,len (symbolList ));copy (_afbf ._ccbg ,symbolList );if len (_afbf ._ccbg )!=_afbf ._egbc .Size (){return _ff .Error (_effe ,"s\u0079\u006d\u0062\u006f\u006c\u0073\u0020\u0061\u006e\u0064\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u004ci\u0073\u0074\u0020\u006f\u0066\u0020\u0064\u0069\u0066\u0066er\u0065\u006e\u0074 \u0073i\u007a\u0065");
};_afbf .NumberOfNewSymbols =uint32 (symbols .Size ());_afbf .NumberOfExportedSymbols =uint32 (symbols .Size ());_afbf ._adf =symbolMap ;_afbf ._ggdd =unborderSymbols ;return nil ;};func (_fdgb *PageInformationSegment )Init (h *Header ,r *_ed .Reader )(_efffa error ){_fdgb ._dcaf =r ;
if _efffa =_fdgb .parseHeader ();_efffa !=nil {return _ff .Wrap (_efffa ,"P\u0061\u0067\u0065\u0049\u006e\u0066o\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0053\u0065g\u006d\u0065\u006et\u002eI\u006e\u0069\u0074","");};return nil ;};func (_bac *GenericRegion )decodeTemplate1 (_fedg ,_beg ,_dbbe int ,_fcb ,_egg int )(_fgec error ){const _bcee ="\u0064e\u0063o\u0064\u0065\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0031";
var (_cfge ,_gca int ;_daac ,_adgd int ;_decg byte ;_bea ,_abb int ;);if _fedg >=1{_decg ,_fgec =_bac .Bitmap .GetByte (_egg );if _fgec !=nil {return _ff .Wrap (_fgec ,_bcee ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00201");};_daac =int (_decg );};if _fedg >=2{_decg ,_fgec =_bac .Bitmap .GetByte (_egg -_bac .Bitmap .RowStride );
if _fgec !=nil {return _ff .Wrap (_fgec ,_bcee ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00202");};_adgd =int (_decg )<<5;};_cfge =((_daac >>1)&0x1f8)|((_adgd >>1)&0x1e00);for _dgbb :=0;_dgbb < _dbbe ;_dgbb =_bea {var (_eeaf byte ;_ecb int ;);_bea =_dgbb +8;
if _aedd :=_beg -_dgbb ;_aedd > 8{_ecb =8;}else {_ecb =_aedd ;};if _fedg > 0{_daac <<=8;if _bea < _beg {_decg ,_fgec =_bac .Bitmap .GetByte (_egg +1);if _fgec !=nil {return _ff .Wrap (_fgec ,_bcee ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0030");};
_daac |=int (_decg );};};if _fedg > 1{_adgd <<=8;if _bea < _beg {_decg ,_fgec =_bac .Bitmap .GetByte (_egg -_bac .Bitmap .RowStride +1);if _fgec !=nil {return _ff .Wrap (_fgec ,_bcee ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0031");};_adgd |=int (_decg )<<5;
};};for _cfa :=0;_cfa < _ecb ;_cfa ++{if _bac ._eeg {_gca =_bac .overrideAtTemplate1 (_cfge ,_dgbb +_cfa ,_fedg ,int (_eeaf ),_cfa );_bac ._cfb .SetIndex (int32 (_gca ));}else {_bac ._cfb .SetIndex (int32 (_cfge ));};_abb ,_fgec =_bac ._bde .DecodeBit (_bac ._cfb );
if _fgec !=nil {return _ff .Wrap (_fgec ,_bcee ,"");};_eeaf |=byte (_abb )<<uint (7-_cfa );_efa :=uint (8-_cfa );_cfge =((_cfge &0xefb)<<1)|_abb |((_daac >>_efa )&0x8)|((_adgd >>_efa )&0x200);};if _daba :=_bac .Bitmap .SetByte (_fcb ,_eeaf );_daba !=nil {return _ff .Wrap (_daba ,_bcee ,"");
};_fcb ++;_egg ++;};return nil ;};func (_dddb *HalftoneRegion )combineGrayscalePlanes (_dgf []*_gf .Bitmap ,_afdcd int )error {_gfbc :=0;for _egf :=0;_egf < _dgf [_afdcd ].Height ;_egf ++{for _bedb :=0;_bedb < _dgf [_afdcd ].Width ;_bedb +=8{_aeg ,_fbga :=_dgf [_afdcd +1].GetByte (_gfbc );
if _fbga !=nil {return _fbga ;};_dcgg ,_fbga :=_dgf [_afdcd ].GetByte (_gfbc );if _fbga !=nil {return _fbga ;};_fbga =_dgf [_afdcd ].SetByte (_gfbc ,_gf .CombineBytes (_dcgg ,_aeg ,_gf .CmbOpXor ));if _fbga !=nil {return _fbga ;};_gfbc ++;};};return nil ;
};func (_dggb *SymbolDictionary )readRegionFlags ()error {var (_fffbg uint64 ;_aeag int ;);_ ,_dgad :=_dggb ._gaeb .ReadBits (3);if _dgad !=nil {return _dgad ;};_aeag ,_dgad =_dggb ._gaeb .ReadBit ();if _dgad !=nil {return _dgad ;};_dggb .SdrTemplate =int8 (_aeag );
_fffbg ,_dgad =_dggb ._gaeb .ReadBits (2);if _dgad !=nil {return _dgad ;};_dggb .SdTemplate =int8 (_fffbg &0xf);_aeag ,_dgad =_dggb ._gaeb .ReadBit ();if _dgad !=nil {return _dgad ;};if _aeag ==1{_dggb ._bbfd =true ;};_aeag ,_dgad =_dggb ._gaeb .ReadBit ();
if _dgad !=nil {return _dgad ;};if _aeag ==1{_dggb ._ebfg =true ;};_aeag ,_dgad =_dggb ._gaeb .ReadBit ();if _dgad !=nil {return _dgad ;};if _aeag ==1{_dggb .SdHuffAggInstanceSelection =true ;};_aeag ,_dgad =_dggb ._gaeb .ReadBit ();if _dgad !=nil {return _dgad ;
};_dggb .SdHuffBMSizeSelection =int8 (_aeag );_fffbg ,_dgad =_dggb ._gaeb .ReadBits (2);if _dgad !=nil {return _dgad ;};_dggb .SdHuffDecodeWidthSelection =int8 (_fffbg &0xf);_fffbg ,_dgad =_dggb ._gaeb .ReadBits (2);if _dgad !=nil {return _dgad ;};_dggb .SdHuffDecodeHeightSelection =int8 (_fffbg &0xf);
_aeag ,_dgad =_dggb ._gaeb .ReadBit ();if _dgad !=nil {return _dgad ;};if _aeag ==1{_dggb .UseRefinementAggregation =true ;};_aeag ,_dgad =_dggb ._gaeb .ReadBit ();if _dgad !=nil {return _dgad ;};if _aeag ==1{_dggb .IsHuffmanEncoded =true ;};return nil ;
};func (_acda *GenericRegion )updateOverrideFlags ()error {const _ffc ="\u0075\u0070\u0064\u0061te\u004f\u0076\u0065\u0072\u0072\u0069\u0064\u0065\u0046\u006c\u0061\u0067\u0073";if _acda .GBAtX ==nil ||_acda .GBAtY ==nil {return nil ;};if len (_acda .GBAtX )!=len (_acda .GBAtY ){return _ff .Errorf (_ffc ,"i\u006eco\u0073i\u0073t\u0065\u006e\u0074\u0020\u0041T\u0020\u0070\u0069x\u0065\u006c\u002e\u0020\u0041m\u006f\u0075\u006et\u0020\u006f\u0066\u0020\u0027\u0078\u0027\u0020\u0070\u0069\u0078e\u006c\u0073\u003a %d\u002c\u0020\u0041\u006d\u006f\u0075n\u0074\u0020\u006f\u0066\u0020\u0027\u0079\u0027\u0020\u0070\u0069\u0078e\u006cs\u003a\u0020\u0025\u0064",len (_acda .GBAtX ),len (_acda .GBAtY ));
};_acda .GBAtOverride =make ([]bool ,len (_acda .GBAtX ));switch _acda .GBTemplate {case 0:if !_acda .UseExtTemplates {if _acda .GBAtX [0]!=3||_acda .GBAtY [0]!=-1{_acda .setOverrideFlag (0);};if _acda .GBAtX [1]!=-3||_acda .GBAtY [1]!=-1{_acda .setOverrideFlag (1);
};if _acda .GBAtX [2]!=2||_acda .GBAtY [2]!=-2{_acda .setOverrideFlag (2);};if _acda .GBAtX [3]!=-2||_acda .GBAtY [3]!=-2{_acda .setOverrideFlag (3);};}else {if _acda .GBAtX [0]!=-2||_acda .GBAtY [0]!=0{_acda .setOverrideFlag (0);};if _acda .GBAtX [1]!=0||_acda .GBAtY [1]!=-2{_acda .setOverrideFlag (1);
};if _acda .GBAtX [2]!=-2||_acda .GBAtY [2]!=-1{_acda .setOverrideFlag (2);};if _acda .GBAtX [3]!=-1||_acda .GBAtY [3]!=-2{_acda .setOverrideFlag (3);};if _acda .GBAtX [4]!=1||_acda .GBAtY [4]!=-2{_acda .setOverrideFlag (4);};if _acda .GBAtX [5]!=2||_acda .GBAtY [5]!=-1{_acda .setOverrideFlag (5);
};if _acda .GBAtX [6]!=-3||_acda .GBAtY [6]!=0{_acda .setOverrideFlag (6);};if _acda .GBAtX [7]!=-4||_acda .GBAtY [7]!=0{_acda .setOverrideFlag (7);};if _acda .GBAtX [8]!=2||_acda .GBAtY [8]!=-2{_acda .setOverrideFlag (8);};if _acda .GBAtX [9]!=3||_acda .GBAtY [9]!=-1{_acda .setOverrideFlag (9);
};if _acda .GBAtX [10]!=-2||_acda .GBAtY [10]!=-2{_acda .setOverrideFlag (10);};if _acda .GBAtX [11]!=-3||_acda .GBAtY [11]!=-1{_acda .setOverrideFlag (11);};};case 1:if _acda .GBAtX [0]!=3||_acda .GBAtY [0]!=-1{_acda .setOverrideFlag (0);};case 2:if _acda .GBAtX [0]!=2||_acda .GBAtY [0]!=-1{_acda .setOverrideFlag (0);
};case 3:if _acda .GBAtX [0]!=2||_acda .GBAtY [0]!=-1{_acda .setOverrideFlag (0);};};return nil ;};func (_dfdb *TableSegment )HtHigh ()int32 {return _dfdb ._bdbeg };func (_adeb *SymbolDictionary )setRetainedCodingContexts (_bcfg *SymbolDictionary ){_adeb ._daed =_bcfg ._daed ;
_adeb .IsHuffmanEncoded =_bcfg .IsHuffmanEncoded ;_adeb .UseRefinementAggregation =_bcfg .UseRefinementAggregation ;_adeb .SdTemplate =_bcfg .SdTemplate ;_adeb .SdrTemplate =_bcfg .SdrTemplate ;_adeb .SdATX =_bcfg .SdATX ;_adeb .SdATY =_bcfg .SdATY ;_adeb .SdrATX =_bcfg .SdrATX ;
_adeb .SdrATY =_bcfg .SdrATY ;_adeb ._bgbe =_bcfg ._bgbe ;};func (_ebda *HalftoneRegion )computeSegmentDataStructure ()error {_ebda .DataOffset =_ebda ._acfg .AbsolutePosition ();_ebda .DataHeaderLength =_ebda .DataOffset -_ebda .DataHeaderOffset ;_ebda .DataLength =int64 (_ebda ._acfg .AbsoluteLength ())-_ebda .DataHeaderLength ;
return nil ;};func (_eed *GenericRegion )GetRegionInfo ()*RegionSegment {return _eed .RegionSegment };func (_cfbf *GenericRegion )decodeLine (_fea ,_cdd ,_ffef int )error {const _gae ="\u0064\u0065\u0063\u006f\u0064\u0065\u004c\u0069\u006e\u0065";_dfb :=_cfbf .Bitmap .GetByteIndex (0,_fea );
_ccg :=_dfb -_cfbf .Bitmap .RowStride ;switch _cfbf .GBTemplate {case 0:if !_cfbf .UseExtTemplates {return _cfbf .decodeTemplate0a (_fea ,_cdd ,_ffef ,_dfb ,_ccg );};return _cfbf .decodeTemplate0b (_fea ,_cdd ,_ffef ,_dfb ,_ccg );case 1:return _cfbf .decodeTemplate1 (_fea ,_cdd ,_ffef ,_dfb ,_ccg );
case 2:return _cfbf .decodeTemplate2 (_fea ,_cdd ,_ffef ,_dfb ,_ccg );case 3:return _cfbf .decodeTemplate3 (_fea ,_cdd ,_ffef ,_dfb ,_ccg );};return _ff .Errorf (_gae ,"\u0069\u006e\u0076a\u006c\u0069\u0064\u0020G\u0042\u0054\u0065\u006d\u0070\u006c\u0061t\u0065\u0020\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u003a\u0020\u0025\u0064",_cfbf .GBTemplate );
};func (_cad *SymbolDictionary )decodeAggregate (_daedd ,_gcaec uint32 )error {var (_ebad int64 ;_bdcd error ;);if _cad .IsHuffmanEncoded {_ebad ,_bdcd =_cad .huffDecodeRefAggNInst ();if _bdcd !=nil {return _bdcd ;};}else {_egef ,_gffb :=_cad ._daed .DecodeInt (_cad ._ebgf );
if _gffb !=nil {return _gffb ;};_ebad =int64 (_egef );};if _ebad > 1{return _cad .decodeThroughTextRegion (_daedd ,_gcaec ,uint32 (_ebad ));}else if _ebad ==1{return _cad .decodeRefinedSymbol (_daedd ,_gcaec );};return nil ;};func (_ddgg *PageInformationSegment )encodeStripingInformation (_geeda _ed .BinaryWriter )(_egeae int ,_dee error ){const _cfbb ="\u0065n\u0063\u006f\u0064\u0065S\u0074\u0072\u0069\u0070\u0069n\u0067I\u006ef\u006f\u0072\u006d\u0061\u0074\u0069\u006fn";
if !_ddgg .IsStripe {if _egeae ,_dee =_geeda .Write ([]byte {0x00,0x00});_dee !=nil {return 0,_ff .Wrap (_dee ,_cfbb ,"n\u006f\u0020\u0073\u0074\u0072\u0069\u0070\u0069\u006e\u0067");};return _egeae ,nil ;};_gcef :=make ([]byte ,2);_dg .BigEndian .PutUint16 (_gcef ,_ddgg .MaxStripeSize |1<<15);
if _egeae ,_dee =_geeda .Write (_gcef );_dee !=nil {return 0,_ff .Wrapf (_dee ,_cfbb ,"\u0073\u0074\u0072i\u0070\u0069\u006e\u0067\u003a\u0020\u0025\u0064",_ddgg .MaxStripeSize );};return _egeae ,nil ;};func (_eec *GenericRefinementRegion )decodeTemplate (_dag ,_bec ,_bd ,_ec ,_afd ,_eea ,_fcd ,_ffa ,_geb ,_bdc int ,_bcd templater )(_feg error ){var (_cbc ,_edaa ,_gde ,_ebec ,_ba int16 ;
_eaa ,_baf ,_aab ,_ade int ;_acb byte ;);if _geb >=1&&(_geb -1)< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc -_ec );if _feg !=nil {return _feg ;};_eaa =int (_acb );};if _geb >=0&&(_geb )< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc );
if _feg !=nil {return _feg ;};_baf =int (_acb );};if _geb >=-1&&(_geb +1)< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc +_ec );if _feg !=nil {return _feg ;};_aab =int (_acb );};_bdc ++;if _dag >=1{_acb ,_feg =_eec .RegionBitmap .GetByte (_ffa -_bd );
if _feg !=nil {return _feg ;};_ade =int (_acb );};_ffa ++;_eded :=_eec .ReferenceDX %8;_dbg :=6+_eded ;_ffdb :=_bdc %_ec ;if _dbg >=0{if _dbg < 8{_cbc =int16 (_eaa >>uint (_dbg ))&0x07;};if _dbg < 8{_edaa =int16 (_baf >>uint (_dbg ))&0x07;};if _dbg < 8{_gde =int16 (_aab >>uint (_dbg ))&0x07;
};if _dbg ==6&&_ffdb > 1{if _geb >=1&&(_geb -1)< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc -_ec -2);if _feg !=nil {return _feg ;};_cbc |=int16 (_acb <<2)&0x04;};if _geb >=0&&_geb < _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc -2);
if _feg !=nil {return _feg ;};_edaa |=int16 (_acb <<2)&0x04;};if _geb >=-1&&_geb +1< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc +_ec -2);if _feg !=nil {return _feg ;};_gde |=int16 (_acb <<2)&0x04;};};if _dbg ==0{_eaa =0;
_baf =0;_aab =0;if _ffdb < _ec -1{if _geb >=1&&_geb -1< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc -_ec );if _feg !=nil {return _feg ;};_eaa =int (_acb );};if _geb >=0&&_geb < _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc );
if _feg !=nil {return _feg ;};_baf =int (_acb );};if _geb >=-1&&_geb +1< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc +_ec );if _feg !=nil {return _feg ;};_aab =int (_acb );};};_bdc ++;};}else {_cbc =int16 (_eaa <<1)&0x07;
_edaa =int16 (_baf <<1)&0x07;_gde =int16 (_aab <<1)&0x07;_eaa =0;_baf =0;_aab =0;if _ffdb < _ec -1{if _geb >=1&&_geb -1< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc -_ec );if _feg !=nil {return _feg ;};_eaa =int (_acb );
};if _geb >=0&&_geb < _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc );if _feg !=nil {return _feg ;};_baf =int (_acb );};if _geb >=-1&&_geb +1< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc +_ec );
if _feg !=nil {return _feg ;};_aab =int (_acb );};_bdc ++;};_cbc |=int16 ((_eaa >>7)&0x07);_edaa |=int16 ((_baf >>7)&0x07);_gde |=int16 ((_aab >>7)&0x07);};_ebec =int16 (_ade >>6);_ba =0;_ggb :=(2-_eded )%8;_eaa <<=uint (_ggb );_baf <<=uint (_ggb );_aab <<=uint (_ggb );
_ade <<=2;var _gbcf int ;for _cdf :=0;_cdf < _bec ;_cdf ++{_edc :=_cdf &0x07;_add :=_bcd .form (_cbc ,_edaa ,_gde ,_ebec ,_ba );if _eec ._fe {_acb ,_feg =_eec .RegionBitmap .GetByte (_eec .RegionBitmap .GetByteIndex (_cdf ,_dag ));if _feg !=nil {return _feg ;
};_eec ._ebb .SetIndex (int32 (_eec .overrideAtTemplate0 (int (_add ),_cdf ,_dag ,int (_acb ),_edc )));}else {_eec ._ebb .SetIndex (int32 (_add ));};_gbcf ,_feg =_eec ._ce .DecodeBit (_eec ._ebb );if _feg !=nil {return _feg ;};if _feg =_eec .RegionBitmap .SetPixel (_cdf ,_dag ,byte (_gbcf ));
_feg !=nil {return _feg ;};_cbc =((_cbc <<1)|0x01&int16 (_eaa >>7))&0x07;_edaa =((_edaa <<1)|0x01&int16 (_baf >>7))&0x07;_gde =((_gde <<1)|0x01&int16 (_aab >>7))&0x07;_ebec =((_ebec <<1)|0x01&int16 (_ade >>7))&0x07;_ba =int16 (_gbcf );if (_cdf -int (_eec .ReferenceDX ))%8==5{_eaa =0;
_baf =0;_aab =0;if ((_cdf -int (_eec .ReferenceDX ))/8)+1< _eec .ReferenceBitmap .RowStride {if _geb >=1&&(_geb -1)< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc -_ec );if _feg !=nil {return _feg ;};_eaa =int (_acb );
};if _geb >=0&&_geb < _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc );if _feg !=nil {return _feg ;};_baf =int (_acb );};if _geb >=-1&&(_geb +1)< _eec .ReferenceBitmap .Height {_acb ,_feg =_eec .ReferenceBitmap .GetByte (_bdc +_ec );
if _feg !=nil {return _feg ;};_aab =int (_acb );};};_bdc ++;}else {_eaa <<=1;_baf <<=1;_aab <<=1;};if _edc ==5&&_dag >=1{if ((_cdf >>3)+1)>=_eec .RegionBitmap .RowStride {_ade =0;}else {_acb ,_feg =_eec .RegionBitmap .GetByte (_ffa -_bd );if _feg !=nil {return _feg ;
};_ade =int (_acb );};_ffa ++;}else {_ade <<=1;};};return nil ;};func (_cabab *SymbolDictionary )Init (h *Header ,r *_ed .Reader )error {_cabab .Header =h ;_cabab ._gaeb =r ;return _cabab .parseHeader ();};func (_dac *GenericRefinementRegion )getPixel (_adcc *_gf .Bitmap ,_eaae ,_agg int )int {if _eaae < 0||_eaae >=_adcc .Width {return 0;
};if _agg < 0||_agg >=_adcc .Height {return 0;};if _adcc .GetPixel (_eaae ,_agg ){return 1;};return 0;};func (_ddb *Header )readReferredToSegmentNumbers (_ccb *_ed .Reader ,_fcgg int )([]int ,error ){const _cege ="\u0072\u0065\u0061\u0064R\u0065\u0066\u0065\u0072\u0072\u0065\u0064\u0054\u006f\u0053e\u0067m\u0065\u006e\u0074\u004e\u0075\u006d\u0062e\u0072\u0073";
_fbf :=make ([]int ,_fcgg );if _fcgg > 0{_ddb .RTSegments =make ([]*Header ,_fcgg );var (_egfe uint64 ;_aaf error ;);for _cdbd :=0;_cdbd < _fcgg ;_cdbd ++{_egfe ,_aaf =_ccb .ReadBits (byte (_ddb .referenceSize ())<<3);if _aaf !=nil {return nil ,_ff .Wrapf (_aaf ,_cege ,"\u0027\u0025\u0064\u0027 \u0072\u0065\u0066\u0065\u0072\u0072\u0065\u0064\u0020\u0073e\u0067m\u0065\u006e\u0074\u0020\u006e\u0075\u006db\u0065\u0072",_cdbd );
};_fbf [_cdbd ]=int (_egfe &_g .MaxInt32 );};};return _fbf ,nil ;};func (_dbcc *SymbolDictionary )decodeThroughTextRegion (_cabd ,_gfbcc ,_bdac uint32 )error {if _dbcc ._fabf ==nil {_dbcc ._fabf =_fgd (_dbcc ._gaeb ,nil );_dbcc ._fabf .setContexts (_dbcc ._bgbe ,_ae .NewStats (512,1),_ae .NewStats (512,1),_ae .NewStats (512,1),_ae .NewStats (512,1),_dbcc ._efg ,_ae .NewStats (512,1),_ae .NewStats (512,1),_ae .NewStats (512,1),_ae .NewStats (512,1));
};if _ddbg :=_dbcc .setSymbolsArray ();_ddbg !=nil {return _ddbg ;};_dbcc ._fabf .setParameters (_dbcc ._daed ,_dbcc .IsHuffmanEncoded ,true ,_cabd ,_gfbcc ,_bdac ,1,_dbcc ._cdeb +_dbcc ._dcbgd ,0,0,0,1,0,0,0,0,0,0,0,0,0,_dbcc .SdrTemplate ,_dbcc .SdrATX ,_dbcc .SdrATY ,_dbcc ._dfe ,_dbcc ._dbce );
return _dbcc .addSymbol (_dbcc ._fabf );};func (_ggad *HalftoneRegion )renderPattern (_cega [][]int )(_feea error ){var _cgbg ,_cac int ;for _gcba :=0;_gcba < int (_ggad .HGridHeight );_gcba ++{for _egff :=0;_egff < int (_ggad .HGridWidth );_egff ++{_cgbg =_ggad .computeX (_gcba ,_egff );
_cac =_ggad .computeY (_gcba ,_egff );_fbba :=_ggad .Patterns [_cega [_gcba ][_egff ]];if _feea =_gf .Blit (_fbba ,_ggad .HalftoneRegionBitmap ,_cgbg +int (_ggad .HGridX ),_cac +int (_ggad .HGridY ),_ggad .CombinationOperator );_feea !=nil {return _feea ;
};};};return nil ;};func (_gfg *GenericRefinementRegion )setParameters (_adg *_ae .DecoderStats ,_adcb *_ae .Decoder ,_gfe int8 ,_fba ,_egd uint32 ,_gbd *_gf .Bitmap ,_adba ,_cbd int32 ,_gccb bool ,_cab []int8 ,_fddc []int8 ){_ge .Log .Trace ("\u005b\u0047\u0045NE\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052E\u0047I\u004fN\u005d \u0073\u0065\u0074\u0050\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073");
if _adg !=nil {_gfg ._ebb =_adg ;};if _adcb !=nil {_gfg ._ce =_adcb ;};_gfg .TemplateID =_gfe ;_gfg .RegionInfo .BitmapWidth =_fba ;_gfg .RegionInfo .BitmapHeight =_egd ;_gfg .ReferenceBitmap =_gbd ;_gfg .ReferenceDX =_adba ;_gfg .ReferenceDY =_cbd ;_gfg .IsTPGROn =_gccb ;
_gfg .GrAtX =_cab ;_gfg .GrAtY =_fddc ;_gfg .RegionBitmap =nil ;_ge .Log .Trace ("[\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052E\u0046\u002d\u0052\u0045\u0047\u0049\u004fN]\u0020\u0073\u0065\u0074P\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073 f\u0069\u006ei\u0073\u0068\u0065\u0064\u002e\u0020\u0025\u0073",_gfg );
};func (_gdae *GenericRegion )overrideAtTemplate0a (_gfcg ,_gba ,_ddd ,_eedb ,_ecgg ,_efe int )int {if _gdae .GBAtOverride [0]{_gfcg &=0xFFEF;if _gdae .GBAtY [0]==0&&_gdae .GBAtX [0]>=-int8 (_ecgg ){_gfcg |=(_eedb >>uint (int8 (_efe )-_gdae .GBAtX [0]&0x1))<<4;
}else {_gfcg |=int (_gdae .getPixel (_gba +int (_gdae .GBAtX [0]),_ddd +int (_gdae .GBAtY [0])))<<4;};};if _gdae .GBAtOverride [1]{_gfcg &=0xFBFF;if _gdae .GBAtY [1]==0&&_gdae .GBAtX [1]>=-int8 (_ecgg ){_gfcg |=(_eedb >>uint (int8 (_efe )-_gdae .GBAtX [1]&0x1))<<10;
}else {_gfcg |=int (_gdae .getPixel (_gba +int (_gdae .GBAtX [1]),_ddd +int (_gdae .GBAtY [1])))<<10;};};if _gdae .GBAtOverride [2]{_gfcg &=0xF7FF;if _gdae .GBAtY [2]==0&&_gdae .GBAtX [2]>=-int8 (_ecgg ){_gfcg |=(_eedb >>uint (int8 (_efe )-_gdae .GBAtX [2]&0x1))<<11;
}else {_gfcg |=int (_gdae .getPixel (_gba +int (_gdae .GBAtX [2]),_ddd +int (_gdae .GBAtY [2])))<<11;};};if _gdae .GBAtOverride [3]{_gfcg &=0x7FFF;if _gdae .GBAtY [3]==0&&_gdae .GBAtX [3]>=-int8 (_ecgg ){_gfcg |=(_eedb >>uint (int8 (_efe )-_gdae .GBAtX [3]&0x1))<<15;
}else {_gfcg |=int (_gdae .getPixel (_gba +int (_gdae .GBAtX [3]),_ddd +int (_gdae .GBAtY [3])))<<15;};};return _gfcg ;};func (_gfagg *TableSegment )HtLow ()int32 {return _gfagg ._fggcd };func (_cacaf *PageInformationSegment )encodeFlags (_cefe _ed .BinaryWriter )(_abbd error ){const _ccfc ="e\u006e\u0063\u006f\u0064\u0065\u0046\u006c\u0061\u0067\u0073";
if _abbd =_cefe .SkipBits (1);_abbd !=nil {return _ff .Wrap (_abbd ,_ccfc ,"\u0072\u0065\u0073e\u0072\u0076\u0065\u0064\u0020\u0062\u0069\u0074");};var _fbaf int ;if _cacaf .CombinationOperatorOverrideAllowed (){_fbaf =1;};if _abbd =_cefe .WriteBit (_fbaf );
_abbd !=nil {return _ff .Wrap (_abbd ,_ccfc ,"\u0063\u006f\u006db\u0069\u006e\u0061\u0074i\u006f\u006e\u0020\u006f\u0070\u0065\u0072a\u0074\u006f\u0072\u0020\u006f\u0076\u0065\u0072\u0072\u0069\u0064\u0064\u0065\u006e");};_fbaf =0;if _cacaf ._efff {_fbaf =1;
};if _abbd =_cefe .WriteBit (_fbaf );_abbd !=nil {return _ff .Wrap (_abbd ,_ccfc ,"\u0072e\u0071\u0075\u0069\u0072e\u0073\u0020\u0061\u0075\u0078i\u006ci\u0061r\u0079\u0020\u0062\u0075\u0066\u0066\u0065r");};if _abbd =_cefe .WriteBit ((int (_cacaf ._bae )>>1)&0x01);
_abbd !=nil {return _ff .Wrap (_abbd ,_ccfc ,"\u0063\u006f\u006d\u0062\u0069\u006e\u0061\u0074\u0069\u006fn\u0020\u006f\u0070\u0065\u0072\u0061\u0074o\u0072\u0020\u0066\u0069\u0072\u0073\u0074\u0020\u0062\u0069\u0074");};if _abbd =_cefe .WriteBit (int (_cacaf ._bae )&0x01);
_abbd !=nil {return _ff .Wrap (_abbd ,_ccfc ,"\u0063\u006f\u006db\u0069\u006e\u0061\u0074i\u006f\u006e\u0020\u006f\u0070\u0065\u0072a\u0074\u006f\u0072\u0020\u0073\u0065\u0063\u006f\u006e\u0064\u0020\u0062\u0069\u0074");};_fbaf =int (_cacaf .DefaultPixelValue );
if _abbd =_cefe .WriteBit (_fbaf );_abbd !=nil {return _ff .Wrap (_abbd ,_ccfc ,"\u0064e\u0066\u0061\u0075\u006c\u0074\u0020\u0070\u0061\u0067\u0065\u0020p\u0069\u0078\u0065\u006c\u0020\u0076\u0061\u006c\u0075\u0065");};_fbaf =0;if _cacaf ._afcf {_fbaf =1;
};if _abbd =_cefe .WriteBit (_fbaf );_abbd !=nil {return _ff .Wrap (_abbd ,_ccfc ,"\u0063\u006f\u006e\u0074ai\u006e\u0073\u0020\u0072\u0065\u0066\u0069\u006e\u0065\u006d\u0065\u006e\u0074");};_fbaf =0;if _cacaf .IsLossless {_fbaf =1;};if _abbd =_cefe .WriteBit (_fbaf );
_abbd !=nil {return _ff .Wrap (_abbd ,_ccfc ,"p\u0061\u0067\u0065\u0020\u0069\u0073 \u0065\u0076\u0065\u006e\u0074\u0075\u0061\u006c\u006cy\u0020\u006c\u006fs\u0073l\u0065\u0073\u0073");};return nil ;};func (_gaac *Header )writeFlags (_gffc _ed .BinaryWriter )(_gfag error ){const _dgcc ="\u0048\u0065\u0061\u0064\u0065\u0072\u002e\u0077\u0072\u0069\u0074\u0065F\u006c\u0061\u0067\u0073";
_dggec :=byte (_gaac .Type );if _gfag =_gffc .WriteByte (_dggec );_gfag !=nil {return _ff .Wrap (_gfag ,_dgcc ,"\u0077\u0072\u0069ti\u006e\u0067\u0020\u0073\u0065\u0067\u006d\u0065\u006et\u0020t\u0079p\u0065 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065\u0064");
};if !_gaac .RetainFlag &&!_gaac .PageAssociationFieldSize {return nil ;};if _gfag =_gffc .SkipBits (-8);_gfag !=nil {return _ff .Wrap (_gfag ,_dgcc ,"\u0073\u006bi\u0070\u0070\u0069\u006e\u0067\u0020\u0062\u0061\u0063\u006b\u0020\u0074\u0068\u0065\u0020\u0062\u0069\u0074\u0073\u0020\u0066\u0061il\u0065\u0064");
};var _ecfe int ;if _gaac .RetainFlag {_ecfe =1;};if _gfag =_gffc .WriteBit (_ecfe );_gfag !=nil {return _ff .Wrap (_gfag ,_dgcc ,"\u0072\u0065\u0074\u0061in\u0020\u0072\u0065\u0074\u0061\u0069\u006e\u0020\u0066\u006c\u0061\u0067\u0073");};_ecfe =0;if _gaac .PageAssociationFieldSize {_ecfe =1;
};if _gfag =_gffc .WriteBit (_ecfe );_gfag !=nil {return _ff .Wrap (_gfag ,_dgcc ,"p\u0061\u0067\u0065\u0020as\u0073o\u0063\u0069\u0061\u0074\u0069o\u006e\u0020\u0066\u006c\u0061\u0067");};_gffc .FinishByte ();return nil ;};func (_aeb *GenericRefinementRegion )decodeTypicalPredictedLineTemplate1 (_cfc ,_cgb ,_bc ,_gcc ,_fedc ,_agd ,_gce ,_dgc ,_ebeb int )(_cba error ){var (_bbg ,_gdf int ;
_abe ,_dcd int ;_bcg ,_abc int ;_ea byte ;);if _cfc > 0{_ea ,_cba =_aeb .RegionBitmap .GetByte (_gce -_bc );if _cba !=nil {return _cba ;};_abe =int (_ea );};if _dgc > 0&&_dgc <=_aeb .ReferenceBitmap .Height {_ea ,_cba =_aeb .ReferenceBitmap .GetByte (_ebeb -_gcc +_agd );
if _cba !=nil {return _cba ;};_dcd =int (_ea )<<2;};if _dgc >=0&&_dgc < _aeb .ReferenceBitmap .Height {_ea ,_cba =_aeb .ReferenceBitmap .GetByte (_ebeb +_agd );if _cba !=nil {return _cba ;};_bcg =int (_ea );};if _dgc > -2&&_dgc < _aeb .ReferenceBitmap .Height -1{_ea ,_cba =_aeb .ReferenceBitmap .GetByte (_ebeb +_gcc +_agd );
if _cba !=nil {return _cba ;};_abc =int (_ea );};_bbg =((_abe >>5)&0x6)|((_abc >>2)&0x30)|(_bcg &0xc0)|(_dcd &0x200);_gdf =((_abc >>2)&0x70)|(_bcg &0xc0)|(_dcd &0x700);var _fdd int ;for _ffe :=0;_ffe < _fedc ;_ffe =_fdd {var (_aeeda int ;_dca int ;);_fdd =_ffe +8;
if _aeeda =_cgb -_ffe ;_aeeda > 8{_aeeda =8;};_eee :=_fdd < _cgb ;_acd :=_fdd < _aeb .ReferenceBitmap .Width ;_fcc :=_agd +1;if _cfc > 0{_ea =0;if _eee {_ea ,_cba =_aeb .RegionBitmap .GetByte (_gce -_bc +1);if _cba !=nil {return _cba ;};};_abe =(_abe <<8)|int (_ea );
};if _dgc > 0&&_dgc <=_aeb .ReferenceBitmap .Height {var _dbe int ;if _acd {_ea ,_cba =_aeb .ReferenceBitmap .GetByte (_ebeb -_gcc +_fcc );if _cba !=nil {return _cba ;};_dbe =int (_ea )<<2;};_dcd =(_dcd <<8)|_dbe ;};if _dgc >=0&&_dgc < _aeb .ReferenceBitmap .Height {_ea =0;
if _acd {_ea ,_cba =_aeb .ReferenceBitmap .GetByte (_ebeb +_fcc );if _cba !=nil {return _cba ;};};_bcg =(_bcg <<8)|int (_ea );};if _dgc > -2&&_dgc < (_aeb .ReferenceBitmap .Height -1){_ea =0;if _acd {_ea ,_cba =_aeb .ReferenceBitmap .GetByte (_ebeb +_gcc +_fcc );
if _cba !=nil {return _cba ;};};_abc =(_abc <<8)|int (_ea );};for _ede :=0;_ede < _aeeda ;_ede ++{var _gda int ;_de :=(_gdf >>4)&0x1ff;switch _de {case 0x1ff:_gda =1;case 0x00:_gda =0;default:_aeb ._ebb .SetIndex (int32 (_bbg ));_gda ,_cba =_aeb ._ce .DecodeBit (_aeb ._ebb );
if _cba !=nil {return _cba ;};};_bcb :=uint (7-_ede );_dca |=_gda <<_bcb ;_bbg =((_bbg &0x0d6)<<1)|_gda |(_abe >>_bcb +5)&0x002|((_abc >>_bcb +2)&0x010)|((_bcg >>_bcb )&0x040)|((_dcd >>_bcb )&0x200);_gdf =((_gdf &0xdb)<<1)|((_abc >>_bcb +2)&0x010)|((_bcg >>_bcb )&0x080)|((_dcd >>_bcb )&0x400);
};_cba =_aeb .RegionBitmap .SetByte (_gce ,byte (_dca ));if _cba !=nil {return _cba ;};_gce ++;_ebeb ++;};return nil ;};func (_fadb *SymbolDictionary )getSymbol (_fdbea int )(*_gf .Bitmap ,error ){const _efbd ="\u0067e\u0074\u0053\u0079\u006d\u0062\u006fl";
_ggcd ,_geaag :=_fadb ._egbc .GetBitmap (_fadb ._ccbg [_fdbea ]);if _geaag !=nil {return nil ,_ff .Wrap (_geaag ,_efbd ,"\u0063\u0061n\u0027\u0074\u0020g\u0065\u0074\u0020\u0073\u0079\u006d\u0062\u006f\u006c");};return _ggcd ,nil ;};var (_cfdcb Segmenter ;
_eecbc =map[Type ]func ()Segmenter {TSymbolDictionary :func ()Segmenter {return &SymbolDictionary {}},TIntermediateTextRegion :func ()Segmenter {return &TextRegion {}},TImmediateTextRegion :func ()Segmenter {return &TextRegion {}},TImmediateLosslessTextRegion :func ()Segmenter {return &TextRegion {}},TPatternDictionary :func ()Segmenter {return &PatternDictionary {}},TIntermediateHalftoneRegion :func ()Segmenter {return &HalftoneRegion {}},TImmediateHalftoneRegion :func ()Segmenter {return &HalftoneRegion {}},TImmediateLosslessHalftoneRegion :func ()Segmenter {return &HalftoneRegion {}},TIntermediateGenericRegion :func ()Segmenter {return &GenericRegion {}},TImmediateGenericRegion :func ()Segmenter {return &GenericRegion {}},TImmediateLosslessGenericRegion :func ()Segmenter {return &GenericRegion {}},TIntermediateGenericRefinementRegion :func ()Segmenter {return &GenericRefinementRegion {}},TImmediateGenericRefinementRegion :func ()Segmenter {return &GenericRefinementRegion {}},TImmediateLosslessGenericRefinementRegion :func ()Segmenter {return &GenericRefinementRegion {}},TPageInformation :func ()Segmenter {return &PageInformationSegment {}},TEndOfPage :func ()Segmenter {return _cfdcb },TEndOfStrip :func ()Segmenter {return &EndOfStripe {}},TEndOfFile :func ()Segmenter {return _cfdcb },TProfiles :func ()Segmenter {return _cfdcb },TTables :func ()Segmenter {return &TableSegment {}},TExtension :func ()Segmenter {return _cfdcb },TBitmap :func ()Segmenter {return _cfdcb }};
);func (_bcef *TextRegion )parseHeader ()error {var _afead error ;_ge .Log .Trace ("\u005b\u0054E\u0058\u0054\u0020\u0052E\u0047\u0049O\u004e\u005d\u005b\u0050\u0041\u0052\u0053\u0045-\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0062\u0065\u0067\u0069n\u0073\u002e\u002e\u002e");
defer func (){if _afead !=nil {_ge .Log .Trace ("\u005b\u0054\u0045\u0058\u0054\u0020\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u005b\u0050\u0041\u0052\u0053\u0045\u002d\u0048\u0045\u0041\u0044E\u0052\u005d\u0020\u0066\u0061i\u006c\u0065d\u002e\u0020\u0025\u0076",_afead );
}else {_ge .Log .Trace ("\u005b\u0054E\u0058\u0054\u0020\u0052E\u0047\u0049O\u004e\u005d\u005b\u0050\u0041\u0052\u0053\u0045-\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0066\u0069\u006e\u0069s\u0068\u0065\u0064\u002e");};}();if _afead =_bcef .RegionInfo .parseHeader ();
_afead !=nil {return _afead ;};if _afead =_bcef .readRegionFlags ();_afead !=nil {return _afead ;};if _bcef .IsHuffmanEncoded {if _afead =_bcef .readHuffmanFlags ();_afead !=nil {return _afead ;};};if _afead =_bcef .readUseRefinement ();_afead !=nil {return _afead ;
};if _afead =_bcef .readAmountOfSymbolInstances ();_afead !=nil {return _afead ;};if _afead =_bcef .getSymbols ();_afead !=nil {return _afead ;};if _afead =_bcef .computeSymbolCodeLength ();_afead !=nil {return _afead ;};if _afead =_bcef .checkInput ();
_afead !=nil {return _afead ;};_ge .Log .Trace ("\u0025\u0073",_bcef .String ());return nil ;};func (_cbfd *HalftoneRegion )Init (hd *Header ,r *_ed .Reader )error {_cbfd ._acfg =r ;_cbfd ._fcee =hd ;_cbfd .RegionSegment =NewRegionSegment (r );return _cbfd .parseHeader ();
};func (_fbcd *PageInformationSegment )readCombinationOperator ()error {_bfbf ,_acab :=_fbcd ._dcaf .ReadBits (2);if _acab !=nil {return _acab ;};_fbcd ._bae =_gf .CombinationOperator (int (_bfbf ));return nil ;};func (_ebac *SymbolDictionary )checkInput ()error {if _ebac .SdHuffDecodeHeightSelection ==2{_ge .Log .Debug ("\u0053\u0079\u006d\u0062\u006fl\u0020\u0044\u0069\u0063\u0074i\u006fn\u0061\u0072\u0079\u0020\u0044\u0065\u0063\u006f\u0064\u0065\u0020\u0048\u0065\u0069\u0067\u0068\u0074\u0020\u0053e\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0064\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006e\u006f\u0074\u0020\u0070\u0065r\u006d\u0069\u0074\u0074\u0065\u0064",_ebac .SdHuffDecodeHeightSelection );
};if _ebac .SdHuffDecodeWidthSelection ==2{_ge .Log .Debug ("\u0053\u0079\u006d\u0062\u006f\u006c\u0020\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0044\u0065\u0063\u006f\u0064\u0065\u0020\u0057\u0069\u0064t\u0068\u0020\u0053\u0065\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u003a\u0020\u0025\u0064\u0020\u0076\u0061l\u0075\u0065\u0020\u006e\u006f\u0074 \u0070\u0065r\u006d\u0069t\u0074e\u0064",_ebac .SdHuffDecodeWidthSelection );
};if _ebac .IsHuffmanEncoded {if _ebac .SdTemplate !=0{_ge .Log .Debug ("\u0053\u0044T\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0020\u003d\u0020\u0025\u0064\u0020\u0028\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062e \u0030\u0029",_ebac .SdTemplate );
};if !_ebac .UseRefinementAggregation {if !_ebac .UseRefinementAggregation {if _ebac ._bbfd {_ge .Log .Debug ("\u0049\u0073\u0043\u006f\u0064\u0069\u006e\u0067C\u006f\u006e\u0074ex\u0074\u0052\u0065\u0074\u0061\u0069n\u0065\u0064\u0020\u003d\u0020\u0074\u0072\u0075\u0065\u0020\u0028\u0073\u0068\u006f\u0075l\u0064\u0020\u0062\u0065\u0020\u0066\u0061\u006cs\u0065\u0029");
_ebac ._bbfd =false ;};if _ebac ._ebfg {_ge .Log .Debug ("\u0069s\u0043\u006fd\u0069\u006e\u0067\u0043o\u006e\u0074\u0065x\u0074\u0055\u0073\u0065\u0064\u0020\u003d\u0020\u0074ru\u0065\u0020\u0028s\u0068\u006fu\u006c\u0064\u0020\u0062\u0065\u0020f\u0061\u006cs\u0065\u0029");
_ebac ._ebfg =false ;};};};}else {if _ebac .SdHuffBMSizeSelection !=0{_ge .Log .Debug ("\u0053\u0064\u0048\u0075\u0066\u0066B\u004d\u0053\u0069\u007a\u0065\u0053\u0065\u006c\u0065\u0063\u0074\u0069\u006fn\u0020\u0073\u0068\u006f\u0075\u006c\u0064 \u0062\u0065\u0020\u0030");
_ebac .SdHuffBMSizeSelection =0;};if _ebac .SdHuffDecodeWidthSelection !=0{_ge .Log .Debug ("\u0053\u0064\u0048\u0075\u0066\u0066\u0044\u0065\u0063\u006f\u0064\u0065\u0057\u0069\u0064\u0074\u0068\u0053\u0065\u006c\u0065\u0063\u0074\u0069o\u006e\u0020\u0073\u0068\u006fu\u006c\u0064 \u0062\u0065\u0020\u0030");
_ebac .SdHuffDecodeWidthSelection =0;};if _ebac .SdHuffDecodeHeightSelection !=0{_ge .Log .Debug ("\u0053\u0064\u0048\u0075\u0066\u0066\u0044\u0065\u0063\u006f\u0064\u0065\u0048e\u0069\u0067\u0068\u0074\u0053\u0065l\u0065\u0063\u0074\u0069\u006f\u006e\u0020\u0073\u0068\u006f\u0075\u006c\u0064 \u0062\u0065\u0020\u0030");
_ebac .SdHuffDecodeHeightSelection =0;};};if !_ebac .UseRefinementAggregation {if _ebac .SdrTemplate !=0{_ge .Log .Debug ("\u0053\u0044\u0052\u0054\u0065\u006d\u0070\u006c\u0061\u0074e\u0020\u003d\u0020\u0025\u0064\u0020\u0028s\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0030\u0029",_ebac .SdrTemplate );
_ebac .SdrTemplate =0;};};if !_ebac .IsHuffmanEncoded ||!_ebac .UseRefinementAggregation {if _ebac .SdHuffAggInstanceSelection {_ge .Log .Debug ("\u0053d\u0048\u0075f\u0066\u0041\u0067g\u0049\u006e\u0073\u0074\u0061\u006e\u0063e\u0053\u0065\u006c\u0065\u0063\u0074i\u006f\u006e\u0020\u003d\u0020\u0025\u0064\u0020\u0028\u0073\u0068o\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0030\u0029",_ebac .SdHuffAggInstanceSelection );
};};return nil ;};func (_gffca *Header )writeSegmentPageAssociation (_dfg _ed .BinaryWriter )(_fdcb int ,_aadd error ){const _cbcf ="w\u0072\u0069\u0074\u0065\u0053\u0065g\u006d\u0065\u006e\u0074\u0050\u0061\u0067\u0065\u0041s\u0073\u006f\u0063i\u0061t\u0069\u006f\u006e";
if _gffca .pageSize ()!=4{if _aadd =_dfg .WriteByte (byte (_gffca .PageAssociation ));_aadd !=nil {return 0,_ff .Wrap (_aadd ,_cbcf ,"\u0070\u0061\u0067\u0065\u0053\u0069\u007a\u0065\u0020\u0021\u003d\u0020\u0034");};return 1,nil ;};_dgcfe :=make ([]byte ,4);
_dg .BigEndian .PutUint32 (_dgcfe ,uint32 (_gffca .PageAssociation ));if _fdcb ,_aadd =_dfg .Write (_dgcfe );_aadd !=nil {return 0,_ff .Wrap (_aadd ,_cbcf ,"\u0034 \u0062y\u0074\u0065\u0020\u0070\u0061g\u0065\u0020n\u0075\u006d\u0062\u0065\u0072");};return _fdcb ,nil ;
};func (_bbca *SymbolDictionary )huffDecodeRefAggNInst ()(int64 ,error ){if !_bbca .SdHuffAggInstanceSelection {_fgbab ,_gfea :=_aee .GetStandardTable (1);if _gfea !=nil {return 0,_gfea ;};return _fgbab .Decode (_bbca ._gaeb );};if _bbca ._bda ==nil {var (_gcee int ;
_egafe error ;);if _bbca .SdHuffDecodeHeightSelection ==3{_gcee ++;};if _bbca .SdHuffDecodeWidthSelection ==3{_gcee ++;};if _bbca .SdHuffBMSizeSelection ==3{_gcee ++;};_bbca ._bda ,_egafe =_bbca .getUserTable (_gcee );if _egafe !=nil {return 0,_egafe ;
};};return _bbca ._bda .Decode (_bbca ._gaeb );};func (_fgba *Header )referenceSize ()uint {switch {case _fgba .SegmentNumber <=255:return 1;case _fgba .SegmentNumber <=65535:return 2;default:return 4;};};func (_eaea *SymbolDictionary )readRefinementAtPixels (_gcfg int )error {_eaea .SdrATX =make ([]int8 ,_gcfg );
_eaea .SdrATY =make ([]int8 ,_gcfg );var (_agga byte ;_eccf error ;);for _efee :=0;_efee < _gcfg ;_efee ++{_agga ,_eccf =_eaea ._gaeb .ReadByte ();if _eccf !=nil {return _eccf ;};_eaea .SdrATX [_efee ]=int8 (_agga );_agga ,_eccf =_eaea ._gaeb .ReadByte ();
if _eccf !=nil {return _eccf ;};_eaea .SdrATY [_efee ]=int8 (_agga );};return nil ;};func (_efec *TextRegion )Encode (w _ed .BinaryWriter )(_efcg int ,_aaba error ){const _bbb ="\u0054\u0065\u0078\u0074\u0052\u0065\u0067\u0069\u006f\u006e\u002e\u0045n\u0063\u006f\u0064\u0065";
if _efcg ,_aaba =_efec .RegionInfo .Encode (w );_aaba !=nil {return _efcg ,_ff .Wrap (_aaba ,_bbb ,"");};var _dffge int ;if _dffge ,_aaba =_efec .encodeFlags (w );_aaba !=nil {return _efcg ,_ff .Wrap (_aaba ,_bbb ,"");};_efcg +=_dffge ;if _dffge ,_aaba =_efec .encodeSymbols (w );
_aaba !=nil {return _efcg ,_ff .Wrap (_aaba ,_bbb ,"");};_efcg +=_dffge ;return _efcg ,nil ;};func (_ebead *PageInformationSegment )readContainsRefinement ()error {_fab ,_ceegb :=_ebead ._dcaf .ReadBit ();if _ceegb !=nil {return _ceegb ;};if _fab ==1{_ebead ._afcf =true ;
};return nil ;};func (_egea *PageInformationSegment )CombinationOperatorOverrideAllowed ()bool {return _egea ._fgeb };func (_ebg *Header )parse (_bdbe Documenter ,_eecb *_ed .Reader ,_ggbb int64 ,_dadc OrganizationType )(_ggc error ){const _eagg ="\u0070\u0061\u0072s\u0065";
_ge .Log .Trace ("\u005b\u0053\u0045\u0047\u004d\u0045\u004e\u0054\u002d\u0048E\u0041\u0044\u0045\u0052\u005d\u005b\u0050A\u0052\u0053\u0045\u005d\u0020\u0042\u0065\u0067\u0069\u006e\u0073");defer func (){if _ggc !=nil {_ge .Log .Trace ("\u005b\u0053\u0045GM\u0045\u004e\u0054\u002d\u0048\u0045\u0041\u0044\u0045R\u005d[\u0050A\u0052S\u0045\u005d\u0020\u0046\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0076",_ggc );
}else {_ge .Log .Trace ("\u005b\u0053\u0045\u0047\u004d\u0045\u004e\u0054\u002d\u0048\u0045\u0041\u0044\u0045\u0052]\u005bP\u0041\u0052\u0053\u0045\u005d\u0020\u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064");};}();_ ,_ggc =_eecb .Seek (_ggbb ,_d .SeekStart );
if _ggc !=nil {return _ff .Wrap (_ggc ,_eagg ,"\u0073\u0065\u0065\u006b\u0020\u0073\u0074\u0061\u0072\u0074");};if _ggc =_ebg .readSegmentNumber (_eecb );_ggc !=nil {return _ff .Wrap (_ggc ,_eagg ,"");};if _ggc =_ebg .readHeaderFlags ();_ggc !=nil {return _ff .Wrap (_ggc ,_eagg ,"");
};var _efdb uint64 ;_efdb ,_ggc =_ebg .readNumberOfReferredToSegments (_eecb );if _ggc !=nil {return _ff .Wrap (_ggc ,_eagg ,"");};_ebg .RTSNumbers ,_ggc =_ebg .readReferredToSegmentNumbers (_eecb ,int (_efdb ));if _ggc !=nil {return _ff .Wrap (_ggc ,_eagg ,"");
};_ggc =_ebg .readSegmentPageAssociation (_bdbe ,_eecb ,_efdb ,_ebg .RTSNumbers ...);if _ggc !=nil {return _ff .Wrap (_ggc ,_eagg ,"");};if _ebg .Type !=TEndOfFile {if _ggc =_ebg .readSegmentDataLength (_eecb );_ggc !=nil {return _ff .Wrap (_ggc ,_eagg ,"");
};};_ebg .readDataStartOffset (_eecb ,_dadc );_ebg .readHeaderLength (_eecb ,_ggbb );_ge .Log .Trace ("\u0025\u0073",_ebg );return nil ;};func (_caae *SymbolDictionary )readNumberOfExportedSymbols ()error {_fbdcc ,_dfgb :=_caae ._gaeb .ReadBits (32);if _dfgb !=nil {return _dfgb ;
};_caae .NumberOfExportedSymbols =uint32 (_fbdcc &_g .MaxUint32 );return nil ;};func (_acfd *Header )readHeaderFlags ()error {const _ebaf ="\u0072e\u0061d\u0048\u0065\u0061\u0064\u0065\u0072\u0046\u006c\u0061\u0067\u0073";_bcc ,_gbf :=_acfd .Reader .ReadBit ();
if _gbf !=nil {return _ff .Wrap (_gbf ,_ebaf ,"r\u0065\u0074\u0061\u0069\u006e\u0020\u0066\u006c\u0061\u0067");};if _bcc !=0{_acfd .RetainFlag =true ;};_bcc ,_gbf =_acfd .Reader .ReadBit ();if _gbf !=nil {return _ff .Wrap (_gbf ,_ebaf ,"\u0070\u0061g\u0065\u0020\u0061s\u0073\u006f\u0063\u0069\u0061\u0074\u0069\u006f\u006e");
};if _bcc !=0{_acfd .PageAssociationFieldSize =true ;};_badd ,_gbf :=_acfd .Reader .ReadBits (6);if _gbf !=nil {return _ff .Wrap (_gbf ,_ebaf ,"\u0073\u0065\u0067m\u0065\u006e\u0074\u0020\u0074\u0079\u0070\u0065");};_acfd .Type =Type (int (_badd ));return nil ;
};func (_efgf *TextRegion )decodeDT ()(_agde int64 ,_cfeeg error ){if _efgf .IsHuffmanEncoded {if _efgf .SbHuffDT ==3{_agde ,_cfeeg =_efgf ._fefg .Decode (_efgf ._cgbc );if _cfeeg !=nil {return 0,_cfeeg ;};}else {var _dfcc _aee .Tabler ;_dfcc ,_cfeeg =_aee .GetStandardTable (11+int (_efgf .SbHuffDT ));
if _cfeeg !=nil {return 0,_cfeeg ;};_agde ,_cfeeg =_dfcc .Decode (_efgf ._cgbc );if _cfeeg !=nil {return 0,_cfeeg ;};};}else {var _abbba int32 ;_abbba ,_cfeeg =_efgf ._afef .DecodeInt (_efgf ._caad );if _cfeeg !=nil {return 0,_cfeeg ;};_agde =int64 (_abbba );
};_agde *=int64 (_efgf .SbStrips );return _agde ,nil ;};func NewRegionSegment (r *_ed .Reader )*RegionSegment {return &RegionSegment {_egdda :r }};func (_cdac *TextRegion )decodeRI ()(int64 ,error ){if !_cdac .UseRefinement {return 0,nil ;};if _cdac .IsHuffmanEncoded {_cbcc ,_dabg :=_cdac ._cgbc .ReadBit ();
return int64 (_cbcc ),_dabg ;};_agdd ,_caag :=_cdac ._afef .DecodeInt (_cdac ._cfce );return int64 (_agdd ),_caag ;};func (_affc *GenericRegion )decodeTemplate3 (_efad ,_aaaa ,_bfb int ,_dfba ,_ffeg int )(_fcg error ){const _bfab ="\u0064e\u0063o\u0064\u0065\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0033";
var (_afff ,_bag int ;_fdgf int ;_ggg byte ;_fbbe ,_edac int ;);if _efad >=1{_ggg ,_fcg =_affc .Bitmap .GetByte (_ffeg );if _fcg !=nil {return _ff .Wrap (_fcg ,_bfab ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00201");};_fdgf =int (_ggg );};_afff =(_fdgf >>1)&0x70;
for _cgaf :=0;_cgaf < _bfb ;_cgaf =_fbbe {var (_ebd byte ;_gbcc int ;);_fbbe =_cgaf +8;if _dcef :=_aaaa -_cgaf ;_dcef > 8{_gbcc =8;}else {_gbcc =_dcef ;};if _efad >=1{_fdgf <<=8;if _fbbe < _aaaa {_ggg ,_fcg =_affc .Bitmap .GetByte (_ffeg +1);if _fcg !=nil {return _ff .Wrap (_fcg ,_bfab ,"\u0069\u006e\u006e\u0065\u0072\u0020\u002d\u0020\u006c\u0069\u006e\u0065 \u003e\u003d\u0020\u0031");
};_fdgf |=int (_ggg );};};for _ccf :=0;_ccf < _gbcc ;_ccf ++{if _affc ._eeg {_bag =_affc .overrideAtTemplate3 (_afff ,_cgaf +_ccf ,_efad ,int (_ebd ),_ccf );_affc ._cfb .SetIndex (int32 (_bag ));}else {_affc ._cfb .SetIndex (int32 (_afff ));};_edac ,_fcg =_affc ._bde .DecodeBit (_affc ._cfb );
if _fcg !=nil {return _ff .Wrap (_fcg ,_bfab ,"");};_ebd |=byte (_edac )<<byte (7-_ccf );_afff =((_afff &0x1f7)<<1)|_edac |((_fdgf >>uint (8-_ccf ))&0x010);};if _fgcb :=_affc .Bitmap .SetByte (_dfba ,_ebd );_fgcb !=nil {return _ff .Wrap (_fgcb ,_bfab ,"");
};_dfba ++;_ffeg ++;};return nil ;};func (_egb *GenericRegion )setParametersMMR (_ceag bool ,_cdfg ,_dggg int64 ,_gcfc ,_gfcc uint32 ,_fgb byte ,_bgc ,_eeafd bool ,_edea ,_ded []int8 ){_egb .DataOffset =_cdfg ;_egb .DataLength =_dggg ;_egb .RegionSegment =&RegionSegment {};
_egb .RegionSegment .BitmapHeight =_gcfc ;_egb .RegionSegment .BitmapWidth =_gfcc ;_egb .GBTemplate =_fgb ;_egb .IsMMREncoded =_ceag ;_egb .IsTPGDon =_bgc ;_egb .GBAtX =_edea ;_egb .GBAtY =_ded ;};type Pager interface{GetSegment (int )(*Header ,error );
GetBitmap ()(*_gf .Bitmap ,error );};func (_bebb *SymbolDictionary )decodeNewSymbols (_dffef ,_dgcb uint32 ,_dbef *_gf .Bitmap ,_adac ,_befc int32 )error {if _bebb ._gcac ==nil {_bebb ._gcac =_bgg (_bebb ._gaeb ,nil );if _bebb ._daed ==nil {var _dgbbe error ;
_bebb ._daed ,_dgbbe =_ae .New (_bebb ._gaeb );if _dgbbe !=nil {return _dgbbe ;};};if _bebb ._bgbe ==nil {_bebb ._bgbe =_ae .NewStats (65536,1);};};_bebb ._gcac .setParameters (_bebb ._bgbe ,_bebb ._daed ,_bebb .SdrTemplate ,_dffef ,_dgcb ,_dbef ,_adac ,_befc ,false ,_bebb .SdrATX ,_bebb .SdrATY );
return _bebb .addSymbol (_bebb ._gcac );};func (_afe *PatternDictionary )readGrayMax ()error {_cccc ,_dcbc :=_afe ._dface .ReadBits (32);if _dcbc !=nil {return _dcbc ;};_afe .GrayMax =uint32 (_cccc &_g .MaxUint32 );return nil ;};func (_cfgge *TextRegion )setCodingStatistics ()error {if _cfgge ._caad ==nil {_cfgge ._caad =_ae .NewStats (512,1);
};if _cfgge ._cagd ==nil {_cfgge ._cagd =_ae .NewStats (512,1);};if _cfgge ._aead ==nil {_cfgge ._aead =_ae .NewStats (512,1);};if _cfgge ._gggeg ==nil {_cfgge ._gggeg =_ae .NewStats (512,1);};if _cfgge ._cfce ==nil {_cfgge ._cfce =_ae .NewStats (512,1);
};if _cfgge ._afcb ==nil {_cfgge ._afcb =_ae .NewStats (512,1);};if _cfgge ._abcgd ==nil {_cfgge ._abcgd =_ae .NewStats (512,1);};if _cfgge ._aecf ==nil {_cfgge ._aecf =_ae .NewStats (1<<uint (_cfgge ._eebg ),1);};if _cfgge ._cbfa ==nil {_cfgge ._cbfa =_ae .NewStats (512,1);
};if _cfgge ._fbeb ==nil {_cfgge ._fbeb =_ae .NewStats (512,1);};if _cfgge ._afef ==nil {var _gadf error ;_cfgge ._afef ,_gadf =_ae .New (_cfgge ._cgbc );if _gadf !=nil {return _gadf ;};};return nil ;};type PatternDictionary struct{_dface *_ed .Reader ;
DataHeaderOffset int64 ;DataHeaderLength int64 ;DataOffset int64 ;DataLength int64 ;GBAtX []int8 ;GBAtY []int8 ;IsMMREncoded bool ;HDTemplate byte ;HdpWidth byte ;HdpHeight byte ;Patterns []*_gf .Bitmap ;GrayMax uint32 ;};func (_fggg *PatternDictionary )checkInput ()error {if _fggg .HdpHeight < 1||_fggg .HdpWidth < 1{return _gb .New ("in\u0076\u0061l\u0069\u0064\u0020\u0048\u0065\u0061\u0064\u0065\u0072 \u0056\u0061\u006c\u0075\u0065\u003a\u0020\u0057\u0069\u0064\u0074\u0068\u002f\u0048\u0065\u0069\u0067\u0068\u0074\u0020\u006d\u0075\u0073\u0074\u0020\u0062\u0065\u0020g\u0072e\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061n\u0020z\u0065\u0072o");
};if _fggg .IsMMREncoded {if _fggg .HDTemplate !=0{_ge .Log .Debug ("\u0076\u0061\u0072\u0069\u0061\u0062\u006c\u0065\u0020\u0048\u0044\u0054\u0065\u006d\u0070\u006c\u0061\u0074e\u0020\u0073\u0068\u006f\u0075\u006c\u0064 \u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0030");
};};return nil ;};func (_ddg *Header )subInputReader ()(*_ed .Reader ,error ){_ddfg :=int (_ddg .SegmentDataLength );if _ddg .SegmentDataLength ==_g .MaxInt32 {_ddfg =-1;};return _ddg .Reader .NewPartialReader (int (_ddg .SegmentDataStartOffset ),_ddfg ,false );
};func (_acg *HalftoneRegion )computeY (_cfdc ,_dcbg int )int {return _acg .shiftAndFill (int (_acg .HGridY )+_cfdc *int (_acg .HRegionX )-_dcbg *int (_acg .HRegionY ));};func (_dbgf *GenericRefinementRegion )updateOverride ()error {if _dbgf .GrAtX ==nil ||_dbgf .GrAtY ==nil {return _gb .New ("\u0041\u0054\u0020\u0070\u0069\u0078\u0065\u006c\u0073\u0020\u006e\u006ft\u0020\u0073\u0065\u0074");
};if len (_dbgf .GrAtX )!=len (_dbgf .GrAtY ){return _gb .New ("A\u0054\u0020\u0070\u0069xe\u006c \u0069\u006e\u0063\u006f\u006es\u0069\u0073\u0074\u0065\u006e\u0074");};_dbgf ._ee =make ([]bool ,len (_dbgf .GrAtX ));switch _dbgf .TemplateID {case 0:if _dbgf .GrAtX [0]!=-1&&_dbgf .GrAtY [0]!=-1{_dbgf ._ee [0]=true ;
_dbgf ._fe =true ;};if _dbgf .GrAtX [1]!=-1&&_dbgf .GrAtY [1]!=-1{_dbgf ._ee [1]=true ;_dbgf ._fe =true ;};case 1:_dbgf ._fe =false ;};return nil ;};func (_bff *Header )GetSegmentData ()(Segmenter ,error ){var _fdbe Segmenter ;if _bff .SegmentData !=nil {_fdbe =_bff .SegmentData ;
};if _fdbe ==nil {_fbdd ,_bad :=_eecbc [_bff .Type ];if !_bad {return nil ,_e .Errorf ("\u0074\u0079\u0070\u0065\u003a\u0020\u0025\u0073\u002f\u0020\u0025\u0064\u0020\u0063\u0072e\u0061t\u006f\u0072\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e\u0020",_bff .Type ,_bff .Type );
};_fdbe =_fbdd ();_ge .Log .Trace ("\u005b\u0053E\u0047\u004d\u0045\u004e\u0054-\u0048\u0045\u0041\u0044\u0045R\u005d\u005b\u0023\u0025\u0064\u005d\u0020\u0047\u0065\u0074\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0044\u0061\u0074\u0061\u0020\u0061\u0074\u0020\u004f\u0066\u0066\u0073\u0065\u0074\u003a\u0020\u0025\u0030\u0034\u0058",_bff .SegmentNumber ,_bff .SegmentDataStartOffset );
_efdf ,_cbcb :=_bff .subInputReader ();if _cbcb !=nil {return nil ,_cbcb ;};if _gcg :=_fdbe .Init (_bff ,_efdf );_gcg !=nil {_ge .Log .Debug ("\u0049\u006e\u0069\u0074 \u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076 \u0066o\u0072\u0020\u0074\u0079\u0070\u0065\u003a \u0025\u0054",_gcg ,_fdbe );
return nil ,_gcg ;};_bff .SegmentData =_fdbe ;};return _fdbe ,nil ;};func (_cd *GenericRefinementRegion )decodeSLTP ()(int ,error ){_cd .Template .setIndex (_cd ._ebb );return _cd ._ce .DecodeBit (_cd ._ebb );};func _cefc (_cabf int )int {if _cabf ==0{return 0;
};_cabf |=_cabf >>1;_cabf |=_cabf >>2;_cabf |=_cabf >>4;_cabf |=_cabf >>8;_cabf |=_cabf >>16;return (_cabf +1)>>1;};func (_gcag *TextRegion )symbolIDCodeLengths ()error {var (_ecfcg []*_aee .Code ;_gdefc uint64 ;_cgbe _aee .Tabler ;_fde error ;);for _cabaf :=0;
_cabaf < 35;_cabaf ++{_gdefc ,_fde =_gcag ._cgbc .ReadBits (4);if _fde !=nil {return _fde ;};_dbee :=int (_gdefc &0xf);if _dbee > 0{_ecfcg =append (_ecfcg ,_aee .NewCode (int32 (_dbee ),0,int32 (_cabaf ),false ));};};_cgbe ,_fde =_aee .NewFixedSizeTable (_ecfcg );
if _fde !=nil {return _fde ;};var (_feda int64 ;_fcbb uint32 ;_acfca []*_aee .Code ;_aabb int64 ;);for _fcbb < _gcag .NumberOfSymbols {_aabb ,_fde =_cgbe .Decode (_gcag ._cgbc );if _fde !=nil {return _fde ;};if _aabb < 32{if _aabb > 0{_acfca =append (_acfca ,_aee .NewCode (int32 (_aabb ),0,int32 (_fcbb ),false ));
};_feda =_aabb ;_fcbb ++;}else {var _dbae ,_adfb int64 ;switch _aabb {case 32:_gdefc ,_fde =_gcag ._cgbc .ReadBits (2);if _fde !=nil {return _fde ;};_dbae =3+int64 (_gdefc );if _fcbb > 0{_adfb =_feda ;};case 33:_gdefc ,_fde =_gcag ._cgbc .ReadBits (3);
if _fde !=nil {return _fde ;};_dbae =3+int64 (_gdefc );case 34:_gdefc ,_fde =_gcag ._cgbc .ReadBits (7);if _fde !=nil {return _fde ;};_dbae =11+int64 (_gdefc );};for _beed :=0;_beed < int (_dbae );_beed ++{if _adfb > 0{_acfca =append (_acfca ,_aee .NewCode (int32 (_adfb ),0,int32 (_fcbb ),false ));
};_fcbb ++;};};};_gcag ._cgbc .Align ();_gcag ._geggd ,_fde =_aee .NewFixedSizeTable (_acfca );return _fde ;};func (_aea *EndOfStripe )Init (h *Header ,r *_ed .Reader )error {_aea ._dgd =r ;return _aea .parseHeader ();};func (_gaa *GenericRefinementRegion )GetRegionInfo ()*RegionSegment {return _gaa .RegionInfo };
func (_cgce *SymbolDictionary )String ()string {_bbcg :=&_a .Builder {};_bbcg .WriteString ("\n\u005b\u0053\u0059\u004dBO\u004c-\u0044\u0049\u0043\u0054\u0049O\u004e\u0041\u0052\u0059\u005d\u000a");_bbcg .WriteString (_e .Sprintf ("\u0009-\u0020S\u0064\u0072\u0054\u0065\u006dp\u006c\u0061t\u0065\u0020\u0025\u0076\u000a",_cgce .SdrTemplate ));
_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0054\u0065\u006d\u0070\u006c\u0061\u0074e\u0020\u0025\u0076\u000a",_cgce .SdTemplate ));_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0069\u0073\u0043\u006f\u0064\u0069\u006eg\u0043\u006f\u006e\u0074\u0065\u0078\u0074R\u0065\u0074\u0061\u0069\u006e\u0065\u0064\u0020\u0025\u0076\u000a",_cgce ._bbfd ));
_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0069\u0073\u0043\u006f\u0064\u0069\u006e\u0067C\u006f\u006e\u0074\u0065\u0078\u0074\u0055\u0073\u0065\u0064 \u0025\u0076\u000a",_cgce ._ebfg ));_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0048u\u0066\u0066\u0041\u0067\u0067\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065S\u0065\u006c\u0065\u0063\u0074\u0069\u006fn\u0020\u0025\u0076\u000a",_cgce .SdHuffAggInstanceSelection ));
_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053d\u0048\u0075\u0066\u0066\u0042\u004d\u0053\u0069\u007a\u0065S\u0065l\u0065\u0063\u0074\u0069\u006f\u006e\u0020%\u0076\u000a",_cgce .SdHuffBMSizeSelection ));_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0048u\u0066\u0066\u0044\u0065\u0063\u006f\u0064\u0065\u0057\u0069\u0064\u0074\u0068S\u0065\u006c\u0065\u0063\u0074\u0069\u006fn\u0020\u0025\u0076\u000a",_cgce .SdHuffDecodeWidthSelection ));
_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020Sd\u0048\u0075\u0066\u0066\u0044\u0065\u0063\u006f\u0064e\u0048e\u0069g\u0068t\u0053\u0065\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u0020\u0025\u0076\u000a",_cgce .SdHuffDecodeHeightSelection ));_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020U\u0073\u0065\u0052\u0065f\u0069\u006e\u0065\u006d\u0065\u006e\u0074A\u0067\u0067\u0072\u0065\u0067\u0061\u0074\u0069\u006f\u006e\u0020\u0025\u0076\u000a",_cgce .UseRefinementAggregation ));
_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020is\u0048\u0075\u0066\u0066\u006d\u0061\u006e\u0045\u006e\u0063\u006f\u0064\u0065\u0064\u0020\u0025\u0076\u000a",_cgce .IsHuffmanEncoded ));_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020S\u0064\u0041\u0054\u0058\u0020\u0025\u0076\u000a",_cgce .SdATX ));
_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020S\u0064\u0041\u0054\u0059\u0020\u0025\u0076\u000a",_cgce .SdATY ));_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0072\u0041\u0054\u0058\u0020\u0025\u0076\u000a",_cgce .SdrATX ));_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0064\u0072\u0041\u0054\u0059\u0020\u0025\u0076\u000a",_cgce .SdrATY ));
_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u004e\u0075\u006d\u0062\u0065\u0072\u004ff\u0045\u0078\u0070\u006f\u0072\u0074\u0065d\u0053\u0079\u006d\u0062\u006f\u006c\u0073\u0020\u0025\u0076\u000a",_cgce .NumberOfExportedSymbols ));_bbcg .WriteString (_e .Sprintf ("\u0009-\u0020\u004e\u0075\u006db\u0065\u0072\u004f\u0066\u004ee\u0077S\u0079m\u0062\u006f\u006c\u0073\u0020\u0025\u0076\n",_cgce .NumberOfNewSymbols ));
_bbcg .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u004ff\u0049\u006d\u0070\u006f\u0072\u0074\u0065d\u0053\u0079\u006d\u0062\u006f\u006c\u0073\u0020\u0025\u0076\u000a",_cgce ._cdeb ));_bbcg .WriteString (_e .Sprintf ("\u0009\u002d \u006e\u0075\u006d\u0062\u0065\u0072\u004f\u0066\u0044\u0065\u0063\u006f\u0064\u0065\u0064\u0053\u0079\u006d\u0062\u006f\u006c\u0073 %\u0076\u000a",_cgce ._dcbgd ));
return _bbcg .String ();};func (_bfg *Header )readNumberOfReferredToSegments (_caca *_ed .Reader )(uint64 ,error ){const _gfgg ="\u0072\u0065\u0061\u0064\u004e\u0075\u006d\u0062\u0065\u0072O\u0066\u0052\u0065\u0066\u0065\u0072\u0072e\u0064\u0054\u006f\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0073";
_ece ,_aad :=_caca .ReadBits (3);if _aad !=nil {return 0,_ff .Wrap (_aad ,_gfgg ,"\u0063\u006f\u0075n\u0074\u0020\u006f\u0066\u0020\u0072\u0074\u0073");};_ece &=0xf;var _bdgcdf []byte ;if _ece <=4{_bdgcdf =make ([]byte ,5);for _ecge :=0;_ecge <=4;_ecge ++{_acdf ,_age :=_caca .ReadBit ();
if _age !=nil {return 0,_ff .Wrap (_age ,_gfgg ,"\u0073\u0068\u006fr\u0074\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_bdgcdf [_ecge ]=byte (_acdf );};}else {_ece ,_aad =_caca .ReadBits (29);if _aad !=nil {return 0,_aad ;};_ece &=_g .MaxInt32 ;_dfaf :=(_ece +8)>>3;
_dfaf <<=3;_bdgcdf =make ([]byte ,_dfaf );var _geed uint64 ;for _geed =0;_geed < _dfaf ;_geed ++{_dacd ,_afgc :=_caca .ReadBit ();if _afgc !=nil {return 0,_ff .Wrap (_afgc ,_gfgg ,"l\u006f\u006e\u0067\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_bdgcdf [_geed ]=byte (_dacd );
};};return _ece ,nil ;};func (_cbe Type )String ()string {switch _cbe {case TSymbolDictionary :return "\u0053\u0079\u006d\u0062\u006f\u006c\u0020\u0044\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079";case TIntermediateTextRegion :return "\u0049n\u0074\u0065\u0072\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020T\u0065\u0078\u0074\u0020\u0052\u0065\u0067\u0069\u006f\u006e";
case TImmediateTextRegion :return "I\u006d\u006d\u0065\u0064ia\u0074e\u0020\u0054\u0065\u0078\u0074 \u0052\u0065\u0067\u0069\u006f\u006e";case TImmediateLosslessTextRegion :return "\u0049\u006d\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020L\u006f\u0073\u0073\u006c\u0065\u0073\u0073 \u0054\u0065\u0078\u0074\u0020\u0052\u0065\u0067\u0069\u006f\u006e";
case TPatternDictionary :return "\u0050a\u0074t\u0065\u0072\u006e\u0020\u0044i\u0063\u0074i\u006f\u006e\u0061\u0072\u0079";case TIntermediateHalftoneRegion :return "\u0049\u006e\u0074\u0065r\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020\u0048\u0061l\u0066t\u006f\u006e\u0065\u0020\u0052\u0065\u0067i\u006f\u006e";
case TImmediateHalftoneRegion :return "\u0049m\u006d\u0065\u0064\u0069a\u0074\u0065\u0020\u0048\u0061l\u0066t\u006fn\u0065\u0020\u0052\u0065\u0067\u0069\u006fn";case TImmediateLosslessHalftoneRegion :return "\u0049\u006d\u006ded\u0069\u0061\u0074\u0065\u0020\u004c\u006f\u0073\u0073l\u0065s\u0073 \u0048a\u006c\u0066\u0074\u006f\u006e\u0065\u0020\u0052\u0065\u0067\u0069\u006f\u006e";
case TIntermediateGenericRegion :return "I\u006e\u0074\u0065\u0072\u006d\u0065d\u0069\u0061\u0074\u0065\u0020\u0047\u0065\u006e\u0065r\u0069\u0063\u0020R\u0065g\u0069\u006f\u006e";case TImmediateGenericRegion :return "\u0049m\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020\u0047\u0065\u006ee\u0072\u0069\u0063\u0020\u0052\u0065\u0067\u0069\u006f\u006e";
case TImmediateLosslessGenericRegion :return "\u0049\u006d\u006d\u0065\u0064\u0069a\u0074\u0065\u0020\u004c\u006f\u0073\u0073\u006c\u0065\u0073\u0073\u0020\u0047e\u006e\u0065\u0072\u0069\u0063\u0020\u0052e\u0067\u0069\u006f\u006e";case TIntermediateGenericRefinementRegion :return "\u0049\u006e\u0074\u0065\u0072\u006d\u0065\u0064\u0069\u0061\u0074\u0065\u0020\u0047\u0065\u006e\u0065\u0072\u0069\u0063\u0020\u0052\u0065\u0066i\u006e\u0065\u006d\u0065\u006et\u0020\u0052e\u0067\u0069\u006f\u006e";
case TImmediateGenericRefinementRegion :return "I\u006d\u006d\u0065\u0064\u0069\u0061t\u0065\u0020\u0047\u0065\u006e\u0065r\u0069\u0063\u0020\u0052\u0065\u0066\u0069n\u0065\u006d\u0065\u006e\u0074\u0020\u0052\u0065\u0067\u0069o\u006e";case TImmediateLosslessGenericRefinementRegion :return "\u0049m\u006d\u0065d\u0069\u0061\u0074\u0065 \u004c\u006f\u0073s\u006c\u0065\u0073\u0073\u0020\u0047\u0065\u006e\u0065ri\u0063\u0020\u0052e\u0066\u0069n\u0065\u006d\u0065\u006e\u0074\u0020R\u0065\u0067i\u006f\u006e";
case TPageInformation :return "\u0050\u0061g\u0065\u0020\u0049n\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e";case TEndOfPage :return "E\u006e\u0064\u0020\u004f\u0066\u0020\u0050\u0061\u0067\u0065";case TEndOfStrip :return "\u0045\u006e\u0064 \u004f\u0066\u0020\u0053\u0074\u0072\u0069\u0070";
case TEndOfFile :return "E\u006e\u0064\u0020\u004f\u0066\u0020\u0046\u0069\u006c\u0065";case TProfiles :return "\u0050\u0072\u006f\u0066\u0069\u006c\u0065\u0073";case TTables :return "\u0054\u0061\u0062\u006c\u0065\u0073";case TExtension :return "\u0045x\u0074\u0065\u006e\u0073\u0069\u006fn";
case TBitmap :return "\u0042\u0069\u0074\u006d\u0061\u0070";};return "I\u006ev\u0061\u006c\u0069\u0064\u0020\u0053\u0065\u0067m\u0065\u006e\u0074\u0020Ki\u006e\u0064";};func (_eda *GenericRefinementRegion )getGrReference ()(*_gf .Bitmap ,error ){segments :=_eda ._ga .RTSegments ;
if len (segments )==0{return nil ,_gb .New ("\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0064\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u006e\u006f\u0074\u0020\u0065\u0078is\u0074\u0073");};_fg ,_cee :=segments [0].GetSegmentData ();
if _cee !=nil {return nil ,_cee ;};_dc ,_dgde :=_fg .(Regioner );if !_dgde {return nil ,_e .Errorf ("\u0072\u0065\u0066\u0065\u0072r\u0065\u0064\u0020\u0074\u006f\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074 \u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0052\u0065\u0067\u0069\u006f\u006e\u0065\u0072\u003a\u0020\u0025\u0054",_fg );
};return _dc .GetRegionBitmap ();};func (_fac *TableSegment )HtPS ()int32 {return _fac ._efgef };func (_dbff *GenericRefinementRegion )overrideAtTemplate0 (_edfd ,_ccee ,_geg ,_bdg ,_baff int )int {if _dbff ._ee [0]{_edfd &=0xfff7;if _dbff .GrAtY [0]==0&&int (_dbff .GrAtX [0])>=-_baff {_edfd |=(_bdg >>uint (7-(_baff +int (_dbff .GrAtX [0])))&0x1)<<3;
}else {_edfd |=_dbff .getPixel (_dbff .RegionBitmap ,_ccee +int (_dbff .GrAtX [0]),_geg +int (_dbff .GrAtY [0]))<<3;};};if _dbff ._ee [1]{_edfd &=0xefff;if _dbff .GrAtY [1]==0&&int (_dbff .GrAtX [1])>=-_baff {_edfd |=(_bdg >>uint (7-(_baff +int (_dbff .GrAtX [1])))&0x1)<<12;
}else {_edfd |=_dbff .getPixel (_dbff .ReferenceBitmap ,_ccee +int (_dbff .GrAtX [1]),_geg +int (_dbff .GrAtY [1]));};};return _edfd ;};func (_cda *GenericRegion )copyLineAbove (_acfc int )error {_ged :=_acfc *_cda .Bitmap .RowStride ;_gbg :=_ged -_cda .Bitmap .RowStride ;
for _aeaf :=0;_aeaf < _cda .Bitmap .RowStride ;_aeaf ++{_bbc ,_ceac :=_cda .Bitmap .GetByte (_gbg );if _ceac !=nil {return _ceac ;};_gbg ++;if _ceac =_cda .Bitmap .SetByte (_ged ,_bbc );_ceac !=nil {return _ceac ;};_ged ++;};return nil ;};func (_agdb *GenericRegion )decodeTemplate2 (_agba ,_gfb ,_dfac int ,_fbb ,_egdd int )(_addd error ){const _ceg ="\u0064e\u0063o\u0064\u0065\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0032";
var (_cgfd ,_dge int ;_ecf ,_dda int ;_ffbf byte ;_cff ,_ddab int ;);if _agba >=1{_ffbf ,_addd =_agdb .Bitmap .GetByte (_egdd );if _addd !=nil {return _ff .Wrap (_addd ,_ceg ,"\u006ci\u006ee\u004e\u0075\u006d\u0062\u0065\u0072\u0020\u003e\u003d\u0020\u0031");
};_ecf =int (_ffbf );};if _agba >=2{_ffbf ,_addd =_agdb .Bitmap .GetByte (_egdd -_agdb .Bitmap .RowStride );if _addd !=nil {return _ff .Wrap (_addd ,_ceg ,"\u006ci\u006ee\u004e\u0075\u006d\u0062\u0065\u0072\u0020\u003e\u003d\u0020\u0032");};_dda =int (_ffbf )<<4;
};_cgfd =(_ecf >>3&0x7c)|(_dda >>3&0x380);for _gad :=0;_gad < _dfac ;_gad =_cff {var (_afba byte ;_aag int ;);_cff =_gad +8;if _acaf :=_gfb -_gad ;_acaf > 8{_aag =8;}else {_aag =_acaf ;};if _agba > 0{_ecf <<=8;if _cff < _gfb {_ffbf ,_addd =_agdb .Bitmap .GetByte (_egdd +1);
if _addd !=nil {return _ff .Wrap (_addd ,_ceg ,"\u006c\u0069\u006e\u0065\u004e\u0075\u006d\u0062\u0065r\u0020\u003e\u0020\u0030");};_ecf |=int (_ffbf );};};if _agba > 1{_dda <<=8;if _cff < _gfb {_ffbf ,_addd =_agdb .Bitmap .GetByte (_egdd -_agdb .Bitmap .RowStride +1);
if _addd !=nil {return _ff .Wrap (_addd ,_ceg ,"\u006c\u0069\u006e\u0065\u004e\u0075\u006d\u0062\u0065r\u0020\u003e\u0020\u0031");};_dda |=int (_ffbf )<<4;};};for _adbad :=0;_adbad < _aag ;_adbad ++{_ggd :=uint (10-_adbad );if _agdb ._eeg {_dge =_agdb .overrideAtTemplate2 (_cgfd ,_gad +_adbad ,_agba ,int (_afba ),_adbad );
_agdb ._cfb .SetIndex (int32 (_dge ));}else {_agdb ._cfb .SetIndex (int32 (_cgfd ));};_ddab ,_addd =_agdb ._bde .DecodeBit (_agdb ._cfb );if _addd !=nil {return _ff .Wrap (_addd ,_ceg ,"");};_afba |=byte (_ddab <<uint (7-_adbad ));_cgfd =((_cgfd &0x1bd)<<1)|_ddab |((_ecf >>_ggd )&0x4)|((_dda >>_ggd )&0x80);
};if _edff :=_agdb .Bitmap .SetByte (_fbb ,_afba );_edff !=nil {return _ff .Wrap (_edff ,_ceg ,"");};_fbb ++;_egdd ++;};return nil ;};func (_ecbe *PageInformationSegment )readMaxStripeSize ()error {_bedd ,_eggg :=_ecbe ._dcaf .ReadBits (15);if _eggg !=nil {return _eggg ;
};_ecbe .MaxStripeSize =uint16 (_bedd &_g .MaxUint16 );return nil ;};func (_bagg *PatternDictionary )GetDictionary ()([]*_gf .Bitmap ,error ){if _bagg .Patterns !=nil {return _bagg .Patterns ,nil ;};if !_bagg .IsMMREncoded {_bagg .setGbAtPixels ();};_ecgf :=NewGenericRegion (_bagg ._dface );
_ecgf .setParametersMMR (_bagg .IsMMREncoded ,_bagg .DataOffset ,_bagg .DataLength ,uint32 (_bagg .HdpHeight ),(_bagg .GrayMax +1)*uint32 (_bagg .HdpWidth ),_bagg .HDTemplate ,false ,false ,_bagg .GBAtX ,_bagg .GBAtY );_gfba ,_dbbg :=_ecgf .GetRegionBitmap ();
if _dbbg !=nil {return nil ,_dbbg ;};if _dbbg =_bagg .extractPatterns (_gfba );_dbbg !=nil {return nil ,_dbbg ;};return _bagg .Patterns ,nil ;};func (_efecf *TextRegion )setContexts (_fbgab *_ae .DecoderStats ,_gcaed *_ae .DecoderStats ,_efaf *_ae .DecoderStats ,_fgcg *_ae .DecoderStats ,_edbd *_ae .DecoderStats ,_cfbaf *_ae .DecoderStats ,_afbbg *_ae .DecoderStats ,_gcad *_ae .DecoderStats ,_eefcc *_ae .DecoderStats ,_fcef *_ae .DecoderStats ){_efecf ._caad =_gcaed ;
_efecf ._cagd =_efaf ;_efecf ._aead =_fgcg ;_efecf ._gggeg =_edbd ;_efecf ._afcb =_afbbg ;_efecf ._abcgd =_gcad ;_efecf ._aecf =_cfbaf ;_efecf ._cbfa =_eefcc ;_efecf ._fbeb =_fcef ;_efecf ._bgeg =_fbgab ;};func (_fadge *SymbolDictionary )decodeDifferenceWidth ()(int64 ,error ){if _fadge .IsHuffmanEncoded {switch _fadge .SdHuffDecodeWidthSelection {case 0:_edgg ,_bebf :=_aee .GetStandardTable (2);
if _bebf !=nil {return 0,_bebf ;};return _edgg .Decode (_fadge ._gaeb );case 1:_efbdg ,_cafc :=_aee .GetStandardTable (3);if _cafc !=nil {return 0,_cafc ;};return _efbdg .Decode (_fadge ._gaeb );case 3:if _fadge ._gedd ==nil {var _gegb int ;if _fadge .SdHuffDecodeHeightSelection ==3{_gegb ++;
};_cgeb ,_gbba :=_fadge .getUserTable (_gegb );if _gbba !=nil {return 0,_gbba ;};_fadge ._gedd =_cgeb ;};return _fadge ._gedd .Decode (_fadge ._gaeb );};}else {_ggcc ,_cbfb :=_fadge ._daed .DecodeInt (_fadge ._beae );if _cbfb !=nil {return 0,_cbfb ;};return int64 (_ggcc ),nil ;
};return 0,nil ;};func (_ebcg *SymbolDictionary )encodeSymbols (_cec _ed .BinaryWriter )(_ecfg int ,_cdbda error ){const _cegb ="\u0065\u006e\u0063o\u0064\u0065\u0053\u0079\u006d\u0062\u006f\u006c";_dabe :=_fff .New ();_dabe .Init ();_gccab ,_cdbda :=_ebcg ._egbc .SelectByIndexes (_ebcg ._ccbg );
if _cdbda !=nil {return 0,_ff .Wrap (_cdbda ,_cegb ,"\u0069n\u0069\u0074\u0069\u0061\u006c");};_fedf :=map[*_gf .Bitmap ]int {};for _gaebd ,_gbb :=range _gccab .Values {_fedf [_gbb ]=_gaebd ;};_gccab .SortByHeight ();var _gcdff ,_aac int ;_dggc ,_cdbda :=_gccab .GroupByHeight ();
if _cdbda !=nil {return 0,_ff .Wrap (_cdbda ,_cegb ,"");};for _ ,_cdbdf :=range _dggc .Values {_bfca :=_cdbdf .Values [0].Height ;_bbga :=_bfca -_gcdff ;if _cdbda =_dabe .EncodeInteger (_fff .IADH ,_bbga );_cdbda !=nil {return 0,_ff .Wrapf (_cdbda ,_cegb ,"\u0049\u0041\u0044\u0048\u0020\u0066\u006f\u0072\u0020\u0064\u0068\u003a \u0027\u0025\u0064\u0027",_bbga );
};_gcdff =_bfca ;_cfdd ,_aafa :=_cdbdf .GroupByWidth ();if _aafa !=nil {return 0,_ff .Wrapf (_aafa ,_cegb ,"\u0068\u0065\u0069g\u0068\u0074\u003a\u0020\u0027\u0025\u0064\u0027",_bfca );};var _cgfa int ;for _ ,_edadd :=range _cfdd .Values {for _ ,_fedfb :=range _edadd .Values {_ccff :=_fedfb .Width ;
_ffgd :=_ccff -_cgfa ;if _aafa =_dabe .EncodeInteger (_fff .IADW ,_ffgd );_aafa !=nil {return 0,_ff .Wrapf (_aafa ,_cegb ,"\u0049\u0041\u0044\u0057\u0020\u0066\u006f\u0072\u0020\u0064\u0077\u003a \u0027\u0025\u0064\u0027",_ffgd );};_cgfa +=_ffgd ;if _aafa =_dabe .EncodeBitmap (_fedfb ,false );
_aafa !=nil {return 0,_ff .Wrapf (_aafa ,_cegb ,"H\u0065i\u0067\u0068\u0074\u003a\u0020\u0025\u0064\u0020W\u0069\u0064\u0074\u0068: \u0025\u0064",_bfca ,_ccff );};_cafgf :=_fedf [_fedfb ];_ebcg ._adf [_cafgf ]=_aac ;_aac ++;};};if _aafa =_dabe .EncodeOOB (_fff .IADW );
_aafa !=nil {return 0,_ff .Wrap (_aafa ,_cegb ,"\u0049\u0041\u0044\u0057");};};if _cdbda =_dabe .EncodeInteger (_fff .IAEX ,0);_cdbda !=nil {return 0,_ff .Wrap (_cdbda ,_cegb ,"\u0065\u0078p\u006f\u0072\u0074e\u0064\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0073");
};if _cdbda =_dabe .EncodeInteger (_fff .IAEX ,len (_ebcg ._ccbg ));_cdbda !=nil {return 0,_ff .Wrap (_cdbda ,_cegb ,"\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020\u0073\u0079m\u0062\u006f\u006c\u0073");};_dabe .Final ();_eefe ,_cdbda :=_dabe .WriteTo (_cec );
if _cdbda !=nil {return 0,_ff .Wrap (_cdbda ,_cegb ,"\u0077\u0072i\u0074\u0069\u006e\u0067 \u0065\u006ec\u006f\u0064\u0065\u0072\u0020\u0063\u006f\u006et\u0065\u0078\u0074\u0020\u0074\u006f\u0020\u0027\u0077\u0027\u0020\u0077r\u0069\u0074\u0065\u0072");
};return int (_eefe ),nil ;};func (_ebf *RegionSegment )readCombinationOperator ()error {_gcfe ,_egba :=_ebf ._egdda .ReadBits (3);if _egba !=nil {return _egba ;};_ebf .CombinaionOperator =_gf .CombinationOperator (_gcfe &0xF);return nil ;};type TextRegion struct{_cgbc *_ed .Reader ;
RegionInfo *RegionSegment ;SbrTemplate int8 ;SbDsOffset int8 ;DefaultPixel int8 ;CombinationOperator _gf .CombinationOperator ;IsTransposed int8 ;ReferenceCorner int16 ;LogSBStrips int16 ;UseRefinement bool ;IsHuffmanEncoded bool ;SbHuffRSize int8 ;SbHuffRDY int8 ;
SbHuffRDX int8 ;SbHuffRDHeight int8 ;SbHuffRDWidth int8 ;SbHuffDT int8 ;SbHuffDS int8 ;SbHuffFS int8 ;SbrATX []int8 ;SbrATY []int8 ;NumberOfSymbolInstances uint32 ;_dea int64 ;SbStrips int8 ;NumberOfSymbols uint32 ;RegionBitmap *_gf .Bitmap ;Symbols []*_gf .Bitmap ;
_afef *_ae .Decoder ;_bgfd *GenericRefinementRegion ;_caad *_ae .DecoderStats ;_cagd *_ae .DecoderStats ;_aead *_ae .DecoderStats ;_gggeg *_ae .DecoderStats ;_cfce *_ae .DecoderStats ;_afcb *_ae .DecoderStats ;_abcgd *_ae .DecoderStats ;_aecf *_ae .DecoderStats ;
_cbfa *_ae .DecoderStats ;_fbeb *_ae .DecoderStats ;_bgeg *_ae .DecoderStats ;_eebg int8 ;_geggd *_aee .FixedSizeTable ;Header *Header ;_fcdd _aee .Tabler ;_bdef _aee .Tabler ;_fefg _aee .Tabler ;_fdcf _aee .Tabler ;_cgac _aee .Tabler ;_eabd _aee .Tabler ;
_bcbf _aee .Tabler ;_bcgd _aee .Tabler ;_ecgb ,_eccc map[int ]int ;_bdgdf []int ;_gaf *_gf .Points ;_baaa *_gf .Bitmaps ;_bbgd *_db .IntSlice ;_fecb ,_cddcd int ;_abec *_gf .Boxes ;};func (_dgccd *SymbolDictionary )addSymbol (_fgf Regioner )error {_agcg ,_gbdf :=_fgf .GetRegionBitmap ();
if _gbdf !=nil {return _gbdf ;};_dgccd ._bfbfa [_dgccd ._dcbgd ]=_agcg ;_dgccd ._dfe =append (_dgccd ._dfe ,_agcg );_ge .Log .Trace ("\u005b\u0053YM\u0042\u004f\u004c \u0044\u0049\u0043\u0054ION\u0041RY\u005d\u0020\u0041\u0064\u0064\u0065\u0064 s\u0079\u006d\u0062\u006f\u006c\u003a\u0020%\u0073",_agcg );
return nil ;};func (_bcggb *SymbolDictionary )setSymbolsArray ()error {if _bcggb ._cccf ==nil {if _fgae :=_bcggb .retrieveImportSymbols ();_fgae !=nil {return _fgae ;};};if _bcggb ._dfe ==nil {_bcggb ._dfe =append (_bcggb ._dfe ,_bcggb ._cccf ...);};return nil ;
};func (_ddfgc *SymbolDictionary )GetDictionary ()([]*_gf .Bitmap ,error ){_ge .Log .Trace ("\u005b\u0053\u0059\u004d\u0042\u004f\u004c-\u0044\u0049\u0043T\u0049\u004f\u004e\u0041R\u0059\u005d\u0020\u0047\u0065\u0074\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0062\u0065\u0067\u0069\u006e\u0073\u002e\u002e\u002e");
defer func (){_ge .Log .Trace ("\u005b\u0053\u0059M\u0042\u004f\u004c\u002d\u0044\u0049\u0043\u0054\u0049\u004f\u004e\u0041\u0052\u0059\u005d\u0020\u0047\u0065\u0074\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064");
_ge .Log .Trace ("\u005b\u0053Y\u004d\u0042\u004f\u004c\u002dD\u0049\u0043\u0054\u0049\u004fN\u0041\u0052\u0059\u005d\u0020\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e\u0020\u000a\u0045\u0078\u003a\u0020\u0027\u0025\u0073\u0027\u002c\u0020\u000a\u006e\u0065\u0077\u003a\u0027\u0025\u0073\u0027",_ddfgc ._gfbg ,_ddfgc ._bfbfa );
}();if _ddfgc ._gfbg ==nil {var _bcge error ;if _ddfgc .UseRefinementAggregation {_ddfgc ._dbce =_ddfgc .getSbSymCodeLen ();};if !_ddfgc .IsHuffmanEncoded {if _bcge =_ddfgc .setCodingStatistics ();_bcge !=nil {return nil ,_bcge ;};};_ddfgc ._bfbfa =make ([]*_gf .Bitmap ,_ddfgc .NumberOfNewSymbols );
var _dfbb []int ;if _ddfgc .IsHuffmanEncoded &&!_ddfgc .UseRefinementAggregation {_dfbb =make ([]int ,_ddfgc .NumberOfNewSymbols );};if _bcge =_ddfgc .setSymbolsArray ();_bcge !=nil {return nil ,_bcge ;};var _ggfg ,_bede int64 ;_ddfgc ._dcbgd =0;for _ddfgc ._dcbgd < _ddfgc .NumberOfNewSymbols {_bede ,_bcge =_ddfgc .decodeHeightClassDeltaHeight ();
if _bcge !=nil {return nil ,_bcge ;};_ggfg +=_bede ;var _eefd ,_agfd uint32 ;_eecg :=int64 (_ddfgc ._dcbgd );for {var _baffbb int64 ;_baffbb ,_bcge =_ddfgc .decodeDifferenceWidth ();if _gb .Is (_bcge ,_b .ErrOOB ){break ;};if _bcge !=nil {return nil ,_bcge ;
};if _ddfgc ._dcbgd >=_ddfgc .NumberOfNewSymbols {break ;};_eefd +=uint32 (_baffbb );_agfd +=_eefd ;if !_ddfgc .IsHuffmanEncoded ||_ddfgc .UseRefinementAggregation {if !_ddfgc .UseRefinementAggregation {_bcge =_ddfgc .decodeDirectlyThroughGenericRegion (_eefd ,uint32 (_ggfg ));
if _bcge !=nil {return nil ,_bcge ;};}else {_bcge =_ddfgc .decodeAggregate (_eefd ,uint32 (_ggfg ));if _bcge !=nil {return nil ,_bcge ;};};}else if _ddfgc .IsHuffmanEncoded &&!_ddfgc .UseRefinementAggregation {_dfbb [_ddfgc ._dcbgd ]=int (_eefd );};_ddfgc ._dcbgd ++;
};if _ddfgc .IsHuffmanEncoded &&!_ddfgc .UseRefinementAggregation {var _ffgg int64 ;if _ddfgc .SdHuffBMSizeSelection ==0{var _bggb _aee .Tabler ;_bggb ,_bcge =_aee .GetStandardTable (1);if _bcge !=nil {return nil ,_bcge ;};_ffgg ,_bcge =_bggb .Decode (_ddfgc ._gaeb );
if _bcge !=nil {return nil ,_bcge ;};}else {_ffgg ,_bcge =_ddfgc .huffDecodeBmSize ();if _bcge !=nil {return nil ,_bcge ;};};_ddfgc ._gaeb .Align ();var _ceec *_gf .Bitmap ;_ceec ,_bcge =_ddfgc .decodeHeightClassCollectiveBitmap (_ffgg ,uint32 (_ggfg ),_agfd );
if _bcge !=nil {return nil ,_bcge ;};_bcge =_ddfgc .decodeHeightClassBitmap (_ceec ,_eecg ,int (_ggfg ),_dfbb );if _bcge !=nil {return nil ,_bcge ;};};};_bdfb ,_bcge :=_ddfgc .getToExportFlags ();if _bcge !=nil {return nil ,_bcge ;};_ddfgc .setExportedSymbols (_bdfb );
};return _ddfgc ._gfbg ,nil ;};func (_geff *PageInformationSegment )readWidthAndHeight ()error {_afcg ,_fefcg :=_geff ._dcaf .ReadBits (32);if _fefcg !=nil {return _fefcg ;};_geff .PageBMWidth =int (_afcg &_g .MaxInt32 );_afcg ,_fefcg =_geff ._dcaf .ReadBits (32);
if _fefcg !=nil {return _fefcg ;};_geff .PageBMHeight =int (_afcg &_g .MaxInt32 );return nil ;};func (_dafc *Header )readHeaderLength (_cdca *_ed .Reader ,_ffbbe int64 ){_dafc .HeaderLength =_cdca .AbsolutePosition ()-_ffbbe ;};type SegmentEncoder interface{Encode (_dgdg _ed .BinaryWriter )(_adcf int ,_daaf error );
};func (_fddcg *PatternDictionary )Init (h *Header ,r *_ed .Reader )error {_fddcg ._dface =r ;return _fddcg .parseHeader ();};func (_egaa *SymbolDictionary )decodeDirectlyThroughGenericRegion (_cfbd ,_becc uint32 )error {if _egaa ._bafd ==nil {_egaa ._bafd =NewGenericRegion (_egaa ._gaeb );
};_egaa ._bafd .setParametersWithAt (false ,byte (_egaa .SdTemplate ),false ,false ,_egaa .SdATX ,_egaa .SdATY ,_cfbd ,_becc ,_egaa ._bgbe ,_egaa ._daed );return _egaa .addSymbol (_egaa ._bafd );};func (_ecggf *TextRegion )encodeFlags (_ecfd _ed .BinaryWriter )(_cabad int ,_febf error ){const _ddcb ="e\u006e\u0063\u006f\u0064\u0065\u0046\u006c\u0061\u0067\u0073";
if _febf =_ecfd .WriteBit (int (_ecggf .SbrTemplate ));_febf !=nil {return _cabad ,_ff .Wrap (_febf ,_ddcb ,"s\u0062\u0072\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");};if _ ,_febf =_ecfd .WriteBits (uint64 (_ecggf .SbDsOffset ),5);_febf !=nil {return _cabad ,_ff .Wrap (_febf ,_ddcb ,"\u0073\u0062\u0044\u0073\u004f\u0066\u0066\u0073\u0065\u0074");
};if _febf =_ecfd .WriteBit (int (_ecggf .DefaultPixel ));_febf !=nil {return _cabad ,_ff .Wrap (_febf ,_ddcb ,"\u0044\u0065\u0066a\u0075\u006c\u0074\u0050\u0069\u0078\u0065\u006c");};if _ ,_febf =_ecfd .WriteBits (uint64 (_ecggf .CombinationOperator ),2);
_febf !=nil {return _cabad ,_ff .Wrap (_febf ,_ddcb ,"\u0043\u006f\u006d\u0062in\u0061\u0074\u0069\u006f\u006e\u004f\u0070\u0065\u0072\u0061\u0074\u006f\u0072");};if _febf =_ecfd .WriteBit (int (_ecggf .IsTransposed ));_febf !=nil {return _cabad ,_ff .Wrap (_febf ,_ddcb ,"\u0069\u0073\u0020\u0074\u0072\u0061\u006e\u0073\u0070\u006f\u0073\u0065\u0064");
};if _ ,_febf =_ecfd .WriteBits (uint64 (_ecggf .ReferenceCorner ),2);_febf !=nil {return _cabad ,_ff .Wrap (_febf ,_ddcb ,"\u0072\u0065f\u0065\u0072\u0065n\u0063\u0065\u0020\u0063\u006f\u0072\u006e\u0065\u0072");};if _ ,_febf =_ecfd .WriteBits (uint64 (_ecggf .LogSBStrips ),2);
_febf !=nil {return _cabad ,_ff .Wrap (_febf ,_ddcb ,"L\u006f\u0067\u0053\u0042\u0053\u0074\u0072\u0069\u0070\u0073");};var _edace int ;if _ecggf .UseRefinement {_edace =1;};if _febf =_ecfd .WriteBit (_edace );_febf !=nil {return _cabad ,_ff .Wrap (_febf ,_ddcb ,"\u0075\u0073\u0065\u0020\u0072\u0065\u0066\u0069\u006ee\u006d\u0065\u006e\u0074");
};_edace =0;if _ecggf .IsHuffmanEncoded {_edace =1;};if _febf =_ecfd .WriteBit (_edace );_febf !=nil {return _cabad ,_ff .Wrap (_febf ,_ddcb ,"u\u0073\u0065\u0020\u0068\u0075\u0066\u0066\u006d\u0061\u006e");};_cabad =2;return _cabad ,nil ;};func (_aged *PatternDictionary )readTemplate ()error {_deeb ,_aecbd :=_aged ._dface .ReadBits (2);
if _aecbd !=nil {return _aecbd ;};_aged .HDTemplate =byte (_deeb );return nil ;};func (_ega *GenericRegion )overrideAtTemplate0b (_ffegd ,_fgg ,_cafg ,_aecb ,_ffde ,_cgc int )int {if _ega .GBAtOverride [0]{_ffegd &=0xFFFD;if _ega .GBAtY [0]==0&&_ega .GBAtX [0]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [0]&0x1))<<1;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [0]),_cafg +int (_ega .GBAtY [0])))<<1;};};if _ega .GBAtOverride [1]{_ffegd &=0xDFFF;if _ega .GBAtY [1]==0&&_ega .GBAtX [1]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [1]&0x1))<<13;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [1]),_cafg +int (_ega .GBAtY [1])))<<13;};};if _ega .GBAtOverride [2]{_ffegd &=0xFDFF;if _ega .GBAtY [2]==0&&_ega .GBAtX [2]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [2]&0x1))<<9;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [2]),_cafg +int (_ega .GBAtY [2])))<<9;};};if _ega .GBAtOverride [3]{_ffegd &=0xBFFF;if _ega .GBAtY [3]==0&&_ega .GBAtX [3]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [3]&0x1))<<14;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [3]),_cafg +int (_ega .GBAtY [3])))<<14;};};if _ega .GBAtOverride [4]{_ffegd &=0xEFFF;if _ega .GBAtY [4]==0&&_ega .GBAtX [4]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [4]&0x1))<<12;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [4]),_cafg +int (_ega .GBAtY [4])))<<12;};};if _ega .GBAtOverride [5]{_ffegd &=0xFFDF;if _ega .GBAtY [5]==0&&_ega .GBAtX [5]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [5]&0x1))<<5;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [5]),_cafg +int (_ega .GBAtY [5])))<<5;};};if _ega .GBAtOverride [6]{_ffegd &=0xFFFB;if _ega .GBAtY [6]==0&&_ega .GBAtX [6]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [6]&0x1))<<2;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [6]),_cafg +int (_ega .GBAtY [6])))<<2;};};if _ega .GBAtOverride [7]{_ffegd &=0xFFF7;if _ega .GBAtY [7]==0&&_ega .GBAtX [7]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [7]&0x1))<<3;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [7]),_cafg +int (_ega .GBAtY [7])))<<3;};};if _ega .GBAtOverride [8]{_ffegd &=0xF7FF;if _ega .GBAtY [8]==0&&_ega .GBAtX [8]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [8]&0x1))<<11;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [8]),_cafg +int (_ega .GBAtY [8])))<<11;};};if _ega .GBAtOverride [9]{_ffegd &=0xFFEF;if _ega .GBAtY [9]==0&&_ega .GBAtX [9]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [9]&0x1))<<4;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [9]),_cafg +int (_ega .GBAtY [9])))<<4;};};if _ega .GBAtOverride [10]{_ffegd &=0x7FFF;if _ega .GBAtY [10]==0&&_ega .GBAtX [10]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [10]&0x1))<<15;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [10]),_cafg +int (_ega .GBAtY [10])))<<15;};};if _ega .GBAtOverride [11]{_ffegd &=0xFDFF;if _ega .GBAtY [11]==0&&_ega .GBAtX [11]>=-int8 (_ffde ){_ffegd |=(_aecb >>uint (int8 (_cgc )-_ega .GBAtX [11]&0x1))<<10;
}else {_ffegd |=int (_ega .getPixel (_fgg +int (_ega .GBAtX [11]),_cafg +int (_ega .GBAtY [11])))<<10;};};return _ffegd ;};func (_begd *GenericRegion )overrideAtTemplate3 (_ffefd ,_dbgc ,_ffcg ,_bdf ,_ddac int )int {_ffefd &=0x3EF;if _begd .GBAtY [0]==0&&_begd .GBAtX [0]>=-int8 (_ddac ){_ffefd |=(_bdf >>uint (7-(int8 (_ddac )+_begd .GBAtX [0]))&0x1)<<4;
}else {_ffefd |=int (_begd .getPixel (_dbgc +int (_begd .GBAtX [0]),_ffcg +int (_begd .GBAtY [0])))<<4;};return _ffefd ;};func (_eccb *TextRegion )decodeStripT ()(_cgfab int64 ,_agbda error ){if _eccb .IsHuffmanEncoded {if _eccb .SbHuffDT ==3{if _eccb ._fefg ==nil {var _ffdee int ;
if _eccb .SbHuffFS ==3{_ffdee ++;};if _eccb .SbHuffDS ==3{_ffdee ++;};_eccb ._fefg ,_agbda =_eccb .getUserTable (_ffdee );if _agbda !=nil {return 0,_agbda ;};};_cgfab ,_agbda =_eccb ._fefg .Decode (_eccb ._cgbc );if _agbda !=nil {return 0,_agbda ;};}else {var _cbcec _aee .Tabler ;
_cbcec ,_agbda =_aee .GetStandardTable (11+int (_eccb .SbHuffDT ));if _agbda !=nil {return 0,_agbda ;};_cgfab ,_agbda =_cbcec .Decode (_eccb ._cgbc );if _agbda !=nil {return 0,_agbda ;};};}else {var _ffba int32 ;_ffba ,_agbda =_eccb ._afef .DecodeInt (_eccb ._caad );
if _agbda !=nil {return 0,_agbda ;};_cgfab =int64 (_ffba );};_cgfab *=int64 (-_eccb .SbStrips );return _cgfab ,nil ;};func (_acad *TextRegion )decodeRdy ()(int64 ,error ){const _cgfdg ="\u0064e\u0063\u006f\u0064\u0065\u0052\u0064y";if _acad .IsHuffmanEncoded {if _acad .SbHuffRDY ==3{if _acad ._bcbf ==nil {var (_cfbc int ;
_bfbe error ;);if _acad .SbHuffFS ==3{_cfbc ++;};if _acad .SbHuffDS ==3{_cfbc ++;};if _acad .SbHuffDT ==3{_cfbc ++;};if _acad .SbHuffRDWidth ==3{_cfbc ++;};if _acad .SbHuffRDHeight ==3{_cfbc ++;};if _acad .SbHuffRDX ==3{_cfbc ++;};_acad ._bcbf ,_bfbe =_acad .getUserTable (_cfbc );
if _bfbe !=nil {return 0,_ff .Wrap (_bfbe ,_cgfdg ,"");};};return _acad ._bcbf .Decode (_acad ._cgbc );};_dgac ,_gfgf :=_aee .GetStandardTable (14+int (_acad .SbHuffRDY ));if _gfgf !=nil {return 0,_gfgf ;};return _dgac .Decode (_acad ._cgbc );};_gbac ,_bgge :=_acad ._afef .DecodeInt (_acad ._fbeb );
if _bgge !=nil {return 0,_ff .Wrap (_bgge ,_cgfdg ,"");};return int64 (_gbac ),nil ;};func (_dbga *SymbolDictionary )encodeFlags (_bfec _ed .BinaryWriter )(_ecfef int ,_aafae error ){const _gbdg ="e\u006e\u0063\u006f\u0064\u0065\u0046\u006c\u0061\u0067\u0073";
if _aafae =_bfec .SkipBits (3);_aafae !=nil {return 0,_ff .Wrap (_aafae ,_gbdg ,"\u0065\u006d\u0070\u0074\u0079\u0020\u0062\u0069\u0074\u0073");};var _bfdg int ;if _dbga .SdrTemplate > 0{_bfdg =1;};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"s\u0064\u0072\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");
};_bfdg =0;if _dbga .SdTemplate > 1{_bfdg =1;};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0073\u0064\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");};_bfdg =0;if _dbga .SdTemplate ==1||_dbga .SdTemplate ==3{_bfdg =1;
};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0073\u0064\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065");};_bfdg =0;if _dbga ._bbfd {_bfdg =1;};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0063\u006f\u0064in\u0067\u0020\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0020\u0072\u0065\u0074\u0061\u0069\u006e\u0065\u0064");
};_bfdg =0;if _dbga ._ebfg {_bfdg =1;};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0063\u006f\u0064\u0069ng\u0020\u0063\u006f\u006e\u0074\u0065\u0078\u0074\u0020\u0075\u0073\u0065\u0064");};_bfdg =0;if _dbga .SdHuffAggInstanceSelection {_bfdg =1;
};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0073\u0064\u0048\u0075\u0066\u0066\u0041\u0067\u0067\u0049\u006e\u0073\u0074");};_bfdg =int (_dbga .SdHuffBMSizeSelection );if _aafae =_bfec .WriteBit (_bfdg );
_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0073\u0064\u0048u\u0066\u0066\u0042\u006d\u0053\u0069\u007a\u0065");};_bfdg =0;if _dbga .SdHuffDecodeWidthSelection > 1{_bfdg =1;};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"s\u0064\u0048\u0075\u0066\u0066\u0057\u0069\u0064\u0074\u0068");
};_bfdg =0;switch _dbga .SdHuffDecodeWidthSelection {case 1,3:_bfdg =1;};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"s\u0064\u0048\u0075\u0066\u0066\u0057\u0069\u0064\u0074\u0068");};_bfdg =0;if _dbga .SdHuffDecodeHeightSelection > 1{_bfdg =1;
};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0073\u0064\u0048u\u0066\u0066\u0048\u0065\u0069\u0067\u0068\u0074");};_bfdg =0;switch _dbga .SdHuffDecodeHeightSelection {case 1,3:_bfdg =1;};if _aafae =_bfec .WriteBit (_bfdg );
_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0073\u0064\u0048u\u0066\u0066\u0048\u0065\u0069\u0067\u0068\u0074");};_bfdg =0;if _dbga .UseRefinementAggregation {_bfdg =1;};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0073\u0064\u0052\u0065\u0066\u0041\u0067\u0067");
};_bfdg =0;if _dbga .IsHuffmanEncoded {_bfdg =1;};if _aafae =_bfec .WriteBit (_bfdg );_aafae !=nil {return _ecfef ,_ff .Wrap (_aafae ,_gbdg ,"\u0073\u0064\u0048\u0075\u0066\u0066");};return 2,nil ;};func (_adbe *template0 )form (_gga ,_dacb ,_gdbe ,_gfcb ,_caba int16 )int16 {return (_gga <<10)|(_dacb <<7)|(_gdbe <<4)|(_gfcb <<1)|_caba ;
};func (_ffee *SymbolDictionary )encodeNumSyms (_gcae _ed .BinaryWriter )(_cdg int ,_ebdcb error ){const _eaec ="\u0065\u006e\u0063\u006f\u0064\u0065\u004e\u0075\u006d\u0053\u0079\u006d\u0073";_ffec :=make ([]byte ,4);_dg .BigEndian .PutUint32 (_ffec ,_ffee .NumberOfExportedSymbols );
if _cdg ,_ebdcb =_gcae .Write (_ffec );_ebdcb !=nil {return _cdg ,_ff .Wrap (_ebdcb ,_eaec ,"\u0065\u0078p\u006f\u0072\u0074e\u0064\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0073");};_dg .BigEndian .PutUint32 (_ffec ,_ffee .NumberOfNewSymbols );_efge ,_ebdcb :=_gcae .Write (_ffec );
if _ebdcb !=nil {return _cdg ,_ff .Wrap (_ebdcb ,_eaec ,"n\u0065\u0077\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0073");};return _cdg +_efge ,nil ;};func (_gddg *HalftoneRegion )computeGrayScalePlanes (_cgcd []*_gf .Bitmap ,_bdgc int )([][]int ,error ){_deg :=make ([][]int ,_gddg .HGridHeight );
for _dagaf :=0;_dagaf < len (_deg );_dagaf ++{_deg [_dagaf ]=make ([]int ,_gddg .HGridWidth );};for _ebbbg :=0;_ebbbg < int (_gddg .HGridHeight );_ebbbg ++{for _cbag :=0;_cbag < int (_gddg .HGridWidth );_cbag +=8{var _gdba int ;if _eedf :=int (_gddg .HGridWidth )-_cbag ;
_eedf > 8{_gdba =8;}else {_gdba =_eedf ;};_eaba :=_cgcd [0].GetByteIndex (_cbag ,_ebbbg );for _gfbcg :=0;_gfbcg < _gdba ;_gfbcg ++{_dfd :=_gfbcg +_cbag ;_deg [_ebbbg ][_dfd ]=0;for _fadg :=0;_fadg < _bdgc ;_fadg ++{_bdd ,_gaec :=_cgcd [_fadg ].GetByte (_eaba );
if _gaec !=nil {return nil ,_gaec ;};_bcbc :=_bdd >>uint (7-_dfd &7);_fgge :=_bcbc &1;_eedfd :=1<<uint (_fadg );_bgdg :=int (_fgge )*_eedfd ;_deg [_ebbbg ][_dfd ]+=_bgdg ;};};};};return _deg ,nil ;};var _ templater =&template1 {};func (_fda *SymbolDictionary )Encode (w _ed .BinaryWriter )(_ebegg int ,_edad error ){const _gcdb ="\u0053\u0079\u006dbo\u006c\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e\u0045\u006e\u0063\u006f\u0064\u0065";
if _fda ==nil {return 0,_ff .Error (_gcdb ,"\u0073\u0079m\u0062\u006f\u006c\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006e\u006f\u0074\u0020\u0064\u0065\u0066in\u0065\u0064");};if _ebegg ,_edad =_fda .encodeFlags (w );_edad !=nil {return _ebegg ,_ff .Wrap (_edad ,_gcdb ,"");
};_ggeb ,_edad :=_fda .encodeATFlags (w );if _edad !=nil {return _ebegg ,_ff .Wrap (_edad ,_gcdb ,"");};_ebegg +=_ggeb ;if _ggeb ,_edad =_fda .encodeRefinementATFlags (w );_edad !=nil {return _ebegg ,_ff .Wrap (_edad ,_gcdb ,"");};_ebegg +=_ggeb ;if _ggeb ,_edad =_fda .encodeNumSyms (w );
_edad !=nil {return _ebegg ,_ff .Wrap (_edad ,_gcdb ,"");};_ebegg +=_ggeb ;if _ggeb ,_edad =_fda .encodeSymbols (w );_edad !=nil {return _ebegg ,_ff .Wrap (_edad ,_gcdb ,"");};_ebegg +=_ggeb ;return _ebegg ,nil ;};func (_gfdb *SymbolDictionary )decodeHeightClassDeltaHeightWithHuffman ()(int64 ,error ){switch _gfdb .SdHuffDecodeHeightSelection {case 0:_eac ,_dgff :=_aee .GetStandardTable (4);
if _dgff !=nil {return 0,_dgff ;};return _eac .Decode (_gfdb ._gaeb );case 1:_efc ,_eace :=_aee .GetStandardTable (5);if _eace !=nil {return 0,_eace ;};return _efc .Decode (_gfdb ._gaeb );case 3:if _gfdb ._bfdbd ==nil {_dbcaa ,_fbef :=_aee .GetStandardTable (0);
if _fbef !=nil {return 0,_fbef ;};_gfdb ._bfdbd =_dbcaa ;};return _gfdb ._bfdbd .Decode (_gfdb ._gaeb );};return 0,nil ;};func (_eef *GenericRegion )getPixel (_gdeb ,_bcdd int )int8 {if _gdeb < 0||_gdeb >=_eef .Bitmap .Width {return 0;};if _bcdd < 0||_bcdd >=_eef .Bitmap .Height {return 0;
};if _eef .Bitmap .GetPixel (_gdeb ,_bcdd ){return 1;};return 0;};const (TSymbolDictionary Type =0;TIntermediateTextRegion Type =4;TImmediateTextRegion Type =6;TImmediateLosslessTextRegion Type =7;TPatternDictionary Type =16;TIntermediateHalftoneRegion Type =20;
TImmediateHalftoneRegion Type =22;TImmediateLosslessHalftoneRegion Type =23;TIntermediateGenericRegion Type =36;TImmediateGenericRegion Type =38;TImmediateLosslessGenericRegion Type =39;TIntermediateGenericRefinementRegion Type =40;TImmediateGenericRefinementRegion Type =42;
TImmediateLosslessGenericRefinementRegion Type =43;TPageInformation Type =48;TEndOfPage Type =49;TEndOfStrip Type =50;TEndOfFile Type =51;TProfiles Type =52;TTables Type =53;TExtension Type =62;TBitmap Type =70;);func _fgd (_gdfc *_ed .Reader ,_eadc *Header )*TextRegion {_fcgf :=&TextRegion {_cgbc :_gdfc ,Header :_eadc ,RegionInfo :NewRegionSegment (_gdfc )};
return _fcgf ;};func (_cabb *GenericRegion )overrideAtTemplate2 (_dagb ,_cae ,_ggfc ,_edeb ,_aggc int )int {_dagb &=0x3FB;if _cabb .GBAtY [0]==0&&_cabb .GBAtX [0]>=-int8 (_aggc ){_dagb |=(_edeb >>uint (7-(int8 (_aggc )+_cabb .GBAtX [0]))&0x1)<<2;}else {_dagb |=int (_cabb .getPixel (_cae +int (_cabb .GBAtX [0]),_ggfc +int (_cabb .GBAtY [0])))<<2;
};return _dagb ;};func (_feb *GenericRegion )Encode (w _ed .BinaryWriter )(_fef int ,_baffb error ){const _cga ="G\u0065n\u0065\u0072\u0069\u0063\u0052\u0065\u0067\u0069o\u006e\u002e\u0045\u006eco\u0064\u0065";if _feb .Bitmap ==nil {return 0,_ff .Error (_cga ,"\u0070\u0072\u006f\u0076id\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0062\u0069\u0074\u006d\u0061\u0070");
};_cde ,_baffb :=_feb .RegionSegment .Encode (w );if _baffb !=nil {return 0,_ff .Wrap (_baffb ,_cga ,"\u0052\u0065\u0067\u0069\u006f\u006e\u0053\u0065\u0067\u006d\u0065\u006e\u0074");};_fef +=_cde ;if _baffb =w .SkipBits (4);_baffb !=nil {return _fef ,_ff .Wrap (_baffb ,_cga ,"\u0073k\u0069p\u0020\u0072\u0065\u0073\u0065r\u0076\u0065d\u0020\u0062\u0069\u0074\u0073");
};var _addb int ;if _feb .IsTPGDon {_addb =1;};if _baffb =w .WriteBit (_addb );_baffb !=nil {return _fef ,_ff .Wrap (_baffb ,_cga ,"\u0074\u0070\u0067\u0064\u006f\u006e");};_addb =0;if _baffb =w .WriteBit (int (_feb .GBTemplate >>1)&0x01);_baffb !=nil {return _fef ,_ff .Wrap (_baffb ,_cga ,"f\u0069r\u0073\u0074\u0020\u0067\u0062\u0074\u0065\u006dp\u006c\u0061\u0074\u0065 b\u0069\u0074");
};if _baffb =w .WriteBit (int (_feb .GBTemplate )&0x01);_baffb !=nil {return _fef ,_ff .Wrap (_baffb ,_cga ,"s\u0065\u0063\u006f\u006ed \u0067b\u0074\u0065\u006d\u0070\u006ca\u0074\u0065\u0020\u0062\u0069\u0074");};if _feb .UseMMR {_addb =1;};if _baffb =w .WriteBit (_addb );
_baffb !=nil {return _fef ,_ff .Wrap (_baffb ,_cga ,"u\u0073\u0065\u0020\u004d\u004d\u0052\u0020\u0062\u0069\u0074");};_fef ++;if _cde ,_baffb =_feb .writeGBAtPixels (w );_baffb !=nil {return _fef ,_ff .Wrap (_baffb ,_cga ,"");};_fef +=_cde ;_ccef :=_fff .New ();
if _baffb =_ccef .EncodeBitmap (_feb .Bitmap ,_feb .IsTPGDon );_baffb !=nil {return _fef ,_ff .Wrap (_baffb ,_cga ,"");};_ccef .Final ();var _bebg int64 ;if _bebg ,_baffb =_ccef .WriteTo (w );_baffb !=nil {return _fef ,_ff .Wrap (_baffb ,_cga ,"");};_fef +=int (_bebg );
return _fef ,nil ;};func (_dcfc *SymbolDictionary )getSbSymCodeLen ()int8 {_ebge :=int8 (_g .Ceil (_g .Log (float64 (_dcfc ._cdeb +_dcfc .NumberOfNewSymbols ))/_g .Log (2)));if _dcfc .IsHuffmanEncoded &&_ebge < 1{return 1;};return _ebge ;};func NewGenericRegion (r *_ed .Reader )*GenericRegion {return &GenericRegion {RegionSegment :NewRegionSegment (r ),_fddcc :r };
};func (_cdfc *Header )readSegmentPageAssociation (_abag Documenter ,_afdb *_ed .Reader ,_gdcc uint64 ,_adga ...int )(_cfca error ){const _befe ="\u0072\u0065\u0061\u0064\u0053\u0065\u0067\u006d\u0065\u006e\u0074P\u0061\u0067\u0065\u0041\u0073\u0073\u006f\u0063\u0069\u0061t\u0069\u006f\u006e";
if !_cdfc .PageAssociationFieldSize {_efb ,_dff :=_afdb .ReadBits (8);if _dff !=nil {return _ff .Wrap (_dff ,_befe ,"\u0073\u0068\u006fr\u0074\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_cdfc .PageAssociation =int (_efb &0xFF);}else {_egfea ,_ccce :=_afdb .ReadBits (32);
if _ccce !=nil {return _ff .Wrap (_ccce ,_befe ,"l\u006f\u006e\u0067\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};_cdfc .PageAssociation =int (_egfea &_g .MaxInt32 );};if _gdcc ==0{return nil ;};if _cdfc .PageAssociation !=0{_cace ,_gdeg :=_abag .GetPage (_cdfc .PageAssociation );
if _gdeg !=nil {return _ff .Wrap (_gdeg ,_befe ,"\u0061s\u0073\u006f\u0063\u0069a\u0074\u0065\u0064\u0020\u0070a\u0067e\u0020n\u006f\u0074\u0020\u0066\u006f\u0075\u006ed");};var _dgge int ;for _fdf :=uint64 (0);_fdf < _gdcc ;_fdf ++{_dgge =_adga [_fdf ];
_cdfc .RTSegments [_fdf ],_gdeg =_cace .GetSegment (_dgge );if _gdeg !=nil {var _ebea error ;_cdfc .RTSegments [_fdf ],_ebea =_abag .GetGlobalSegment (_dgge );if _ebea !=nil {return _ff .Wrapf (_gdeg ,_befe ,"\u0072\u0065\u0066\u0065\u0072\u0065n\u0063\u0065\u0020s\u0065\u0067\u006de\u006e\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075n\u0064\u0020\u0061\u0074\u0020pa\u0067\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0072\u0020\u0069\u006e\u0020\u0067\u006c\u006f\u0062\u0061\u006c\u0073",_cdfc .PageAssociation );
};};};return nil ;};for _agcb :=uint64 (0);_agcb < _gdcc ;_agcb ++{_cdfc .RTSegments [_agcb ],_cfca =_abag .GetGlobalSegment (_adga [_agcb ]);if _cfca !=nil {return _ff .Wrapf (_cfca ,_befe ,"\u0067\u006c\u006f\u0062\u0061\u006c\u0020\u0073\u0065\u0067m\u0065\u006e\u0074\u003a\u0020\u0027\u0025d\u0027\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064",_adga [_agcb ]);
};};return nil ;};func (_edb *Header )CleanSegmentData (){if _edb .SegmentData !=nil {_edb .SegmentData =nil ;};};func (_bgb *PageInformationSegment )readIsStriped ()error {_gbab ,_bccf :=_bgb ._dcaf .ReadBit ();if _bccf !=nil {return _bccf ;};if _gbab ==1{_bgb .IsStripe =true ;
};return nil ;};var _ _aee .BasicTabler =&TableSegment {};func (_abffd *TextRegion )getSymbols ()error {if _abffd .Header .RTSegments !=nil {return _abffd .initSymbols ();};return nil ;};type Regioner interface{GetRegionBitmap ()(*_gf .Bitmap ,error );
GetRegionInfo ()*RegionSegment ;};func (_baggc *RegionSegment )parseHeader ()error {const _gcce ="p\u0061\u0072\u0073\u0065\u0048\u0065\u0061\u0064\u0065\u0072";_ge .Log .Trace ("\u005b\u0052\u0045\u0047I\u004f\u004e\u005d\u005b\u0050\u0041\u0052\u0053\u0045\u002dH\u0045A\u0044\u0045\u0052\u005d\u0020\u0042\u0065g\u0069\u006e");
defer func (){_ge .Log .Trace ("\u005b\u0052\u0045G\u0049\u004f\u004e\u005d[\u0050\u0041\u0052\u0053\u0045\u002d\u0048E\u0041\u0044\u0045\u0052\u005d\u0020\u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064");}();_ebce ,_abab :=_baggc ._egdda .ReadBits (32);
if _abab !=nil {return _ff .Wrap (_abab ,_gcce ,"\u0077\u0069\u0064t\u0068");};_baggc .BitmapWidth =uint32 (_ebce &_g .MaxUint32 );_ebce ,_abab =_baggc ._egdda .ReadBits (32);if _abab !=nil {return _ff .Wrap (_abab ,_gcce ,"\u0068\u0065\u0069\u0067\u0068\u0074");
};_baggc .BitmapHeight =uint32 (_ebce &_g .MaxUint32 );_ebce ,_abab =_baggc ._egdda .ReadBits (32);if _abab !=nil {return _ff .Wrap (_abab ,_gcce ,"\u0078\u0020\u006c\u006f\u0063\u0061\u0074\u0069\u006f\u006e");};_baggc .XLocation =uint32 (_ebce &_g .MaxUint32 );
_ebce ,_abab =_baggc ._egdda .ReadBits (32);if _abab !=nil {return _ff .Wrap (_abab ,_gcce ,"\u0079\u0020\u006c\u006f\u0063\u0061\u0074\u0069\u006f\u006e");};_baggc .YLocation =uint32 (_ebce &_g .MaxUint32 );if _ ,_abab =_baggc ._egdda .ReadBits (5);_abab !=nil {return _ff .Wrap (_abab ,_gcce ,"\u0064i\u0072\u0079\u0020\u0072\u0065\u0061d");
};if _abab =_baggc .readCombinationOperator ();_abab !=nil {return _ff .Wrap (_abab ,_gcce ,"c\u006fm\u0062\u0069\u006e\u0061\u0074\u0069\u006f\u006e \u006f\u0070\u0065\u0072at\u006f\u0072");};return nil ;};func (_cegdg *PageInformationSegment )String ()string {_bcea :=&_a .Builder {};
_bcea .WriteString ("\u000a\u005b\u0050\u0041G\u0045\u002d\u0049\u004e\u0046\u004f\u0052\u004d\u0041\u0054I\u004fN\u002d\u0053\u0045\u0047\u004d\u0045\u004eT\u005d\u000a");_bcea .WriteString (_e .Sprintf ("\u0009\u002d \u0042\u004d\u0048e\u0069\u0067\u0068\u0074\u003a\u0020\u0025\u0064\u000a",_cegdg .PageBMHeight ));
_bcea .WriteString (_e .Sprintf ("\u0009-\u0020B\u004d\u0057\u0069\u0064\u0074\u0068\u003a\u0020\u0025\u0064\u000a",_cegdg .PageBMWidth ));_bcea .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0052es\u006f\u006c\u0075\u0074\u0069\u006f\u006e\u0058\u003a\u0020\u0025\u0064\u000a",_cegdg .ResolutionX ));
_bcea .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0052es\u006f\u006c\u0075\u0074\u0069\u006f\u006e\u0059\u003a\u0020\u0025\u0064\u000a",_cegdg .ResolutionY ));_bcea .WriteString (_e .Sprintf ("\t\u002d\u0020\u0043\u006f\u006d\u0062i\u006e\u0061\u0074\u0069\u006f\u006e\u004f\u0070\u0065r\u0061\u0074\u006fr\u003a \u0025\u0073\u000a",_cegdg ._bae ));
_bcea .WriteString (_e .Sprintf ("\t\u002d\u0020\u0043\u006f\u006d\u0062i\u006e\u0061\u0074\u0069\u006f\u006eO\u0070\u0065\u0072\u0061\u0074\u006f\u0072O\u0076\u0065\u0072\u0072\u0069\u0064\u0065\u003a\u0020\u0025v\u000a",_cegdg ._fgeb ));_bcea .WriteString (_e .Sprintf ("\u0009-\u0020I\u0073\u004c\u006f\u0073\u0073l\u0065\u0073s\u003a\u0020\u0025\u0076\u000a",_cegdg .IsLossless ));
_bcea .WriteString (_e .Sprintf ("\u0009\u002d\u0020R\u0065\u0071\u0075\u0069r\u0065\u0073\u0041\u0075\u0078\u0069\u006ci\u0061\u0072\u0079\u0042\u0075\u0066\u0066\u0065\u0072\u003a\u0020\u0025\u0076\u000a",_cegdg ._efff ));_bcea .WriteString (_e .Sprintf ("\u0009\u002d\u0020M\u0069\u0067\u0068\u0074C\u006f\u006e\u0074\u0061\u0069\u006e\u0052e\u0066\u0069\u006e\u0065\u006d\u0065\u006e\u0074\u0073\u003a\u0020\u0025\u0076\u000a",_cegdg ._afcf ));
_bcea .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0049\u0073\u0053\u0074\u0072\u0069\u0070\u0065\u0064:\u0020\u0025\u0076\u000a",_cegdg .IsStripe ));_bcea .WriteString (_e .Sprintf ("\t\u002d\u0020\u004d\u0061xS\u0074r\u0069\u0070\u0065\u0053\u0069z\u0065\u003a\u0020\u0025\u0076\u000a",_cegdg .MaxStripeSize ));
return _bcea .String ();};func (_gcaf *Header )writeReferredToCount (_fbcf _ed .BinaryWriter )(_ecc int ,_abbbe error ){const _fggc ="w\u0072i\u0074\u0065\u0052\u0065\u0066\u0065\u0072\u0072e\u0064\u0054\u006f\u0043ou\u006e\u0074";_gcaf .RTSNumbers =make ([]int ,len (_gcaf .RTSegments ));
for _cabac ,_bbf :=range _gcaf .RTSegments {_gcaf .RTSNumbers [_cabac ]=int (_bbf .SegmentNumber );};if len (_gcaf .RTSNumbers )<=4{var _ggba byte ;if len (_gcaf .RetainBits )>=1{_ggba =_gcaf .RetainBits [0];};_ggba |=byte (len (_gcaf .RTSNumbers ))<<5;
if _abbbe =_fbcf .WriteByte (_ggba );_abbbe !=nil {return 0,_ff .Wrap (_abbbe ,_fggc ,"\u0073\u0068\u006fr\u0074\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};return 1,nil ;};_gebe :=uint32 (len (_gcaf .RTSNumbers ));_cegd :=make ([]byte ,4+_db .Ceil (len (_gcaf .RTSNumbers )+1,8));
_gebe |=0x7<<29;_dg .BigEndian .PutUint32 (_cegd ,_gebe );copy (_cegd [1:],_gcaf .RetainBits );_ecc ,_abbbe =_fbcf .Write (_cegd );if _abbbe !=nil {return 0,_ff .Wrap (_abbbe ,_fggc ,"l\u006f\u006e\u0067\u0020\u0066\u006f\u0072\u006d\u0061\u0074");};return _ecc ,nil ;
};func (_bfde *GenericRegion )parseHeader ()(_cbaf error ){_ge .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052I\u0043\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0050\u0061\u0072s\u0069\u006e\u0067\u0048\u0065\u0061\u0064e\u0072\u002e\u002e\u002e");
defer func (){if _cbaf !=nil {_ge .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0047\u0049\u004f\u004e]\u0020\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0048\u0065\u0061\u0064\u0065r\u0020\u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u0020\u0077\u0069th\u0020\u0065\u0072\u0072\u006f\u0072\u002e\u0020\u0025\u0076",_cbaf );
}else {_ge .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049C\u002d\u0052\u0045G\u0049\u004f\u004e]\u0020\u0050a\u0072\u0073\u0069\u006e\u0067\u0048e\u0061de\u0072\u0020\u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u0020\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0066\u0075\u006c\u006c\u0079\u002e\u002e\u002e");
};}();var (_def int ;_ddf uint64 ;);if _cbaf =_bfde .RegionSegment .parseHeader ();_cbaf !=nil {return _cbaf ;};if _ ,_cbaf =_bfde ._fddcc .ReadBits (3);_cbaf !=nil {return _cbaf ;};_def ,_cbaf =_bfde ._fddcc .ReadBit ();if _cbaf !=nil {return _cbaf ;};
if _def ==1{_bfde .UseExtTemplates =true ;};_def ,_cbaf =_bfde ._fddcc .ReadBit ();if _cbaf !=nil {return _cbaf ;};if _def ==1{_bfde .IsTPGDon =true ;};_ddf ,_cbaf =_bfde ._fddcc .ReadBits (2);if _cbaf !=nil {return _cbaf ;};_bfde .GBTemplate =byte (_ddf &0xf);
_def ,_cbaf =_bfde ._fddcc .ReadBit ();if _cbaf !=nil {return _cbaf ;};if _def ==1{_bfde .IsMMREncoded =true ;};if !_bfde .IsMMREncoded {_bgee :=1;if _bfde .GBTemplate ==0{_bgee =4;if _bfde .UseExtTemplates {_bgee =12;};};if _cbaf =_bfde .readGBAtPixels (_bgee );
_cbaf !=nil {return _cbaf ;};};if _cbaf =_bfde .computeSegmentDataStructure ();_cbaf !=nil {return _cbaf ;};_ge .Log .Trace ("\u0025\u0073",_bfde );return nil ;};func (_afge *SymbolDictionary )setCodingStatistics ()error {if _afge ._cdfce ==nil {_afge ._cdfce =_ae .NewStats (512,1);
};if _afge ._gcab ==nil {_afge ._gcab =_ae .NewStats (512,1);};if _afge ._beae ==nil {_afge ._beae =_ae .NewStats (512,1);};if _afge ._ebgf ==nil {_afge ._ebgf =_ae .NewStats (512,1);};if _afge ._gcfac ==nil {_afge ._gcfac =_ae .NewStats (512,1);};if _afge .UseRefinementAggregation &&_afge ._efg ==nil {_afge ._efg =_ae .NewStats (1<<uint (_afge ._dbce ),1);
_afge ._fag =_ae .NewStats (512,1);_afge ._cddc =_ae .NewStats (512,1);};if _afge ._bgbe ==nil {_afge ._bgbe =_ae .NewStats (65536,1);};if _afge ._daed ==nil {var _baed error ;_afge ._daed ,_baed =_ae .New (_afge ._gaeb );if _baed !=nil {return _baed ;
};};return nil ;};func (_cddbb *TextRegion )decodeRdw ()(int64 ,error ){const _cbga ="\u0064e\u0063\u006f\u0064\u0065\u0052\u0064w";if _cddbb .IsHuffmanEncoded {if _cddbb .SbHuffRDWidth ==3{if _cddbb ._fdcf ==nil {var (_gbfb int ;_ggfe error ;);if _cddbb .SbHuffFS ==3{_gbfb ++;
};if _cddbb .SbHuffDS ==3{_gbfb ++;};if _cddbb .SbHuffDT ==3{_gbfb ++;};_cddbb ._fdcf ,_ggfe =_cddbb .getUserTable (_gbfb );if _ggfe !=nil {return 0,_ff .Wrap (_ggfe ,_cbga ,"");};};return _cddbb ._fdcf .Decode (_cddbb ._cgbc );};_cfec ,_dfeb :=_aee .GetStandardTable (14+int (_cddbb .SbHuffRDWidth ));
if _dfeb !=nil {return 0,_ff .Wrap (_dfeb ,_cbga ,"");};return _cfec .Decode (_cddbb ._cgbc );};_aaab ,_deee :=_cddbb ._afef .DecodeInt (_cddbb ._afcb );if _deee !=nil {return 0,_ff .Wrap (_deee ,_cbga ,"");};return int64 (_aaab ),nil ;};var (_ Regioner =&TextRegion {};
_ Segmenter =&TextRegion {};);func (_afca *SymbolDictionary )encodeRefinementATFlags (_bbfc _ed .BinaryWriter )(_fgefa int ,_ffcef error ){const _ccdd ="\u0065\u006e\u0063od\u0065\u0052\u0065\u0066\u0069\u006e\u0065\u006d\u0065\u006e\u0074\u0041\u0054\u0046\u006c\u0061\u0067\u0073";
if !_afca .UseRefinementAggregation ||_afca .SdrTemplate !=0{return 0,nil ;};for _geaa :=0;_geaa < 2;_geaa ++{if _ffcef =_bbfc .WriteByte (byte (_afca .SdrATX [_geaa ]));_ffcef !=nil {return _fgefa ,_ff .Wrapf (_ffcef ,_ccdd ,"\u0053\u0064\u0072\u0041\u0054\u0058\u005b\u0025\u0064\u005d",_geaa );
};_fgefa ++;if _ffcef =_bbfc .WriteByte (byte (_afca .SdrATY [_geaa ]));_ffcef !=nil {return _fgefa ,_ff .Wrapf (_ffcef ,_ccdd ,"\u0053\u0064\u0072\u0041\u0054\u0059\u005b\u0025\u0064\u005d",_geaa );};_fgefa ++;};return _fgefa ,nil ;};type Segmenter interface{Init (_ffcd *Header ,_dcbd *_ed .Reader )error ;
};func (_gab *GenericRegion )decodeTemplate0a (_gfa ,_dcag ,_cgd int ,_fdce ,_fgc int )(_dbbb error ){const _faa ="\u0064\u0065c\u006f\u0064\u0065T\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0030\u0061";var (_cgbb ,_gefg int ;_aggf ,_ffb int ;_eeae byte ;
_cdbb int ;);if _gfa >=1{_eeae ,_dbbb =_gab .Bitmap .GetByte (_fgc );if _dbbb !=nil {return _ff .Wrap (_dbbb ,_faa ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00201");};_aggf =int (_eeae );};if _gfa >=2{_eeae ,_dbbb =_gab .Bitmap .GetByte (_fgc -_gab .Bitmap .RowStride );
if _dbbb !=nil {return _ff .Wrap (_dbbb ,_faa ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00202");};_ffb =int (_eeae )<<6;};_cgbb =(_aggf &0xf0)|(_ffb &0x3800);for _abaf :=0;_abaf < _cgd ;_abaf =_cdbb {var (_cgfc byte ;_fbdc int ;);_cdbb =_abaf +8;if _bafa :=_dcag -_abaf ;
_bafa > 8{_fbdc =8;}else {_fbdc =_bafa ;};if _gfa > 0{_aggf <<=8;if _cdbb < _dcag {_eeae ,_dbbb =_gab .Bitmap .GetByte (_fgc +1);if _dbbb !=nil {return _ff .Wrap (_dbbb ,_faa ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0030");};_aggf |=int (_eeae );};
};if _gfa > 1{_aca :=_fgc -_gab .Bitmap .RowStride +1;_ffb <<=8;if _cdbb < _dcag {_eeae ,_dbbb =_gab .Bitmap .GetByte (_aca );if _dbbb !=nil {return _ff .Wrap (_dbbb ,_faa ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0031");};_ffb |=int (_eeae )<<6;}else {_ffb |=0;
};};for _cbce :=0;_cbce < _fbdc ;_cbce ++{_bfa :=uint (7-_cbce );if _gab ._eeg {_gefg =_gab .overrideAtTemplate0a (_cgbb ,_abaf +_cbce ,_gfa ,int (_cgfc ),_cbce ,int (_bfa ));_gab ._cfb .SetIndex (int32 (_gefg ));}else {_gab ._cfb .SetIndex (int32 (_cgbb ));
};var _afdc int ;_afdc ,_dbbb =_gab ._bde .DecodeBit (_gab ._cfb );if _dbbb !=nil {return _ff .Wrap (_dbbb ,_faa ,"");};_cgfc |=byte (_afdc )<<_bfa ;_cgbb =((_cgbb &0x7bf7)<<1)|_afdc |((_aggf >>_bfa )&0x10)|((_ffb >>_bfa )&0x800);};if _dcg :=_gab .Bitmap .SetByte (_fdce ,_cgfc );
_dcg !=nil {return _ff .Wrap (_dcg ,_faa ,"");};_fdce ++;_fgc ++;};return nil ;};func (_eefc *TextRegion )decodeDfs ()(int64 ,error ){if _eefc .IsHuffmanEncoded {if _eefc .SbHuffFS ==3{if _eefc ._fcdd ==nil {var _cbcg error ;_eefc ._fcdd ,_cbcg =_eefc .getUserTable (0);
if _cbcg !=nil {return 0,_cbcg ;};};return _eefc ._fcdd .Decode (_eefc ._cgbc );};_aaff ,_aadc :=_aee .GetStandardTable (6+int (_eefc .SbHuffFS ));if _aadc !=nil {return 0,_aadc ;};return _aaff .Decode (_eefc ._cgbc );};_cacaa ,_aeedb :=_eefc ._afef .DecodeInt (_eefc ._cagd );
if _aeedb !=nil {return 0,_aeedb ;};return int64 (_cacaa ),nil ;};func (_baef *SymbolDictionary )decodeHeightClassDeltaHeight ()(int64 ,error ){if _baef .IsHuffmanEncoded {return _baef .decodeHeightClassDeltaHeightWithHuffman ();};_fbgf ,_dgfb :=_baef ._daed .DecodeInt (_baef ._gcab );
if _dgfb !=nil {return 0,_dgfb ;};return int64 (_fbgf ),nil ;};type GenericRefinementRegion struct{_cf templater ;_eb templater ;_ebe *_ed .Reader ;_ga *Header ;RegionInfo *RegionSegment ;IsTPGROn bool ;TemplateID int8 ;Template templater ;GrAtX []int8 ;
GrAtY []int8 ;RegionBitmap *_gf .Bitmap ;ReferenceBitmap *_gf .Bitmap ;ReferenceDX int32 ;ReferenceDY int32 ;_ce *_ae .Decoder ;_ebb *_ae .DecoderStats ;_fe bool ;_ee []bool ;};func (_fgcbd *PatternDictionary )computeSegmentDataStructure ()error {_fgcbd .DataOffset =_fgcbd ._dface .AbsolutePosition ();
_fgcbd .DataHeaderLength =_fgcbd .DataOffset -_fgcbd .DataHeaderOffset ;_fgcbd .DataLength =int64 (_fgcbd ._dface .AbsoluteLength ())-_fgcbd .DataHeaderLength ;return nil ;};func (_cfcb *SymbolDictionary )setRefinementAtPixels ()error {if !_cfcb .UseRefinementAggregation ||_cfcb .SdrTemplate !=0{return nil ;
};if _gadd :=_cfcb .readRefinementAtPixels (2);_gadd !=nil {return _gadd ;};return nil ;};type OrganizationType uint8 ;func (_da *GenericRefinementRegion )decodeOptimized (_ab ,_dga ,_be ,_bga ,_gea ,_gfc ,_ace int )error {var (_dad error ;_aed int ;_gcb int ;
);_fd :=_ab -int (_da .ReferenceDY );if _gcd :=int (-_da .ReferenceDX );_gcd > 0{_aed =_gcd ;};_adc :=_da .ReferenceBitmap .GetByteIndex (_aed ,_fd );if _da .ReferenceDX > 0{_gcb =int (_da .ReferenceDX );};_fbc :=_da .RegionBitmap .GetByteIndex (_gcb ,_ab );
switch _da .TemplateID {case 0:_dad =_da .decodeTemplate (_ab ,_dga ,_be ,_bga ,_gea ,_gfc ,_ace ,_fbc ,_fd ,_adc ,_da ._cf );case 1:_dad =_da .decodeTemplate (_ab ,_dga ,_be ,_bga ,_gea ,_gfc ,_ace ,_fbc ,_fd ,_adc ,_da ._eb );};return _dad ;};func (_afgd *TableSegment )Init (h *Header ,r *_ed .Reader )error {_afgd ._gdcb =r ;
return _afgd .parseHeader ();};func (_bab *GenericRegion )Init (h *Header ,r *_ed .Reader )error {_bab .RegionSegment =NewRegionSegment (r );_bab ._fddcc =r ;return _bab .parseHeader ();};func (_gbe *GenericRegion )decodeTemplate0b (_bba ,_cfg ,_ebbb int ,_cdc ,_gcca int )(_accc error ){const _abaa ="\u0064\u0065c\u006f\u0064\u0065T\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0030\u0062";
var (_caf ,_cbdf int ;_beba ,_ffbb int ;_cfd byte ;_gbeb int ;);if _bba >=1{_cfd ,_accc =_gbe .Bitmap .GetByte (_gcca );if _accc !=nil {return _ff .Wrap (_accc ,_abaa ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00201");};_beba =int (_cfd );};if _bba >=2{_cfd ,_accc =_gbe .Bitmap .GetByte (_gcca -_gbe .Bitmap .RowStride );
if _accc !=nil {return _ff .Wrap (_accc ,_abaa ,"\u006ci\u006e\u0065\u0020\u003e\u003d\u00202");};_ffbb =int (_cfd )<<6;};_caf =(_beba &0xf0)|(_ffbb &0x3800);for _aceg :=0;_aceg < _ebbb ;_aceg =_gbeb {var (_gdc byte ;_abgd int ;);_gbeb =_aceg +8;if _gge :=_cfg -_aceg ;
_gge > 8{_abgd =8;}else {_abgd =_gge ;};if _bba > 0{_beba <<=8;if _gbeb < _cfg {_cfd ,_accc =_gbe .Bitmap .GetByte (_gcca +1);if _accc !=nil {return _ff .Wrap (_accc ,_abaa ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0030");};_beba |=int (_cfd );};};
if _bba > 1{_ffbb <<=8;if _gbeb < _cfg {_cfd ,_accc =_gbe .Bitmap .GetByte (_gcca -_gbe .Bitmap .RowStride +1);if _accc !=nil {return _ff .Wrap (_accc ,_abaa ,"\u006c\u0069\u006e\u0065\u0020\u003e\u0020\u0031");};_ffbb |=int (_cfd )<<6;};};for _ege :=0;
_ege < _abgd ;_ege ++{_dgg :=uint (7-_ege );if _gbe ._eeg {_cbdf =_gbe .overrideAtTemplate0b (_caf ,_aceg +_ege ,_bba ,int (_gdc ),_ege ,int (_dgg ));_gbe ._cfb .SetIndex (int32 (_cbdf ));}else {_gbe ._cfb .SetIndex (int32 (_caf ));};var _dfa int ;_dfa ,_accc =_gbe ._bde .DecodeBit (_gbe ._cfb );
if _accc !=nil {return _ff .Wrap (_accc ,_abaa ,"");};_gdc |=byte (_dfa <<_dgg );_caf =((_caf &0x7bf7)<<1)|_dfa |((_beba >>_dgg )&0x10)|((_ffbb >>_dgg )&0x800);};if _bbcb :=_gbe .Bitmap .SetByte (_cdc ,_gdc );_bbcb !=nil {return _ff .Wrap (_bbcb ,_abaa ,"");
};_cdc ++;_gcca ++;};return nil ;};func (_abcc *GenericRegion )GetRegionBitmap ()(_aaa *_gf .Bitmap ,_dec error ){if _abcc .Bitmap !=nil {return _abcc .Bitmap ,nil ;};if _abcc .IsMMREncoded {if _abcc ._gbce ==nil {_abcc ._gbce ,_dec =_ac .New (_abcc ._fddcc ,int (_abcc .RegionSegment .BitmapWidth ),int (_abcc .RegionSegment .BitmapHeight ),_abcc .DataOffset ,_abcc .DataLength );
if _dec !=nil {return nil ,_dec ;};};_abcc .Bitmap ,_dec =_abcc ._gbce .UncompressMMR ();return _abcc .Bitmap ,_dec ;};if _dec =_abcc .updateOverrideFlags ();_dec !=nil {return nil ,_dec ;};var _dccg int ;if _abcc ._bde ==nil {_abcc ._bde ,_dec =_ae .New (_abcc ._fddcc );
if _dec !=nil {return nil ,_dec ;};};if _abcc ._cfb ==nil {_abcc ._cfb =_ae .NewStats (65536,1);};_abcc .Bitmap =_gf .New (int (_abcc .RegionSegment .BitmapWidth ),int (_abcc .RegionSegment .BitmapHeight ));_baa :=int (uint32 (_abcc .Bitmap .Width +7)&(^uint32 (7)));
for _adbg :=0;_adbg < _abcc .Bitmap .Height ;_adbg ++{if _abcc .IsTPGDon {var _egdf int ;_egdf ,_dec =_abcc .decodeSLTP ();if _dec !=nil {return nil ,_dec ;};_dccg ^=_egdf ;};if _dccg ==1{if _adbg > 0{if _dec =_abcc .copyLineAbove (_adbg );_dec !=nil {return nil ,_dec ;
};};}else {if _dec =_abcc .decodeLine (_adbg ,_abcc .Bitmap .Width ,_baa );_dec !=nil {return nil ,_dec ;};};};return _abcc .Bitmap ,nil ;};func (_afc *GenericRefinementRegion )decodeTypicalPredictedLineTemplate0 (_abd ,_bfd ,_dae ,_ggf ,_cb ,_fbd ,_dce ,_dbb ,_caa int )error {var (_abf ,_aae ,_aec ,_gdd ,_gfd ,_bge int ;
_gcf byte ;_acf error ;);if _abd > 0{_gcf ,_acf =_afc .RegionBitmap .GetByte (_dce -_dae );if _acf !=nil {return _acf ;};_aec =int (_gcf );};if _dbb > 0&&_dbb <=_afc .ReferenceBitmap .Height {_gcf ,_acf =_afc .ReferenceBitmap .GetByte (_caa -_ggf +_fbd );
if _acf !=nil {return _acf ;};_gdd =int (_gcf )<<4;};if _dbb >=0&&_dbb < _afc .ReferenceBitmap .Height {_gcf ,_acf =_afc .ReferenceBitmap .GetByte (_caa +_fbd );if _acf !=nil {return _acf ;};_gfd =int (_gcf )<<1;};if _dbb > -2&&_dbb < _afc .ReferenceBitmap .Height -1{_gcf ,_acf =_afc .ReferenceBitmap .GetByte (_caa +_ggf +_fbd );
if _acf !=nil {return _acf ;};_bge =int (_gcf );};_abf =((_aec >>5)&0x6)|((_bge >>2)&0x30)|(_gfd &0x180)|(_gdd &0xc00);var _eg int ;for _bb :=0;_bb < _cb ;_bb =_eg {var _fbe int ;_eg =_bb +8;var _aeed int ;if _aeed =_bfd -_bb ;_aeed > 8{_aeed =8;};_fce :=_eg < _bfd ;
_abg :=_eg < _afc .ReferenceBitmap .Width ;_agb :=_fbd +1;if _abd > 0{_gcf =0;if _fce {_gcf ,_acf =_afc .RegionBitmap .GetByte (_dce -_dae +1);if _acf !=nil {return _acf ;};};_aec =(_aec <<8)|int (_gcf );};if _dbb > 0&&_dbb <=_afc .ReferenceBitmap .Height {var _gec int ;
if _abg {_gcf ,_acf =_afc .ReferenceBitmap .GetByte (_caa -_ggf +_agb );if _acf !=nil {return _acf ;};_gec =int (_gcf )<<4;};_gdd =(_gdd <<8)|_gec ;};if _dbb >=0&&_dbb < _afc .ReferenceBitmap .Height {var _cdb int ;if _abg {_gcf ,_acf =_afc .ReferenceBitmap .GetByte (_caa +_agb );
if _acf !=nil {return _acf ;};_cdb =int (_gcf )<<1;};_gfd =(_gfd <<8)|_cdb ;};if _dbb > -2&&_dbb < (_afc .ReferenceBitmap .Height -1){_gcf =0;if _abg {_gcf ,_acf =_afc .ReferenceBitmap .GetByte (_caa +_ggf +_agb );if _acf !=nil {return _acf ;};};_bge =(_bge <<8)|int (_gcf );
};for _fdg :=0;_fdg < _aeed ;_fdg ++{var _cbf int ;_adb :=false ;_bed :=(_abf >>4)&0x1ff;if _bed ==0x1ff{_adb =true ;_cbf =1;}else if _bed ==0x00{_adb =true ;};if !_adb {if _afc ._fe {_aae =_afc .overrideAtTemplate0 (_abf ,_bb +_fdg ,_abd ,_fbe ,_fdg );
_afc ._ebb .SetIndex (int32 (_aae ));}else {_afc ._ebb .SetIndex (int32 (_abf ));};_cbf ,_acf =_afc ._ce .DecodeBit (_afc ._ebb );if _acf !=nil {return _acf ;};};_ffd :=uint (7-_fdg );_fbe |=_cbf <<_ffd ;_abf =((_abf &0xdb6)<<1)|_cbf |(_aec >>_ffd +5)&0x002|((_bge >>_ffd +2)&0x010)|((_gfd >>_ffd )&0x080)|((_gdd >>_ffd )&0x400);
};_acf =_afc .RegionBitmap .SetByte (_dce ,byte (_fbe ));if _acf !=nil {return _acf ;};_dce ++;_caa ++;};return nil ;};func (_afb *GenericRegion )decodeSLTP ()(int ,error ){switch _afb .GBTemplate {case 0:_afb ._cfb .SetIndex (0x9B25);case 1:_afb ._cfb .SetIndex (0x795);
case 2:_afb ._cfb .SetIndex (0xE5);case 3:_afb ._cfb .SetIndex (0x195);};return _afb ._bde .DecodeBit (_afb ._cfb );};type Header struct{SegmentNumber uint32 ;Type Type ;RetainFlag bool ;PageAssociation int ;PageAssociationFieldSize bool ;RTSegments []*Header ;
HeaderLength int64 ;SegmentDataLength uint64 ;SegmentDataStartOffset uint64 ;Reader *_ed .Reader ;SegmentData Segmenter ;RTSNumbers []int ;RetainBits []uint8 ;};func (_cffg *TextRegion )decodeCurrentT ()(int64 ,error ){if _cffg .SbStrips !=1{if _cffg .IsHuffmanEncoded {_geba ,_ecgfa :=_cffg ._cgbc .ReadBits (byte (_cffg .LogSBStrips ));
return int64 (_geba ),_ecgfa ;};_gffba ,_cagg :=_cffg ._afef .DecodeInt (_cffg ._gggeg );if _cagg !=nil {return 0,_cagg ;};return int64 (_gffba ),nil ;};return 0,nil ;};func (_egec *Header )pageSize ()uint {if _egec .PageAssociation <=255{return 1;};return 4;
};func (_ecbfe *SymbolDictionary )setAtPixels ()error {if _ecbfe .IsHuffmanEncoded {return nil ;};_cefb :=1;if _ecbfe .SdTemplate ==0{_cefb =4;};if _dde :=_ecbfe .readAtPixels (_cefb );_dde !=nil {return _dde ;};return nil ;};func (_dbab *PageInformationSegment )readCombinationOperatorOverrideAllowed ()error {_aeac ,_agf :=_dbab ._dcaf .ReadBit ();
if _agf !=nil {return _agf ;};if _aeac ==1{_dbab ._fgeb =true ;};return nil ;};func (_gcfag *TextRegion )readHuffmanFlags ()error {var (_deea int ;_gebb uint64 ;_cfaaf error ;);_ ,_cfaaf =_gcfag ._cgbc .ReadBit ();if _cfaaf !=nil {return _cfaaf ;};_deea ,_cfaaf =_gcfag ._cgbc .ReadBit ();
if _cfaaf !=nil {return _cfaaf ;};_gcfag .SbHuffRSize =int8 (_deea );_gebb ,_cfaaf =_gcfag ._cgbc .ReadBits (2);if _cfaaf !=nil {return _cfaaf ;};_gcfag .SbHuffRDY =int8 (_gebb )&0xf;_gebb ,_cfaaf =_gcfag ._cgbc .ReadBits (2);if _cfaaf !=nil {return _cfaaf ;
};_gcfag .SbHuffRDX =int8 (_gebb )&0xf;_gebb ,_cfaaf =_gcfag ._cgbc .ReadBits (2);if _cfaaf !=nil {return _cfaaf ;};_gcfag .SbHuffRDHeight =int8 (_gebb )&0xf;_gebb ,_cfaaf =_gcfag ._cgbc .ReadBits (2);if _cfaaf !=nil {return _cfaaf ;};_gcfag .SbHuffRDWidth =int8 (_gebb )&0xf;
_gebb ,_cfaaf =_gcfag ._cgbc .ReadBits (2);if _cfaaf !=nil {return _cfaaf ;};_gcfag .SbHuffDT =int8 (_gebb )&0xf;_gebb ,_cfaaf =_gcfag ._cgbc .ReadBits (2);if _cfaaf !=nil {return _cfaaf ;};_gcfag .SbHuffDS =int8 (_gebb )&0xf;_gebb ,_cfaaf =_gcfag ._cgbc .ReadBits (2);
if _cfaaf !=nil {return _cfaaf ;};_gcfag .SbHuffFS =int8 (_gebb )&0xf;return nil ;};func (_bfc *GenericRegion )readGBAtPixels (_ceeg int )error {const _fefc ="\u0072\u0065\u0061\u0064\u0047\u0042\u0041\u0074\u0050i\u0078\u0065\u006c\u0073";_bfc .GBAtX =make ([]int8 ,_ceeg );
_bfc .GBAtY =make ([]int8 ,_ceeg );for _fbda :=0;_fbda < _ceeg ;_fbda ++{_fdb ,_gbad :=_bfc ._fddcc .ReadByte ();if _gbad !=nil {return _ff .Wrapf (_gbad ,_fefc ,"\u0058\u0020\u0061t\u0020\u0069\u003a\u0020\u0027\u0025\u0064\u0027",_fbda );};_bfc .GBAtX [_fbda ]=int8 (_fdb );
_fdb ,_gbad =_bfc ._fddcc .ReadByte ();if _gbad !=nil {return _ff .Wrapf (_gbad ,_fefc ,"\u0059\u0020\u0061t\u0020\u0069\u003a\u0020\u0027\u0025\u0064\u0027",_fbda );};_bfc .GBAtY [_fbda ]=int8 (_fdb );};return nil ;};func (_bdcg *HalftoneRegion )checkInput ()error {if _bdcg .IsMMREncoded {if _bdcg .HTemplate !=0{_ge .Log .Debug ("\u0048\u0054\u0065\u006d\u0070l\u0061\u0074\u0065\u0020\u003d\u0020\u0025\u0064\u0020\u0073\u0068\u006f\u0075l\u0064\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0030",_bdcg .HTemplate );
};if _bdcg .HSkipEnabled {_ge .Log .Debug ("\u0048\u0053\u006b\u0069\u0070\u0045\u006e\u0061\u0062\u006c\u0065\u0064\u0020\u0030\u0020\u0025\u0076\u0020(\u0073\u0068\u006f\u0075\u006c\u0064\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020v\u0061\u006c\u0075\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u0029",_bdcg .HSkipEnabled );
};};return nil ;};func NewHeader (d Documenter ,r *_ed .Reader ,offset int64 ,organizationType OrganizationType )(*Header ,error ){_ccdc :=&Header {Reader :r };if _dacc :=_ccdc .parse (d ,r ,offset ,organizationType );_dacc !=nil {return nil ,_ff .Wrap (_dacc ,"\u004ee\u0077\u0048\u0065\u0061\u0064\u0065r","");
};return _ccdc ,nil ;};type EncodeInitializer interface{InitEncode ();};type RegionSegment struct{_egdda *_ed .Reader ;BitmapWidth uint32 ;BitmapHeight uint32 ;XLocation uint32 ;YLocation uint32 ;CombinaionOperator _gf .CombinationOperator ;};func (_dgdc *PageInformationSegment )readIsLossless ()error {_gaed ,_edag :=_dgdc ._dcaf .ReadBit ();
if _edag !=nil {return _edag ;};if _gaed ==1{_dgdc .IsLossless =true ;};return nil ;};func (_edgc *SymbolDictionary )readAtPixels (_cbg int )error {_edgc .SdATX =make ([]int8 ,_cbg );_edgc .SdATY =make ([]int8 ,_cbg );var (_fdbg byte ;_faag error ;);for _gbde :=0;
_gbde < _cbg ;_gbde ++{_fdbg ,_faag =_edgc ._gaeb .ReadByte ();if _faag !=nil {return _faag ;};_edgc .SdATX [_gbde ]=int8 (_fdbg );_fdbg ,_faag =_edgc ._gaeb .ReadByte ();if _faag !=nil {return _faag ;};_edgc .SdATY [_gbde ]=int8 (_fdbg );};return nil ;
};func (_ca *EndOfStripe )LineNumber ()int {return _ca ._dbc };func (_bbff *TextRegion )initSymbols ()error {const _beef ="i\u006e\u0069\u0074\u0053\u0079\u006d\u0062\u006f\u006c\u0073";for _ ,_edca :=range _bbff .Header .RTSegments {if _edca ==nil {return _ff .Error (_beef ,"\u006e\u0069\u006c\u0020\u0073\u0065\u0067\u006de\u006e\u0074\u0020pr\u006f\u0076\u0069\u0064\u0065\u0064 \u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0074\u0065\u0078\u0074\u0020\u0072\u0065g\u0069\u006f\u006e\u0020\u0053\u0079\u006d\u0062o\u006c\u0073");
};if _edca .Type ==0{_ebag ,_afgef :=_edca .GetSegmentData ();if _afgef !=nil {return _ff .Wrap (_afgef ,_beef ,"");};_cede ,_afa :=_ebag .(*SymbolDictionary );if !_afa {return _ff .Error (_beef ,"\u0072e\u0066\u0065r\u0072\u0065\u0064 \u0054\u006f\u0020\u0053\u0065\u0067\u006de\u006e\u0074\u0020\u0069\u0073\u0020n\u006f\u0074\u0020\u0061\u0020\u0053\u0079\u006d\u0062\u006f\u006cD\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079");
};_cede ._efg =_bbff ._aecf ;_bdaa ,_afgef :=_cede .GetDictionary ();if _afgef !=nil {return _ff .Wrap (_afgef ,_beef ,"");};_bbff .Symbols =append (_bbff .Symbols ,_bdaa ...);};};_bbff .NumberOfSymbols =uint32 (len (_bbff .Symbols ));return nil ;};func (_dbdg *TableSegment )StreamReader ()*_ed .Reader {return _dbdg ._gdcb };
var _ templater =&template0 {};func (_bcda *PatternDictionary )setGbAtPixels (){if _bcda .HDTemplate ==0{_bcda .GBAtX =make ([]int8 ,4);_bcda .GBAtY =make ([]int8 ,4);_bcda .GBAtX [0]=-int8 (_bcda .HdpWidth );_bcda .GBAtY [0]=0;_bcda .GBAtX [1]=-3;_bcda .GBAtY [1]=-1;
_bcda .GBAtX [2]=2;_bcda .GBAtY [2]=-2;_bcda .GBAtX [3]=-2;_bcda .GBAtY [3]=-2;}else {_bcda .GBAtX =[]int8 {-int8 (_bcda .HdpWidth )};_bcda .GBAtY =[]int8 {0};};};type template0 struct{};func (_cabacg *TextRegion )Init (header *Header ,r *_ed .Reader )error {_cabacg .Header =header ;
_cabacg ._cgbc =r ;_cabacg .RegionInfo =NewRegionSegment (_cabacg ._cgbc );return _cabacg .parseHeader ();};func (_cdaf *PageInformationSegment )Encode (w _ed .BinaryWriter )(_begb int ,_cfbe error ){const _abgb ="\u0050\u0061g\u0065\u0049\u006e\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u002e\u0045\u006eco\u0064\u0065";
_cffa :=make ([]byte ,4);_dg .BigEndian .PutUint32 (_cffa ,uint32 (_cdaf .PageBMWidth ));_begb ,_cfbe =w .Write (_cffa );if _cfbe !=nil {return _begb ,_ff .Wrap (_cfbe ,_abgb ,"\u0077\u0069\u0064t\u0068");};_dg .BigEndian .PutUint32 (_cffa ,uint32 (_cdaf .PageBMHeight ));
var _fca int ;_fca ,_cfbe =w .Write (_cffa );if _cfbe !=nil {return _fca +_begb ,_ff .Wrap (_cfbe ,_abgb ,"\u0068\u0065\u0069\u0067\u0068\u0074");};_begb +=_fca ;_dg .BigEndian .PutUint32 (_cffa ,uint32 (_cdaf .ResolutionX ));_fca ,_cfbe =w .Write (_cffa );
if _cfbe !=nil {return _fca +_begb ,_ff .Wrap (_cfbe ,_abgb ,"\u0078\u0020\u0072e\u0073\u006f\u006c\u0075\u0074\u0069\u006f\u006e");};_begb +=_fca ;_dg .BigEndian .PutUint32 (_cffa ,uint32 (_cdaf .ResolutionY ));if _fca ,_cfbe =w .Write (_cffa );_cfbe !=nil {return _fca +_begb ,_ff .Wrap (_cfbe ,_abgb ,"\u0079\u0020\u0072e\u0073\u006f\u006c\u0075\u0074\u0069\u006f\u006e");
};_begb +=_fca ;if _cfbe =_cdaf .encodeFlags (w );_cfbe !=nil {return _begb ,_ff .Wrap (_cfbe ,_abgb ,"");};_begb ++;if _fca ,_cfbe =_cdaf .encodeStripingInformation (w );_cfbe !=nil {return _begb ,_ff .Wrap (_cfbe ,_abgb ,"");};_begb +=_fca ;return _begb ,nil ;
};func (_cgef *TextRegion )getUserTable (_ecfde int )(_aee .Tabler ,error ){const _bdab ="\u0067\u0065\u0074U\u0073\u0065\u0072\u0054\u0061\u0062\u006c\u0065";var _cbbg int ;for _ ,_bfdf :=range _cgef .Header .RTSegments {if _bfdf .Type ==53{if _cbbg ==_ecfde {_fcde ,_bebac :=_bfdf .GetSegmentData ();
if _bebac !=nil {return nil ,_bebac ;};_afgce ,_ddeb :=_fcde .(*TableSegment );if !_ddeb {_ge .Log .Debug (_e .Sprintf ("\u0073\u0065\u0067\u006d\u0065\u006e\u0074 \u0077\u0069\u0074h\u0020\u0054\u0079p\u0065\u00205\u0033\u0020\u002d\u0020\u0061\u006ed\u0020in\u0064\u0065\u0078\u003a\u0020\u0025\u0064\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0054\u0061\u0062\u006c\u0065\u0053\u0065\u0067\u006d\u0065\u006e\u0074",_bfdf .SegmentNumber ));
return nil ,_ff .Error (_bdab ,"\u0073\u0065\u0067\u006d\u0065\u006e\u0074 \u0077\u0069\u0074h\u0020\u0054\u0079\u0070e\u0020\u0035\u0033\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u002a\u0054\u0061\u0062\u006c\u0065\u0053\u0065\u0067\u006d\u0065\u006e\u0074");
};return _aee .NewEncodedTable (_afgce );};_cbbg ++;};};return nil ,nil ;};func (_bafg *TextRegion )checkInput ()error {const _afea ="\u0063\u0068\u0065\u0063\u006b\u0049\u006e\u0070\u0075\u0074";if !_bafg .UseRefinement {if _bafg .SbrTemplate !=0{_ge .Log .Debug ("\u0053\u0062\u0072Te\u006d\u0070\u006c\u0061\u0074\u0065\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0030");
_bafg .SbrTemplate =0;};};if _bafg .SbHuffFS ==2||_bafg .SbHuffRDWidth ==2||_bafg .SbHuffRDHeight ==2||_bafg .SbHuffRDX ==2||_bafg .SbHuffRDY ==2{return _ff .Error (_afea ,"h\u0075\u0066\u0066\u006d\u0061\u006e \u0066\u006c\u0061\u0067\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0073\u0020n\u006f\u0074\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064");
};if !_bafg .UseRefinement {if _bafg .SbHuffRSize !=0{_ge .Log .Debug ("\u0053\u0062\u0048uf\u0066\u0052\u0053\u0069\u007a\u0065\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u0030");_bafg .SbHuffRSize =0;};if _bafg .SbHuffRDY !=0{_ge .Log .Debug ("S\u0062\u0048\u0075\u0066fR\u0044Y\u0020\u0073\u0068\u006f\u0075l\u0064\u0020\u0062\u0065\u0020\u0030");
_bafg .SbHuffRDY =0;};if _bafg .SbHuffRDX !=0{_ge .Log .Debug ("S\u0062\u0048\u0075\u0066fR\u0044X\u0020\u0073\u0068\u006f\u0075l\u0064\u0020\u0062\u0065\u0020\u0030");_bafg .SbHuffRDX =0;};if _bafg .SbHuffRDWidth !=0{_ge .Log .Debug ("\u0053b\u0048\u0075\u0066\u0066R\u0044\u0057\u0069\u0064\u0074h\u0020s\u0068o\u0075\u006c\u0064\u0020\u0062\u0065\u00200");
_bafg .SbHuffRDWidth =0;};if _bafg .SbHuffRDHeight !=0{_ge .Log .Debug ("\u0053\u0062\u0048\u0075\u0066\u0066\u0052\u0044\u0048\u0065\u0069g\u0068\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020b\u0065\u0020\u0030");_bafg .SbHuffRDHeight =0;};};return nil ;
};func (_caeg *SymbolDictionary )setExportedSymbols (_gcec []int ){for _cfee :=uint32 (0);_cfee < _caeg ._cdeb +_caeg .NumberOfNewSymbols ;_cfee ++{if _gcec [_cfee ]==1{var _eeb *_gf .Bitmap ;if _cfee < _caeg ._cdeb {_eeb =_caeg ._cccf [_cfee ];}else {_eeb =_caeg ._bfbfa [_cfee -_caeg ._cdeb ];
};_ge .Log .Trace ("\u005bS\u0059\u004dB\u004f\u004c\u002d\u0044I\u0043\u0054\u0049O\u004e\u0041\u0052\u0059\u005d\u0020\u0041\u0064\u0064 E\u0078\u0070\u006fr\u0074\u0065d\u0053\u0079\u006d\u0062\u006f\u006c:\u0020\u0027%\u0073\u0027",_eeb );_caeg ._gfbg =append (_caeg ._gfbg ,_eeb );
};};};func _bgg (_ebbf *_ed .Reader ,_gdef *Header )*GenericRefinementRegion {return &GenericRefinementRegion {_ebe :_ebbf ,RegionInfo :NewRegionSegment (_ebbf ),_ga :_gdef ,_cf :&template0 {},_eb :&template1 {}};};type GenericRegion struct{_fddcc *_ed .Reader ;
DataHeaderOffset int64 ;DataHeaderLength int64 ;DataOffset int64 ;DataLength int64 ;RegionSegment *RegionSegment ;UseExtTemplates bool ;IsTPGDon bool ;GBTemplate byte ;IsMMREncoded bool ;UseMMR bool ;GBAtX []int8 ;GBAtY []int8 ;GBAtOverride []bool ;_eeg bool ;
Bitmap *_gf .Bitmap ;_bde *_ae .Decoder ;_cfb *_ae .DecoderStats ;_gbce *_ac .Decoder ;};func (_cbfg *TextRegion )decodeIb (_cefbb ,_bccg int64 )(*_gf .Bitmap ,error ){const _gbebc ="\u0064\u0065\u0063\u006f\u0064\u0065\u0049\u0062";var (_gfccd error ;
_gbdgd *_gf .Bitmap ;);if _cefbb ==0{if int (_bccg )> len (_cbfg .Symbols )-1{return nil ,_ff .Error (_gbebc ,"\u0064\u0065\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0049\u0042\u0020\u0062\u0069\u0074\u006d\u0061\u0070\u002e\u0020\u0069\u006e\u0064\u0065x\u0020\u006f\u0075\u0074\u0020o\u0066\u0020r\u0061\u006e\u0067\u0065");
};return _cbfg .Symbols [int (_bccg )],nil ;};var _fafc ,_cceg ,_gccbd ,_cecb int64 ;_fafc ,_gfccd =_cbfg .decodeRdw ();if _gfccd !=nil {return nil ,_ff .Wrap (_gfccd ,_gbebc ,"");};_cceg ,_gfccd =_cbfg .decodeRdh ();if _gfccd !=nil {return nil ,_ff .Wrap (_gfccd ,_gbebc ,"");
};_gccbd ,_gfccd =_cbfg .decodeRdx ();if _gfccd !=nil {return nil ,_ff .Wrap (_gfccd ,_gbebc ,"");};_cecb ,_gfccd =_cbfg .decodeRdy ();if _gfccd !=nil {return nil ,_ff .Wrap (_gfccd ,_gbebc ,"");};if _cbfg .IsHuffmanEncoded {if _ ,_gfccd =_cbfg .decodeSymInRefSize ();
_gfccd !=nil {return nil ,_ff .Wrap (_gfccd ,_gbebc ,"");};_cbfg ._cgbc .Align ();};_cgfcc :=_cbfg .Symbols [_bccg ];_aabef :=uint32 (_cgfcc .Width );_fcgc :=uint32 (_cgfcc .Height );_afgcb :=int32 (uint32 (_fafc )>>1)+int32 (_gccbd );_abdb :=int32 (uint32 (_cceg )>>1)+int32 (_cecb );
if _cbfg ._bgfd ==nil {_cbfg ._bgfd =_bgg (_cbfg ._cgbc ,nil );};_cbfg ._bgfd .setParameters (_cbfg ._bgeg ,_cbfg ._afef ,_cbfg .SbrTemplate ,_aabef +uint32 (_fafc ),_fcgc +uint32 (_cceg ),_cgfcc ,_afgcb ,_abdb ,false ,_cbfg .SbrATX ,_cbfg .SbrATY );_gbdgd ,_gfccd =_cbfg ._bgfd .GetRegionBitmap ();
if _gfccd !=nil {return nil ,_ff .Wrap (_gfccd ,_gbebc ,"\u0067\u0072\u0066");};if _cbfg .IsHuffmanEncoded {_cbfg ._cgbc .Align ();};return _gbdgd ,nil ;};func (_fegc *TextRegion )decodeSymbolInstances ()error {_afgeg ,_aadg :=_fegc .decodeStripT ();if _aadg !=nil {return _aadg ;
};var (_cfgg int64 ;_cadg uint32 ;);for _cadg < _fegc .NumberOfSymbolInstances {_fcgdb ,_eceg :=_fegc .decodeDT ();if _eceg !=nil {return _eceg ;};_afgeg +=_fcgdb ;var _fggf int64 ;_cdag :=true ;_fegc ._dea =0;for {if _cdag {_fggf ,_eceg =_fegc .decodeDfs ();
if _eceg !=nil {return _eceg ;};_cfgg +=_fggf ;_fegc ._dea =_cfgg ;_cdag =false ;}else {_gefd ,_ddcf :=_fegc .decodeIds ();if _gb .Is (_ddcf ,_b .ErrOOB ){break ;};if _ddcf !=nil {return _ddcf ;};if _cadg >=_fegc .NumberOfSymbolInstances {break ;};_fegc ._dea +=_gefd +int64 (_fegc .SbDsOffset );
};_beff ,_fcdg :=_fegc .decodeCurrentT ();if _fcdg !=nil {return _fcdg ;};_egad :=_afgeg +_beff ;_abaed ,_fcdg :=_fegc .decodeID ();if _fcdg !=nil {return _fcdg ;};_bdefa ,_fcdg :=_fegc .decodeRI ();if _fcdg !=nil {return _fcdg ;};_dffee ,_fcdg :=_fegc .decodeIb (_bdefa ,_abaed );
if _fcdg !=nil {return _fcdg ;};if _fcdg =_fegc .blit (_dffee ,_egad );_fcdg !=nil {return _fcdg ;};_cadg ++;};};return nil ;};func (_cfcg *GenericRefinementRegion )parseHeader ()(_dba error ){_ge .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0070\u0061\u0072s\u0069\u006e\u0067\u0020\u0048e\u0061\u0064e\u0072\u002e\u002e\u002e");
_eeaa :=_c .Now ();defer func (){if _dba ==nil {_ge .Log .Trace ("\u005b\u0047\u0045\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045G\u0049\u004f\u004e\u005d\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020h\u0065\u0061\u0064\u0065\u0072\u0020\u0066\u0069\u006e\u0069\u0073\u0068id\u0020\u0069\u006e\u003a\u0020\u0025\u0064\u0020\u006e\u0073",_c .Since (_eeaa ).Nanoseconds ());
}else {_ge .Log .Trace ("\u005b\u0047E\u004e\u0045\u0052\u0049\u0043\u002d\u0052\u0045\u0046\u002d\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u0020\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0068\u0065\u0061\u0064\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0073",_dba );
};}();if _dba =_cfcg .RegionInfo .parseHeader ();_dba !=nil {return _dba ;};_ ,_dba =_cfcg ._ebe .ReadBits (6);if _dba !=nil {return _dba ;};_cfcg .IsTPGROn ,_dba =_cfcg ._ebe .ReadBool ();if _dba !=nil {return _dba ;};var _fge int ;_fge ,_dba =_cfcg ._ebe .ReadBit ();
if _dba !=nil {return _dba ;};_cfcg .TemplateID =int8 (_fge );switch _cfcg .TemplateID {case 0:_cfcg .Template =_cfcg ._cf ;if _dba =_cfcg .readAtPixels ();_dba !=nil {return _dba ;};case 1:_cfcg .Template =_cfcg ._eb ;};return nil ;};func (_gebd *SymbolDictionary )setInSyms ()error {if _gebd .Header .RTSegments !=nil {return _gebd .retrieveImportSymbols ();
};_gebd ._cccf =make ([]*_gf .Bitmap ,0);return nil ;};func (_dagd *RegionSegment )Encode (w _ed .BinaryWriter )(_aebc int ,_cdba error ){const _gfee ="R\u0065g\u0069\u006f\u006e\u0053\u0065\u0067\u006d\u0065n\u0074\u002e\u0045\u006eco\u0064\u0065";_fafg :=make ([]byte ,4);
_dg .BigEndian .PutUint32 (_fafg ,_dagd .BitmapWidth );_aebc ,_cdba =w .Write (_fafg );if _cdba !=nil {return 0,_ff .Wrap (_cdba ,_gfee ,"\u0057\u0069\u0064t\u0068");};_dg .BigEndian .PutUint32 (_fafg ,_dagd .BitmapHeight );var _dcac int ;_dcac ,_cdba =w .Write (_fafg );
if _cdba !=nil {return 0,_ff .Wrap (_cdba ,_gfee ,"\u0048\u0065\u0069\u0067\u0068\u0074");};_aebc +=_dcac ;_dg .BigEndian .PutUint32 (_fafg ,_dagd .XLocation );_dcac ,_cdba =w .Write (_fafg );if _cdba !=nil {return 0,_ff .Wrap (_cdba ,_gfee ,"\u0058L\u006f\u0063\u0061\u0074\u0069\u006fn");
};_aebc +=_dcac ;_dg .BigEndian .PutUint32 (_fafg ,_dagd .YLocation );_dcac ,_cdba =w .Write (_fafg );if _cdba !=nil {return 0,_ff .Wrap (_cdba ,_gfee ,"\u0059L\u006f\u0063\u0061\u0074\u0069\u006fn");};_aebc +=_dcac ;if _cdba =w .WriteByte (byte (_dagd .CombinaionOperator )&0x07);
_cdba !=nil {return 0,_ff .Wrap (_cdba ,_gfee ,"c\u006fm\u0062\u0069\u006e\u0061\u0074\u0069\u006f\u006e \u006f\u0070\u0065\u0072at\u006f\u0072");};_aebc ++;return _aebc ,nil ;};type SymbolDictionary struct{_gaeb *_ed .Reader ;SdrTemplate int8 ;SdTemplate int8 ;
_bbfd bool ;_ebfg bool ;SdHuffAggInstanceSelection bool ;SdHuffBMSizeSelection int8 ;SdHuffDecodeWidthSelection int8 ;SdHuffDecodeHeightSelection int8 ;UseRefinementAggregation bool ;IsHuffmanEncoded bool ;SdATX []int8 ;SdATY []int8 ;SdrATX []int8 ;SdrATY []int8 ;
NumberOfExportedSymbols uint32 ;NumberOfNewSymbols uint32 ;Header *Header ;_cdeb uint32 ;_cccf []*_gf .Bitmap ;_dcbgd uint32 ;_bfbfa []*_gf .Bitmap ;_bfdbd _aee .Tabler ;_gedd _aee .Tabler ;_edg _aee .Tabler ;_bda _aee .Tabler ;_gfbg []*_gf .Bitmap ;_dfe []*_gf .Bitmap ;
_daed *_ae .Decoder ;_fabf *TextRegion ;_bafd *GenericRegion ;_gcac *GenericRefinementRegion ;_bgbe *_ae .DecoderStats ;_gcab *_ae .DecoderStats ;_beae *_ae .DecoderStats ;_ebgf *_ae .DecoderStats ;_gcfac *_ae .DecoderStats ;_fag *_ae .DecoderStats ;_cddc *_ae .DecoderStats ;
_cdfce *_ae .DecoderStats ;_efg *_ae .DecoderStats ;_dbce int8 ;_egbc *_gf .Bitmaps ;_ccbg []int ;_adf map[int ]int ;_ggdd bool ;};func (_gbafb *HalftoneRegion )shiftAndFill (_ada int )int {_ada >>=8;if _ada < 0{_aaaad :=int (_g .Log (float64 (_cefc (_ada )))/_g .Log (2));
_cfgf :=31-_aaaad ;for _bfe :=1;_bfe < _cfgf ;_bfe ++{_ada |=1<<uint (31-_bfe );};};return _ada ;};func (_daga *GenericRegion )String ()string {_dab :=&_a .Builder {};_dab .WriteString ("\u000a[\u0047E\u004e\u0045\u0052\u0049\u0043 \u0052\u0045G\u0049\u004f\u004e\u005d\u000a");
_dab .WriteString (_daga .RegionSegment .String ()+"\u000a");_dab .WriteString (_e .Sprintf ("\u0009\u002d\u0020Us\u0065\u0045\u0078\u0074\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u0073\u003a\u0020\u0025\u0076\u000a",_daga .UseExtTemplates ));_dab .WriteString (_e .Sprintf ("\u0009\u002d \u0049\u0073\u0054P\u0047\u0044\u006f\u006e\u003a\u0020\u0025\u0076\u000a",_daga .IsTPGDon ));
_dab .WriteString (_e .Sprintf ("\u0009-\u0020G\u0042\u0054\u0065\u006d\u0070l\u0061\u0074e\u003a\u0020\u0025\u0064\u000a",_daga .GBTemplate ));_dab .WriteString (_e .Sprintf ("\t\u002d \u0049\u0073\u004d\u004d\u0052\u0045\u006e\u0063o\u0064\u0065\u0064\u003a %\u0076\u000a",_daga .IsMMREncoded ));
_dab .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0047\u0042\u0041\u0074\u0058\u003a\u0020\u0025\u0076\u000a",_daga .GBAtX ));_dab .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0047\u0042\u0041\u0074\u0059\u003a\u0020\u0025\u0076\u000a",_daga .GBAtY ));
_dab .WriteString (_e .Sprintf ("\t\u002d \u0047\u0042\u0041\u0074\u004f\u0076\u0065\u0072r\u0069\u0064\u0065\u003a %\u0076\u000a",_daga .GBAtOverride ));return _dab .String ();};func (_accg *TextRegion )decodeID ()(int64 ,error ){if _accg .IsHuffmanEncoded {if _accg ._geggd ==nil {_begdc ,_dfgge :=_accg ._cgbc .ReadBits (byte (_accg ._eebg ));
return int64 (_begdc ),_dfgge ;};return _accg ._geggd .Decode (_accg ._cgbc );};return _accg ._afef .DecodeIAID (uint64 (_accg ._eebg ),_accg ._aecf );};func (_gdefg *Header )writeSegmentDataLength (_egfd _ed .BinaryWriter )(_aga int ,_fec error ){_bfbc :=make ([]byte ,4);
_dg .BigEndian .PutUint32 (_bfbc ,uint32 (_gdefg .SegmentDataLength ));if _aga ,_fec =_egfd .Write (_bfbc );_fec !=nil {return 0,_ff .Wrap (_fec ,"\u0048\u0065a\u0064\u0065\u0072\u002e\u0077\u0072\u0069\u0074\u0065\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0044\u0061\u0074\u0061\u004c\u0065ng\u0074\u0068","");
};return _aga ,nil ;};func (_deeg *TextRegion )createRegionBitmap ()error {_deeg .RegionBitmap =_gf .New (int (_deeg .RegionInfo .BitmapWidth ),int (_deeg .RegionInfo .BitmapHeight ));if _deeg .DefaultPixel !=0{_deeg .RegionBitmap .SetDefaultPixel ();};
return nil ;};func (_ggge *GenericRegion )writeGBAtPixels (_dgdd _ed .BinaryWriter )(_gceb int ,_ceb error ){const _dcdc ="\u0077r\u0069t\u0065\u0047\u0042\u0041\u0074\u0050\u0069\u0078\u0065\u006c\u0073";if _ggge .UseMMR {return 0,nil ;};_eff :=1;if _ggge .GBTemplate ==0{_eff =4;
}else if _ggge .UseExtTemplates {_eff =12;};if len (_ggge .GBAtX )!=_eff {return 0,_ff .Errorf (_dcdc ,"\u0067\u0062\u0020\u0061\u0074\u0020\u0070\u0061\u0069\u0072\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020d\u006f\u0065\u0073\u006e\u0027\u0074\u0020m\u0061\u0074\u0063\u0068\u0020\u0074\u006f\u0020\u0047\u0042\u0041t\u0058\u0020\u0073\u006c\u0069\u0063\u0065\u0020\u006c\u0065\u006e");
};if len (_ggge .GBAtY )!=_eff {return 0,_ff .Errorf (_dcdc ,"\u0067\u0062\u0020\u0061\u0074\u0020\u0070\u0061\u0069\u0072\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020d\u006f\u0065\u0073\u006e\u0027\u0074\u0020m\u0061\u0074\u0063\u0068\u0020\u0074\u006f\u0020\u0047\u0042\u0041t\u0059\u0020\u0073\u006c\u0069\u0063\u0065\u0020\u006c\u0065\u006e");
};for _bgf :=0;_bgf < _eff ;_bgf ++{if _ceb =_dgdd .WriteByte (byte (_ggge .GBAtX [_bgf ]));_ceb !=nil {return _gceb ,_ff .Wrap (_ceb ,_dcdc ,"w\u0072\u0069\u0074\u0065\u0020\u0047\u0042\u0041\u0074\u0058");};_gceb ++;if _ceb =_dgdd .WriteByte (byte (_ggge .GBAtY [_bgf ]));
_ceb !=nil {return _gceb ,_ff .Wrap (_ceb ,_dcdc ,"w\u0072\u0069\u0074\u0065\u0020\u0047\u0042\u0041\u0074\u0059");};_gceb ++;};return _gceb ,nil ;};func (_eca *TextRegion )GetRegionBitmap ()(*_gf .Bitmap ,error ){if _eca .RegionBitmap !=nil {return _eca .RegionBitmap ,nil ;
};if !_eca .IsHuffmanEncoded {if _cefef :=_eca .setCodingStatistics ();_cefef !=nil {return nil ,_cefef ;};};if _dgfe :=_eca .createRegionBitmap ();_dgfe !=nil {return nil ,_dgfe ;};if _ebaa :=_eca .decodeSymbolInstances ();_ebaa !=nil {return nil ,_ebaa ;
};return _eca .RegionBitmap ,nil ;};func (_dfaca *TextRegion )InitEncode (globalSymbolsMap ,localSymbolsMap map[int ]int ,comps []int ,inLL *_gf .Points ,symbols *_gf .Bitmaps ,classIDs *_db .IntSlice ,boxes *_gf .Boxes ,width ,height ,symBits int ){_dfaca .RegionInfo =&RegionSegment {BitmapWidth :uint32 (width ),BitmapHeight :uint32 (height )};
_dfaca ._ecgb =globalSymbolsMap ;_dfaca ._eccc =localSymbolsMap ;_dfaca ._bdgdf =comps ;_dfaca ._gaf =inLL ;_dfaca ._baaa =symbols ;_dfaca ._bbgd =classIDs ;_dfaca ._abec =boxes ;_dfaca ._cddcd =symBits ;};func (_dfbe *TextRegion )setParameters (_afgb *_ae .Decoder ,_fbgc ,_cagc bool ,_ebafd ,_ddcd uint32 ,_edfb uint32 ,_ebaag int8 ,_bfbfb uint32 ,_eeec int8 ,_bdgdff _gf .CombinationOperator ,_bgcf int8 ,_cccfd int16 ,_gaef ,_degfg ,_aadcg ,_abba ,_fffc ,_ggbe ,_egeb ,_bacf ,_fceg ,_gbcfd int8 ,_eaag ,_facf []int8 ,_befa []*_gf .Bitmap ,_egfb int8 ){_dfbe ._afef =_afgb ;
_dfbe .IsHuffmanEncoded =_fbgc ;_dfbe .UseRefinement =_cagc ;_dfbe .RegionInfo .BitmapWidth =_ebafd ;_dfbe .RegionInfo .BitmapHeight =_ddcd ;_dfbe .NumberOfSymbolInstances =_edfb ;_dfbe .SbStrips =_ebaag ;_dfbe .NumberOfSymbols =_bfbfb ;_dfbe .DefaultPixel =_eeec ;
_dfbe .CombinationOperator =_bdgdff ;_dfbe .IsTransposed =_bgcf ;_dfbe .ReferenceCorner =_cccfd ;_dfbe .SbDsOffset =_gaef ;_dfbe .SbHuffFS =_degfg ;_dfbe .SbHuffDS =_aadcg ;_dfbe .SbHuffDT =_abba ;_dfbe .SbHuffRDWidth =_fffc ;_dfbe .SbHuffRDHeight =_ggbe ;
_dfbe .SbHuffRSize =_fceg ;_dfbe .SbHuffRDX =_egeb ;_dfbe .SbHuffRDY =_bacf ;_dfbe .SbrTemplate =_gbcfd ;_dfbe .SbrATX =_eaag ;_dfbe .SbrATY =_facf ;_dfbe .Symbols =_befa ;_dfbe ._eebg =_egfb ;};func (_afdg *TextRegion )readRegionFlags ()error {var (_gbgb int ;
_caceg uint64 ;_fage error ;);_gbgb ,_fage =_afdg ._cgbc .ReadBit ();if _fage !=nil {return _fage ;};_afdg .SbrTemplate =int8 (_gbgb );_caceg ,_fage =_afdg ._cgbc .ReadBits (5);if _fage !=nil {return _fage ;};_afdg .SbDsOffset =int8 (_caceg );if _afdg .SbDsOffset > 0x0f{_afdg .SbDsOffset -=0x20;
};_gbgb ,_fage =_afdg ._cgbc .ReadBit ();if _fage !=nil {return _fage ;};_afdg .DefaultPixel =int8 (_gbgb );_caceg ,_fage =_afdg ._cgbc .ReadBits (2);if _fage !=nil {return _fage ;};_afdg .CombinationOperator =_gf .CombinationOperator (int (_caceg )&0x3);
_gbgb ,_fage =_afdg ._cgbc .ReadBit ();if _fage !=nil {return _fage ;};_afdg .IsTransposed =int8 (_gbgb );_caceg ,_fage =_afdg ._cgbc .ReadBits (2);if _fage !=nil {return _fage ;};_afdg .ReferenceCorner =int16 (_caceg )&0x3;_caceg ,_fage =_afdg ._cgbc .ReadBits (2);
if _fage !=nil {return _fage ;};_afdg .LogSBStrips =int16 (_caceg )&0x3;_afdg .SbStrips =1<<uint (_afdg .LogSBStrips );_gbgb ,_fage =_afdg ._cgbc .ReadBit ();if _fage !=nil {return _fage ;};if _gbgb ==1{_afdg .UseRefinement =true ;};_gbgb ,_fage =_afdg ._cgbc .ReadBit ();
if _fage !=nil {return _fage ;};if _gbgb ==1{_afdg .IsHuffmanEncoded =true ;};return nil ;};func (_aeddd *PageInformationSegment )readResolution ()error {_ddad ,_bcgg :=_aeddd ._dcaf .ReadBits (32);if _bcgg !=nil {return _bcgg ;};_aeddd .ResolutionX =int (_ddad &_g .MaxInt32 );
_ddad ,_bcgg =_aeddd ._dcaf .ReadBits (32);if _bcgg !=nil {return _bcgg ;};_aeddd .ResolutionY =int (_ddad &_g .MaxInt32 );return nil ;};func (_afbb *Header )readSegmentDataLength (_dffe *_ed .Reader )(_ggbg error ){_afbb .SegmentDataLength ,_ggbg =_dffe .ReadBits (32);
if _ggbg !=nil {return _ggbg ;};_afbb .SegmentDataLength &=_g .MaxInt32 ;return nil ;};func (_daea *SymbolDictionary )readNumberOfNewSymbols ()error {_bcde ,_edae :=_daea ._gaeb .ReadBits (32);if _edae !=nil {return _edae ;};_daea .NumberOfNewSymbols =uint32 (_bcde &_g .MaxUint32 );
return nil ;};func (_gedf *GenericRegion )setOverrideFlag (_bcf int ){_gedf .GBAtOverride [_bcf ]=true ;_gedf ._eeg =true ;};func (_eagd *HalftoneRegion )grayScaleDecoding (_egde int )([][]int ,error ){var (_ffg []int8 ;_dgag []int8 ;);if !_eagd .IsMMREncoded {_ffg =make ([]int8 ,4);
_dgag =make ([]int8 ,4);if _eagd .HTemplate <=1{_ffg [0]=3;}else if _eagd .HTemplate >=2{_ffg [0]=2;};_dgag [0]=-1;_ffg [1]=-3;_dgag [1]=-1;_ffg [2]=2;_dgag [2]=-2;_ffg [3]=-2;_dgag [3]=-2;};_acba :=make ([]*_gf .Bitmap ,_egde );_ebc :=NewGenericRegion (_eagd ._acfg );
_ebc .setParametersMMR (_eagd .IsMMREncoded ,_eagd .DataOffset ,_eagd .DataLength ,_eagd .HGridHeight ,_eagd .HGridWidth ,_eagd .HTemplate ,false ,_eagd .HSkipEnabled ,_ffg ,_dgag );_ddc :=_egde -1;var _cbdg error ;_acba [_ddc ],_cbdg =_ebc .GetRegionBitmap ();
if _cbdg !=nil {return nil ,_cbdg ;};for _ddc > 0{_ddc --;_ebc .Bitmap =nil ;_acba [_ddc ],_cbdg =_ebc .GetRegionBitmap ();if _cbdg !=nil {return nil ,_cbdg ;};if _cbdg =_eagd .combineGrayscalePlanes (_acba ,_ddc );_cbdg !=nil {return nil ,_cbdg ;};};return _eagd .computeGrayScalePlanes (_acba ,_egde );
};func (_acc *template0 )setIndex (_eag *_ae .DecoderStats ){_eag .SetIndex (0x100)};func (_dcdf *PageInformationSegment )CombinationOperator ()_gf .CombinationOperator {return _dcdf ._bae };func (_cgag *SymbolDictionary )retrieveImportSymbols ()error {for _ ,_gded :=range _cgag .Header .RTSegments {if _gded .Type ==0{_cfgfc ,_egddb :=_gded .GetSegmentData ();
if _egddb !=nil {return _egddb ;};_acddb ,_bagb :=_cfgfc .(*SymbolDictionary );if !_bagb {return _e .Errorf ("\u0070\u0072\u006f\u0076\u0069\u0064\u0065\u0064\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0020\u0044\u0061\u0074a\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0053\u0079\u006d\u0062\u006f\u006c\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0053\u0065\u0067m\u0065\u006e\u0074\u003a\u0020%\u0054",_cfgfc );
};_dfcf ,_egddb :=_acddb .GetDictionary ();if _egddb !=nil {return _e .Errorf ("\u0072\u0065\u006c\u0061\u0074\u0065\u0064 \u0073\u0065\u0067m\u0065\u006e\u0074 \u0077\u0069t\u0068\u0020\u0069\u006e\u0064\u0065x\u003a %\u0064\u0020\u0067\u0065\u0074\u0044\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u002e\u0020\u0025\u0073",_gded .SegmentNumber ,_egddb .Error ());
};_cgag ._cccf =append (_cgag ._cccf ,_dfcf ...);_cgag ._cdeb +=_acddb .NumberOfExportedSymbols ;};};return nil ;};func (_ceacd *TextRegion )encodeSymbols (_fede _ed .BinaryWriter )(_fffd int ,_edgd error ){const _aggae ="\u0065\u006e\u0063\u006f\u0064\u0065\u0053\u0079\u006d\u0062\u006f\u006c\u0073";
_gbfg :=make ([]byte ,4);_dg .BigEndian .PutUint32 (_gbfg ,_ceacd .NumberOfSymbols );if _fffd ,_edgd =_fede .Write (_gbfg );_edgd !=nil {return _fffd ,_ff .Wrap (_edgd ,_aggae ,"\u004e\u0075\u006dbe\u0072\u004f\u0066\u0053\u0079\u006d\u0062\u006f\u006c\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065\u0073");
};_gdgf ,_edgd :=_gf .NewClassedPoints (_ceacd ._gaf ,_ceacd ._bdgdf );if _edgd !=nil {return 0,_ff .Wrap (_edgd ,_aggae ,"");};var _adgc ,_dfcb int ;_fccgg :=_fff .New ();_fccgg .Init ();if _edgd =_fccgg .EncodeInteger (_fff .IADT ,0);_edgd !=nil {return _fffd ,_ff .Wrap (_edgd ,_aggae ,"\u0069\u006e\u0069\u0074\u0069\u0061\u006c\u0020\u0044\u0054");
};_efdd ,_edgd :=_gdgf .GroupByY ();if _edgd !=nil {return 0,_ff .Wrap (_edgd ,_aggae ,"");};for _ ,_addea :=range _efdd {_ddbc :=int (_addea .YAtIndex (0));_gcfbg :=_ddbc -_adgc ;if _edgd =_fccgg .EncodeInteger (_fff .IADT ,_gcfbg );_edgd !=nil {return _fffd ,_ff .Wrap (_edgd ,_aggae ,"");
};var _dfdc int ;for _gacb ,_baee :=range _addea .IntSlice {switch _gacb {case 0:_ffaee :=int (_addea .XAtIndex (_gacb ))-_dfcb ;if _edgd =_fccgg .EncodeInteger (_fff .IAFS ,_ffaee );_edgd !=nil {return _fffd ,_ff .Wrap (_edgd ,_aggae ,"");};_dfcb +=_ffaee ;
_dfdc =_dfcb ;default:_gcbe :=int (_addea .XAtIndex (_gacb ))-_dfdc ;if _edgd =_fccgg .EncodeInteger (_fff .IADS ,_gcbe );_edgd !=nil {return _fffd ,_ff .Wrap (_edgd ,_aggae ,"");};_dfdc +=_gcbe ;};_acbc ,_dbeff :=_ceacd ._bbgd .Get (_baee );if _dbeff !=nil {return _fffd ,_ff .Wrap (_dbeff ,_aggae ,"");
};_efdda ,_gbfbg :=_ceacd ._ecgb [_acbc ];if !_gbfbg {_efdda ,_gbfbg =_ceacd ._eccc [_acbc ];if !_gbfbg {return _fffd ,_ff .Errorf (_aggae ,"\u0053\u0079\u006d\u0062\u006f\u006c:\u0020\u0027\u0025d\u0027\u0020\u0069s\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064 \u0069\u006e\u0020\u0067\u006cob\u0061\u006c\u0020\u0061\u006e\u0064\u0020\u006c\u006f\u0063\u0061\u006c\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0020\u006d\u0061\u0070",_acbc );
};};if _dbeff =_fccgg .EncodeIAID (_ceacd ._cddcd ,_efdda );_dbeff !=nil {return _fffd ,_ff .Wrap (_dbeff ,_aggae ,"");};};if _edgd =_fccgg .EncodeOOB (_fff .IADS );_edgd !=nil {return _fffd ,_ff .Wrap (_edgd ,_aggae ,"");};};_fccgg .Final ();_cdab ,_edgd :=_fccgg .WriteTo (_fede );
if _edgd !=nil {return _fffd ,_ff .Wrap (_edgd ,_aggae ,"");};_fffd +=int (_cdab );return _fffd ,nil ;};type Type int ;func (_dgcf *GenericRegion )InitEncode (bm *_gf .Bitmap ,xLoc ,yLoc ,template int ,duplicateLineRemoval bool )error {const _gff ="\u0047e\u006e\u0065\u0072\u0069\u0063\u0052\u0065\u0067\u0069\u006f\u006e.\u0049\u006e\u0069\u0074\u0045\u006e\u0063\u006f\u0064\u0065";
if bm ==nil {return _ff .Error (_gff ,"\u0070\u0072\u006f\u0076id\u0065\u0064\u0020\u006e\u0069\u006c\u0020\u0062\u0069\u0074\u006d\u0061\u0070");};if xLoc < 0||yLoc < 0{return _ff .Error (_gff ,"\u0078\u0020\u0061\u006e\u0064\u0020\u0079\u0020\u006c\u006f\u0063\u0061\u0074i\u006f\u006e\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0067\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0074h\u0061\u006e\u0020\u0030");
};_dgcf .Bitmap =bm ;_dgcf .GBTemplate =byte (template );switch _dgcf .GBTemplate {case 0:_dgcf .GBAtX =[]int8 {3,-3,2,-2};_dgcf .GBAtY =[]int8 {-1,-1,-2,-2};case 1:_dgcf .GBAtX =[]int8 {3};_dgcf .GBAtY =[]int8 {-1};case 2,3:_dgcf .GBAtX =[]int8 {2};_dgcf .GBAtY =[]int8 {-1};
default:return _ff .Errorf (_gff ,"\u0070\u0072o\u0076\u0069\u0064\u0065\u0064 \u0074\u0065\u006d\u0070\u006ca\u0074\u0065\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0020\u0076\u0061\u006c\u0069\u0064\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u007b\u0030\u002c\u0031\u002c\u0032\u002c\u0033\u007d",template );
};_dgcf .RegionSegment =&RegionSegment {BitmapHeight :uint32 (bm .Height ),BitmapWidth :uint32 (bm .Width ),XLocation :uint32 (xLoc ),YLocation :uint32 (yLoc )};_dgcf .IsTPGDon =duplicateLineRemoval ;return nil ;};func (_agc *HalftoneRegion )parseHeader ()error {if _bfcc :=_agc .RegionSegment .parseHeader ();
_bfcc !=nil {return _bfcc ;};_aagd ,_fcgd :=_agc ._acfg .ReadBit ();if _fcgd !=nil {return _fcgd ;};_agc .HDefaultPixel =int8 (_aagd );_efd ,_fcgd :=_agc ._acfg .ReadBits (3);if _fcgd !=nil {return _fcgd ;};_agc .CombinationOperator =_gf .CombinationOperator (_efd &0xf);
_aagd ,_fcgd =_agc ._acfg .ReadBit ();if _fcgd !=nil {return _fcgd ;};if _aagd ==1{_agc .HSkipEnabled =true ;};_efd ,_fcgd =_agc ._acfg .ReadBits (2);if _fcgd !=nil {return _fcgd ;};_agc .HTemplate =byte (_efd &0xf);_aagd ,_fcgd =_agc ._acfg .ReadBit ();
if _fcgd !=nil {return _fcgd ;};if _aagd ==1{_agc .IsMMREncoded =true ;};_efd ,_fcgd =_agc ._acfg .ReadBits (32);if _fcgd !=nil {return _fcgd ;};_agc .HGridWidth =uint32 (_efd &_g .MaxUint32 );_efd ,_fcgd =_agc ._acfg .ReadBits (32);if _fcgd !=nil {return _fcgd ;
};_agc .HGridHeight =uint32 (_efd &_g .MaxUint32 );_efd ,_fcgd =_agc ._acfg .ReadBits (32);if _fcgd !=nil {return _fcgd ;};_agc .HGridX =int32 (_efd &_g .MaxInt32 );_efd ,_fcgd =_agc ._acfg .ReadBits (32);if _fcgd !=nil {return _fcgd ;};_agc .HGridY =int32 (_efd &_g .MaxInt32 );
_efd ,_fcgd =_agc ._acfg .ReadBits (16);if _fcgd !=nil {return _fcgd ;};_agc .HRegionX =uint16 (_efd &_g .MaxUint16 );_efd ,_fcgd =_agc ._acfg .ReadBits (16);if _fcgd !=nil {return _fcgd ;};_agc .HRegionY =uint16 (_efd &_g .MaxUint16 );if _fcgd =_agc .computeSegmentDataStructure ();
_fcgd !=nil {return _fcgd ;};return _agc .checkInput ();};type TableSegment struct{_gdcb *_ed .Reader ;_bbgc int32 ;_efgef int32 ;_caab int32 ;_fggcd int32 ;_bdbeg int32 ;};func (_gef *GenericRegion )Size ()int {return _gef .RegionSegment .Size ()+1+2*len (_gef .GBAtX )};
func (_bdgd *HalftoneRegion )GetRegionBitmap ()(*_gf .Bitmap ,error ){if _bdgd .HalftoneRegionBitmap !=nil {return _bdgd .HalftoneRegionBitmap ,nil ;};var _ggff error ;_bdgd .HalftoneRegionBitmap =_gf .New (int (_bdgd .RegionSegment .BitmapWidth ),int (_bdgd .RegionSegment .BitmapHeight ));
if _bdgd .Patterns ==nil ||(_bdgd .Patterns !=nil &&len (_bdgd .Patterns )==0){_bdgd .Patterns ,_ggff =_bdgd .GetPatterns ();if _ggff !=nil {return nil ,_ggff ;};};if _bdgd .HDefaultPixel ==1{_bdgd .HalftoneRegionBitmap .SetDefaultPixel ();};_egga :=_g .Ceil (_g .Log (float64 (len (_bdgd .Patterns )))/_g .Log (2));
_gggg :=int (_egga );var _eae [][]int ;_eae ,_ggff =_bdgd .grayScaleDecoding (_gggg );if _ggff !=nil {return nil ,_ggff ;};if _ggff =_bdgd .renderPattern (_eae );_ggff !=nil {return nil ,_ggff ;};return _bdgd .HalftoneRegionBitmap ,nil ;};func (_gdegd *TextRegion )decodeRdx ()(int64 ,error ){const _eebf ="\u0064e\u0063\u006f\u0064\u0065\u0052\u0064x";
if _gdegd .IsHuffmanEncoded {if _gdegd .SbHuffRDX ==3{if _gdegd ._eabd ==nil {var (_ecbb int ;_degb error ;);if _gdegd .SbHuffFS ==3{_ecbb ++;};if _gdegd .SbHuffDS ==3{_ecbb ++;};if _gdegd .SbHuffDT ==3{_ecbb ++;};if _gdegd .SbHuffRDWidth ==3{_ecbb ++;
};if _gdegd .SbHuffRDHeight ==3{_ecbb ++;};_gdegd ._eabd ,_degb =_gdegd .getUserTable (_ecbb );if _degb !=nil {return 0,_ff .Wrap (_degb ,_eebf ,"");};};return _gdegd ._eabd .Decode (_gdegd ._cgbc );};_bgff ,_bbfcg :=_aee .GetStandardTable (14+int (_gdegd .SbHuffRDX ));
if _bbfcg !=nil {return 0,_ff .Wrap (_bbfcg ,_eebf ,"");};return _bgff .Decode (_gdegd ._cgbc );};_faga ,_bcca :=_gdegd ._afef .DecodeInt (_gdegd ._cbfa );if _bcca !=nil {return 0,_ff .Wrap (_bcca ,_eebf ,"");};return int64 (_faga ),nil ;};func (_gcge *SymbolDictionary )getToExportFlags ()([]int ,error ){var (_cgg int ;
_cebe int32 ;_bged error ;_edffc =int32 (_gcge ._cdeb +_gcge .NumberOfNewSymbols );_gcgef =make ([]int ,_edffc ););for _dffg :=int32 (0);_dffg < _edffc ;_dffg +=_cebe {if _gcge .IsHuffmanEncoded {_abcg ,_gggb :=_aee .GetStandardTable (1);if _gggb !=nil {return nil ,_gggb ;
};_dfgg ,_gggb :=_abcg .Decode (_gcge ._gaeb );if _gggb !=nil {return nil ,_gggb ;};_cebe =int32 (_dfgg );}else {_cebe ,_bged =_gcge ._daed .DecodeInt (_gcge ._gcfac );if _bged !=nil {return nil ,_bged ;};};if _cebe !=0{if _dffg +_cebe > _edffc {return nil ,_ff .Error ("\u0053\u0079\u006d\u0062\u006f\u006cD\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e\u0067\u0065\u0074T\u006f\u0045\u0078\u0070\u006f\u0072\u0074F\u006c\u0061\u0067\u0073","\u006d\u0061\u006c\u0066\u006f\u0072m\u0065\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u0064\u0061\u0074\u0061\u0020\u0070\u0072\u006f\u0076\u0069\u0064e\u0064\u002e\u0020\u0069\u006e\u0064\u0065\u0078\u0020\u006f\u0075\u0074\u0020\u006ff\u0020r\u0061\u006e\u0067\u0065");
};for _cfcgd :=_dffg ;_cfcgd < _dffg +_cebe ;_cfcgd ++{_gcgef [_cfcgd ]=_cgg ;};};if _cgg ==0{_cgg =1;}else {_cgg =0;};};return _gcgef ,nil ;};func (_cfba *TextRegion )String ()string {_cbed :=&_a .Builder {};_cbed .WriteString ("\u000a[\u0054E\u0058\u0054\u0020\u0052\u0045\u0047\u0049\u004f\u004e\u005d\u000a");
_cbed .WriteString (_cfba .RegionInfo .String ()+"\u000a");_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053br\u0054\u0065\u006d\u0070\u006c\u0061\u0074\u0065\u003a\u0020\u0025\u0076\u000a",_cfba .SbrTemplate ));_cbed .WriteString (_e .Sprintf ("\u0009-\u0020S\u0062\u0044\u0073\u004f\u0066f\u0073\u0065t\u003a\u0020\u0025\u0076\u000a",_cfba .SbDsOffset ));
_cbed .WriteString (_e .Sprintf ("\t\u002d \u0044\u0065\u0066\u0061\u0075\u006c\u0074\u0050i\u0078\u0065\u006c\u003a %\u0076\u000a",_cfba .DefaultPixel ));_cbed .WriteString (_e .Sprintf ("\t\u002d\u0020\u0043\u006f\u006d\u0062i\u006e\u0061\u0074\u0069\u006f\u006e\u004f\u0070\u0065r\u0061\u0074\u006fr\u003a \u0025\u0076\u000a",_cfba .CombinationOperator ));
_cbed .WriteString (_e .Sprintf ("\t\u002d \u0049\u0073\u0054\u0072\u0061\u006e\u0073\u0070o\u0073\u0065\u0064\u003a %\u0076\u000a",_cfba .IsTransposed ));_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020Re\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0043\u006f\u0072\u006e\u0065\u0072\u003a\u0020\u0025\u0076\u000a",_cfba .ReferenceCorner ));
_cbed .WriteString (_e .Sprintf ("\t\u002d\u0020\u0055\u0073eR\u0065f\u0069\u006e\u0065\u006d\u0065n\u0074\u003a\u0020\u0025\u0076\u000a",_cfba .UseRefinement ));_cbed .WriteString (_e .Sprintf ("\u0009-\u0020\u0049\u0073\u0048\u0075\u0066\u0066\u006d\u0061\u006e\u0045n\u0063\u006f\u0064\u0065\u0064\u003a\u0020\u0025\u0076\u000a",_cfba .IsHuffmanEncoded ));
if _cfba .IsHuffmanEncoded {_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053bH\u0075\u0066\u0066\u0052\u0053\u0069\u007a\u0065\u003a\u0020\u0025\u0076\u000a",_cfba .SbHuffRSize ));_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0048\u0075\u0066\u0066\u0052\u0044\u0059:\u0020\u0025\u0076\u000a",_cfba .SbHuffRDY ));
_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0048\u0075\u0066\u0066\u0052\u0044\u0058:\u0020\u0025\u0076\u000a",_cfba .SbHuffRDX ));_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053bH\u0075\u0066\u0066\u0052\u0044\u0048\u0065\u0069\u0067\u0068\u0074\u003a\u0020\u0025v\u000a",_cfba .SbHuffRDHeight ));
_cbed .WriteString (_e .Sprintf ("\t\u002d\u0020\u0053\u0062Hu\u0066f\u0052\u0044\u0057\u0069\u0064t\u0068\u003a\u0020\u0025\u0076\u000a",_cfba .SbHuffRDWidth ));_cbed .WriteString (_e .Sprintf ("\u0009\u002d \u0053\u0062\u0048u\u0066\u0066\u0044\u0054\u003a\u0020\u0025\u0076\u000a",_cfba .SbHuffDT ));
_cbed .WriteString (_e .Sprintf ("\u0009\u002d \u0053\u0062\u0048u\u0066\u0066\u0044\u0053\u003a\u0020\u0025\u0076\u000a",_cfba .SbHuffDS ));_cbed .WriteString (_e .Sprintf ("\u0009\u002d \u0053\u0062\u0048u\u0066\u0066\u0046\u0053\u003a\u0020\u0025\u0076\u000a",_cfba .SbHuffFS ));
};_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0072\u0041\u0054\u0058:\u0020\u0025\u0076\u000a",_cfba .SbrATX ));_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0072\u0041\u0054\u0059:\u0020\u0025\u0076\u000a",_cfba .SbrATY ));
_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020N\u0075\u006d\u0062\u0065r\u004f\u0066\u0053\u0079\u006d\u0062\u006fl\u0049\u006e\u0073\u0074\u0061\u006e\u0063\u0065\u0073\u003a\u0020\u0025\u0076\u000a",_cfba .NumberOfSymbolInstances ));_cbed .WriteString (_e .Sprintf ("\u0009\u002d\u0020\u0053\u0062\u0072\u0041\u0054\u0058:\u0020\u0025\u0076\u000a",_cfba .SbrATX ));
return _cbed .String ();};func (_gggba *TextRegion )readAmountOfSymbolInstances ()error {_cbeg ,_deef :=_gggba ._cgbc .ReadBits (32);if _deef !=nil {return _deef ;};_gggba .NumberOfSymbolInstances =uint32 (_cbeg &_g .MaxUint32 );_efde :=_gggba .RegionInfo .BitmapWidth *_gggba .RegionInfo .BitmapHeight ;
if _efde < _gggba .NumberOfSymbolInstances {_ge .Log .Debug ("\u004c\u0069\u006d\u0069t\u0069\u006e\u0067\u0020t\u0068\u0065\u0020n\u0075\u006d\u0062\u0065\u0072\u0020o\u0066\u0020d\u0065\u0063\u006f\u0064e\u0064\u0020\u0073\u0079m\u0062\u006f\u006c\u0020\u0069n\u0073\u0074\u0061\u006e\u0063\u0065\u0073 \u0074\u006f\u0020\u006f\u006ee\u0020\u0070\u0065\u0072\u0020\u0070\u0069\u0078\u0065l\u0020\u0028\u0020\u0025\u0064\u0020\u0069\u006e\u0073\u0074\u0065\u0061\u0064\u0020\u006f\u0066\u0020\u0025\u0064\u0029",_efde ,_gggba .NumberOfSymbolInstances );
_gggba .NumberOfSymbolInstances =_efde ;};return nil ;};func (_acef *PageInformationSegment )parseHeader ()(_ccga error ){_ge .Log .Trace ("\u005b\u0050\u0061\u0067\u0065I\u006e\u0066\u006f\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u0053\u0065\u0067m\u0065\u006e\u0074\u005d\u0020\u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0048\u0065\u0061\u0064\u0065\u0072\u002e\u002e\u002e");
defer func (){var _ddbd ="[\u0050\u0061\u0067\u0065\u0049\u006e\u0066\u006f\u0072m\u0061\u0074\u0069\u006f\u006e\u0053\u0065gm\u0065\u006e\u0074\u005d \u0050\u0061\u0072\u0073\u0069\u006e\u0067\u0048\u0065ad\u0065\u0072 \u0046\u0069\u006e\u0069\u0073\u0068\u0065\u0064";
if _ccga !=nil {_ddbd +="\u0020\u0077\u0069t\u0068\u0020\u0065\u0072\u0072\u006f\u0072\u0020"+_ccga .Error ();}else {_ddbd +="\u0020\u0073\u0075\u0063\u0063\u0065\u0073\u0073\u0066\u0075\u006c\u006c\u0079";};_ge .Log .Trace (_ddbd );}();if _ccga =_acef .readWidthAndHeight ();
_ccga !=nil {return _ccga ;};if _ccga =_acef .readResolution ();_ccga !=nil {return _ccga ;};_ ,_ccga =_acef ._dcaf .ReadBit ();if _ccga !=nil {return _ccga ;};if _ccga =_acef .readCombinationOperatorOverrideAllowed ();_ccga !=nil {return _ccga ;};if _ccga =_acef .readRequiresAuxiliaryBuffer ();
_ccga !=nil {return _ccga ;};if _ccga =_acef .readCombinationOperator ();_ccga !=nil {return _ccga ;};if _ccga =_acef .readDefaultPixelValue ();_ccga !=nil {return _ccga ;};if _ccga =_acef .readContainsRefinement ();_ccga !=nil {return _ccga ;};if _ccga =_acef .readIsLossless ();
_ccga !=nil {return _ccga ;};if _ccga =_acef .readIsStriped ();_ccga !=nil {return _ccga ;};if _ccga =_acef .readMaxStripeSize ();_ccga !=nil {return _ccga ;};if _ccga =_acef .checkInput ();_ccga !=nil {return _ccga ;};_ge .Log .Trace ("\u0025\u0073",_acef );
return nil ;};func (_ebeg *Header )readDataStartOffset (_ffce *_ed .Reader ,_fbfd OrganizationType ){if _fbfd ==OSequential {_ebeg .SegmentDataStartOffset =uint64 (_ffce .AbsolutePosition ());};};func (_ffega *HalftoneRegion )computeX (_cdea ,_cfga int )int {return _ffega .shiftAndFill (int (_ffega .HGridX )+_cdea *int (_ffega .HRegionY )+_cfga *int (_ffega .HRegionX ));
};func (_eaf *template1 )setIndex (_fbag *_ae .DecoderStats ){_fbag .SetIndex (0x080)};func (_beaa *SymbolDictionary )parseHeader ()(_daedg error ){_ge .Log .Trace ("\u005b\u0053\u0059\u004d\u0042\u004f\u004c \u0044\u0049\u0043T\u0049\u004f\u004e\u0041R\u0059\u005d\u005b\u0050\u0041\u0052\u0053\u0045\u002d\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0062\u0065\u0067\u0069\u006e\u0073\u002e\u002e\u002e");
defer func (){if _daedg !=nil {_ge .Log .Trace ("\u005bS\u0059\u004dB\u004f\u004c\u0020\u0044I\u0043\u0054\u0049O\u004e\u0041\u0052\u0059\u005d\u005b\u0050\u0041\u0052SE\u002d\u0048\u0045A\u0044\u0045R\u005d\u0020\u0066\u0061\u0069\u006ce\u0064\u002e \u0025\u0076",_daedg );
}else {_ge .Log .Trace ("\u005b\u0053\u0059\u004d\u0042\u004f\u004c \u0044\u0049\u0043T\u0049\u004f\u004e\u0041R\u0059\u005d\u005b\u0050\u0041\u0052\u0053\u0045\u002d\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0066\u0069\u006e\u0069\u0073\u0068\u0065\u0064\u002e");
};}();if _daedg =_beaa .readRegionFlags ();_daedg !=nil {return _daedg ;};if _daedg =_beaa .setAtPixels ();_daedg !=nil {return _daedg ;};if _daedg =_beaa .setRefinementAtPixels ();_daedg !=nil {return _daedg ;};if _daedg =_beaa .readNumberOfExportedSymbols ();
_daedg !=nil {return _daedg ;};if _daedg =_beaa .readNumberOfNewSymbols ();_daedg !=nil {return _daedg ;};if _daedg =_beaa .setInSyms ();_daedg !=nil {return _daedg ;};if _beaa ._ebfg {_fbgac :=_beaa .Header .RTSegments ;for _degf :=len (_fbgac )-1;_degf >=0;
_degf --{if _fbgac [_degf ].Type ==0{_abgbg ,_dfde :=_fbgac [_degf ].SegmentData .(*SymbolDictionary );if !_dfde {_daedg =_e .Errorf ("\u0072\u0065\u006c\u0061\u0074\u0065\u0064\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074:\u0020\u0025\u0076\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020S\u0079\u006d\u0062\u006f\u006c\u0020\u0044\u0069\u0063\u0074\u0069\u006fna\u0072\u0079\u0020\u0053\u0065\u0067\u006d\u0065\u006e\u0074",_fbgac [_degf ]);
return _daedg ;};if _abgbg ._ebfg {_beaa .setRetainedCodingContexts (_abgbg );};break ;};};};if _daedg =_beaa .checkInput ();_daedg !=nil {return _daedg ;};return nil ;};func (_dbbd *Header )Encode (w _ed .BinaryWriter )(_ecfc int ,_fdca error ){const _gcfa ="\u0048\u0065\u0061d\u0065\u0072\u002e\u0057\u0072\u0069\u0074\u0065";
var _feac _ed .BinaryWriter ;_ge .Log .Trace ("\u005b\u0053\u0045G\u004d\u0045\u004e\u0054-\u0048\u0045\u0041\u0044\u0045\u0052\u005d[\u0045\u004e\u0043\u004f\u0044\u0045\u005d\u0020\u0042\u0065\u0067\u0069\u006e\u0073");defer func (){if _fdca !=nil {_ge .Log .Trace ("[\u0053\u0045\u0047\u004d\u0045\u004eT\u002d\u0048\u0045\u0041\u0044\u0045R\u005d\u005b\u0045\u004e\u0043\u004f\u0044E\u005d\u0020\u0046\u0061\u0069\u006c\u0065\u0064\u002e\u0020%\u0076",_fdca );
}else {_ge .Log .Trace ("\u005b\u0053\u0045\u0047ME\u004e\u0054\u002d\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u0020\u0025\u0076",_dbbd );_ge .Log .Trace ("\u005b\u0053\u0045\u0047\u004d\u0045N\u0054\u002d\u0048\u0045\u0041\u0044\u0045\u0052\u005d\u005b\u0045\u004e\u0043O\u0044\u0045\u005d\u0020\u0046\u0069\u006ei\u0073\u0068\u0065\u0064");
};}();w .FinishByte ();if _dbbd .SegmentData !=nil {_feeaa ,_fgef :=_dbbd .SegmentData .(SegmentEncoder );if !_fgef {return 0,_ff .Errorf (_gcfa ,"\u0053\u0065\u0067\u006d\u0065\u006e\u0074\u003a\u0020\u0025\u0054\u0020\u0064\u006f\u0065s\u006e\u0027\u0074\u0020\u0069\u006d\u0070\u006c\u0065\u006d\u0065\u006e\u0074 \u0053\u0065\u0067\u006d\u0065\u006e\u0074\u0045\u006e\u0063\u006f\u0064er\u0020\u0069\u006e\u0074\u0065\u0072\u0066\u0061\u0063\u0065",_dbbd .SegmentData );
};_feac =_ed .BufferedMSB ();_ecfc ,_fdca =_feeaa .Encode (_feac );if _fdca !=nil {return 0,_ff .Wrap (_fdca ,_gcfa ,"");};_dbbd .SegmentDataLength =uint64 (_ecfc );};if _dbbd .pageSize ()==4{_dbbd .PageAssociationFieldSize =true ;};var _eaad int ;_eaad ,_fdca =_dbbd .writeSegmentNumber (w );
if _fdca !=nil {return 0,_ff .Wrap (_fdca ,_gcfa ,"");};_ecfc +=_eaad ;if _fdca =_dbbd .writeFlags (w );_fdca !=nil {return _ecfc ,_ff .Wrap (_fdca ,_gcfa ,"");};_ecfc ++;_eaad ,_fdca =_dbbd .writeReferredToCount (w );if _fdca !=nil {return 0,_ff .Wrap (_fdca ,_gcfa ,"");
};_ecfc +=_eaad ;_eaad ,_fdca =_dbbd .writeReferredToSegments (w );if _fdca !=nil {return 0,_ff .Wrap (_fdca ,_gcfa ,"");};_ecfc +=_eaad ;_eaad ,_fdca =_dbbd .writeSegmentPageAssociation (w );if _fdca !=nil {return 0,_ff .Wrap (_fdca ,_gcfa ,"");};_ecfc +=_eaad ;
_eaad ,_fdca =_dbbd .writeSegmentDataLength (w );if _fdca !=nil {return 0,_ff .Wrap (_fdca ,_gcfa ,"");};_ecfc +=_eaad ;_dbbd .HeaderLength =int64 (_ecfc )-int64 (_dbbd .SegmentDataLength );if _feac !=nil {if _ ,_fdca =w .Write (_feac .Data ());_fdca !=nil {return _ecfc ,_ff .Wrap (_fdca ,_gcfa ,"\u0077r\u0069t\u0065\u0020\u0073\u0065\u0067m\u0065\u006et\u0020\u0064\u0061\u0074\u0061");
};};return _ecfc ,nil ;};func (_adfa *TextRegion )readUseRefinement ()error {if !_adfa .UseRefinement ||_adfa .SbrTemplate !=0{return nil ;};var (_dagc byte ;_ecedc error ;);_adfa .SbrATX =make ([]int8 ,2);_adfa .SbrATY =make ([]int8 ,2);_dagc ,_ecedc =_adfa ._cgbc .ReadByte ();
if _ecedc !=nil {return _ecedc ;};_adfa .SbrATX [0]=int8 (_dagc );_dagc ,_ecedc =_adfa ._cgbc .ReadByte ();if _ecedc !=nil {return _ecedc ;};_adfa .SbrATY [0]=int8 (_dagc );_dagc ,_ecedc =_adfa ._cgbc .ReadByte ();if _ecedc !=nil {return _ecedc ;};_adfa .SbrATX [1]=int8 (_dagc );
_dagc ,_ecedc =_adfa ._cgbc .ReadByte ();if _ecedc !=nil {return _ecedc ;};_adfa .SbrATY [1]=int8 (_dagc );return nil ;};func (_cbae *TextRegion )GetRegionInfo ()*RegionSegment {return _cbae .RegionInfo };var _ SegmentEncoder =&GenericRegion {};var _ SegmentEncoder =&RegionSegment {};
type EndOfStripe struct{_dgd *_ed .Reader ;_dbc int ;};func (_gfdg *SymbolDictionary )getUserTable (_ccde int )(_aee .Tabler ,error ){var _eaggc int ;for _ ,_bead :=range _gfdg .Header .RTSegments {if _bead .Type ==53{if _eaggc ==_ccde {_dfc ,_cabc :=_bead .GetSegmentData ();
if _cabc !=nil {return nil ,_cabc ;};_dbba :=_dfc .(_aee .BasicTabler );return _aee .NewEncodedTable (_dbba );};_eaggc ++;};};return nil ,nil ;};func (_gfgb *HalftoneRegion )GetRegionInfo ()*RegionSegment {return _gfgb .RegionSegment };func (_fead *TextRegion )decodeIds ()(int64 ,error ){const _gfaf ="\u0064e\u0063\u006f\u0064\u0065\u0049\u0064s";
if _fead .IsHuffmanEncoded {if _fead .SbHuffDS ==3{if _fead ._bdef ==nil {_abde :=0;if _fead .SbHuffFS ==3{_abde ++;};var _aaee error ;_fead ._bdef ,_aaee =_fead .getUserTable (_abde );if _aaee !=nil {return 0,_ff .Wrap (_aaee ,_gfaf ,"");};};return _fead ._bdef .Decode (_fead ._cgbc );
};_dcafc ,_feeg :=_aee .GetStandardTable (8+int (_fead .SbHuffDS ));if _feeg !=nil {return 0,_ff .Wrap (_feeg ,_gfaf ,"");};return _dcafc .Decode (_fead ._cgbc );};_eedd ,_cdgc :=_fead ._afef .DecodeInt (_fead ._aead );if _cdgc !=nil {return 0,_ff .Wrap (_cdgc ,_gfaf ,"\u0063\u0078\u0049\u0041\u0044\u0053");
};return int64 (_eedd ),nil ;};func (_gccc *SymbolDictionary )huffDecodeBmSize ()(int64 ,error ){if _gccc ._edg ==nil {var (_afec int ;_cbbb error ;);if _gccc .SdHuffDecodeHeightSelection ==3{_afec ++;};if _gccc .SdHuffDecodeWidthSelection ==3{_afec ++;
};_gccc ._edg ,_cbbb =_gccc .getUserTable (_afec );if _cbbb !=nil {return 0,_cbbb ;};};return _gccc ._edg .Decode (_gccc ._gaeb );};