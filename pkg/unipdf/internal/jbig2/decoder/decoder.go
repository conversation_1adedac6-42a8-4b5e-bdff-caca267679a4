//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package decoder ;import (_fb "github.com/unidoc/unipdf/v4/internal/bitwise";_a "github.com/unidoc/unipdf/v4/internal/jbig2/bitmap";_b "github.com/unidoc/unipdf/v4/internal/jbig2/document";_ad "github.com/unidoc/unipdf/v4/internal/jbig2/errors";_g "image";
);func Decode (input []byte ,parameters Parameters ,globals *_b .Globals )(*Decoder ,error ){_cd :=_fb .NewReader (input );_eab ,_ab :=_b .DecodeDocument (_cd ,globals );if _ab !=nil {return nil ,_ab ;};return &Decoder {_be :_cd ,_c :_eab ,_fbg :parameters },nil ;
};type Parameters struct{UnpaddedData bool ;Color _a .Color ;};func (_cbf *Decoder )decodePage (_fa int )([]byte ,error ){const _adc ="\u0064\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0067\u0065";if _fa < 0{return nil ,_ad .Errorf (_adc ,"\u0069n\u0076\u0061\u006c\u0069d\u0020\u0070\u0061\u0067\u0065 \u006eu\u006db\u0065\u0072\u003a\u0020\u0027\u0025\u0064'",_fa );
};if _fa > int (_cbf ._c .NumberOfPages ){return nil ,_ad .Errorf (_adc ,"p\u0061\u0067\u0065\u003a\u0020\u0027%\u0064\u0027\u0020\u006e\u006f\u0074 \u0066\u006f\u0075\u006e\u0064\u0020\u0069n\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0063\u006f\u0064e\u0072",_fa );
};_fad ,_aa :=_cbf ._c .GetPage (_fa );if _aa !=nil {return nil ,_ad .Wrap (_aa ,_adc ,"");};_ea ,_aa :=_fad .GetBitmap ();if _aa !=nil {return nil ,_ad .Wrap (_aa ,_adc ,"");};_ea .InverseData ();if !_cbf ._fbg .UnpaddedData {return _ea .Data ,nil ;};
return _ea .GetUnpaddedData ();};func (_gd *Decoder )DecodePageImage (pageNumber int )(_g .Image ,error ){const _cb ="\u0064\u0065\u0063od\u0065\u0072\u002e\u0044\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0067\u0065\u0049\u006d\u0061\u0067\u0065";_e ,_gg :=_gd .decodePageImage (pageNumber );
if _gg !=nil {return nil ,_ad .Wrap (_gg ,_cb ,"");};return _e ,nil ;};func (_d *Decoder )DecodeNextPage ()([]byte ,error ){_d ._fe ++;_bg :=_d ._fe ;return _d .decodePage (_bg );};func (_db *Decoder )decodePageImage (_ec int )(_g .Image ,error ){const _de ="\u0064e\u0063o\u0064\u0065\u0050\u0061\u0067\u0065\u0049\u006d\u0061\u0067\u0065";
if _ec < 0{return nil ,_ad .Errorf (_de ,"\u0069n\u0076\u0061\u006c\u0069d\u0020\u0070\u0061\u0067\u0065 \u006eu\u006db\u0065\u0072\u003a\u0020\u0027\u0025\u0064'",_ec );};if _ec > int (_db ._c .NumberOfPages ){return nil ,_ad .Errorf (_de ,"p\u0061\u0067\u0065\u003a\u0020\u0027%\u0064\u0027\u0020\u006e\u006f\u0074 \u0066\u006f\u0075\u006e\u0064\u0020\u0069n\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0063\u006f\u0064e\u0072",_ec );
};_fg ,_ag :=_db ._c .GetPage (_ec );if _ag !=nil {return nil ,_ad .Wrap (_ag ,_de ,"");};_bc ,_ag :=_fg .GetBitmap ();if _ag !=nil {return nil ,_ad .Wrap (_ag ,_de ,"");};_bc .InverseData ();return _bc .ToImage (),nil ;};func (_fc *Decoder )PageNumber ()(int ,error ){const _gdc ="\u0044e\u0063o\u0064\u0065\u0072\u002e\u0050a\u0067\u0065N\u0075\u006d\u0062\u0065\u0072";
if _fc ._c ==nil {return 0,_ad .Error (_gdc ,"d\u0065\u0063\u006f\u0064\u0065\u0072 \u006e\u006f\u0074\u0020\u0069\u006e\u0069\u0074\u0069a\u006c\u0069\u007ae\u0064 \u0079\u0065\u0074");};return int (_fc ._c .NumberOfPages ),nil ;};func (_ca *Decoder )DecodePage (pageNumber int )([]byte ,error ){return _ca .decodePage (pageNumber )};
type Decoder struct{_be *_fb .Reader ;_c *_b .Document ;_fe int ;_fbg Parameters ;};