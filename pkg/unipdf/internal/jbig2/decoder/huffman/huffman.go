//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package huffman ;import (_gb "errors";_ca "fmt";_bd "github.com/unidoc/unipdf/v4/internal/bitwise";_g "github.com/unidoc/unipdf/v4/internal/jbig2/internal";_c "math";_d "strings";);func (_ecd *FixedSizeTable )Decode (r *_bd .Reader )(int64 ,error ){return _ecd ._bde .Decode (r )};
func (_gg *EncodedTable )InitTree (codeTable []*Code )error {_ebe (codeTable );for _ ,_gc :=range codeTable {if _gd :=_gg ._f .append (_gc );_gd !=nil {return _gd ;};};return nil ;};func (_dc *OutOfBandNode )Decode (r *_bd .Reader )(int64 ,error ){return 0,_g .ErrOOB };
func (_ebc *ValueNode )Decode (r *_bd .Reader )(int64 ,error ){_af ,_cbcb :=r .ReadBits (byte (_ebc ._ad ));if _cbcb !=nil {return 0,_cbcb ;};if _ebc ._ffb {_af =-_af ;};return int64 (_ebc ._bgd )+int64 (_af ),nil ;};func _dcd (_aeea [][]int32 )(*StandardTable ,error ){var _ee []*Code ;
for _ffbe :=0;_ffbe < len (_aeea );_ffbe ++{_eab :=_aeea [_ffbe ][0];_cca :=_aeea [_ffbe ][1];_cfa :=_aeea [_ffbe ][2];var _df bool ;if len (_aeea [_ffbe ])> 3{_df =true ;};_ee =append (_ee ,NewCode (_eab ,_cca ,_cfa ,_df ));};_db :=&StandardTable {_aa :_bec (0)};
if _ecg :=_db .InitTree (_ee );_ecg !=nil {return nil ,_ecg ;};return _db ,nil ;};func (_fad *Code )String ()string {var _bca string ;if _fad ._ggc !=-1{_bca =_ced (_fad ._ggc ,_fad ._eed );}else {_bca ="\u003f";};return _ca .Sprintf ("%\u0073\u002f\u0025\u0064\u002f\u0025\u0064\u002f\u0025\u0064",_bca ,_fad ._eed ,_fad ._eag ,_fad ._bga );
};var _ffba =[][][]int32 {{{1,4,0},{2,8,16},{3,16,272},{3,32,65808}},{{1,0,0},{2,0,1},{3,0,2},{4,3,3},{5,6,11},{6,32,75},{6,-1,0}},{{8,8,-256},{1,0,0},{2,0,1},{3,0,2},{4,3,3},{5,6,11},{8,32,-257,999},{7,32,75},{6,-1,0}},{{1,0,1},{2,0,2},{3,0,3},{4,3,4},{5,6,12},{5,32,76}},{{7,8,-255},{1,0,1},{2,0,2},{3,0,3},{4,3,4},{5,6,12},{7,32,-256,999},{6,32,76}},{{5,10,-2048},{4,9,-1024},{4,8,-512},{4,7,-256},{5,6,-128},{5,5,-64},{4,5,-32},{2,7,0},{3,7,128},{3,8,256},{4,9,512},{4,10,1024},{6,32,-2049,999},{6,32,2048}},{{4,9,-1024},{3,8,-512},{4,7,-256},{5,6,-128},{5,5,-64},{4,5,-32},{4,5,0},{5,5,32},{5,6,64},{4,7,128},{3,8,256},{3,9,512},{3,10,1024},{5,32,-1025,999},{5,32,2048}},{{8,3,-15},{9,1,-7},{8,1,-5},{9,0,-3},{7,0,-2},{4,0,-1},{2,1,0},{5,0,2},{6,0,3},{3,4,4},{6,1,20},{4,4,22},{4,5,38},{5,6,70},{5,7,134},{6,7,262},{7,8,390},{6,10,646},{9,32,-16,999},{9,32,1670},{2,-1,0}},{{8,4,-31},{9,2,-15},{8,2,-11},{9,1,-7},{7,1,-5},{4,1,-3},{3,1,-1},{3,1,1},{5,1,3},{6,1,5},{3,5,7},{6,2,39},{4,5,43},{4,6,75},{5,7,139},{5,8,267},{6,8,523},{7,9,779},{6,11,1291},{9,32,-32,999},{9,32,3339},{2,-1,0}},{{7,4,-21},{8,0,-5},{7,0,-4},{5,0,-3},{2,2,-2},{5,0,2},{6,0,3},{7,0,4},{8,0,5},{2,6,6},{5,5,70},{6,5,102},{6,6,134},{6,7,198},{6,8,326},{6,9,582},{6,10,1094},{7,11,2118},{8,32,-22,999},{8,32,4166},{2,-1,0}},{{1,0,1},{2,1,2},{4,0,4},{4,1,5},{5,1,7},{5,2,9},{6,2,13},{7,2,17},{7,3,21},{7,4,29},{7,5,45},{7,6,77},{7,32,141}},{{1,0,1},{2,0,2},{3,1,3},{5,0,5},{5,1,6},{6,1,8},{7,0,10},{7,1,11},{7,2,13},{7,3,17},{7,4,25},{8,5,41},{8,32,73}},{{1,0,1},{3,0,2},{4,0,3},{5,0,4},{4,1,5},{3,3,7},{6,1,15},{6,2,17},{6,3,21},{6,4,29},{6,5,45},{7,6,77},{7,32,141}},{{3,0,-2},{3,0,-1},{1,0,0},{3,0,1},{3,0,2}},{{7,4,-24},{6,2,-8},{5,1,-4},{4,0,-2},{3,0,-1},{1,0,0},{3,0,1},{4,0,2},{5,1,3},{6,2,5},{7,4,9},{7,32,-25,999},{7,32,25}}};
func NewCode (prefixLength ,rangeLength ,rangeLow int32 ,isLowerRange bool )*Code {return &Code {_eed :prefixLength ,_eag :rangeLength ,_bga :rangeLow ,_bbf :isLowerRange ,_ggc :-1};};func (_bg *FixedSizeTable )InitTree (codeTable []*Code )error {_ebe (codeTable );
for _ ,_ba :=range codeTable {_aee :=_bg ._bde .append (_ba );if _aee !=nil {return _aee ;};};return nil ;};func (_gcd *InternalNode )append (_caee *Code )(_de error ){if _caee ._eed ==0{return nil ;};_ef :=_caee ._eed -1-_gcd ._cd ;if _ef < 0{return _gb .New ("\u004e\u0065\u0067\u0061\u0074\u0069\u0076\u0065\u0020\u0073\u0068\u0069\u0066\u0074\u0069n\u0067 \u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u006c\u006c\u006f\u0077\u0065\u0064");
};_bbg :=(_caee ._ggc >>uint (_ef ))&0x1;if _ef ==0{if _caee ._eag ==-1{if _bbg ==1{if _gcd ._be !=nil {return _ca .Errorf ("O\u004f\u0042\u0020\u0061\u006c\u0072e\u0061\u0064\u0079\u0020\u0073\u0065\u0074\u0020\u0066o\u0072\u0020\u0063o\u0064e\u0020\u0025\u0073",_caee );
};_gcd ._be =_cae (_caee );}else {if _gcd ._fa !=nil {return _ca .Errorf ("O\u004f\u0042\u0020\u0061\u006c\u0072e\u0061\u0064\u0079\u0020\u0073\u0065\u0074\u0020\u0066o\u0072\u0020\u0063o\u0064e\u0020\u0025\u0073",_caee );};_gcd ._fa =_cae (_caee );};}else {if _bbg ==1{if _gcd ._be !=nil {return _ca .Errorf ("\u0056\u0061\u006cue\u0020\u004e\u006f\u0064\u0065\u0020\u0061\u006c\u0072e\u0061d\u0079 \u0073e\u0074\u0020\u0066\u006f\u0072\u0020\u0063\u006f\u0064\u0065\u0020\u0025\u0073",_caee );
};_gcd ._be =_fg (_caee );}else {if _gcd ._fa !=nil {return _ca .Errorf ("\u0056\u0061\u006cue\u0020\u004e\u006f\u0064\u0065\u0020\u0061\u006c\u0072e\u0061d\u0079 \u0073e\u0074\u0020\u0066\u006f\u0072\u0020\u0063\u006f\u0064\u0065\u0020\u0025\u0073",_caee );
};_gcd ._fa =_fg (_caee );};};}else {if _bbg ==1{if _gcd ._be ==nil {_gcd ._be =_bec (_gcd ._cd +1);};if _de =_gcd ._be .(*InternalNode ).append (_caee );_de !=nil {return _de ;};}else {if _gcd ._fa ==nil {_gcd ._fa =_bec (_gcd ._cd +1);};if _de =_gcd ._fa .(*InternalNode ).append (_caee );
_de !=nil {return _de ;};};};return nil ;};var _ Node =&OutOfBandNode {};func (_dgf *StandardTable )String ()string {return _dgf ._aa .String ()+"\u000a"};func (_aef *FixedSizeTable )RootNode ()*InternalNode {return _aef ._bde };type FixedSizeTable struct{_bde *InternalNode };
func (_faef *InternalNode )pad (_cfc *_d .Builder ){for _ega :=int32 (0);_ega < _faef ._cd ;_ega ++{_cfc .WriteString ("\u0020\u0020\u0020");};};type Tabler interface{Decode (_ecc *_bd .Reader )(int64 ,error );InitTree (_eea []*Code )error ;String ()string ;
RootNode ()*InternalNode ;};var _ Tabler =&EncodedTable {};func (_bc *EncodedTable )RootNode ()*InternalNode {return _bc ._f };func (_ga *EncodedTable )String ()string {return _ga ._f .String ()+"\u000a"};func (_ec *EncodedTable )parseTable ()error {var (_fc []*Code ;
_bf ,_cb ,_ed int32 ;_ff uint64 ;_fb error ;);_ae :=_ec .StreamReader ();_cbd :=_ec .HtLow ();for _cbd < _ec .HtHigh (){_ff ,_fb =_ae .ReadBits (byte (_ec .HtPS ()));if _fb !=nil {return _fb ;};_bf =int32 (_ff );_ff ,_fb =_ae .ReadBits (byte (_ec .HtRS ()));
if _fb !=nil {return _fb ;};_cb =int32 (_ff );_fc =append (_fc ,NewCode (_bf ,_cb ,_ed ,false ));_cbd +=1<<uint (_cb );};_ff ,_fb =_ae .ReadBits (byte (_ec .HtPS ()));if _fb !=nil {return _fb ;};_bf =int32 (_ff );_cb =32;_ed =_ec .HtLow ()-1;_fc =append (_fc ,NewCode (_bf ,_cb ,_ed ,true ));
_ff ,_fb =_ae .ReadBits (byte (_ec .HtPS ()));if _fb !=nil {return _fb ;};_bf =int32 (_ff );_cb =32;_ed =_ec .HtHigh ();_fc =append (_fc ,NewCode (_bf ,_cb ,_ed ,false ));if _ec .HtOOB ()==1{_ff ,_fb =_ae .ReadBits (byte (_ec .HtPS ()));if _fb !=nil {return _fb ;
};_bf =int32 (_ff );_fc =append (_fc ,NewCode (_bf ,-1,-1,false ));};if _fb =_ec .InitTree (_fc );_fb !=nil {return _fb ;};return nil ;};type InternalNode struct{_cd int32 ;_fa Node ;_be Node ;};func _ced (_ded ,_bdcg int32 )string {var _egd int32 ;_bagc :=make ([]rune ,_bdcg );
for _ggg :=int32 (1);_ggg <=_bdcg ;_ggg ++{_egd =_ded >>uint (_bdcg -_ggg )&1;if _egd !=0{_bagc [_ggg -1]='1';}else {_bagc [_ggg -1]='0';};};return string (_bagc );};type StandardTable struct{_aa *InternalNode };type ValueNode struct{_ad int32 ;_bgd int32 ;
_ffb bool ;};type BasicTabler interface{HtHigh ()int32 ;HtLow ()int32 ;StreamReader ()*_bd .Reader ;HtPS ()int32 ;HtRS ()int32 ;HtOOB ()int32 ;};func GetStandardTable (number int )(Tabler ,error ){if number <=0||number > len (_bdc ){return nil ,_gb .New ("\u0049n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_ge :=_bdc [number -1];if _ge ==nil {var _cc error ;_ge ,_cc =_dcd (_ffba [number -1]);if _cc !=nil {return nil ,_cc ;};_bdc [number -1]=_ge ;};return _ge ,nil ;};func NewEncodedTable (table BasicTabler )(*EncodedTable ,error ){_ce :=&EncodedTable {_f :&InternalNode {},BasicTabler :table };
if _a :=_ce .parseTable ();_a !=nil {return nil ,_a ;};return _ce ,nil ;};type OutOfBandNode struct{};func (_dde *StandardTable )InitTree (codeTable []*Code )error {_ebe (codeTable );for _ ,_feg :=range codeTable {if _bbb :=_dde ._aa .append (_feg );_bbb !=nil {return _bbb ;
};};return nil ;};func _ecf (_da ,_bbbc int32 )int32 {if _da > _bbbc {return _da ;};return _bbbc ;};func (_cg *InternalNode )String ()string {_dg :=&_d .Builder {};_dg .WriteString ("\u000a");_cg .pad (_dg );_dg .WriteString ("\u0030\u003a\u0020");_dg .WriteString (_cg ._fa .String ()+"\u000a");
_cg .pad (_dg );_dg .WriteString ("\u0031\u003a\u0020");_dg .WriteString (_cg ._be .String ()+"\u000a");return _dg .String ();};func _ebe (_bcd []*Code ){var _eccc int32 ;for _ ,_ag :=range _bcd {_eccc =_ecf (_eccc ,_ag ._eed );};_dee :=make ([]int32 ,_eccc +1);
for _ ,_bcde :=range _bcd {_dee [_bcde ._eed ]++;};var _cgd int32 ;_faa :=make ([]int32 ,len (_dee )+1);_dee [0]=0;for _ceb :=int32 (1);_ceb <=int32 (len (_dee ));_ceb ++{_faa [_ceb ]=(_faa [_ceb -1]+(_dee [_ceb -1]))<<1;_cgd =_faa [_ceb ];for _ ,_bgc :=range _bcd {if _bgc ._eed ==_ceb {_bgc ._ggc =_cgd ;
_cgd ++;};};};};func NewFixedSizeTable (codeTable []*Code )(*FixedSizeTable ,error ){_gdd :=&FixedSizeTable {_bde :&InternalNode {}};if _bb :=_gdd .InitTree (codeTable );_bb !=nil {return nil ,_bb ;};return _gdd ,nil ;};func (_edd *ValueNode )String ()string {return _ca .Sprintf ("\u0025\u0064\u002f%\u0064",_edd ._ad ,_edd ._bgd );
};type Code struct{_eed int32 ;_eag int32 ;_bga int32 ;_bbf bool ;_ggc int32 ;};func (_cf *InternalNode )Decode (r *_bd .Reader )(int64 ,error ){_cab ,_fae :=r .ReadBit ();if _fae !=nil {return 0,_fae ;};if _cab ==0{return _cf ._fa .Decode (r );};return _cf ._be .Decode (r );
};func (_ea *EncodedTable )Decode (r *_bd .Reader )(int64 ,error ){return _ea ._f .Decode (r )};func (_aea *FixedSizeTable )String ()string {return _aea ._bde .String ()+"\u000a"};var _bdc =make ([]Tabler ,len (_ffba ));func (_ac *StandardTable )Decode (r *_bd .Reader )(int64 ,error ){return _ac ._aa .Decode (r )};
type Node interface{Decode (_bff *_bd .Reader )(int64 ,error );String ()string ;};func (_bge *StandardTable )RootNode ()*InternalNode {return _bge ._aa };func _fg (_fe *Code )*ValueNode {return &ValueNode {_ad :_fe ._eag ,_bgd :_fe ._bga ,_ffb :_fe ._bbf }};
var _ Node =&ValueNode {};func (_eb *OutOfBandNode )String ()string {return _ca .Sprintf ("\u0025\u0030\u00364\u0062",int64 (_c .MaxInt64 ));};func _cae (_aead *Code )*OutOfBandNode {return &OutOfBandNode {}};func _bec (_cbdc int32 )*InternalNode {return &InternalNode {_cd :_cbdc }};
type EncodedTable struct{BasicTabler ;_f *InternalNode ;};var _ Node =&InternalNode {};