//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package jbig2 ;import (_f "github.com/unidoc/unipdf/v4/internal/bitwise";_ab "github.com/unidoc/unipdf/v4/internal/jbig2/decoder";_b "github.com/unidoc/unipdf/v4/internal/jbig2/document";_e "github.com/unidoc/unipdf/v4/internal/jbig2/document/segments";
_c "github.com/unidoc/unipdf/v4/internal/jbig2/errors";_af "sort";);func DecodeBytes (encoded []byte ,parameters _ab .Parameters ,globals ...Globals )([]byte ,error ){var _ca Globals ;if len (globals )> 0{_ca =globals [0];};_cg ,_be :=_ab .Decode (encoded ,parameters ,_ca .ToDocumentGlobals ());
if _be !=nil {return nil ,_be ;};return _cg .DecodeNextPage ();};func DecodeGlobals (encoded []byte )(Globals ,error ){const _g ="\u0044\u0065\u0063\u006f\u0064\u0065\u0047\u006c\u006f\u0062\u0061\u006c\u0073";_bc :=_f .NewReader (encoded );_bg ,_d :=_b .DecodeDocument (_bc ,nil );
if _d !=nil {return nil ,_c .Wrap (_d ,_g ,"");};if _bg .GlobalSegments ==nil ||(_bg .GlobalSegments .Segments ==nil ){return nil ,_c .Error (_g ,"\u006eo\u0020\u0067\u006c\u006f\u0062\u0061\u006c\u0020\u0073\u0065\u0067m\u0065\u006e\u0074\u0073\u0020\u0066\u006f\u0075\u006e\u0064");
};_ac :=Globals {};for _ ,_ae :=range _bg .GlobalSegments .Segments {_ac [int (_ae .SegmentNumber )]=_ae ;};return _ac ,nil ;};func (_bfc Globals )ToDocumentGlobals ()*_b .Globals {if _bfc ==nil {return nil ;};_aa :=[]*_e .Header {};for _ ,_abd :=range _bfc {_aa =append (_aa ,_abd );
};_af .Slice (_aa ,func (_bb ,_beb int )bool {return _aa [_bb ].SegmentNumber < _aa [_beb ].SegmentNumber });return &_b .Globals {Segments :_aa };};type Globals map[int ]*_e .Header ;