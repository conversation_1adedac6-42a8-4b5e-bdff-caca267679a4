//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package arithmetic ;import (_e "bytes";_c "github.com/unidoc/unipdf/v4/common";_a "github.com/unidoc/unipdf/v4/internal/jbig2/bitmap";_ef "github.com/unidoc/unipdf/v4/internal/jbig2/errors";_d "io";);func (_ag *Encoder )Init (){_ag ._gd =_fa (_de );_ag ._ff =0x8000;
_ag ._aa =0;_ag ._ffg =12;_ag ._ee =-1;_ag ._gag =0;_ag ._afb =0;_ag ._af =make ([]byte ,_ggde );for _ce :=0;_ce < len (_ag ._acbg );_ce ++{_ag ._acbg [_ce ]=_fa (512);};_ag ._eaa =nil ;};func (_eag *Encoder )Flush (){_eag ._afb =0;_eag ._gad =nil ;_eag ._ee =-1};
func (_cebe *Encoder )encodeOOB (_ceg Class )error {_fefg :=_cebe ._acbg [_ceg ];_gda :=_cebe .encodeBit (_fefg ,1,1);if _gda !=nil {return _gda ;};_gda =_cebe .encodeBit (_fefg ,3,0);if _gda !=nil {return _gda ;};_gda =_cebe .encodeBit (_fefg ,6,0);if _gda !=nil {return _gda ;
};_gda =_cebe .encodeBit (_fefg ,12,0);if _gda !=nil {return _gda ;};return nil ;};func (_aee *Encoder )Reset (){_aee ._ff =0x8000;_aee ._aa =0;_aee ._ffg =12;_aee ._ee =-1;_aee ._gag =0;_aee ._eaa =nil ;_aee ._gd =_fa (_de );};var _ _d .WriterTo =&Encoder {};
var _f =[]intEncRangeS {{0,3,0,2,0,2},{-1,-1,9,4,0,0},{-3,-2,5,3,2,1},{4,19,2,3,4,4},{-19,-4,3,3,4,4},{20,83,6,4,20,6},{-83,-20,7,4,20,6},{84,339,14,5,84,8},{-339,-84,15,5,84,8},{340,4435,30,6,340,12},{-4435,-340,31,6,340,12},{4436,2000000000,62,6,4436,32},{-2000000000,-4436,63,6,4436,32}};
func (_bbb *Encoder )Refine (iTemp ,iTarget *_a .Bitmap ,ox ,oy int )error {for _ggb :=0;_ggb < iTarget .Height ;_ggb ++{var _gcf int ;_agb :=_ggb +oy ;var (_dad ,_ggd ,_aaef ,_ede ,_gcfa uint16 ;_ge ,_bc ,_aca ,_fe ,_egd byte ;);if _agb >=1&&(_agb -1)< iTemp .Height {_ge =iTemp .Data [(_agb -1)*iTemp .RowStride ];
};if _agb >=0&&_agb < iTemp .Height {_bc =iTemp .Data [_agb *iTemp .RowStride ];};if _agb >=-1&&_agb +1< iTemp .Height {_aca =iTemp .Data [(_agb +1)*iTemp .RowStride ];};if _ggb >=1{_fe =iTarget .Data [(_ggb -1)*iTarget .RowStride ];};_egd =iTarget .Data [_ggb *iTarget .RowStride ];
_eed :=uint (6+ox );_dad =uint16 (_ge >>_eed );_ggd =uint16 (_bc >>_eed );_aaef =uint16 (_aca >>_eed );_ede =uint16 (_fe >>6);_cg :=uint (2-ox );_ge <<=_cg ;_bc <<=_cg ;_aca <<=_cg ;_fe <<=2;for _gcf =0;_gcf < iTarget .Width ;_gcf ++{_fdg :=(_dad <<10)|(_ggd <<7)|(_aaef <<4)|(_ede <<1)|_gcfa ;
_ccd :=_egd >>7;_dae :=_bbb .encodeBit (_bbb ._gd ,uint32 (_fdg ),_ccd );if _dae !=nil {return _dae ;};_dad <<=1;_ggd <<=1;_aaef <<=1;_ede <<=1;_dad |=uint16 (_ge >>7);_ggd |=uint16 (_bc >>7);_aaef |=uint16 (_aca >>7);_ede |=uint16 (_fe >>7);_gcfa =uint16 (_ccd );
_fef :=_gcf %8;_bfa :=_gcf /8+1;if _fef ==5+ox {_ge ,_bc ,_aca =0,0,0;if _bfa < iTemp .RowStride &&_agb >=1&&(_agb -1)< iTemp .Height {_ge =iTemp .Data [(_agb -1)*iTemp .RowStride +_bfa ];};if _bfa < iTemp .RowStride &&_agb >=0&&_agb < iTemp .Height {_bc =iTemp .Data [_agb *iTemp .RowStride +_bfa ];
};if _bfa < iTemp .RowStride &&_agb >=-1&&(_agb +1)< iTemp .Height {_aca =iTemp .Data [(_agb +1)*iTemp .RowStride +_bfa ];};}else {_ge <<=1;_bc <<=1;_aca <<=1;};if _fef ==5&&_ggb >=1{_fe =0;if _bfa < iTarget .RowStride {_fe =iTarget .Data [(_ggb -1)*iTarget .RowStride +_bfa ];
};}else {_fe <<=1;};if _fef ==7{_egd =0;if _bfa < iTarget .RowStride {_egd =iTarget .Data [_ggb *iTarget .RowStride +_bfa ];};}else {_egd <<=1;};_dad &=7;_ggd &=7;_aaef &=7;_ede &=7;};};return nil ;};func (_gba *Encoder )EncodeOOB (proc Class )(_bgg error ){_c .Log .Trace ("E\u006e\u0063\u006f\u0064\u0065\u0020O\u004f\u0042\u0020\u0077\u0069\u0074\u0068\u0020\u0043l\u0061\u0073\u0073:\u0020'\u0025\u0073\u0027",proc );
if _bgg =_gba .encodeOOB (proc );_bgg !=nil {return _ef .Wrap (_bgg ,"\u0045n\u0063\u006f\u0064\u0065\u004f\u004fB","");};return nil ;};type Encoder struct{_aa uint32 ;_ff uint16 ;_ffg ,_gag uint8 ;_ee int ;_cbb int ;_gad [][]byte ;_af []byte ;_afb int ;
_gd *codingContext ;_acbg [13]*codingContext ;_eaa *codingContext ;};func New ()*Encoder {_fb :=&Encoder {};_fb .Init ();return _fb };const _aae =0x9b25;func _fa (_db int )*codingContext {return &codingContext {_ac :make ([]byte ,_db ),_ea :make ([]byte ,_db )};
};func (_bebe *Encoder )codeLPS (_ada *codingContext ,_bcc uint32 ,_fec uint16 ,_bgb byte ){_bebe ._ff -=_fec ;if _bebe ._ff < _fec {_bebe ._aa +=uint32 (_fec );}else {_bebe ._ff =_fec ;};if _cagf [_bgb ]._dec ==1{_ada .flipMps (_bcc );};_ada ._ac [_bcc ]=_cagf [_bgb ]._geb ;
_bebe .renormalize ();};func (_cbd *Encoder )byteOut (){if _cbd ._gag ==0xff{_cbd .rBlock ();return ;};if _cbd ._aa < 0x8000000{_cbd .lBlock ();return ;};_cbd ._gag ++;if _cbd ._gag !=0xff{_cbd .lBlock ();return ;};_cbd ._aa &=0x7ffffff;_cbd .rBlock ();
};func (_facd *Encoder )flush (){_facd .setBits ();_facd ._aa <<=_facd ._ffg ;_facd .byteOut ();_facd ._aa <<=_facd ._ffg ;_facd .byteOut ();_facd .emit ();if _facd ._gag !=0xff{_facd ._ee ++;_facd ._gag =0xff;_facd .emit ();};_facd ._ee ++;_facd ._gag =0xac;
_facd ._ee ++;_facd .emit ();};func (_eg *Encoder )Final (){_eg .flush ()};func (_aab *Encoder )encodeInteger (_eff Class ,_gdff int )error {const _dg ="E\u006e\u0063\u006f\u0064er\u002ee\u006e\u0063\u006f\u0064\u0065I\u006e\u0074\u0065\u0067\u0065\u0072";
if _gdff > 2000000000||_gdff < -2000000000{return _ef .Errorf (_dg ,"\u0061\u0072\u0069\u0074\u0068\u006d\u0065\u0074i\u0063\u0020\u0065nc\u006f\u0064\u0065\u0072\u0020\u002d \u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072 \u0076\u0061\u006c\u0075\u0065\u003a\u0020\u0027%\u0064\u0027",_gdff );
};_daa :=_aab ._acbg [_eff ];_cf :=uint32 (1);var _ecc int ;for ;;_ecc ++{if _f [_ecc ]._ab <=_gdff &&_f [_ecc ]._g >=_gdff {break ;};};if _gdff < 0{_gdff =-_gdff ;};_gdff -=int (_f [_ecc ]._bf );_abc :=_f [_ecc ]._bd ;for _ege :=uint8 (0);_ege < _f [_ecc ]._ae ;
_ege ++{_dfe :=_abc &1;if _ebd :=_aab .encodeBit (_daa ,_cf ,_dfe );_ebd !=nil {return _ef .Wrap (_ebd ,_dg ,"");};_abc >>=1;if _cf &0x100> 0{_cf =(((_cf <<1)|uint32 (_dfe ))&0x1ff)|0x100;}else {_cf =(_cf <<1)|uint32 (_dfe );};};_gdff <<=32-_f [_ecc ]._bdc ;
for _add :=uint8 (0);_add < _f [_ecc ]._bdc ;_add ++{_eda :=uint8 ((uint32 (_gdff )&0x80000000)>>31);if _agfb :=_aab .encodeBit (_daa ,_cf ,_eda );_agfb !=nil {return _ef .Wrap (_agfb ,_dg ,"\u006d\u006f\u0076\u0065 \u0064\u0061\u0074\u0061\u0020\u0074\u006f\u0020\u0074\u0068e\u0020t\u006f\u0070\u0020\u006f\u0066\u0020\u0077o\u0072\u0064");
};_gdff <<=1;if _cf &0x100!=0{_cf =(((_cf <<1)|uint32 (_eda ))&0x1ff)|0x100;}else {_cf =(_cf <<1)|uint32 (_eda );};};return nil ;};func (_df Class )String ()string {switch _df {case IAAI :return "\u0049\u0041\u0041\u0049";case IADH :return "\u0049\u0041\u0044\u0048";
case IADS :return "\u0049\u0041\u0044\u0053";case IADT :return "\u0049\u0041\u0044\u0054";case IADW :return "\u0049\u0041\u0044\u0057";case IAEX :return "\u0049\u0041\u0045\u0058";case IAFS :return "\u0049\u0041\u0046\u0053";case IAIT :return "\u0049\u0041\u0049\u0054";
case IARDH :return "\u0049\u0041\u0052D\u0048";case IARDW :return "\u0049\u0041\u0052D\u0057";case IARDX :return "\u0049\u0041\u0052D\u0058";case IARDY :return "\u0049\u0041\u0052D\u0059";case IARI :return "\u0049\u0041\u0052\u0049";default:return "\u0055N\u004b\u004e\u004f\u0057\u004e";
};};type state struct{_ccdf uint16 ;_bfe ,_geb uint8 ;_dec uint8 ;};func (_fc *Encoder )dataSize ()int {return _ggde *len (_fc ._gad )+_fc ._afb };func (_fg *Encoder )DataSize ()int {return _fg .dataSize ()};func (_eeea *Encoder )encodeIAID (_gdc ,_cef int )error {if _eeea ._eaa ==nil {_eeea ._eaa =_fa (1<<uint (_gdc ));
};_ggbg :=uint32 (1<<uint32 (_gdc +1))-1;_cef <<=uint (32-_gdc );_cag :=uint32 (1);for _eeg :=0;_eeg < _gdc ;_eeg ++{_bce :=_cag &_ggbg ;_dfef :=uint8 ((uint32 (_cef )&0x80000000)>>31);if _abf :=_eeea .encodeBit (_eeea ._eaa ,_bce ,_dfef );_abf !=nil {return _abf ;
};_cag =(_cag <<1)|uint32 (_dfef );_cef <<=1;};return nil ;};func (_dadg *Encoder )codeMPS (_eec *codingContext ,_ecb uint32 ,_bef uint16 ,_bfd byte ){_dadg ._ff -=_bef ;if _dadg ._ff &0x8000!=0{_dadg ._aa +=uint32 (_bef );return ;};if _dadg ._ff < _bef {_dadg ._ff =_bef ;
}else {_dadg ._aa +=uint32 (_bef );};_eec ._ac [_ecb ]=_cagf [_bfd ]._bfe ;_dadg .renormalize ();};func (_ca *Encoder )EncodeIAID (symbolCodeLength ,value int )(_eb error ){_c .Log .Trace ("\u0045\u006e\u0063\u006f\u0064\u0065\u0020\u0049A\u0049\u0044\u002e S\u0079\u006d\u0062\u006f\u006c\u0043o\u0064\u0065\u004c\u0065\u006e\u0067\u0074\u0068\u003a\u0020\u0027\u0025\u0064\u0027\u002c \u0056\u0061\u006c\u0075\u0065\u003a\u0020\u0027%\u0064\u0027",symbolCodeLength ,value );
if _eb =_ca .encodeIAID (symbolCodeLength ,value );_eb !=nil {return _ef .Wrap (_eb ,"\u0045\u006e\u0063\u006f\u0064\u0065\u0049\u0041\u0049\u0044","");};return nil ;};func (_gg *Encoder )EncodeInteger (proc Class ,value int )(_dbf error ){_c .Log .Trace ("\u0045\u006eco\u0064\u0065\u0020I\u006e\u0074\u0065\u0067er:\u0027%d\u0027\u0020\u0077\u0069\u0074\u0068\u0020Cl\u0061\u0073\u0073\u003a\u0020\u0027\u0025s\u0027",value ,proc );
if _dbf =_gg .encodeInteger (proc ,value );_dbf !=nil {return _ef .Wrap (_dbf ,"\u0045\u006e\u0063\u006f\u0064\u0065\u0049\u006e\u0074\u0065\u0067\u0065\u0072","");};return nil ;};type intEncRangeS struct{_ab ,_g int ;_bd ,_ae uint8 ;_bf uint16 ;_bdc uint8 ;
};const (_de =65536;_ggde =20*1024;);const (IAAI Class =iota ;IADH ;IADS ;IADT ;IADW ;IAEX ;IAFS ;IAIT ;IARDH ;IARDW ;IARDX ;IARDY ;IARI ;);func (_ade *Encoder )renormalize (){for {_ade ._ff <<=1;_ade ._aa <<=1;_ade ._ffg --;if _ade ._ffg ==0{_ade .byteOut ();
};if (_ade ._ff &0x8000)!=0{break ;};};};func (_fafe *Encoder )encodeBit (_fad *codingContext ,_fca uint32 ,_dd uint8 )error {const _efe ="\u0045\u006e\u0063\u006f\u0064\u0065\u0072\u002e\u0065\u006e\u0063\u006fd\u0065\u0042\u0069\u0074";_fafe ._cbb ++;
if _fca >=uint32 (len (_fad ._ac )){return _ef .Errorf (_efe ,"\u0061r\u0069\u0074h\u006d\u0065\u0074i\u0063\u0020\u0065\u006e\u0063\u006f\u0064e\u0072\u0020\u002d\u0020\u0069\u006ev\u0061\u006c\u0069\u0064\u0020\u0063\u0074\u0078\u0020\u006e\u0075m\u0062\u0065\u0072\u003a\u0020\u0027\u0025\u0064\u0027",_fca );
};_bgbf :=_fad ._ac [_fca ];_cea :=_fad .mps (_fca );_gcb :=_cagf [_bgbf ]._ccdf ;_c .Log .Trace ("\u0045\u0043\u003a\u0020\u0025d\u0009\u0020D\u003a\u0020\u0025d\u0009\u0020\u0049\u003a\u0020\u0025d\u0009\u0020\u004dPS\u003a \u0025\u0064\u0009\u0020\u0051\u0045\u003a \u0025\u0030\u0034\u0058\u0009\u0020\u0020\u0041\u003a\u0020\u0025\u0030\u0034\u0058\u0009\u0020\u0043\u003a %\u0030\u0038\u0058\u0009\u0020\u0043\u0054\u003a\u0020\u0025\u0064\u0009\u0020\u0042\u003a\u0020\u0025\u0030\u0032\u0058\u0009\u0020\u0042\u0050\u003a\u0020\u0025\u0064",_fafe ._cbb ,_dd ,_bgbf ,_cea ,_gcb ,_fafe ._ff ,_fafe ._aa ,_fafe ._ffg ,_fafe ._gag ,_fafe ._ee );
if _dd ==0{_fafe .code0 (_fad ,_fca ,_gcb ,_bgbf );}else {_fafe .code1 (_fad ,_fca ,_gcb ,_bgbf );};return nil ;};func (_gcc *Encoder )WriteTo (w _d .Writer )(int64 ,error ){const _abb ="\u0045n\u0063o\u0064\u0065\u0072\u002e\u0057\u0072\u0069\u0074\u0065\u0054\u006f";
var _ggg int64 ;for _eaaa ,_fac :=range _gcc ._gad {_ec ,_gf :=w .Write (_fac );if _gf !=nil {return 0,_ef .Wrapf (_gf ,_abb ,"\u0066\u0061\u0069\u006c\u0065\u0064\u0020\u0061\u0074\u0020\u0069'\u0074\u0068\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u0063h\u0075\u006e\u006b",_eaaa );
};_ggg +=int64 (_ec );};_gcc ._af =_gcc ._af [:_gcc ._afb ];_agd ,_gca :=w .Write (_gcc ._af );if _gca !=nil {return 0,_ef .Wrap (_gca ,_abb ,"\u0062u\u0066f\u0065\u0072\u0065\u0064\u0020\u0063\u0068\u0075\u006e\u006b\u0073");};_ggg +=int64 (_agd );return _ggg ,nil ;
};func (_faf *Encoder )code0 (_agbd *codingContext ,_gde uint32 ,_acg uint16 ,_fee byte ){if _agbd .mps (_gde )==0{_faf .codeMPS (_agbd ,_gde ,_acg ,_fee );}else {_faf .codeLPS (_agbd ,_gde ,_acg ,_fee );};};type codingContext struct{_ac []byte ;_ea []byte ;
};func (_acb *codingContext )mps (_aec uint32 )int {return int (_acb ._ea [_aec ])};type Class int ;func (_bbd *Encoder )rBlock (){if _bbd ._ee >=0{_bbd .emit ();};_bbd ._ee ++;_bbd ._gag =uint8 (_bbd ._aa >>20);_bbd ._aa &=0xfffff;_bbd ._ffg =7;};func (_cbf *Encoder )setBits (){_bgc :=_cbf ._aa +uint32 (_cbf ._ff );
_cbf ._aa |=0xffff;if _cbf ._aa >=_bgc {_cbf ._aa -=0x8000;};};var _cagf =[]state {{0x5601,1,1,1},{0x3401,2,6,0},{0x1801,3,9,0},{0x0AC1,4,12,0},{0x0521,5,29,0},{0x0221,38,33,0},{0x5601,7,6,1},{0x5401,8,14,0},{0x4801,9,14,0},{0x3801,10,14,0},{0x3001,11,17,0},{0x2401,12,18,0},{0x1C01,13,20,0},{0x1601,29,21,0},{0x5601,15,14,1},{0x5401,16,14,0},{0x5101,17,15,0},{0x4801,18,16,0},{0x3801,19,17,0},{0x3401,20,18,0},{0x3001,21,19,0},{0x2801,22,19,0},{0x2401,23,20,0},{0x2201,24,21,0},{0x1C01,25,22,0},{0x1801,26,23,0},{0x1601,27,24,0},{0x1401,28,25,0},{0x1201,29,26,0},{0x1101,30,27,0},{0x0AC1,31,28,0},{0x09C1,32,29,0},{0x08A1,33,30,0},{0x0521,34,31,0},{0x0441,35,32,0},{0x02A1,36,33,0},{0x0221,37,34,0},{0x0141,38,35,0},{0x0111,39,36,0},{0x0085,40,37,0},{0x0049,41,38,0},{0x0025,42,39,0},{0x0015,43,40,0},{0x0009,44,41,0},{0x0005,45,42,0},{0x0001,45,43,0},{0x5601,46,46,0}};
func (_cb *codingContext )flipMps (_ga uint32 ){_cb ._ea [_ga ]=1-_cb ._ea [_ga ]};func (_be *Encoder )EncodeBitmap (bm *_a .Bitmap ,duplicateLineRemoval bool )error {_c .Log .Trace ("\u0045n\u0063\u006f\u0064\u0065 \u0042\u0069\u0074\u006d\u0061p\u0020[\u0025d\u0078\u0025\u0064\u005d\u002c\u0020\u0025s",bm .Width ,bm .Height ,bm );
var (_eef ,_beb uint8 ;_ed ,_fae ,_eea uint16 ;_fga ,_da ,_ad byte ;_gb ,_cc ,_abe int ;_fd ,_edc []byte ;);for _bg :=0;_bg < bm .Height ;_bg ++{_fga ,_da =0,0;if _bg >=2{_fga =bm .Data [(_bg -2)*bm .RowStride ];};if _bg >=1{_da =bm .Data [(_bg -1)*bm .RowStride ];
if duplicateLineRemoval {_cc =_bg *bm .RowStride ;_fd =bm .Data [_cc :_cc +bm .RowStride ];_abe =(_bg -1)*bm .RowStride ;_edc =bm .Data [_abe :_abe +bm .RowStride ];if _e .Equal (_fd ,_edc ){_beb =_eef ^1;_eef =1;}else {_beb =_eef ;_eef =0;};};};if duplicateLineRemoval {if _agf :=_be .encodeBit (_be ._gd ,_aae ,_beb );
_agf !=nil {return _agf ;};if _eef !=0{continue ;};};_ad =bm .Data [_bg *bm .RowStride ];_ed =uint16 (_fga >>5);_fae =uint16 (_da >>4);_fga <<=3;_da <<=4;_eea =0;for _gb =0;_gb < bm .Width ;_gb ++{_acc :=uint32 (_ed <<11|_fae <<4|_eea );_cd :=(_ad &0x80)>>7;
_bb :=_be .encodeBit (_be ._gd ,_acc ,_cd );if _bb !=nil {return _bb ;};_ed <<=1;_fae <<=1;_eea <<=1;_ed |=uint16 ((_fga &0x80)>>7);_fae |=uint16 ((_da &0x80)>>7);_eea |=uint16 (_cd );_gc :=_gb %8;_aga :=_gb /8+1;if _gc ==4&&_bg >=2{_fga =0;if _aga < bm .RowStride {_fga =bm .Data [(_bg -2)*bm .RowStride +_aga ];
};}else {_fga <<=1;};if _gc ==3&&_bg >=1{_da =0;if _aga < bm .RowStride {_da =bm .Data [(_bg -1)*bm .RowStride +_aga ];};}else {_da <<=1;};if _gc ==7{_ad =0;if _aga < bm .RowStride {_ad =bm .Data [_bg *bm .RowStride +_aga ];};}else {_ad <<=1;};_ed &=31;
_fae &=127;_eea &=15;};};return nil ;};func (_aaf *Encoder )emit (){if _aaf ._afb ==_ggde {_aaf ._gad =append (_aaf ._gad ,_aaf ._af );_aaf ._af =make ([]byte ,_ggde );_aaf ._afb =0;};_aaf ._af [_aaf ._afb ]=_aaf ._gag ;_aaf ._afb ++;};func (_fbg *Encoder )lBlock (){if _fbg ._ee >=0{_fbg .emit ();
};_fbg ._ee ++;_fbg ._gag =uint8 (_fbg ._aa >>19);_fbg ._aa &=0x7ffff;_fbg ._ffg =8;};func (_ceb *Encoder )code1 (_eee *codingContext ,_gdf uint32 ,_fgf uint16 ,_egc byte ){if _eee .mps (_gdf )==1{_ceb .codeMPS (_eee ,_gdf ,_fgf ,_egc );}else {_ceb .codeLPS (_eee ,_gdf ,_fgf ,_egc );
};};