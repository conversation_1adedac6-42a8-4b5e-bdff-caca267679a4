//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package precision ;import _f "math";func RoundFloat (value float64 ,precision int )float64 {_b :=_f .Pow (10,float64 (precision ));return _f .Round (value *_b )/_b ;};func RoundDefault (value float64 )float64 {return RoundFloat (value ,DefaultPrecision )};
const (DefaultPrecision =4;);