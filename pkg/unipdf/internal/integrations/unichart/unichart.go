//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package unichart ;import (_b "bytes";_c "fmt";_f "github.com/unidoc/unichart/render";_fg "github.com/unidoc/unipdf/v4/common";_gc "github.com/unidoc/unipdf/v4/contentstream";_a "github.com/unidoc/unipdf/v4/contentstream/draw";_g "github.com/unidoc/unipdf/v4/core";
_eb "github.com/unidoc/unipdf/v4/model";_df "image/color";_e "io";_cg "math";);func (_gc<PERSON> *Renderer )SetFont (font _f .Font ){_eeg ,_bdc :=font .(*_eb .PdfFont );if !_bdc {_fg .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0069\u006e\u0076\u0061\u006c\u0069d\u0020\u0066\u006f\u006e\u0074\u0020\u0074\u0079\u0070\u0065");
return ;};_dc ,_bdc :=_gcde ._ede [_eeg ];if !_bdc {_dc =_ceb ("\u0046\u006f\u006e\u0074",1,_gcde ._dd .HasFontByName );if _eef :=_gcde ._dd .SetFontByName (_dc ,_eeg .ToPdfObject ());_eef !=nil {_fg .Log .Debug ("\u0045\u0052\u0052\u004f\u0052:\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0061\u0064d\u0020\u0066\u006f\u006e\u0074\u0020\u0025\u0076\u0020\u0074\u006f\u0020\u0072\u0065\u0073\u006f\u0075\u0072\u0063\u0065\u0073",_eeg );
};_gcde ._ede [_eeg ]=_dc ;};_gcde ._cb .Add_Tf (_dc ,_gcde ._dg );_gcde ._ge =_eeg ;};type Renderer struct{_ga int ;_ed int ;_gcd float64 ;_cb *_gc .ContentCreator ;_dd *_eb .PdfPageResources ;_be _df .Color ;_ad _df .Color ;_ag float64 ;_ge *_eb .PdfFont ;
_dg float64 ;_cd _df .Color ;_dgb float64 ;_ede map[*_eb .PdfFont ]_g .PdfObjectName ;};func _gcc (_fbe _df .Color )(uint8 ,uint8 ,uint8 ,uint8 ){_agg ,_effb ,_dad ,_bfcd :=_fbe .RGBA ();return uint8 (_agg >>8),uint8 (_effb >>8),uint8 (_dad >>8),uint8 (_bfcd >>8);
};func (_dga *Renderer )QuadCurveTo (cx ,cy ,x ,y int ){_dga ._cb .Add_v (float64 (x ),float64 (y ),float64 (cx ),float64 (cy ));};func (_fe *Renderer )MoveTo (x ,y int ){_fe ._cb .Add_m (float64 (x ),float64 (y ))};func (_egd *Renderer )FillStroke (){_egd ._cb .Add_B ()};
func (_af *Renderer )SetStrokeDashArray (dashArray []float64 ){_bd :=make ([]int64 ,len (dashArray ));for _da ,_aff :=range dashArray {_bd [_da ]=int64 (_aff );};_af ._cb .Add_d (_bd ,0);};func _afb (_aae _df .Color )(float64 ,float64 ,float64 ,float64 ){_fbca ,_cab ,_gda ,_gff :=_gcc (_aae );
return float64 (_fbca )/255,float64 (_cab )/255,float64 (_gda )/255,float64 (_gff )/255;};func (_gg *Renderer )ArcTo (cx ,cy int ,rx ,ry ,startAngle ,deltaAngle float64 ){startAngle =_add (2.0*_cg .Pi -startAngle );deltaAngle =_add (-deltaAngle );_cfg ,_cea :=deltaAngle ,1;
if _cg .Abs (deltaAngle )> 90.0{_cea =int (_cg .Ceil (_cg .Abs (deltaAngle )/90.0));_cfg =deltaAngle /float64 (_cea );};var (_dbc =_cge (_cfg /2);_gb =_cg .Abs (4.0/3.0*(1.0-_cg .Cos (_dbc ))/_cg .Sin (_dbc ));_gaf =float64 (cx );_ebd =float64 (cy ););
for _dfa :=0;_dfa < _cea ;_dfa ++{_fga :=_cge (startAngle +float64 (_dfa )*_cfg );_cfgb :=_cge (startAngle +float64 (_dfa +1)*_cfg );_gce :=_cg .Cos (_fga );_aded :=_cg .Cos (_cfgb );_agf :=_cg .Sin (_fga );_dfc :=_cg .Sin (_cfgb );var _ef []float64 ;if _cfg > 0{_ef =[]float64 {_gaf +rx *_gce ,_ebd -ry *_agf ,_gaf +rx *(_gce -_gb *_agf ),_ebd -ry *(_agf +_gb *_gce ),_gaf +rx *(_aded +_gb *_dfc ),_ebd -ry *(_dfc -_gb *_aded ),_gaf +rx *_aded ,_ebd -ry *_dfc };
}else {_ef =[]float64 {_gaf +rx *_gce ,_ebd -ry *_agf ,_gaf +rx *(_gce +_gb *_agf ),_ebd -ry *(_agf -_gb *_gce ),_gaf +rx *(_aded -_gb *_dfc ),_ebd -ry *(_dfc +_gb *_aded ),_gaf +rx *_aded ,_ebd -ry *_dfc };};if _dfa ==0{_gg ._cb .Add_l (_ef [0],_ef [1]);
};_gg ._cb .Add_c (_ef [2],_ef [3],_ef [4],_ef [5],_ef [6],_ef [7]);};};func _cge (_gab float64 )float64 {return _gab *_cg .Pi /180.0};func (_dbb *Renderer )wrapText (_bb string )[]string {var (_cee []string ;_dbg []rune ;);for _ ,_ebf :=range _bb {if _ebf =='\n'{_cee =append (_cee ,string (_dbg ));
_dbg =[]rune {};continue ;};_dbg =append (_dbg ,_ebf );};if len (_dbg )> 0{_cee =append (_cee ,string (_dbg ));};return _cee ;};func (_ebc *Renderer )Stroke (){_ebc ._cb .Add_S ()};func (_baf *Renderer )Text (text string ,x ,y int ){_baf ._cb .Add_q ();
_baf .SetFont (_baf ._ge );_dcd ,_dfb ,_beg ,_ :=_afb (_baf ._cd );_baf ._cb .Add_rg (_dcd ,_dfb ,_beg );_baf ._cb .Translate (float64 (x ),float64 (y )).Scale (1,-1);if _fbb :=_baf ._dgb ;_fbb !=0{_baf ._cb .RotateDeg (_fbb );};_baf ._cb .Add_BT ().Add_TL (_baf ._dg );
var (_begb =_baf ._ge .Encoder ();_dcb =_baf .wrapText (text );_affe =len (_dcb ););for _cdf ,_ggb :=range _dcb {_baf ._cb .Add_TJ (_g .MakeStringFromBytes (_begb .Encode (_ggb )));if _cdf !=_affe -1{_baf ._cb .Add_Tstar ();};};_baf ._cb .Add_ET ();_baf ._cb .Add_Q ();
};func (_ceg *Renderer )Close (){_ceg ._cb .Add_h ()};func (_cc *Renderer )SetFillColor (color _df .Color ){_cc ._be =color ;_edd ,_eg ,_ffg ,_ :=_afb (color );_cc ._cb .Add_rg (_edd ,_eg ,_ffg );};func (_adee *Renderer )getTextWidth (_ged string )float64 {var _dgc float64 ;
for _ ,_ecg :=range _ged {_aba ,_cff :=_adee ._ge .GetRuneMetrics (_ecg );if !_cff {_fg .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0075\u006e\u0073\u0075\u0070\u0070\u006fr\u0074e\u0064 \u0072u\u006e\u0065\u0020\u0025\u0076\u0020\u0069\u006e\u0020\u0066\u006f\u006e\u0074",_ecg );
};_dgc +=_aba .Wx ;};return _adee ._dg *_dgc /1000.0;};func (_eda *Renderer )Fill (){_eda ._cb .Add_f ()};func (_ee *Renderer )SetStrokeWidth (width float64 ){_ee ._ag =width ;_ee ._cb .Add_w (width )};func (_cf *Renderer )ResetStyle (){_cf .SetFillColor (_df .Black );
_cf .SetStrokeColor (_df .Transparent );_cf .SetStrokeWidth (0);_cf .SetFont (_eb .DefaultFont ());_cf .SetFontColor (_df .Black );_cf .SetFontSize (12);_cf .SetTextRotation (0);};func (_fa *Renderer )SetTextRotation (radians float64 ){_fa ._dgb =_add (-radians )};
func (_bec *Renderer )SetFontColor (color _df .Color ){_bec ._cd =color };func (_dfd *Renderer )MeasureText (text string )_f .Box {_aa :=_dfd ._dg ;_egb ,_abc :=_dfd ._ge .GetFontDescriptor ();if _abc !=nil {_fg .Log .Debug ("W\u0041\u0052\u004e\u003a\u0020\u0055n\u0061\u0062\u006c\u0065\u0020\u0074o\u0020\u0067\u0065\u0074\u0020\u0066\u006fn\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074o\u0072");
}else {_edec ,_fbc :=_egb .GetCapHeight ();if _fbc !=nil {_fg .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0055\u006e\u0061\u0062\u006c\u0065\u0020t\u006f\u0020\u0067\u0065\u0074\u0020f\u006f\u006e\u0074\u0020\u0063\u0061\u0070\u0020\u0068\u0065\u0069\u0067\u0068t\u003a\u0020\u0025\u0076",_fbc );
}else {_aa =_edec /1000.0*_dfd ._dg ;};};var (_aaf =0.0;_bcf =_dfd .wrapText (text ););for _ ,_dgbg :=range _bcf {if _dba :=_dfd .getTextWidth (_dgbg );_dba > _aaf {_aaf =_dba ;};};_fca :=_f .NewBox (0,0,int (_aaf ),int (_aa ));if _eefd :=_dfd ._dgb ;_eefd !=0{_fca =_fca .Corners ().Rotate (_eefd ).Box ();
};return _fca ;};func (_gdb *Renderer )Circle (radius float64 ,x ,y int ){_de :=radius ;if _bfc :=_gdb ._ag ;_bfc !=0{_de -=_bfc /2;};_ba :=_de *0.551784;_gf :=_a .CubicBezierPath {Curves :[]_a .CubicBezierCurve {_a .NewCubicBezierCurve (-_de ,0,-_de ,_ba ,-_ba ,_de ,0,_de ),_a .NewCubicBezierCurve (0,_de ,_ba ,_de ,_de ,_ba ,_de ,0),_a .NewCubicBezierCurve (_de ,0,_de ,-_ba ,_ba ,-_de ,0,-_de ),_a .NewCubicBezierCurve (0,-_de ,-_ba ,-_de ,-_de ,-_ba ,-_de ,0)}};
if _eff :=_gdb ._ag ;_eff !=0{_gf =_gf .Offset (_eff /2,_eff /2);};_gf =_gf .Offset (float64 (x ),float64 (y ));_a .DrawBezierPathWithCreator (_gf ,_gdb ._cb );};func (_bc *Renderer )SetDPI (dpi float64 ){_bc ._gcd =dpi };func (_cdb *Renderer )SetStrokeColor (color _df .Color ){_cdb ._ad =color ;
_ac ,_bf ,_fc ,_ :=_afb (color );_cdb ._cb .Add_RG (_ac ,_bf ,_fc );};func (_ff *Renderer )SetClassName (name string ){};func _add (_ebe float64 )float64 {return _ebe *180/_cg .Pi };func (_gbf *Renderer )Save (w _e .Writer )error {if w ==nil {return nil ;
};_ ,_gcg :=_e .Copy (w ,_b .NewBuffer (_gbf ._cb .Bytes ()));return _gcg ;};func (_ab *Renderer )GetDPI ()float64 {return _ab ._gcd };func _ceb (_fd string ,_gag int ,_eddg func (_g .PdfObjectName )bool )_g .PdfObjectName {_begg :=_g .PdfObjectName (_c .Sprintf ("\u0025\u0073\u0025\u0064",_fd ,_gag ));
for _acg :=_gag ;_eddg (_begg );{_acg ++;_begg =_g .PdfObjectName (_c .Sprintf ("\u0025\u0073\u0025\u0064",_fd ,_acg ));};return _begg ;};func NewRenderer (cc *_gc .ContentCreator ,res *_eb .PdfPageResources )func (int ,int )(_f .Renderer ,error ){return func (_ec ,_ade int )(_f .Renderer ,error ){_ce :=&Renderer {_ga :_ec ,_ed :_ade ,_gcd :72,_cb :cc ,_dd :res ,_ede :map[*_eb .PdfFont ]_g .PdfObjectName {}};
_ce .ResetStyle ();return _ce ,nil ;};};func (_bad *Renderer )SetFontSize (size float64 ){_bad ._dg =size };func (_db *Renderer )LineTo (x ,y int ){_db ._cb .Add_l (float64 (x ),float64 (y ))};func (_ceaa *Renderer )ClearTextRotation (){_ceaa ._dgb =0};
