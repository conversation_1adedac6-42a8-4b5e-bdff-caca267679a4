//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package timeutils ;import (_g "errors";_ab "fmt";_ag "regexp";_d "strconv";_a "time";);func ParsePdfTime (pdfTime string )(_a .Time ,error ){_c :=_ae .FindAllStringSubmatch (pdfTime ,1);if len (_c )< 1{if len (pdfTime )> 0&&pdfTime [0]!='D'{pdfTime =_ab .Sprintf ("\u0044\u003a\u0025\u0073",pdfTime );
return ParsePdfTime (pdfTime );};return _a .Time {},_ab .Errorf ("\u0069n\u0076\u0061\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0065\u0020s\u0074\u0072\u0069\u006e\u0067\u0020\u0028\u0025\u0073\u0029",pdfTime );};if len (_c [0])!=10{return _a .Time {},_g .New ("\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0072\u0065\u0067\u0065\u0078p\u0020\u0067\u0072\u006f\u0075\u0070 \u006d\u0061\u0074\u0063\u0068\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u0020!\u003d\u0020\u0031\u0030");
};_agd ,_ :=_d .ParseInt (_c [0][1],10,32);_ee ,_ :=_d .ParseInt (_c [0][2],10,32);_agb ,_ :=_d .ParseInt (_c [0][3],10,32);_df ,_ :=_d .ParseInt (_c [0][4],10,32);_fd ,_ :=_d .ParseInt (_c [0][5],10,32);_ge ,_ :=_d .ParseInt (_c [0][6],10,32);var (_abc byte ;
_fc int64 ;_fb int64 ;);_abc ='+';if len (_c [0][7])> 0{if _c [0][7]=="\u002d"{_abc ='-';}else if _c [0][7]=="\u005a"{_abc ='Z';};};if len (_c [0][8])> 0{_fc ,_ =_d .ParseInt (_c [0][8],10,32);}else {_fc =0;};if len (_c [0][9])> 0{_fb ,_ =_d .ParseInt (_c [0][9],10,32);
}else {_fb =0;};_bb :=int (_fc *60*60+_fb *60);switch _abc {case '-':_bb =-_bb ;case 'Z':_bb =0;};_fbc :=_ab .Sprintf ("\u0055\u0054\u0043\u0025\u0063\u0025\u002e\u0032\u0064\u0025\u002e\u0032\u0064",_abc ,_fc ,_fb );_gec :=_a .FixedZone (_fbc ,_bb );return _a .Date (int (_agd ),_a .Month (_ee ),int (_agb ),int (_df ),int (_fd ),int (_ge ),0,_gec ),nil ;
};var _ae =_ag .MustCompile ("\u005cs\u002a\u0044\u005cs\u002a\u003a\u005cs\u002a(\\\u0064\u007b\u0034\u007d\u0029\u0028\u005cd\u007b\u0032\u007d\u0029\u0028\u005c\u0064\u007b\u0032\u007d\u0029\u0028\u005c\u0064\u007b\u0032\u007d\u0029\u0028\u005c\u0064\u007b\u0032\u007d\u0029\u0028\u005c\u0064{2\u007d)\u003f\u0028\u005b\u002b\u002d\u005a]\u0029\u003f\u0028\u005c\u0064{\u0032\u007d\u0029\u003f\u0027\u003f\u0028\u005c\u0064\u007b\u0032}\u0029\u003f");
func FormatPdfTime (in _a .Time )string {_gb :=in .Format ("\u002d\u0030\u0037\u003a\u0030\u0030");_gc ,_ :=_d .ParseInt (_gb [1:3],10,32);_ga ,_ :=_d .ParseInt (_gb [4:6],10,32);_eb :=int64 (in .Year ());_ac :=int64 (in .Month ());_gaa :=int64 (in .Day ());
_bd :=int64 (in .Hour ());_dg :=int64 (in .Minute ());_ec :=int64 (in .Second ());_bf :=_gb [0];return _ab .Sprintf ("\u0044\u003a\u0025\u002e\u0034\u0064\u0025\u002e\u0032\u0064\u0025\u002e\u0032\u0064\u0025\u002e\u0032\u0064\u0025\u002e\u0032\u0064\u0025\u002e2\u0064\u0025\u0063\u0025\u002e2\u0064\u0027%\u002e\u0032\u0064\u0027",_eb ,_ac ,_gaa ,_bd ,_dg ,_ec ,_bf ,_gc ,_ga );
};