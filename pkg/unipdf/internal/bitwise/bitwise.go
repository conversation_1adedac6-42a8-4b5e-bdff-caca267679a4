//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package bitwise ;import (_ad "encoding/binary";_g "errors";_a "fmt";_gf "github.com/unidoc/unipdf/v4/common";_e "github.com/unidoc/unipdf/v4/internal/jbig2/errors";_c "io";);func (_ebba *Reader )Reset (){_ebba ._gce =_ebba ._dae ;_ebba ._gd =_ebba ._cfd ;
_ebba ._eed =_ebba ._fdb ;_ebba ._ace =_ebba ._ba ;};func (_dge *Reader )Mark (){_dge ._dae =_dge ._gce ;_dge ._cfd =_dge ._gd ;_dge ._fdb =_dge ._eed ;_dge ._ba =_dge ._ace ;};func BufferedMSB ()*BufferedWriter {return &BufferedWriter {_ca :true }};func (_gcea *Writer )UseMSB ()bool {return _gcea ._bac };
type Writer struct{_eff []byte ;_aec uint8 ;_aa int ;_bac bool ;};func NewWriter (data []byte )*Writer {return &Writer {_eff :data }};type BufferedWriter struct{_ac []byte ;_cf uint8 ;_ab int ;_ca bool ;};func (_ea *BufferedWriter )tryGrowByReslice (_fe int )bool {if _ee :=len (_ea ._ac );
_fe <=cap (_ea ._ac )-_ee {_ea ._ac =_ea ._ac [:_ee +_fe ];return true ;};return false ;};func (_edf *BufferedWriter )writeShiftedBytes (_ada []byte )int {for _ ,_af :=range _ada {_edf .writeByte (_af );};return len (_ada );};const (_b =64;_da =int (^uint (0)>>1);
);func (_fcb *Reader )ReadBool ()(bool ,error ){return _fcb .readBool ()};func (_gg *Reader )ConsumeRemainingBits ()(uint64 ,error ){if _gg ._gd !=0{return _gg .ReadBits (_gg ._gd );};return 0,nil ;};type BitWriter interface{WriteBit (_cbg int )error ;
WriteBits (_fd uint64 ,_eae int )(_efg int ,_aff error );FinishByte ();SkipBits (_ga int )error ;};func (_abg *Reader )ReadUint32 ()(uint32 ,error ){_cca :=make ([]byte ,4);_ ,_fdg :=_abg .Read (_cca );if _fdg !=nil {return 0,_fdg ;};return _ad .BigEndian .Uint32 (_cca ),nil ;
};func NewReader (data []byte )*Reader {return &Reader {_ff :readerSource {_ge :data ,_dff :len (data ),_dbd :0}};};func (_bd *BufferedWriter )writeFullBytes (_gfc []byte )int {_cfb :=copy (_bd ._ac [_bd .fullOffset ():],_gfc );_bd ._ab +=_cfb ;return _cfb ;
};func (_gac *Reader )RelativePosition ()int64 {return _gac ._gce };func (_fc *BufferedWriter )Len ()int {return _fc .byteCapacity ()};type StreamReader interface{_c .Reader ;_c .ByteReader ;_c .Seeker ;Align ()byte ;BitPosition ()int ;Mark ();Length ()uint64 ;
ReadBit ()(int ,error );ReadBits (_ece byte )(uint64 ,error );ReadBool ()(bool ,error );ReadUint32 ()(uint32 ,error );Reset ();AbsolutePosition ()int64 ;};func NewWriterMSB (data []byte )*Writer {return &Writer {_eff :data ,_bac :true }};func (_gc *BufferedWriter )WriteBits (bits uint64 ,number int )(_ed int ,_acf error ){const _dgd ="\u0042u\u0066\u0066\u0065\u0072e\u0064\u0057\u0072\u0069\u0074e\u0072.\u0057r\u0069\u0074\u0065\u0072\u0042\u0069\u0074s";
if number < 0||number > 64{return 0,_e .Errorf (_dgd ,"\u0062i\u0074\u0073 \u006e\u0075\u006db\u0065\u0072\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0069\u006e\u0020r\u0061\u006e\u0067\u0065\u0020\u003c\u0030\u002c\u0036\u0034\u003e,\u0020\u0069\u0073\u003a\u0020\u0027\u0025\u0064\u0027",number );
};_ag :=number /8;if _ag > 0{_dc :=number -_ag *8;for _ced :=_ag -1;_ced >=0;_ced --{_ef :=byte ((bits >>uint (_ced *8+_dc ))&0xff);if _acf =_gc .WriteByte (_ef );_acf !=nil {return _ed ,_e .Wrapf (_acf ,_dgd ,"\u0062\u0079\u0074\u0065\u003a\u0020\u0027\u0025\u0064\u0027",_ag -_ced +1);
};};number -=_ag *8;if number ==0{return _ag ,nil ;};};var _gfd int ;for _dgf :=0;_dgf < number ;_dgf ++{if _gc ._ca {_gfd =int ((bits >>uint (number -1-_dgf ))&0x1);}else {_gfd =int (bits &0x1);bits >>=1;};if _acf =_gc .WriteBit (_gfd );_acf !=nil {return _ed ,_e .Wrapf (_acf ,_dgd ,"\u0062i\u0074\u003a\u0020\u0025\u0064",_dgf );
};};return _ag ,nil ;};func (_db *BufferedWriter )ResetBitIndex (){_db ._cf =0};func (_cc *BufferedWriter )FinishByte (){if _cc ._cf ==0{return ;};_cc ._cf =0;_cc ._ab ++;};func (_eba *Writer )WriteBit (bit int )error {switch bit {case 0,1:return _eba .writeBit (uint8 (bit ));
};return _e .Error ("\u0057\u0072\u0069\u0074\u0065\u0042\u0069\u0074","\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0062\u0069\u0074\u0020v\u0061\u006c\u0075\u0065");};type Reader struct{_ff readerSource ;_eed byte ;_gd byte ;_gce int64 ;_ace int ;
_edff int ;_dae int64 ;_cfd byte ;_fdb byte ;_ba int ;};var _ BinaryWriter =&BufferedWriter {};var (_ _c .Reader =&Reader {};_ _c .ByteReader =&Reader {};_ _c .Seeker =&Reader {};_ StreamReader =&Reader {};);func (_gfa *Writer )FinishByte (){if _gfa ._aec ==0{return ;
};_gfa ._aec =0;_gfa ._aa ++;};func (_eea *Reader )readBufferByte ()(byte ,error ){if _eea ._gce >=int64 (_eea ._ff ._dff ){return 0,_c .EOF ;};_eea ._edff =-1;_ggbb :=_eea ._ff ._ge [int64 (_eea ._ff ._dbd )+_eea ._gce ];_eea ._gce ++;_eea ._ace =int (_ggbb );
return _ggbb ,nil ;};var _ _c .ByteWriter =&BufferedWriter {};func (_gbc *Writer )SkipBits (skip int )error {const _ecea ="\u0057r\u0069t\u0065\u0072\u002e\u0053\u006b\u0069\u0070\u0042\u0069\u0074\u0073";if skip ==0{return nil ;};_bed :=int (_gbc ._aec )+skip ;
if _bed >=0&&_bed < 8{_gbc ._aec =uint8 (_bed );return nil ;};_bed =int (_gbc ._aec )+_gbc ._aa *8+skip ;if _bed < 0{return _e .Errorf (_ecea ,"\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");};_fbc :=_bed /8;
_ccb :=_bed %8;_gf .Log .Trace ("\u0053\u006b\u0069\u0070\u0042\u0069\u0074\u0073");_gf .Log .Trace ("\u0042\u0069\u0074\u0049\u006e\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u0020\u0042\u0079\u0074\u0065\u0049n\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0046\u0075\u006c\u006c\u0042\u0069\u0074\u0073\u003a\u0020'\u0025\u0064\u0027\u002c\u0020\u004c\u0065\u006e\u003a\u0020\u0027\u0025\u0064\u0027,\u0020\u0043\u0061p\u003a\u0020\u0027\u0025\u0064\u0027",_gbc ._aec ,_gbc ._aa ,int (_gbc ._aec )+(_gbc ._aa )*8,len (_gbc ._eff ),cap (_gbc ._eff ));
_gf .Log .Trace ("S\u006b\u0069\u0070\u003a\u0020\u0027%\u0064\u0027\u002c\u0020\u0064\u003a \u0027\u0025\u0064\u0027\u002c\u0020\u0062i\u0074\u0049\u006e\u0064\u0065\u0078\u003a\u0020\u0027\u0025d\u0027",skip ,_bed ,_ccb );_gbc ._aec =uint8 (_ccb );if _fba :=_fbc -_gbc ._aa ;
_fba > 0&&len (_gbc ._eff )-1< _fbc {_gf .Log .Trace ("\u0042\u0079\u0074e\u0044\u0069\u0066\u0066\u003a\u0020\u0025\u0064",_fba );return _e .Errorf (_ecea ,"\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");};_gbc ._aa =_fbc ;
_gf .Log .Trace ("\u0042\u0069\u0074I\u006e\u0064\u0065\u0078:\u0020\u0027\u0025\u0064\u0027\u002c\u0020B\u0079\u0074\u0065\u0049\u006e\u0064\u0065\u0078\u003a\u0020\u0027\u0025\u0064\u0027",_gbc ._aec ,_gbc ._aa );return nil ;};func (_feg *Writer )Data ()[]byte {return _feg ._eff };
func (_dg *BufferedWriter )Write (d []byte )(int ,error ){_dg .expandIfNeeded (len (d ));if _dg ._cf ==0{return _dg .writeFullBytes (d ),nil ;};return _dg .writeShiftedBytes (d ),nil ;};func (_acd *Writer )WriteByte (c byte )error {return _acd .writeByte (c )};
type BinaryWriter interface{BitWriter ;_c .Writer ;_c .ByteWriter ;Data ()[]byte ;};func (_aed *Reader )Read (p []byte )(_bgc int ,_aef error ){if _aed ._gd ==0{return _aed .read (p );};for ;_bgc < len (p );_bgc ++{if p [_bgc ],_aef =_aed .readUnalignedByte ();
_aef !=nil {return 0,_aef ;};};return _bgc ,nil ;};type readerSource struct{_ge []byte ;_dbd int ;_dff int ;};func (_bf *BufferedWriter )WriteByte (bt byte )error {if _bf ._ab > len (_bf ._ac )-1||(_bf ._ab ==len (_bf ._ac )-1&&_bf ._cf !=0){_bf .expandIfNeeded (1);
};_bf .writeByte (bt );return nil ;};func (_bfd *Reader )readBool ()(_gae bool ,_gcg error ){if _bfd ._gd ==0{_bfd ._eed ,_gcg =_bfd .readBufferByte ();if _gcg !=nil {return false ,_gcg ;};_gae =(_bfd ._eed &0x80)!=0;_bfd ._eed ,_bfd ._gd =_bfd ._eed &0x7f,7;
return _gae ,nil ;};_bfd ._gd --;_gae =(_bfd ._eed &(1<<_bfd ._gd ))!=0;_bfd ._eed &=1<<_bfd ._gd -1;return _gae ,nil ;};func (_cfa *BufferedWriter )SkipBits (skip int )error {if skip ==0{return nil ;};_fcf :=int (_cfa ._cf )+skip ;if _fcf >=0&&_fcf < 8{_cfa ._cf =uint8 (_fcf );
return nil ;};_fcf =int (_cfa ._cf )+_cfa ._ab *8+skip ;if _fcf < 0{return _e .Errorf ("\u0057r\u0069t\u0065\u0072\u002e\u0053\u006b\u0069\u0070\u0042\u0069\u0074\u0073","\u0069n\u0064e\u0078\u0020\u006f\u0075\u0074 \u006f\u0066 \u0072\u0061\u006e\u0067\u0065");
};_fg :=_fcf /8;_ae :=_fcf %8;_cfa ._cf =uint8 (_ae );if _ce :=_fg -_cfa ._ab ;_ce > 0&&len (_cfa ._ac )-1< _fg {if _cfa ._cf !=0{_ce ++;};_cfa .expandIfNeeded (_ce );};_cfa ._ab =_fg ;return nil ;};func (_daea *Writer )Write (p []byte )(int ,error ){if len (p )> _daea .byteCapacity (){return 0,_c .EOF ;
};for _ ,_fbg :=range p {if _deb :=_daea .writeByte (_fbg );_deb !=nil {return 0,_deb ;};};return len (p ),nil ;};func (_fgc *Reader )ReadByte ()(byte ,error ){if _fgc ._gd ==0{return _fgc .readBufferByte ();};return _fgc .readUnalignedByte ();};func (_caa *Writer )byteCapacity ()int {_dgfa :=len (_caa ._eff )-_caa ._aa ;
if _caa ._aec !=0{_dgfa --;};return _dgfa ;};func (_gbf *Reader )read (_gbe []byte )(int ,error ){if _gbf ._gce >=int64 (_gbf ._ff ._dff ){return 0,_c .EOF ;};_gbf ._edff =-1;_edbc :=copy (_gbe ,_gbf ._ff ._ge [(int64 (_gbf ._ff ._dbd )+_gbf ._gce ):(_gbf ._ff ._dbd +_gbf ._ff ._dff )]);
_gbf ._gce +=int64 (_edbc );return _edbc ,nil ;};func (_ede *Writer )writeBit (_dbca uint8 )error {if len (_ede ._eff )-1< _ede ._aa {return _c .EOF ;};_aeb :=_ede ._aec ;if _ede ._bac {_aeb =7-_ede ._aec ;};_ede ._eff [_ede ._aa ]|=byte (uint16 (_dbca <<_aeb )&0xff);
_ede ._aec ++;if _ede ._aec ==8{_ede ._aa ++;_ede ._aec =0;};return nil ;};var _ BinaryWriter =&Writer {};func (_dbc *Reader )readUnalignedByte ()(_def byte ,_bba error ){_beg :=_dbc ._gd ;_def =_dbc ._eed <<(8-_beg );_dbc ._eed ,_bba =_dbc .readBufferByte ();
if _bba !=nil {return 0,_bba ;};_def |=_dbc ._eed >>_beg ;_dbc ._eed &=1<<_beg -1;return _def ,nil ;};func (_gacf *Writer )writeByte (_aad byte )error {if _gacf ._aa > len (_gacf ._eff )-1{return _c .EOF ;};if _gacf ._aa ==len (_gacf ._eff )-1&&_gacf ._aec !=0{return _c .EOF ;
};if _gacf ._aec ==0{_gacf ._eff [_gacf ._aa ]=_aad ;_gacf ._aa ++;return nil ;};if _gacf ._bac {_gacf ._eff [_gacf ._aa ]|=_aad >>_gacf ._aec ;_gacf ._aa ++;_gacf ._eff [_gacf ._aa ]=byte (uint16 (_aad )<<(8-_gacf ._aec )&0xff);}else {_gacf ._eff [_gacf ._aa ]|=byte (uint16 (_aad )<<_gacf ._aec &0xff);
_gacf ._aa ++;_gacf ._eff [_gacf ._aa ]=_aad >>(8-_gacf ._aec );};return nil ;};func (_gcd *BufferedWriter )grow (_eb int ){if _gcd ._ac ==nil &&_eb < _b {_gcd ._ac =make ([]byte ,_eb ,_b );return ;};_aba :=len (_gcd ._ac );if _gcd ._cf !=0{_aba ++;};_fb :=cap (_gcd ._ac );
switch {case _eb <=_fb /2-_aba :_gf .Log .Trace ("\u005b\u0042\u0075\u0066\u0066\u0065r\u0065\u0064\u0057\u0072\u0069t\u0065\u0072\u005d\u0020\u0067\u0072o\u0077\u0020\u002d\u0020\u0072e\u0073\u006c\u0069\u0063\u0065\u0020\u006f\u006e\u006c\u0079\u002e\u0020L\u0065\u006e\u003a\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u0043\u0061\u0070\u003a\u0020'\u0025\u0064\u0027\u002c\u0020\u006e\u003a\u0020'\u0025\u0064\u0027",len (_gcd ._ac ),cap (_gcd ._ac ),_eb );
_gf .Log .Trace ("\u0020\u006e\u0020\u003c\u003d\u0020\u0063\u0020\u002f\u0020\u0032\u0020\u002d\u006d\u002e \u0043:\u0020\u0027\u0025\u0064\u0027\u002c\u0020\u006d\u003a\u0020\u0027\u0025\u0064\u0027",_fb ,_aba );copy (_gcd ._ac ,_gcd ._ac [_gcd .fullOffset ():]);
case _fb > _da -_fb -_eb :_gf .Log .Error ("\u0042\u0055F\u0046\u0045\u0052 \u0074\u006f\u006f\u0020\u006c\u0061\u0072\u0067\u0065");return ;default:_bb :=make ([]byte ,2*_fb +_eb );copy (_bb ,_gcd ._ac );_gcd ._ac =_bb ;};_gcd ._ac =_gcd ._ac [:_aba +_eb ];
};func (_eg *BufferedWriter )expandIfNeeded (_adb int ){if !_eg .tryGrowByReslice (_adb ){_eg .grow (_adb );};};func (_fgd *BufferedWriter )fullOffset ()int {_dbf :=_fgd ._ab ;if _fgd ._cf !=0{_dbf ++;};return _dbf ;};var _ _c .Writer =&BufferedWriter {};
func (_fca *BufferedWriter )byteCapacity ()int {_gb :=len (_fca ._ac )-_fca ._ab ;if _fca ._cf !=0{_gb --;};return _gb ;};func (_fae *Reader )Align ()(_gde byte ){_gde =_fae ._gd ;_fae ._gd =0;return _gde };func (_afa *Writer )WriteBits (bits uint64 ,number int )(_bbe int ,_fade error ){const _eca ="\u0057\u0072\u0069\u0074\u0065\u0072\u002e\u0057\u0072\u0069\u0074\u0065r\u0042\u0069\u0074\u0073";
if number < 0||number > 64{return 0,_e .Errorf (_eca ,"\u0062i\u0074\u0073 \u006e\u0075\u006db\u0065\u0072\u0020\u006d\u0075\u0073\u0074 \u0062\u0065\u0020\u0069\u006e\u0020r\u0061\u006e\u0067\u0065\u0020\u003c\u0030\u002c\u0036\u0034\u003e,\u0020\u0069\u0073\u003a\u0020\u0027\u0025\u0064\u0027",number );
};if number ==0{return 0,nil ;};_adga :=number /8;if _adga > 0{_fcbe :=number -_adga *8;for _fdgg :=_adga -1;_fdgg >=0;_fdgg --{_abf :=byte ((bits >>uint (_fdgg *8+_fcbe ))&0xff);if _fade =_afa .WriteByte (_abf );_fade !=nil {return _bbe ,_e .Wrapf (_fade ,_eca ,"\u0062\u0079\u0074\u0065\u003a\u0020\u0027\u0025\u0064\u0027",_adga -_fdgg +1);
};};number -=_adga *8;if number ==0{return _adga ,nil ;};};var _fge int ;for _baa :=0;_baa < number ;_baa ++{if _afa ._bac {_fge =int ((bits >>uint (number -1-_baa ))&0x1);}else {_fge =int (bits &0x1);bits >>=1;};if _fade =_afa .WriteBit (_fge );_fade !=nil {return _bbe ,_e .Wrapf (_fade ,_eca ,"\u0062i\u0074\u003a\u0020\u0025\u0064",_baa );
};};return _adga ,nil ;};func (_ec *BufferedWriter )Reset (){_ec ._ac =_ec ._ac [:0];_ec ._ab =0;_ec ._cf =0};func (_bcf *Reader )NewPartialReader (offset ,length int ,relative bool )(*Reader ,error ){if offset < 0{return nil ,_g .New ("p\u0061\u0072\u0074\u0069\u0061\u006c\u0020\u0072\u0065\u0061\u0064\u0065\u0072\u0020\u006f\u0066\u0066\u0073e\u0074\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062e \u006e\u0065\u0067a\u0074i\u0076\u0065");
};if relative {offset =_bcf ._ff ._dbd +offset ;};if length > 0{_adg :=len (_bcf ._ff ._ge );if relative {_adg =_bcf ._ff ._dff ;};if offset +length > _adg {return nil ,_a .Errorf ("\u0070\u0061r\u0074\u0069\u0061l\u0020\u0072\u0065\u0061\u0064e\u0072\u0020\u006f\u0066\u0066se\u0074\u0028\u0025\u0064\u0029\u002b\u006c\u0065\u006e\u0067\u0074\u0068\u0028\u0025\u0064\u0029\u003d\u0025d\u0020i\u0073\u0020\u0067\u0072\u0065\u0061ter\u0020\u0074\u0068\u0061\u006e\u0020\u0074\u0068\u0065\u0020\u006f\u0072ig\u0069n\u0061\u006c\u0020\u0072e\u0061d\u0065r\u0020\u006ce\u006e\u0067th\u003a\u0020\u0025\u0064",offset ,length ,offset +length ,_bcf ._ff ._dff );
};};if length < 0{_bca :=len (_bcf ._ff ._ge );if relative {_bca =_bcf ._ff ._dff ;};length =_bca -offset ;};return &Reader {_ff :readerSource {_ge :_bcf ._ff ._ge ,_dff :length ,_dbd :offset }},nil ;};func (_eda *Reader )Length ()uint64 {return uint64 (_eda ._ff ._dff )};
func (_ggb *Reader )AbsolutePosition ()int64 {return _ggb ._gce +int64 (_ggb ._ff ._dbd )};func (_edb *Reader )ReadBit ()(_dd int ,_daee error ){_gfcb ,_daee :=_edb .readBool ();if _daee !=nil {return 0,_daee ;};if _gfcb {_dd =1;};return _dd ,nil ;};func (_gga *Reader )Seek (offset int64 ,whence int )(int64 ,error ){_gga ._edff =-1;
_gga ._gd =0;_gga ._eed =0;_gga ._ace =0;var _fag int64 ;switch whence {case _c .SeekStart :_fag =offset ;case _c .SeekCurrent :_fag =_gga ._gce +offset ;case _c .SeekEnd :_fag =int64 (_gga ._ff ._dff )+offset ;default:return 0,_g .New ("\u0072\u0065\u0061de\u0072\u002e\u0052\u0065\u0061\u0064\u0065\u0072\u002eS\u0065e\u006b:\u0020i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0077\u0068\u0065\u006e\u0063\u0065");
};if _fag < 0{return 0,_g .New ("\u0072\u0065a\u0064\u0065\u0072\u002eR\u0065\u0061d\u0065\u0072\u002e\u0053\u0065\u0065\u006b\u003a \u006e\u0065\u0067\u0061\u0074\u0069\u0076\u0065\u0020\u0070\u006f\u0073i\u0074\u0069\u006f\u006e");};_gga ._gce =_fag ;
_gga ._gd =0;return _fag ,nil ;};func (_ebb *Reader )BitPosition ()int {return int (_ebb ._gd )};func (_edfe *Reader )AbsoluteLength ()uint64 {return uint64 (len (_edfe ._ff ._ge ))};func (_de *BufferedWriter )writeByte (_bg byte ){switch {case _de ._cf ==0:_de ._ac [_de ._ab ]=_bg ;
_de ._ab ++;case _de ._ca :_de ._ac [_de ._ab ]|=_bg >>_de ._cf ;_de ._ab ++;_de ._ac [_de ._ab ]=byte (uint16 (_bg )<<(8-_de ._cf )&0xff);default:_de ._ac [_de ._ab ]|=byte (uint16 (_bg )<<_de ._cf &0xff);_de ._ab ++;_de ._ac [_de ._ab ]=_bg >>(8-_de ._cf );
};};func (_gcde *Writer )ResetBit (){_gcde ._aec =0};func (_acb *BufferedWriter )Data ()[]byte {return _acb ._ac };func (_daa *Reader )ReadBits (n byte )(_fad uint64 ,_abaf error ){if n < _daa ._gd {_be :=_daa ._gd -n ;_fad =uint64 (_daa ._eed >>_be );
_daa ._eed &=1<<_be -1;_daa ._gd =_be ;return _fad ,nil ;};if n > _daa ._gd {if _daa ._gd > 0{_fad =uint64 (_daa ._eed );n -=_daa ._gd ;};for n >=8{_fgb ,_bdc :=_daa .readBufferByte ();if _bdc !=nil {return 0,_bdc ;};_fad =_fad <<8+uint64 (_fgb );n -=8;
};if n > 0{if _daa ._eed ,_abaf =_daa .readBufferByte ();_abaf !=nil {return 0,_abaf ;};_efgd :=8-n ;_fad =_fad <<n +uint64 (_daa ._eed >>_efgd );_daa ._eed &=1<<_efgd -1;_daa ._gd =_efgd ;}else {_daa ._gd =0;};return _fad ,nil ;};_daa ._gd =0;return uint64 (_daa ._eed ),nil ;
};func (_dad *BufferedWriter )WriteBit (bit int )error {if bit !=1&&bit !=0{return _e .Errorf ("\u0042\u0075\u0066fe\u0072\u0065\u0064\u0057\u0072\u0069\u0074\u0065\u0072\u002e\u0057\u0072\u0069\u0074\u0065\u0042\u0069\u0074","\u0062\u0069\u0074\u0020\u0076\u0061\u006cu\u0065\u0020\u006du\u0073\u0074\u0020\u0062e\u0020\u0069\u006e\u0020\u0072\u0061\u006e\u0067\u0065\u0020\u007b\u0030\u002c\u0031\u007d\u0020\u0062\u0075\u0074\u0020\u0069\u0073\u003a\u0020\u0025\u0064",bit );
};if len (_dad ._ac )-1< _dad ._ab {_dad .expandIfNeeded (1);};_df :=_dad ._cf ;if _dad ._ca {_df =7-_dad ._cf ;};_dad ._ac [_dad ._ab ]|=byte (uint16 (bit <<_df )&0xff);_dad ._cf ++;if _dad ._cf ==8{_dad ._ab ++;_dad ._cf =0;};return nil ;};