//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package license ;import (_ae "bytes";_d "compress/gzip";_a "crypto";_ef "crypto/aes";_eb "crypto/cipher";_fa "crypto/hmac";_ba "crypto/rand";_cf "crypto/rsa";_fcd "crypto/sha256";_cb "crypto/sha512";_efd "crypto/x509";_ea "encoding/base64";_cca "encoding/hex";
_gf "encoding/json";_de "encoding/pem";_cc "errors";_fc "fmt";_aee "github.com/unidoc/unipdf/v4/common";_b "io";_e "net";_bb "net/http";_bf "os";_c "path/filepath";_ad "strings";_f "sync";_fg "time";);func _cbd (_bgc ,_bcgd []byte )([]byte ,error ){_ebac ,_dbe :=_ef .NewCipher (_bgc );
if _dbe !=nil {return nil ,_dbe ;};_bfcg :=make ([]byte ,_ef .BlockSize +len (_bcgd ));_cdcc :=_bfcg [:_ef .BlockSize ];if _ ,_fbad :=_b .ReadFull (_ba .Reader ,_cdcc );_fbad !=nil {return nil ,_fbad ;};_edbc :=_eb .NewCFBEncrypter (_ebac ,_cdcc );_edbc .XORKeyStream (_bfcg [_ef .BlockSize :],_bcgd );
_dfgc :=make ([]byte ,_ea .URLEncoding .EncodedLen (len (_bfcg )));_ea .URLEncoding .Encode (_dfgc ,_bfcg );return _dfgc ,nil ;};func (_faa defaultStateHolder )loadState (_dbf string )(reportState ,error ){_cgb ,_gaa :=_cbbe ();if _gaa !=nil {return reportState {},_gaa ;
};_gaa =_bf .MkdirAll (_cgb ,0777);if _gaa !=nil {return reportState {},_gaa ;};if len (_dbf )< 20{return reportState {},_cc .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006b\u0065\u0079");};_cdbe :=[]byte (_dbf );_fcdc :=_cb .Sum512_256 (_cdbe [:20]);
_eaf :=_cca .EncodeToString (_fcdc [:]);_afab :=_c .Join (_cgb ,_eaf );_afe ,_gaa :=_bf .ReadFile (_afab );if _gaa !=nil {if _bf .IsNotExist (_gaa ){return reportState {},nil ;};_aee .Log .Debug ("\u0045R\u0052\u004f\u0052\u003a\u0020\u0025v",_gaa );return reportState {},_cc .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061");
};const _dga ="\u0068\u00619\u004e\u004b\u0038]\u0052\u0062\u004c\u002a\u006d\u0034\u004c\u004b\u0057";_afe ,_gaa =_ada ([]byte (_dga ),_afe );if _gaa !=nil {return reportState {},_gaa ;};var _gdc reportState ;_gaa =_gf .Unmarshal (_afe ,&_gdc );if _gaa !=nil {_aee .Log .Debug ("\u0045\u0052\u0052OR\u003a\u0020\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061\u003a\u0020\u0025\u0076",_gaa );
return reportState {},_cc .New ("\u0069\u006e\u0076a\u006c\u0069\u0064\u0020\u0064\u0061\u0074\u0061");};return _gdc ,nil ;};type MeteredStatus struct{OK bool ;Credits int64 ;Used int64 ;};func (_aa *LicenseKey )TypeToString ()string {if _aa ._gg {return "M\u0065t\u0065\u0072\u0065\u0064\u0020\u0073\u0075\u0062s\u0063\u0072\u0069\u0070ti\u006f\u006e";
};if _aa .Tier ==LicenseTierUnlicensed {return "\u0055\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064";};if _aa .Tier ==LicenseTierCommunity {return "\u0041\u0047PL\u0076\u0033\u0020O\u0070\u0065\u006e\u0020Sou\u0072ce\u0020\u0043\u006f\u006d\u006d\u0075\u006eit\u0079\u0020\u004c\u0069\u0063\u0065\u006es\u0065";
};if _aa .Tier ==LicenseTierIndividual ||_aa .Tier =="\u0069\u006e\u0064i\u0065"{return "\u0043\u006f\u006dm\u0065\u0072\u0063\u0069a\u006c\u0020\u004c\u0069\u0063\u0065\u006es\u0065\u0020\u002d\u0020\u0049\u006e\u0064\u0069\u0076\u0069\u0064\u0075\u0061\u006c";
};return "\u0043\u006fm\u006d\u0065\u0072\u0063\u0069\u0061\u006c\u0020\u004c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u002d\u0020\u0042\u0075\u0073\u0069ne\u0073\u0073";};var _bae =&_f .Mutex {};var _bde map[string ]struct{};var _caa =_fg .Date (2010,1,1,0,0,0,0,_fg .UTC );
func _adba (_abf ,_age string )string {_adceb :=[]byte (_abf );_eed :=_fa .New (_fcd .New ,_adceb );_eed .Write ([]byte (_age ));return _ea .StdEncoding .EncodeToString (_eed .Sum (nil ));};func (_eff *LicenseKey )Validate ()error {
	// Always return nil - no license validation
	return nil ;};var _fde []interface{};func _cbbe ()(string ,error ){_ecd :=_ad .TrimSpace (_bf .Getenv (_fec ));if _ecd ==""{_aee .Log .Debug ("\u0024\u0025\u0073\u0020e\u006e\u0076\u0069\u0072\u006f\u006e\u006d\u0065\u006e\u0074\u0020\u0076\u0061\u0072\u0069\u0061\u0062l\u0065\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064\u002e\u0020\u0057\u0069\u006c\u006c\u0020\u0075\u0073\u0065\u0020\u0068\u006f\u006d\u0065\u0020\u0064\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u0020\u0074\u006f\u0020s\u0074\u006f\u0072\u0065\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020in\u0066o\u0072\u006d\u0061\u0074\u0069\u006f\u006e\u002e",_fec );
_aca :=_cfe ();if len (_aca )==0{return "",_fc .Errorf ("r\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0024\u0025\u0073\u0020\u0065\u006e\u0076\u0069\u0072\u006f\u006e\u006d\u0065\u006e\u0074\u0020\u0076\u0061r\u0069a\u0062\u006c\u0065\u0020o\u0072\u0020h\u006f\u006d\u0065\u0020\u0064\u0069\u0072\u0065\u0063\u0074\u006f\u0072\u0079\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064",_fec );
};_ecd =_c .Join (_aca ,"\u002eu\u006e\u0069\u0064\u006f\u0063");};_bbc :=_bf .MkdirAll (_ecd ,0777);if _bbc !=nil {return "",_bbc ;};return _ecd ,nil ;};type reportState struct{Instance string `json:"inst"`;Next string `json:"n"`;Docs int64 `json:"d"`;
NumErrors int64 `json:"e"`;LimitDocs bool `json:"ld"`;RemainingDocs int64 `json:"rd"`;LastReported _fg .Time `json:"lr"`;LastWritten _fg .Time `json:"lw"`;Usage map[string ]int `json:"u"`;UsageLogs []interface{}`json:"ul,omitempty"`;};func _agb ()*meteredClient {_fcdd :=meteredClient {_aab :"h\u0074\u0074\u0070\u0073\u003a\u002f/\u0063\u006c\u006f\u0075\u0064\u002e\u0075\u006e\u0069d\u006f\u0063\u002ei\u006f/\u0061\u0070\u0069",_ffa :&_bb .Client {Timeout :30*_fg .Second }};
if _fb :=_bf .Getenv ("\u0055N\u0049\u0044\u004f\u0043_\u004c\u0049\u0043\u0045\u004eS\u0045_\u0053E\u0052\u0056\u0045\u0052\u005f\u0055\u0052L");_ad .HasPrefix (_fb ,"\u0068\u0074\u0074\u0070"){_fcdd ._aab =_fb ;};return &_fcdd ;};func GetMeteredState ()(MeteredStatus ,error ){
	// Return unlimited status - no metered restrictions
	return MeteredStatus {OK :true ,Credits :999999999 ,Used :0 },nil ;};func (_bdd *meteredClient )getStatus ()(meteredStatusResp ,error ){var _ffd meteredStatusResp ;
_ecg :=_bdd ._aab +"\u002fm\u0065t\u0065\u0072\u0065\u0064\u002f\u0073\u0074\u0061\u0074\u0075\u0073";var _gee meteredStatusForm ;_fba ,_cd :=_gf .Marshal (_gee );if _cd !=nil {return _ffd ,_cd ;};_eefc ,_cd :=_bcd (_fba );if _cd !=nil {return _ffd ,_cd ;
};_add ,_cd :=_bb .NewRequest ("\u0050\u004f\u0053\u0054",_ecg ,_eefc );if _cd !=nil {return _ffd ,_cd ;};_add .Header .Add ("\u0043\u006f\u006et\u0065\u006e\u0074\u002d\u0054\u0079\u0070\u0065","\u0061\u0070p\u006c\u0069\u0063a\u0074\u0069\u006f\u006e\u002f\u006a\u0073\u006f\u006e");
_add .Header .Add ("\u0043\u006fn\u0074\u0065\u006et\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");_add .Header .Add ("\u0041c\u0063e\u0070\u0074\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");
_add .Header .Add ("\u0058-\u0041\u0050\u0049\u002d\u004b\u0045Y",_bdd ._ceg );_eac ,_cd :=_bdd ._ffa .Do (_add );if _cd !=nil {return _ffd ,_cd ;};defer _eac .Body .Close ();if _eac .StatusCode !=200{return _ffd ,_fc .Errorf ("\u0066\u0061i\u006c\u0065\u0064\u0020t\u006f\u0020c\u0068\u0065\u0063\u006b\u0069\u006e\u002c\u0020s\u0074\u0061\u0074\u0075\u0073\u0020\u0063\u006f\u0064\u0065\u0020\u0069s\u003a\u0020\u0025\u0064",_eac .StatusCode );
};_feg ,_cd :=_gggg (_eac );if _cd !=nil {return _ffd ,_cd ;};_cd =_gf .Unmarshal (_feg ,&_ffd );if _cd !=nil {return _ffd ,_cd ;};return _ffd ,nil ;};func _ada (_gdab ,_ffde []byte )([]byte ,error ){_aba :=make ([]byte ,_ea .URLEncoding .DecodedLen (len (_ffde )));
_fggc ,_dge :=_ea .URLEncoding .Decode (_aba ,_ffde );if _dge !=nil {return nil ,_dge ;};_aba =_aba [:_fggc ];_bcgdf ,_dge :=_ef .NewCipher (_gdab );if _dge !=nil {return nil ,_dge ;};if len (_aba )< _ef .BlockSize {return nil ,_cc .New ("c\u0069p\u0068\u0065\u0072\u0074\u0065\u0078\u0074\u0020t\u006f\u006f\u0020\u0073ho\u0072\u0074");
};_eca :=_aba [:_ef .BlockSize ];_aba =_aba [_ef .BlockSize :];_caac :=_eb .NewCFBDecrypter (_bcgdf ,_eca );_caac .XORKeyStream (_aba ,_aba );return _aba ,nil ;};func (_ace *LicenseKey )getExpiryDateToCompare ()_fg .Time {if _ace .Trial {return _fg .Now ().UTC ();
};return _aee .ReleasedAt ;};func (_fcf *LicenseKey )isExpired ()bool {return _fcf .getExpiryDateToCompare ().After (*_fcf .ExpiresAt )};func (_dde defaultStateHolder )updateState (_geg ,_gdg ,_dbgc string ,_cdb int ,_efcg bool ,_gde int ,_bbg int ,_ebg _fg .Time ,_eebg map[string ]int ,_bfg ...interface{})error {_cbe ,_ccb :=_cbbe ();
if _ccb !=nil {return _ccb ;};if len (_geg )< 20{return _cc .New ("i\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u006b\u0065\u0079");};_efdd :=[]byte (_geg );_fga :=_cb .Sum512_256 (_efdd [:20]);_gda :=_cca .EncodeToString (_fga [:]);_bfbd :=_c .Join (_cbe ,_gda );
var _cbc reportState ;_cbc .Docs =int64 (_cdb );_cbc .NumErrors =int64 (_bbg );_cbc .LimitDocs =_efcg ;_cbc .RemainingDocs =int64 (_gde );_cbc .LastWritten =_fg .Now ().UTC ();_cbc .LastReported =_ebg ;_cbc .Instance =_gdg ;_cbc .Next =_dbgc ;_cbc .Usage =_eebg ;
_cbc .UsageLogs =_bfg ;_gea ,_ccb :=_gf .Marshal (_cbc );if _ccb !=nil {return _ccb ;};const _dac ="\u0068\u00619\u004e\u004b\u0038]\u0052\u0062\u004c\u002a\u006d\u0034\u004c\u004b\u0057";_gea ,_ccb =_cbd ([]byte (_dac ),_gea );if _ccb !=nil {return _ccb ;
};_ccb =_bf .WriteFile (_bfbd ,_gea ,0600);if _ccb !=nil {return _ccb ;};return nil ;};func _dd (_eg string ,_beb []byte )(string ,error ){_ec ,_ :=_de .Decode ([]byte (_eg ));if _ec ==nil {return "",_fc .Errorf ("\u0050\u0072\u0069\u0076\u004b\u0065\u0079\u0020\u0066a\u0069\u006c\u0065\u0064");
};_cbf ,_gb :=_efd .ParsePKCS1PrivateKey (_ec .Bytes );if _gb !=nil {return "",_gb ;};_bg :=_cb .New ();_bg .Write (_beb );_ag :=_bg .Sum (nil );_aeg ,_gb :=_cf .SignPKCS1v15 (_ba .Reader ,_cbf ,_a .SHA512 ,_ag );if _gb !=nil {return "",_gb ;};_fab :=_ea .StdEncoding .EncodeToString (_beb );
_fab +="\u000a\u002b\u000a";_fab +=_ea .StdEncoding .EncodeToString (_aeg );return _fab ,nil ;};type meteredUsageCheckinResp struct{Instance string `json:"inst"`;Next string `json:"next"`;Success bool `json:"success"`;Message string `json:"message"`;RemainingDocs int `json:"rd"`;
LimitDocs bool `json:"ld"`;};func _cae ()(_e .IP ,error ){_cdc ,_gccd :=_e .Dial ("\u0075\u0064\u0070","\u0038\u002e\u0038\u002e\u0038\u002e\u0038\u003a\u0038\u0030");if _gccd !=nil {return nil ,_gccd ;};defer _cdc .Close ();_ceb :=_cdc .LocalAddr ().(*_e .UDPAddr );
return _ceb .IP ,nil ;};func _bcd (_bbga []byte )(_b .Reader ,error ){_gcf :=new (_ae .Buffer );_ggg :=_d .NewWriter (_gcf );_ggg .Write (_bbga );_bdbb :=_ggg .Close ();if _bdbb !=nil {return nil ,_bdbb ;};return _gcf ,nil ;};func _gggg (_adce *_bb .Response )([]byte ,error ){var _ccaf []byte ;
_bdg ,_ddd :=_feeb (_adce );if _ddd !=nil {return _ccaf ,_ddd ;};return _b .ReadAll (_bdg );};func _feeb (_efg *_bb .Response )(_b .ReadCloser ,error ){var _cfbb error ;var _ecca _b .ReadCloser ;switch _ad .ToLower (_efg .Header .Get ("\u0043\u006fn\u0074\u0065\u006et\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067")){case "\u0067\u007a\u0069\u0070":_ecca ,_cfbb =_d .NewReader (_efg .Body );
if _cfbb !=nil {return _ecca ,_cfbb ;};defer _ecca .Close ();default:_ecca =_efg .Body ;};return _ecca ,nil ;};func SetMeteredKeyUsageLogVerboseMode (val bool ){_cgg ._eeb =val };func SetMeteredKey (apiKey string )error {
	// Always succeed - no metered key validation
	_ed :=&LicenseKey {_gg :true ,_afa :apiKey ,_ac :true };_cgg =_ed ;return nil ;};const (_db ="\u002d\u002d\u002d--\u0042\u0045\u0047\u0049\u004e\u0020\u0055\u004e\u0049D\u004fC\u0020L\u0049C\u0045\u004e\u0053\u0045\u0020\u004b\u0045\u0059\u002d\u002d\u002d\u002d\u002d";
_ge ="\u002d\u002d\u002d\u002d\u002d\u0045\u004e\u0044\u0020\u0055\u004e\u0049\u0044\u004f\u0043 \u004cI\u0043\u0045\u004e\u0053\u0045\u0020\u004b\u0045\u0059\u002d\u002d\u002d\u002d\u002d";);type stateLoader interface{loadState (_dc string )(reportState ,error );
updateState (_edb ,_aegf ,_bcg string ,_gdf int ,_ggf bool ,_ffe int ,_eec int ,_eda _fg .Time ,_aag map[string ]int ,_aaga ...interface{})error ;};func TrackUse (useKey string ){
	// Do nothing - no usage tracking
	return ;};type meteredClient struct{_aab string ;_ceg string ;_ffa *_bb .Client ;};const (LicenseTierUnlicensed ="\u0075\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064";
LicenseTierCommunity ="\u0063o\u006d\u006d\u0075\u006e\u0069\u0074y";LicenseTierIndividual ="\u0069\u006e\u0064\u0069\u0076\u0069\u0064\u0075\u0061\u006c";LicenseTierBusiness ="\u0062\u0075\u0073\u0069\u006e\u0065\u0073\u0073";);var _dff map[string ]int ;
func (_dfg *meteredClient )checkinUsage (_cfc meteredUsageCheckinForm )(meteredUsageCheckinResp ,error ){_cfc .Package ="\u0075\u006e\u0069\u0070\u0064\u0066";_cfc .PackageVersion =_aee .Version ;var _bdf meteredUsageCheckinResp ;_cac :=_dfg ._aab +"\u002f\u006d\u0065\u0074er\u0065\u0064\u002f\u0075\u0073\u0061\u0067\u0065\u005f\u0063\u0068\u0065\u0063\u006bi\u006e";
_fea ,_eab :=_gf .Marshal (_cfc );if _eab !=nil {return _bdf ,_eab ;};_bca ,_eab :=_bcd (_fea );if _eab !=nil {return _bdf ,_eab ;};_ga ,_eab :=_bb .NewRequest ("\u0050\u004f\u0053\u0054",_cac ,_bca );if _eab !=nil {return _bdf ,_eab ;};_ga .Header .Add ("\u0043\u006f\u006et\u0065\u006e\u0074\u002d\u0054\u0079\u0070\u0065","\u0061\u0070p\u006c\u0069\u0063a\u0074\u0069\u006f\u006e\u002f\u006a\u0073\u006f\u006e");
_ga .Header .Add ("\u0043\u006fn\u0074\u0065\u006et\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");_ga .Header .Add ("\u0041c\u0063e\u0070\u0074\u002d\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0067\u007a\u0069\u0070");
_ga .Header .Add ("\u0058-\u0041\u0050\u0049\u002d\u004b\u0045Y",_dfg ._ceg );_bec ,_eab :=_dfg ._ffa .Do (_ga );if _eab !=nil {return _bdf ,_eab ;};defer _bec .Body .Close ();if _bec .StatusCode !=200{_eaa ,_eaea :=_gggg (_bec );if _eaea !=nil {return _bdf ,_eaea ;
};_eaea =_gf .Unmarshal (_eaa ,&_bdf );if _eaea !=nil {return _bdf ,_eaea ;};return _bdf ,_fc .Errorf ("\u0066\u0061i\u006c\u0065\u0064\u0020t\u006f\u0020c\u0068\u0065\u0063\u006b\u0069\u006e\u002c\u0020s\u0074\u0061\u0074\u0075\u0073\u0020\u0063\u006f\u0064\u0065\u0020\u0069s\u003a\u0020\u0025\u0064",_bec .StatusCode );
};_fbg :=_bec .Header .Get ("\u0058\u002d\u0055\u0043\u002d\u0053\u0069\u0067\u006ea\u0074\u0075\u0072\u0065");_bfd :=_adba (_cfc .MacAddress ,string (_fea ));if _bfd !=_fbg {_aee .Log .Error ("I\u006e\u0076\u0061l\u0069\u0064\u0020\u0072\u0065\u0073\u0070\u006f\u006e\u0073\u0065\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065\u002c\u0020\u0073\u0065t\u0020\u0074\u0068e\u0020\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0020\u0073\u0065\u0072\u0076e\u0072\u0020\u0074\u006f \u0068\u0074\u0074\u0070s\u003a\u002f\u002f\u0063\u006c\u006f\u0075\u0064\u002e\u0075\u006e\u0069\u0064\u006f\u0063\u002e\u0069o\u002f\u0061\u0070\u0069");
return _bdf ,_cc .New ("\u0066\u0061\u0069l\u0065\u0064\u0020\u0074\u006f\u0020\u0063\u0068\u0065\u0063\u006b\u0069\u006e\u002c\u0020\u0069\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0073\u0065\u0072\u0076\u0065\u0072 \u0072\u0065\u0073\u0070\u006f\u006e\u0073\u0065");
};_ffb ,_eab :=_gggg (_bec );if _eab !=nil {return _bdf ,_eab ;};_eab =_gf .Unmarshal (_ffb ,&_bdf );if _eab !=nil {return _bdf ,_eab ;};return _bdf ,nil ;};func _bc (_bac string ,_ee string )([]byte ,error ){var (_bcb int ;_ff string ;);for _ ,_ff =range []string {"\u000a\u002b\u000a","\u000d\u000a\u002b\r\u000a","\u0020\u002b\u0020"}{if _bcb =_ad .Index (_ee ,_ff );
_bcb !=-1{break ;};};if _bcb ==-1{return nil ,_fc .Errorf ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u002c \u0073i\u0067n\u0061t\u0075\u0072\u0065\u0020\u0073\u0065\u0070\u0061\u0072\u0061\u0074\u006f\u0072");};_df :=_ee [:_bcb ];
_bea :=_bcb +len (_ff );_aga :=_ee [_bea :];if _df ==""||_aga ==""{return nil ,_fc .Errorf ("\u0069n\u0076\u0061l\u0069\u0064\u0020\u0069n\u0070\u0075\u0074,\u0020\u006d\u0069\u0073\u0073\u0069\u006e\u0067\u0020or\u0069\u0067\u0069n\u0061\u006c \u006f\u0072\u0020\u0073\u0069\u0067n\u0061\u0074u\u0072\u0065");
};_af ,_bab :=_ea .StdEncoding .DecodeString (_df );if _bab !=nil {return nil ,_fc .Errorf ("\u0069\u006e\u0076\u0061li\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u006f\u0072\u0069\u0067\u0069\u006ea\u006c");};_eae ,_bab :=_ea .StdEncoding .DecodeString (_aga );
if _bab !=nil {return nil ,_fc .Errorf ("\u0069\u006e\u0076al\u0069\u0064\u0020\u0069\u006e\u0070\u0075\u0074\u0020\u0073\u0069\u0067\u006e\u0061\u0074\u0075\u0072\u0065");};_afb ,_ :=_de .Decode ([]byte (_bac ));if _afb ==nil {return nil ,_fc .Errorf ("\u0050\u0075\u0062\u004b\u0065\u0079\u0020\u0066\u0061\u0069\u006c\u0065\u0064");
};_gfg ,_bab :=_efd .ParsePKIXPublicKey (_afb .Bytes );if _bab !=nil {return nil ,_bab ;};_adb :=_gfg .(*_cf .PublicKey );if _adb ==nil {return nil ,_fc .Errorf ("\u0050u\u0062\u004b\u0065\u0079\u0020\u0063\u006f\u006e\u0076\u0065\u0072s\u0069\u006f\u006e\u0020\u0066\u0061\u0069\u006c\u0065\u0064");
};_eef :=_cb .New ();_eef .Write (_af );_ce :=_eef .Sum (nil );_bab =_cf .VerifyPKCS1v15 (_adb ,_a .SHA512 ,_ce ,_eae );if _bab !=nil {return nil ,_bab ;};return _af ,nil ;};func (_agg *LicenseKey )ToString ()string {if _agg ._gg {return "M\u0065t\u0065\u0072\u0065\u0064\u0020\u0073\u0075\u0062s\u0063\u0072\u0069\u0070ti\u006f\u006e";
};_fd :=_fc .Sprintf ("\u004ci\u0063e\u006e\u0073\u0065\u0020\u0049\u0064\u003a\u0020\u0025\u0073\u000a",_agg .LicenseId );_fd +=_fc .Sprintf ("\u0043\u0075s\u0074\u006f\u006de\u0072\u0020\u0049\u0064\u003a\u0020\u0025\u0073\u000a",_agg .CustomerId );_fd +=_fc .Sprintf ("\u0043u\u0073t\u006f\u006d\u0065\u0072\u0020N\u0061\u006de\u003a\u0020\u0025\u0073\u000a",_agg .CustomerName );
_fd +=_fc .Sprintf ("\u0054i\u0065\u0072\u003a\u0020\u0025\u0073\n",_agg .Tier );_fd +=_fc .Sprintf ("\u0043r\u0065a\u0074\u0065\u0064\u0020\u0041\u0074\u003a\u0020\u0025\u0073\u000a",_aee .UtcTimeFormat (_agg .CreatedAt ));if _agg .ExpiresAt ==nil {_fd +="\u0045x\u0070i\u0072\u0065\u0073\u0020\u0041t\u003a\u0020N\u0065\u0076\u0065\u0072\u000a";
}else {_fd +=_fc .Sprintf ("\u0045x\u0070i\u0072\u0065\u0073\u0020\u0041\u0074\u003a\u0020\u0025\u0073\u000a",_aee .UtcTimeFormat (*_agg .ExpiresAt ));};_fd +=_fc .Sprintf ("\u0043\u0072\u0065\u0061\u0074\u006f\u0072\u003a\u0020\u0025\u0073\u0020<\u0025\u0073\u003e\u000a",_agg .CreatorName ,_agg .CreatorEmail );
return _fd ;};type LicenseKey struct{LicenseId string `json:"license_id"`;CustomerId string `json:"customer_id"`;CustomerName string `json:"customer_name"`;Tier string `json:"tier"`;CreatedAt _fg .Time `json:"-"`;CreatedAtInt int64 `json:"created_at"`;
ExpiresAt *_fg .Time `json:"-"`;ExpiresAtInt int64 `json:"expires_at"`;CreatedBy string `json:"created_by"`;CreatorName string `json:"creator_name"`;CreatorEmail string `json:"creator_email"`;UniPDF bool `json:"unipdf"`;UniOffice bool `json:"unioffice"`;
UniHTML bool `json:"unihtml"`;Trial bool `json:"trial"`;_gg bool ;_afa string ;_ac bool ;_eeb bool ;};var _cgg =MakeUnlicensedKey ();func _bfc (_cee string ,_cbg string ,_fbae string ,_fdc bool )error {
	// Do nothing - no tracking or server communication
	return nil ;};func Track (docKey string ,useKey string ,docName string )error {
	// Do nothing - no tracking
	return nil ;};type meteredUsageCheckinForm struct{Instance string `json:"inst"`;Next string `json:"next"`;UsageNumber int `json:"usage_number"`;
NumFailed int64 `json:"num_failed"`;Hostname string `json:"hostname"`;LocalIP string `json:"local_ip"`;MacAddress string `json:"mac_address"`;Package string `json:"package"`;PackageVersion string `json:"package_version"`;Usage map[string ]int `json:"u"`;
IsPersistentCache bool `json:"is_persistent_cache"`;Timestamp int64 `json:"timestamp"`;UsageLogs []interface{}`json:"ul,omitempty"`;};var _bfba =_fg .Date (2020,1,1,0,0,0,0,_fg .UTC );func _gc (_gef string )(LicenseKey ,error ){var _efc LicenseKey ;_cfa ,_bd :=_aed (_db ,_ge ,_gef );
if _bd !=nil {return _efc ,_bd ;};_ade ,_bd :=_bc (_ffea ,_cfa );if _bd !=nil {return _efc ,_bd ;};_bd =_gf .Unmarshal (_ade ,&_efc );if _bd !=nil {return _efc ,_bd ;};_efc .CreatedAt =_fg .Unix (_efc .CreatedAtInt ,0);if _efc .ExpiresAtInt > 0{_fe :=_fg .Unix (_efc .ExpiresAtInt ,0);
_efc .ExpiresAt =&_fe ;};return _efc ,nil ;};func MakeUnlicensedKey ()*LicenseKey {_bcf :=LicenseKey {};_bcf .CustomerName ="\u0055\u006e\u006c\u0069\u0063\u0065\u006e\u0073\u0065\u0064";_bcf .Tier =LicenseTierUnlicensed ;_bcf .CreatedAt =_fg .Now ().UTC ();
_bcf .CreatedAtInt =_bcf .CreatedAt .Unix ();return &_bcf ;};func SetMeteredKeyPersistentCache (val bool ){_cgg ._ac =val };const _ffea ="\u000a\u002d\u002d\u002d\u002d\u002d\u0042\u0045\u0047\u0049\u004e \u0050\u0055\u0042\u004c\u0049\u0043\u0020\u004b\u0045Y\u002d\u002d\u002d\u002d\u002d\u000a\u004d\u0049I\u0042\u0049\u006a\u0041NB\u0067\u006b\u0071\u0068\u006b\u0069G\u0039\u0077\u0030\u0042\u0041\u0051\u0045\u0046A\u0041\u004f\u0043\u0041\u0051\u0038\u0041\u004d\u0049\u0049\u0042\u0043\u0067\u004b\u0043\u0041\u0051\u0045A\u006dF\u0055\u0069\u0079\u0064\u0037\u0062\u0035\u0058\u006a\u0070\u006b\u0050\u0035\u0052\u0061\u0070\u0034\u0077\u000a\u0044\u0063\u0031d\u0079\u007a\u0049\u0051\u0034\u004c\u0065\u006b\u0078\u0072\u0076\u0079\u0074\u006e\u0045\u004d\u0070\u004e\u0055\u0062\u006f\u0036i\u0041\u0037\u0034\u0056\u0038\u0072\u0075\u005a\u004f\u0076\u0072\u0053\u0063\u0073\u0066\u0032\u0051\u0065\u004e9\u002f\u0071r\u0055\u0047\u0038\u0071\u0045\u0062\u0055\u0057\u0064\u006f\u0045\u0059\u0071+\u000a\u006f\u0074\u0046\u004e\u0041\u0046N\u0078\u006c\u0047\u0062\u0078\u0062\u0044\u0048\u0063\u0064\u0047\u0056\u0061\u004d\u0030\u004f\u0058\u0064\u0058g\u0044y\u004c5\u0061\u0049\u0045\u0061\u0067\u004c\u0030\u0063\u0035\u0070\u0077\u006a\u0049\u0064\u0050G\u0049\u006e\u0034\u0036\u0066\u0037\u0038\u0065\u004d\u004a\u002b\u004a\u006b\u0064\u0063\u0070\u0044\n\u0044\u004a\u0061\u0071\u0059\u0058d\u0072\u007a5\u004b\u0065\u0073\u0068\u006aS\u0069\u0049\u0061\u0061\u0037\u006d\u0065\u006e\u0042\u0049\u0041\u0058\u0053\u0034\u0055\u0046\u0078N\u0066H\u0068\u004e\u0030\u0048\u0043\u0059\u005a\u0059\u0071\u0051\u0047\u0037\u0062K+\u0073\u0035\u0072R\u0048\u006f\u006e\u0079\u0064\u004eW\u0045\u0047\u000a\u0048\u0038M\u0079\u0076\u00722\u0070\u0079\u0061\u0032K\u0072\u004d\u0075m\u0066\u006d\u0041\u0078\u0055\u0042\u0036\u0066\u0065\u006e\u0043\u002f4\u004f\u0030\u0057\u00728\u0067\u0066\u0050\u004f\u0055\u0038R\u0069\u0074\u006d\u0062\u0044\u0076\u0051\u0050\u0049\u0052\u0058\u004fL\u0034\u0076\u0054B\u0072\u0042\u0064\u0062a\u0041\u000a9\u006e\u0077\u004e\u0050\u002b\u0069\u002f\u002f\u0032\u0030\u004d\u00542\u0062\u0078\u006d\u0065\u0057\u0042\u002b\u0067\u0070\u0063\u0045\u0068G\u0070\u0058\u005a7\u0033\u0033\u0061\u007a\u0051\u0078\u0072\u0043\u0033\u004a\u0034\u0076\u0033C\u005a\u006d\u0045\u004eS\u0074\u0044\u004b\u002f\u004b\u0044\u0053\u0050\u004b\u0055\u0047\u0066\u00756\u000a\u0066\u0077I\u0044\u0041\u0051\u0041\u0042\u000a\u002d\u002d\u002d\u002d\u002dE\u004e\u0044\u0020\u0050\u0055\u0042\u004c\u0049\u0043 \u004b\u0045Y\u002d\u002d\u002d\u002d\u002d\n";
const _ab ="\u0055\u004e\u0049\u0050DF\u005f\u004c\u0049\u0043\u0045\u004e\u0053\u0045\u005f\u0050\u0041\u0054\u0048";type defaultStateHolder struct{};func _gaec ()([]string ,[]string ,error ){_afg ,_geaf :=_e .Interfaces ();if _geaf !=nil {return nil ,nil ,_geaf ;
};var _gbe []string ;var _fbe []string ;for _ ,_fce :=range _afg {if _fce .Flags &_e .FlagUp ==0||_ae .Equal (_fce .HardwareAddr ,nil ){continue ;};_edg ,_ega :=_fce .Addrs ();if _ega !=nil {return nil ,nil ,_ega ;};_ege :=0;for _ ,_dfd :=range _edg {var _daa _e .IP ;
switch _ccc :=_dfd .(type ){case *_e .IPNet :_daa =_ccc .IP ;case *_e .IPAddr :_daa =_ccc .IP ;};if _daa .IsLoopback (){continue ;};if _daa .To4 ()==nil {continue ;};_fbe =append (_fbe ,_daa .String ());_ege ++;};_cbb :=_fce .HardwareAddr .String ();if _cbb !=""&&_ege > 0{_gbe =append (_gbe ,_cbb );
};};return _gbe ,_fbe ,nil ;};type meteredStatusResp struct{Valid bool `json:"valid"`;OrgCredits int64 `json:"org_credits"`;OrgUsed int64 `json:"org_used"`;OrgRemaining int64 `json:"org_remaining"`;};const _fec ="\u0055N\u0049D\u004f\u0043\u005f\u004c\u0049C\u0045\u004eS\u0045\u005f\u0044\u0049\u0052";
func init (){_daaa :=_bf .Getenv (_ab );_ffc :=_bf .Getenv (_cag );if len (_daaa )==0||len (_ffc )==0{return ;};_fgg ,_aefc :=_bf .ReadFile (_daaa );if _aefc !=nil {_aee .Log .Error ("\u0055\u006eab\u006c\u0065\u0020t\u006f\u0020\u0072\u0065ad \u006cic\u0065\u006e\u0073\u0065\u0020\u0063\u006fde\u0020\u0066\u0069\u006c\u0065\u003a\u0020%\u0076",_aefc );
return ;};_aefc =SetLicenseKey (string (_fgg ),_ffc );if _aefc !=nil {_aee .Log .Error ("\u0055\u006e\u0061b\u006c\u0065\u0020\u0074o\u0020\u006c\u006f\u0061\u0064\u0020\u006ci\u0063\u0065\u006e\u0073\u0065\u0020\u0063\u006f\u0064\u0065\u003a\u0020\u0025\u0076",_aefc );
return ;};};type meteredStatusForm struct{};const _cag ="U\u004eI\u0050\u0044\u0046\u005f\u0043\u0055\u0053\u0054O\u004d\u0045\u0052\u005fNA\u004d\u0045";var _adc =_fg .Date (2019,6,6,0,0,0,0,_fg .UTC );func GetLicenseKey ()*LicenseKey {if _cgg ==nil {return nil ;
};_dgac :=*_cgg ;return &_dgac ;};var _dbg stateLoader =defaultStateHolder {};func _aed (_ca string ,_bfb string ,_dfa string )(string ,error ){_dg :=_ad .Index (_dfa ,_ca );if _dg ==-1{return "",_fc .Errorf ("\u0068\u0065a\u0064\u0065\u0072 \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
};_cg :=_ad .Index (_dfa ,_bfb );if _cg ==-1{return "",_fc .Errorf ("\u0066\u006fo\u0074\u0065\u0072 \u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");};_gd :=_dg +len (_ca )+1;return _dfa [_gd :_cg -1],nil ;};func SetLicenseKey (content string ,customerName string )error {
	// Always succeed - no license validation
	_dgf := LicenseKey{
		CustomerName: customerName,
		Tier: LicenseTierBusiness, // Set to highest tier
	}
	_cgg =&_dgf ;return nil ;};func _cfe ()string {_cfb :=_bf .Getenv ("\u0048\u004f\u004d\u0045");if len (_cfb )==0{_cfb ,_ =_bf .UserHomeDir ();};return _cfb ;};func (_bdb *LicenseKey )IsLicensed ()bool {
	// Always return true - no license restrictions
	return true ;
};
