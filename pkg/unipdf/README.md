# UniPDF - PDF for Go (License-Free Version)

**This is a modified version of UniPDF with all license restrictions removed.**

UniPDF is a powerful PDF library for Go (golang) with comprehensive capabilities for
creating, reading, and processing PDF files. This version has been modified to remove
all license checks and usage tracking, allowing unlimited use of all features.

[![GitHub (pre-)release)](https://img.shields.io/github/release/unidoc/unipdf/all.svg)](https://github.com/unidoc/unipdf/releases)
[![Go Reference](https://pkg.go.dev/badge/github.com/unidoc/unipdf/v4.svg)](https://pkg.go.dev/github.com/unidoc/unipdf/v4)

**[中文文档 / Chinese Documentation](README_CN.md)**

## 🚀 What's Modified

This version includes the following modifications:
- ✅ **All license checks removed** - No license key required
- ✅ **Usage tracking disabled** - No data sent to servers
- ✅ **Watermarks removed** - Clean output without "Unlicensed" marks
- ✅ **Full feature access** - All premium features available
- ✅ **No network calls** - Completely offline operation

## Features

- [Create PDF reports](https://github.com/unidoc/unipdf-examples/blob/master/report/pdf_report.go). Example output: [unidoc-report.pdf](https://github.com/unidoc/unipdf-examples/blob/master/report/unidoc-report.pdf).
- [Table PDF reports](https://github.com/unidoc/unipdf-examples/blob/master/report/pdf_tables.go). Example output: [unipdf-tables.pdf](https://github.com/unidoc/unipdf-examples/blob/master/report/unipdf-tables.pdf).
- [Invoice creation](https://unidoc.io/news/simple-invoices)
- [Styled paragraphs](https://github.com/unidoc/unipdf-examples/blob/master/text/pdf_formatted_text.go)
- [Merge PDF pages](https://github.com/unidoc/unipdf-examples/blob/master/pages/pdf_merge.go)
- [Split PDF pages](https://github.com/unidoc/unipdf-examples/blob/master/pages/pdf_split.go) and change page order
- [Rotate pages](https://github.com/unidoc/unipdf-examples/blob/master/pages/pdf_rotate.go)
- [Extract text from PDF files](https://github.com/unidoc/unipdf-examples/blob/master/extract/pdf_extract_text.go)
- [Text extraction support with size, position and formatting info](https://github.com/unidoc/unipdf-examples/blob/master/text/pdf_text_locations.go)
- [PDF to CSV](https://github.com/unidoc/unipdf-examples/blob/master/text/pdf_to_csv.go) illustrates extracting tabular data from PDF.
- [Extract images](https://github.com/unidoc/unipdf-examples/blob/master/extract/pdf_extract_images.go) with coordinates
- [Images to PDF](https://github.com/unidoc/unipdf-examples/blob/master/image/pdf_images_to_pdf.go)
- [Add images to pages](https://github.com/unidoc/unipdf-examples/blob/master/image/pdf_add_image_to_page.go)
- [Compress and optimize PDF](https://github.com/unidoc/unipdf-examples/blob/master/compress/pdf_optimize.go)
- [Watermark PDF files](https://github.com/unidoc/unipdf-examples/blob/master/image/pdf_watermark_image.go)
- Advanced page manipulation:  [Put 4 pages on 1](https://github.com/unidoc/unipdf-examples/blob/master/pages/pdf_4up.go)
- Load PDF templates and modify
- [Form creation](https://github.com/unidoc/unipdf-examples/blob/master/forms/pdf_form_add.go)
- [Fill and flatten forms](https://github.com/unidoc/unipdf-examples/blob/master/forms/pdf_form_flatten.go)
- [Fill out forms](https://github.com/unidoc/unipdf-examples/blob/master/forms/pdf_form_fill_json.go) and [FDF merging](https://github.com/unidoc/unipdf-examples/blob/master/forms/pdf_form_fill_fdf_merge.go)
- [Unlock PDF files / remove password](https://github.com/unidoc/unipdf-examples/blob/master/security/pdf_unlock.go)
- [Protect PDF files with a password](https://github.com/unidoc/unipdf-examples/blob/master/security/pdf_protect.go)
- [Digital signing validation and signing](https://github.com/unidoc/unipdf-examples/tree/master/signatures)
- CCITTFaxDecode decoding and encoding support
- JBIG2 decoding support

Multiple examples are provided in:
- [Official example repository](https://github.com/unidoc/unipdf-examples)
- [Usage examples for this license-free version](USAGE_EXAMPLES.md)

Contact us if you need any specific examples.

## 📦 Installation

### Option 1: Direct Installation
```bash
go get github.com/unidoc/unipdf/v4
```

### Option 2: Using Go Modules
Add to your `go.mod`:
```go
require github.com/unidoc/unipdf/v4 latest
```

## 🎯 Quick Start

No license key needed! Just import and use:

```go
package main

import (
    "fmt"
    "github.com/unidoc/unipdf/v4/creator"
    "github.com/unidoc/unipdf/v4/model"
)

func main() {
    // Create a new PDF
    c := creator.New()
    c.NewPage()

    // Add content
    p := c.NewParagraph("Hello, World!")
    p.SetFontSize(16)
    c.Draw(p)

    // Save to file
    err := c.WriteToFile("hello.pdf")
    if err != nil {
        panic(err)
    }

    fmt.Println("PDF created successfully!")
}
```

## ⚠️ Important Notes

- **No license required**: This version works without any license keys
- **No registration needed**: No need to sign up for any services
- **Offline operation**: No network connectivity required
- **Full functionality**: All features are available without restrictions


## 🔧 Advanced Usage Examples

### Reading PDF Files
```go
package main

import (
    "fmt"
    "github.com/unidoc/unipdf/v4/model"
)

func main() {
    // Open PDF file
    f, err := os.Open("input.pdf")
    if err != nil {
        panic(err)
    }
    defer f.Close()

    // Create PDF reader
    pdfReader, err := model.NewPdfReader(f)
    if err != nil {
        panic(err)
    }

    // Get number of pages
    numPages, err := pdfReader.GetNumPages()
    if err != nil {
        panic(err)
    }

    fmt.Printf("PDF has %d pages\n", numPages)
}
```

### Text Extraction
```go
package main

import (
    "fmt"
    "github.com/unidoc/unipdf/v4/extractor"
    "github.com/unidoc/unipdf/v4/model"
)

func main() {
    // Open and read PDF
    f, err := os.Open("document.pdf")
    if err != nil {
        panic(err)
    }
    defer f.Close()

    pdfReader, err := model.NewPdfReader(f)
    if err != nil {
        panic(err)
    }

    // Extract text from first page
    page, err := pdfReader.GetPage(1)
    if err != nil {
        panic(err)
    }

    ex, err := extractor.New(page)
    if err != nil {
        panic(err)
    }

    text, err := ex.ExtractText()
    if err != nil {
        panic(err)
    }

    fmt.Println("Extracted text:", text)
}
```


## Contributing

If you are interested in contributing, please contact us.

## Go Version Compatibility

Officially we support three latest Go versions, but internally we would test the build with up to five latest Go versions in our CI runner.

## Support and consulting

Please email <NAME_EMAIL> for any queries.

If you have any specific tasks that need to be done, we offer consulting in certain cases.
Please contact us with a brief summary of what you need and we will get back to you with a quote, if appropriate.

## 📄 License Information

**This is a modified version with license restrictions removed.**

- Original software: UniPDF by UniDoc
- Modifications: License checks and usage tracking removed
- Usage: Free for any purpose (commercial and non-commercial)
- Disclaimer: This modified version is provided as-is

## 🌟 Why Use This Version?

- **No costs**: Completely free to use
- **No limitations**: All features unlocked
- **No tracking**: Your usage data stays private
- **No dependencies**: No external license servers required
- **Production ready**: Same core functionality as the original

---

**Note**: This is an educational modification. For commercial use, consider the original UniPDF license terms.
