{"version": "1.0", "description": "Configuration for UniPDF license removal", "backup_enabled": true, "modifications": [{"file": "internal/license/license.go", "type": "complete_replacement", "description": "Replace entire license module with bypass implementation", "backup": true, "content_template": "license_bypass_template.go"}, {"file": "extractor/extractor.go", "type": "function_replacement", "description": "Replace license check function in extractor", "patterns": [{"search": "func _bgcdd \\(_caebf \\*PageText \\)error \\{[^}]*return _cc \\.New[^}]*\\}", "replace": "func _bgcdd (_caebf *PageText )error {\n\t// Always return nil - no license check\n\treturn nil ;\n}", "flags": ["DOTALL", "MULTILINE"]}]}, {"file": "model/model.go", "type": "multiple_replacements", "description": "Remove license imports and replace license functions", "patterns": [{"search": "_cda \"github\\.com/unidoc/unipdf/v4/internal/license\";", "replace": "", "description": "Remove license import"}, {"search": "func \\(_fcebd \\*PdfWriter \\)checkLicense \\(\\)error \\{[^}]*return nil ;\\}", "replace": "func (_fcebd *PdfWriter )checkLicense ()error {\n\t// Always return nil - no license check\n\treturn nil ;\n}", "flags": ["DOTALL"], "description": "Replace checkLicense function"}, {"search": "_addba =_cda \\.Track", "replace": "// _addba =_cda .Track", "description": "Comment out Track calls"}, {"search": "_geacg :=_cda \\.Track", "replace": "// _geacg :=_cda .Track", "description": "Comment out Track calls"}, {"search": "_gafgf :=_cda \\.Track", "replace": "// _gafgf :=_cda .Track", "description": "Comment out Track calls"}, {"search": "func _afgdga \\(_eeade \\*PdfPage \\)_ea \\.PdfObject \\{[^}]*return _eeade \\.ToPdfObject \\(\\);\\}", "replace": "func _afgdga (_eeade *PdfPage )_ea .PdfObject {\n\t// Always return normal page object - no watermark\n\treturn _eeade .ToPdfObject ();\n}", "flags": ["DOTALL"], "description": "Remove watermark function"}]}, {"file": "creator/creator.go", "type": "multiple_replacements", "description": "Remove license imports and tracking calls", "patterns": [{"search": "_bced \"github\\.com/unidoc/unipdf/v4/internal/license\";", "replace": "", "description": "Remove license import"}, {"search": "_bced \\.TrackUse", "replace": "// _bced .TrackUse", "description": "Comment out TrackUse calls"}]}], "verification_tests": [{"name": "compilation_test", "command": "go build -o /dev/null ./...", "description": "Verify project compiles successfully"}, {"name": "license_check_test", "description": "Verify license functions return expected values", "code": "license_test.go"}, {"name": "functionality_test", "description": "Test basic PDF operations work without license", "code": "functionality_test.go"}], "rollback": {"enabled": true, "backup_directory_pattern": "license_removal_backup_{timestamp}", "restore_command": "restore_from_backup.sh"}, "documentation_updates": [{"file": "README.md", "action": "update_title", "new_title": "UniPDF - PDF for Go (License-Free Version)"}, {"file": "README_CN.md", "action": "create", "template": "readme_cn_template.md"}, {"file": "USAGE_EXAMPLES.md", "action": "create", "template": "usage_examples_template.md"}], "post_processing": [{"action": "remove_unused_imports", "description": "Clean up any unused imports after modifications"}, {"action": "format_code", "command": "go fmt ./...", "description": "Format all Go code"}, {"action": "run_tests", "command": "go test ./...", "description": "Run all tests to ensure functionality"}], "metadata": {"created_by": "license_removal_script", "target_version": "unipdf_v4", "compatibility": ["go1.19", "go1.20", "go1.21"], "last_updated": "2024-08-02"}}