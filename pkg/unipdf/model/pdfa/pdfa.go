//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

// Package pdfa provides abstraction to optimize and verify documents with respect to the PDF/A standards.
// NOTE: This implementation is in experimental development state.
//
//	Keep in mind that it might change in the subsequent minor versions.
package pdfa ;import (_ce "errors";_d "fmt";_bd "github.com/adrg/sysfont";_df "github.com/trimmer-io/go-xmp/models/dc";_gd "github.com/trimmer-io/go-xmp/models/pdf";_ag "github.com/trimmer-io/go-xmp/models/xmp_base";_de "github.com/trimmer-io/go-xmp/models/xmp_mm";
_dd "github.com/trimmer-io/go-xmp/models/xmp_rights";_cc "github.com/trimmer-io/go-xmp/xmp";_bf "github.com/unidoc/unipdf/v4/common";_e "github.com/unidoc/unipdf/v4/contentstream";_eb "github.com/unidoc/unipdf/v4/core";_ba "github.com/unidoc/unipdf/v4/internal/cmap";
_ec "github.com/unidoc/unipdf/v4/internal/imageutil";_be "github.com/unidoc/unipdf/v4/internal/timeutils";_a "github.com/unidoc/unipdf/v4/model";_bea "github.com/unidoc/unipdf/v4/model/internal/colorprofile";_gde "github.com/unidoc/unipdf/v4/model/internal/docutil";
_bad "github.com/unidoc/unipdf/v4/model/internal/fonts";_bg "github.com/unidoc/unipdf/v4/model/xmputil";_ea "github.com/unidoc/unipdf/v4/model/xmputil/pdfaextension";_aga "github.com/unidoc/unipdf/v4/model/xmputil/pdfaid";_b "image/color";_g "math";_c "sort";
_bb "strings";_ca "time";);func _bgf ()standardType {return standardType {_dda :3,_ff :"\u0041"}};func _dfcg (_dbgd *_eb .PdfObjectDictionary ,_gbff map[*_eb .PdfObjectStream ][]byte ,_bdfa map[*_eb .PdfObjectStream ]*_ba .CMap )ViolatedRule {const (_bafc ="\u0036.\u0033\u002e\u0038\u002d\u0031";
_dbaf ="\u0054\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006cl\u0020\u0069\u006e\u0063l\u0075\u0064e\u0020\u0061 \u0054\u006f\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u0020\u0065\u006e\u0074\u0072\u0079\u0020w\u0068\u006f\u0073\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073 \u0061\u0020\u0043M\u0061\u0070\u0020\u0073\u0074\u0072\u0065\u0061\u006d \u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u006d\u0061p\u0073\u0020\u0063\u0068\u0061\u0072ac\u0074\u0065\u0072\u0020\u0063\u006fd\u0065s\u0020\u0074\u006f\u0020\u0055\u006e\u0069\u0063\u006f\u0064e \u0076a\u006c\u0075\u0065\u0073,\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063r\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020P\u0044\u0046\u0020\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0035.\u0039\u002c\u0020\u0075\u006e\u006ce\u0073\u0073\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u006d\u0065\u0065\u0074\u0073 \u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u0020\u0074\u0068\u0072\u0065\u0065\u0020\u0063\u006f\u006e\u0064\u0069\u0074\u0069\u006f\u006e\u0073\u003a\u000a\u0020\u002d\u0020\u0066o\u006e\u0074\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0075\u0073\u0065\u0020\u0074\u0068\u0065\u0020\u0070\u0072\u0065\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0065\u006e\u0063\u006f\u0064\u0069n\u0067\u0073\u0020M\u0061\u0063\u0052o\u006d\u0061\u006e\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002c\u0020\u004d\u0061\u0063\u0045\u0078\u0070\u0065\u0072\u0074E\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006f\u0072\u0020\u0057\u0069\u006e\u0041n\u0073\u0069\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002c\u0020\u006f\u0072\u0020\u0074\u0068\u0061\u0074\u0020\u0075\u0073\u0065\u0020t\u0068\u0065\u0020\u0070\u0072\u0065d\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048\u0020\u006f\u0072\u0020\u0049\u0064\u0065n\u0074\u0069\u0074\u0079\u002d\u0056\u0020C\u004d\u0061\u0070s\u003b\u000a\u0020\u002d\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0077\u0068\u006f\u0073\u0065\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u006e\u0061\u006d\u0065\u0073\u0020a\u0072\u0065 \u0074\u0061k\u0065\u006e\u0020\u0066\u0072\u006f\u006d\u0020\u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065\u0020\u0073\u0074\u0061n\u0064\u0061\u0072\u0064\u0020L\u0061t\u0069\u006e\u0020\u0063\u0068a\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0065\u0074\u0020\u006fr\u0020\u0074\u0068\u0065 \u0073\u0065\u0074\u0020\u006f\u0066 \u006e\u0061\u006d\u0065\u0064\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065r\u0073\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0079\u006d\u0062\u006f\u006c\u0020\u0066\u006f\u006e\u0074\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020i\u006e\u0020\u0050\u0044\u0046 \u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0041\u0070\u0070\u0065\u006e\u0064\u0069\u0078 \u0044\u003b\u000a\u0020\u002d\u0020\u0054\u0079\u0070\u0065\u0020\u0030\u0020\u0066\u006f\u006e\u0074\u0073\u0020w\u0068\u006f\u0073e\u0020d\u0065\u0073\u0063\u0065n\u0064\u0061\u006e\u0074 \u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0075\u0073\u0065\u0073\u0020\u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u0047B\u0031\u002c\u0020\u0041\u0064\u006fb\u0065\u002d\u0043\u004e\u0053\u0031\u002c\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u004a\u0061\u0070\u0061\u006e\u0031\u0020\u006f\u0072\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u004b\u006fr\u0065\u0061\u0031\u0020\u0063\u0068\u0061r\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u006c\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u0073\u002e";
);_acga :=_dbgd .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");if _cgbb ,_edbfa :=_eb .GetName (_acga );_edbfa {if _cgbb .String ()=="\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048"||_cgbb .String ()=="\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056"||_cgbb .String ()=="\u004d\u0061c\u0052\u006f\u006da\u006e\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067"||_cgbb .String ()=="\u004d\u0061\u0063\u0045\u0078\u0070\u0065\u0072\u0074\u0045\u006e\u0063o\u0064\u0069\u006e\u0067"||_cgbb .String ()=="\u0057i\u006eA\u006e\u0073\u0069\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067"{return _ddd ;
};};_gdab ,_cdcde :=_eb .GetStream (_dbgd .Get ("\u0054o\u0055\u006e\u0069\u0063\u006f\u0064e"));if _cdcde {_ ,_fbbe :=_cafgb (_gdab ,_gbff ,_bdfa );if _fbbe !=nil {return _ee (_bafc ,_dbaf );};return _ddd ;};_feb ,_cdcde :=_eb .GetName (_dbgd .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_cdcde {return _ee (_bafc ,_dbaf );};switch _feb .String (){case "\u0054\u0079\u0070e\u0031":return _ddd ;};return _ee (_bafc ,_dbaf );};func _ffeb (_ffgc *_gde .Document ,_fgg int )error {for _ ,_acb :=range _ffgc .Objects {_add ,_cbga :=_eb .GetDict (_acb );
if !_cbga {continue ;};_dff :=_add .Get ("\u0054\u0079\u0070\u0065");if _dff ==nil {continue ;};if _fbdc ,_dfd :=_eb .GetName (_dff );_dfd &&_fbdc .String ()!="\u0041\u0063\u0074\u0069\u006f\u006e"{continue ;};_egdf ,_bed :=_eb .GetName (_add .Get ("\u0053"));
if !_bed {continue ;};switch _a .PdfActionType (*_egdf ){case _a .ActionTypeLaunch ,_a .ActionTypeSound ,_a .ActionTypeMovie ,_a .ActionTypeResetForm ,_a .ActionTypeImportData ,_a .ActionTypeJavaScript :_add .Remove ("\u0053");case _a .ActionTypeHide ,_a .ActionTypeSetOCGState ,_a .ActionTypeRendition ,_a .ActionTypeTrans ,_a .ActionTypeGoTo3DView :if _fgg ==2{_add .Remove ("\u0053");
};case _a .ActionTypeNamed :_eab ,_cfb :=_eb .GetName (_add .Get ("\u004e"));if !_cfb {continue ;};switch *_eab {case "\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065","\u0050\u0072\u0065\u0076\u0050\u0061\u0067\u0065","\u0046i\u0072\u0073\u0074\u0050\u0061\u0067e","\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065":default:_add .Remove ("\u004e");
};};};return nil ;};func _dcbe (_ebg bool ,_fbe standardType )(pageColorspaceOptimizeFunc ,documentColorspaceOptimizeFunc ){var _ffgcd ,_dbe ,_egeb bool ;_agec :=func (_ddebc *_gde .Document ,_beg *_gde .Page ,_bae []*_gde .Image )error {_dbe =true ;for _ ,_accb :=range _bae {switch _accb .Colorspace {case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_dbe =true ;
case "\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_ffgcd =true ;case "\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_egeb =true ;};};_fec ,_gae :=_beg .GetContents ();if !_gae {return nil ;};for _ ,_fdd :=range _fec {_bff ,_edb :=_fdd .GetData ();
if _edb !=nil {continue ;};_dcfc :=_e .NewContentStreamParser (string (_bff ));_ffaa ,_edb :=_dcfc .Parse ();if _edb !=nil {continue ;};for _ ,_aef :=range *_ffaa {switch _aef .Operand {case "\u0047","\u0067":_dbe =true ;case "\u0052\u0047","\u0072\u0067":_ffgcd =true ;
case "\u004b","\u006b":_egeb =true ;case "\u0043\u0053","\u0063\u0073":if len (_aef .Params )==0{continue ;};_efef ,_fae :=_eb .GetName (_aef .Params [0]);if !_fae {continue ;};switch _efef .String (){case "\u0052\u0047\u0042","\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_ffgcd =true ;
case "\u0047","\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_dbe =true ;case "\u0043\u004d\u0059\u004b","\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_egeb =true ;};};};};_eaf :=_beg .FindXObjectForms ();for _ ,_bfg :=range _eaf {_ccf :=_e .NewContentStreamParser (string (_bfg .Stream ));
_aec ,_ffd :=_ccf .Parse ();if _ffd !=nil {continue ;};for _ ,_bceac :=range *_aec {switch _bceac .Operand {case "\u0047","\u0067":_dbe =true ;case "\u0052\u0047","\u0072\u0067":_ffgcd =true ;case "\u004b","\u006b":_egeb =true ;case "\u0043\u0053","\u0063\u0073":if len (_bceac .Params )==0{continue ;
};_cfcc ,_fbcd :=_eb .GetName (_bceac .Params [0]);if !_fbcd {continue ;};switch _cfcc .String (){case "\u0052\u0047\u0042","\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_ffgcd =true ;case "\u0047","\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_dbe =true ;
case "\u0043\u004d\u0059\u004b","\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_egeb =true ;};};};_fcba ,_bbec :=_eb .GetArray (_beg .Object .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));if !_bbec {return nil ;};for _ ,_bbd :=range _fcba .Elements (){_ebgc ,_bfgg :=_eb .GetDict (_bbd );
if !_bfgg {continue ;};_eee :=_ebgc .Get ("\u0043");if _eee ==nil {continue ;};_aaed ,_bfgg :=_eb .GetArray (_eee );if !_bfgg {continue ;};switch _aaed .Len (){case 0:case 1:_dbe =true ;case 3:_ffgcd =true ;case 4:_egeb =true ;};};};return nil ;};_efgf :=func (_ceg *_gde .Document ,_edfb []*_gde .Image )error {_dgff ,_bfdg :=_ceg .FindCatalog ();
if !_bfdg {return nil ;};_dfc ,_bfdg :=_dgff .GetOutputIntents ();if _bfdg &&_dfc .Len ()> 0{return nil ;};if !_bfdg {_dfc =_dgff .NewOutputIntents ();};if !(_ffgcd ||_egeb ||_dbe ){return nil ;};defer _dgff .SetOutputIntents (_dfc );if _ffgcd &&!_egeb &&!_dbe {return _acf (_ceg ,_fbe ,_dfc );
};if _egeb &&!_ffgcd &&!_dbe {return _afe (_fbe ,_dfc );};if _dbe &&!_ffgcd &&!_egeb {return _fea (_fbe ,_dfc );};if (_ffgcd &&_egeb )||(_ffgcd &&_dbe )||(_egeb &&_dbe ){if _geaf :=_db (_edfb ,_ebg );_geaf !=nil {return _geaf ;};if _cdad :=_fab (_ceg ,_ebg );
_cdad !=nil {return _cdad ;};if _fbcc :=_beaeg (_ceg ,_ebg );_fbcc !=nil {return _fbcc ;};if _bded :=_fdde (_ceg ,_ebg );_bded !=nil {return _bded ;};if _ebg {return _afe (_fbe ,_dfc );};return _acf (_ceg ,_fbe ,_dfc );};return nil ;};return _agec ,_efgf ;
};

// Part gets the PDF/A version level.
func (_ggee *profile3 )Part ()int {return _ggee ._fddb ._dda };func _afgb (_bbcba *_a .CompliancePdfReader )(_ddagg []ViolatedRule ){var _dabe ,_gbaab ,_eacg ,_afggg ,_cdba ,_fgagb ,_egccd bool ;_ccfdc :=func ()bool {return _dabe &&_gbaab &&_eacg &&_afggg &&_cdba &&_fgagb &&_egccd };
for _ ,_eebfd :=range _bbcba .PageList {_gcgg ,_fffb :=_eebfd .GetAnnotations ();if _fffb !=nil {_bf .Log .Trace ("\u006c\u006f\u0061\u0064\u0069\u006e\u0067\u0020\u0061\u006en\u006f\u0074\u0061\u0074\u0069\u006f\u006es\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_fffb );
continue ;};for _ ,_aaea :=range _gcgg {if !_dabe {switch _aaea .GetContext ().(type ){case *_a .PdfAnnotationScreen ,*_a .PdfAnnotation3D ,*_a .PdfAnnotationSound ,*_a .PdfAnnotationMovie ,nil :_ddagg =append (_ddagg ,_ee ("\u0036.\u0033\u002e\u0031\u002d\u0031","\u0041nn\u006f\u0074\u0061\u0074i\u006f\u006e t\u0079\u0070\u0065\u0073\u0020\u006e\u006f\u0074\u0020\u0064\u0065f\u0069\u006e\u0065\u0064\u0020i\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072e\u006e\u0063\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065r\u006d\u0069t\u0074\u0065\u0064\u002e\u0020\u0041\u0064d\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0033\u0044\u002c\u0020\u0053\u006f\u0075\u006e\u0064\u002c\u0020\u0053\u0063\u0072\u0065\u0065\u006e\u0020\u0061n\u0064\u0020\u004d\u006f\u0076\u0069\u0065\u0020\u0074\u0079\u0070\u0065\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_dabe =true ;if _ccfdc (){return _ddagg ;};};};_afefa ,_bcffd :=_eb .GetDict (_aaea .GetContainingPdfObject ());if !_bcffd {continue ;};_ ,_cecb :=_aaea .GetContext ().(*_a .PdfAnnotationPopup );if !_cecb &&!_gbaab {_ ,_daaaf :=_eb .GetIntVal (_afefa .Get ("\u0046"));
if !_daaaf {_ddagg =append (_ddagg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0031","\u0045\u0078\u0063\u0065\u0070\u0074\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069o\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072i\u0065\u0073\u0020\u0077\u0068\u006fs\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0020\u0076\u0061l\u0075\u0065\u0020\u0069\u0073\u0020\u0050\u006f\u0070u\u0070\u002c\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0046 \u006b\u0065y."));
_gbaab =true ;if _ccfdc (){return _ddagg ;};};};if !_eacg {_adfd ,_gebfd :=_eb .GetIntVal (_afefa .Get ("\u0046"));if _gebfd &&!(_adfd &4==4&&_adfd &1==0&&_adfd &2==0&&_adfd &32==0&&_adfd &256==0){_ddagg =append (_ddagg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0032","I\u0066\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u0020\u0046 \u006b\u0065\u0079\u0027\u0073\u0020\u0050\u0072\u0069\u006e\u0074\u0020\u0066\u006c\u0061\u0067\u0020\u0062\u0069\u0074\u0020\u0073\u0068\u0061l\u006c\u0020\u0062\u0065\u0020\u0073\u0065\u0074\u0020\u0074\u006f\u0020\u0031\u0020\u0061\u006e\u0064\u0020\u0069\u0074\u0073\u0020\u0048\u0069\u0064\u0064\u0065\u006e\u002c\u0020\u0049\u006e\u0076\u0069\u0073\u0069\u0062\u006c\u0065\u002c\u0020\u0054\u006f\u0067\u0067\u006c\u0065\u004e\u006f\u0056\u0069\u0065\u0077\u002c\u0020\u0061\u006e\u0064 \u004eo\u0056\u0069\u0065\u0077\u0020\u0066\u006c\u0061\u0067\u0020\u0062\u0069\u0074\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020s\u0065\u0074\u0020t\u006f\u0020\u0030."));
_eacg =true ;if _ccfdc (){return _ddagg ;};};};_ ,_fdddg :=_aaea .GetContext ().(*_a .PdfAnnotationText );if _fdddg &&!_afggg {_ecdf ,_eagaa :=_eb .GetIntVal (_afefa .Get ("\u0046"));if _eagaa &&!(_ecdf &8==8&&_ecdf &16==16){_ddagg =append (_ddagg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0033","\u0054\u0065\u0078\u0074\u0020a\u006e\u006e\u006f\u0074\u0061t\u0069o\u006e\u0020\u0068\u0061\u0073\u0020\u006f\u006e\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006ca\u0067\u0073\u0020\u004e\u006f\u005a\u006f\u006f\u006d\u0020\u006f\u0072\u0020\u004e\u006f\u0052\u006f\u0074\u0061\u0074\u0065\u0020\u0073\u0065t\u0020\u0074\u006f\u0020\u0030\u002e"));
_afggg =true ;if _ccfdc (){return _ddagg ;};};};if !_cdba {_eecb ,_daddd :=_eb .GetDict (_afefa .Get ("\u0041\u0050"));if _daddd {_fdgf :=_eecb .Get ("\u004e");if _fdgf ==nil ||len (_eecb .Keys ())> 1{_ddagg =append (_ddagg ,_ee ("\u0036.\u0033\u002e\u0033\u002d\u0032","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_cdba =true ;if _ccfdc (){return _ddagg ;};continue ;};_ ,_bfde :=_aaea .GetContext ().(*_a .PdfAnnotationWidget );if _bfde {_baab ,_ffaac :=_eb .GetName (_afefa .Get ("\u0046\u0054"));if _ffaac &&*_baab =="\u0042\u0074\u006e"{if _ ,_dgeg :=_eb .GetDict (_fdgf );
!_dgeg {_ddagg =append (_ddagg ,_ee ("\u0036.\u0033\u002e\u0033\u002d\u0032","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_cdba =true ;if _ccfdc (){return _ddagg ;};continue ;};};};_ ,_beef :=_eb .GetStream (_fdgf );if !_beef {_ddagg =append (_ddagg ,_ee ("\u0036.\u0033\u002e\u0033\u002d\u0032","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_cdba =true ;if _ccfdc (){return _ddagg ;};continue ;};};};_edgfe ,_bfacd :=_aaea .GetContext ().(*_a .PdfAnnotationWidget );if !_bfacd {continue ;};if !_fgagb {if _edgfe .A !=nil {_ddagg =append (_ddagg ,_ee ("\u0036.\u0034\u002e\u0031\u002d\u0031","A \u0057\u0069d\u0067\u0065\u0074\u0020\u0061\u006e\u006e\u006f\u0074a\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0069\u006ec\u006cu\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0020e\u006et\u0072\u0079."));
_fgagb =true ;if _ccfdc (){return _ddagg ;};};};if !_egccd {if _edgfe .AA !=nil {_ddagg =append (_ddagg ,_ee ("\u0036.\u0034\u002e\u0031\u002d\u0031","\u0041\u0020\u0057\u0069\u0064\u0067\u0065\u0074\u0020\u0061\u006e\u006eo\u0074\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020\u0073h\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0041\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0061d\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u002d\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
_egccd =true ;if _ccfdc (){return _ddagg ;};};};};};return _ddagg ;};func _efag (_gdfd *_eb .PdfObjectDictionary ,_edfcdg map[*_eb .PdfObjectStream ][]byte ,_bcga map[*_eb .PdfObjectStream ]*_ba .CMap )ViolatedRule {const (_dgeac ="\u0046\u006f\u0072\u0020\u0061\u006e\u0079\u0020\u0067\u0069\u0076\u0065\u006e\u0020\u0063\u006f\u006d\u0070o\u0073\u0069\u0074e\u0020\u0028\u0054\u0079\u0070\u0065\u0020\u0030\u0029 \u0066\u006fn\u0074\u0020\u0077\u0069\u0074\u0068\u0069\u006e \u0061\u0020\u0063\u006fn\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f \u0065\u006e\u0074\u0072\u0079\u0020\u0069\u006e\u0020\u0069\u0074\u0073 \u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u006e\u0064\u0020\u0069\u0074\u0073\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0068\u0061\u0076\u0065\u0020\u0074\u0068\u0065\u0020\u0066\u006fl\u006c\u006f\u0077\u0069\u006e\u0067\u0020\u0072\u0065l\u0061t\u0069\u006f\u006e\u0073\u0068\u0069\u0070. \u0049\u0066\u0020\u0074\u0068\u0065\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006b\u0065\u0079 \u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0054\u0079\u0070\u0065\u0020\u0030 \u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0069\u0073\u0020I\u0064\u0065n\u0074\u0069\u0074\u0079\u002d\u0048\u0020\u006f\u0072\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056\u002c\u0020\u0061\u006e\u0079\u0020v\u0061\u006c\u0075\u0065\u0073\u0020\u006f\u0066\u0020\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079\u002c\u0020\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067\u002c\u0020\u0061\u006e\u0064\u0020\u0053up\u0070\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u0069n\u0020\u0074h\u0065\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f\u0020\u0065\u006e\u0074r\u0079\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044F\u006f\u006e\u0074\u002e\u0020\u004f\u0074\u0068\u0065\u0072\u0077\u0069\u0073\u0065\u002c\u0020\u0074\u0068\u0065\u0020\u0063\u006f\u0072\u0072\u0065\u0073\u0070\u006f\u006e\u0064\u0069\u006e\u0067\u0020\u0052\u0065\u0067\u0069\u0073\u0074\u0072\u0079\u0020a\u006e\u0064\u0020\u004f\u0072\u0064\u0065\u0072\u0069\u006e\u0067\u0020s\u0074\u0072\u0069\u006e\u0067\u0073\u0020\u0069\u006e\u0020\u0062\u006f\u0074h\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065m\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0073\u0068\u0061\u006cl\u0020\u0062\u0065\u0020i\u0064en\u0074\u0069\u0063\u0061\u006c\u002c \u0061n\u0064\u0020\u0074\u0068\u0065\u0020v\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0053\u0075\u0070\u0070l\u0065\u006d\u0065\u006e\u0074 \u006b\u0065\u0079\u0020\u0069\u006e\u0020t\u0068\u0065\u0020\u0043I\u0044S\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066o\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0067re\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u0072\u0020\u0065\u0071\u0075\u0061\u006c\u0020\u0074\u006f t\u0068\u0065\u0020\u0053\u0075\u0070\u0070\u006c\u0065\u006d\u0065\u006e\u0074\u0020\u006b\u0065\u0079\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066o\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006ff\u0020\u0074\u0068\u0065\u0020\u0043M\u0061p\u002e";
_bgcbf ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0033\u002d\u0031";);var _dada string ;if _aege ,_gacf :=_eb .GetName (_gdfd .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_gacf {_dada =_aege .String ();};if _dada !="\u0054\u0079\u0070e\u0030"{return _ddd ;
};_cbbd :=_gdfd .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");if _fgagc ,_cbba :=_eb .GetName (_cbbd );_cbba {switch _fgagc .String (){case "\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048","\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056":return _ddd ;
};_fdfe ,_bfgc :=_ba .LoadPredefinedCMap (_fgagc .String ());if _bfgc !=nil {return _ee (_bgcbf ,_dgeac );};_efea :=_fdfe .CIDSystemInfo ();if _efea .Ordering !=_efea .Registry {return _ee (_bgcbf ,_dgeac );};return _ddd ;};_gdege ,_bacbf :=_eb .GetStream (_cbbd );
if !_bacbf {return _ee (_bgcbf ,_dgeac );};_aebfa ,_bccac :=_cafgb (_gdege ,_edfcdg ,_bcga );if _bccac !=nil {return _ee (_bgcbf ,_dgeac );};_ccacb :=_aebfa .CIDSystemInfo ();if _ccacb .Ordering !=_ccacb .Registry {return _ee (_bgcbf ,_dgeac );};return _ddd ;
};var _ Profile =(*Profile1A )(nil );type profile1 struct{_ecd standardType ;_bdbb Profile1Options ;};func _bga (_dacf *_gde .Document ,_caba []pageColorspaceOptimizeFunc ,_efg []documentColorspaceOptimizeFunc )error {_egfb ,_fbc :=_dacf .GetPages ();if !_fbc {return nil ;
};var _ddg []*_gde .Image ;for _adbff ,_beaed :=range _egfb {_aded ,_ab :=_beaed .FindXObjectImages ();if _ab !=nil {return _ab ;};for _ ,_dgbg :=range _caba {if _ab =_dgbg (_dacf ,&_egfb [_adbff ],_aded );_ab !=nil {return _ab ;};};_ddg =append (_ddg ,_aded ...);
};for _ ,_abf :=range _efg {if _cff :=_abf (_dacf ,_ddg );_cff !=nil {return _cff ;};};return nil ;};func _dged (_afff *_a .CompliancePdfReader )(_gddgb []ViolatedRule ){var _gccf ,_daaaaa ,_agdf ,_dgcca ,_added ,_egbd ,_gcca bool ;_agfed :=func ()bool {return _gccf &&_daaaaa &&_agdf &&_dgcca &&_added &&_egbd &&_gcca };
_ded :=func (_efcg *_eb .PdfObjectDictionary )bool {if !_gccf &&_efcg .Get ("\u0054\u0052")!=nil {_gccf =true ;_gddgb =append (_gddgb ,_ee ("\u0036.\u0032\u002e\u0035\u002d\u0031","\u0041\u006e\u0020\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0054\u0052\u0020\u006b\u0065\u0079\u002e"));
};if _cdgg :=_efcg .Get ("\u0054\u0052\u0032");!_daaaaa &&_cdgg !=nil {_fceda ,_gdac :=_eb .GetName (_cdgg );if !_gdac ||(_gdac &&*_fceda !="\u0044e\u0066\u0061\u0075\u006c\u0074"){_daaaaa =true ;_gddgb =append (_gddgb ,_ee ("\u0036.\u0032\u002e\u0035\u002d\u0032","\u0041\u006e \u0045\u0078\u0074G\u0053\u0074\u0061\u0074\u0065 \u0064\u0069\u0063\u0074\u0069on\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074a\u0069n\u0020\u0074\u0068\u0065\u0020\u0054R2 \u006b\u0065\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020\u0076al\u0075e\u0020\u006f\u0074\u0068e\u0072 \u0074h\u0061\u006e \u0044\u0065fa\u0075\u006c\u0074\u002e"));
if _agfed (){return true ;};};};if !_agdf &&_efcg .Get ("\u0048\u0054\u0050")!=nil {_agdf =true ;_gddgb =append (_gddgb ,_ee ("\u0036.\u0032\u002e\u0035\u002d\u0033","\u0041\u006e\u0020\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c \u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020th\u0065\u0020\u0048\u0054\u0050\u0020\u006b\u0065\u0079\u002e"));
};_ebce ,_eeec :=_eb .GetDict (_efcg .Get ("\u0048\u0054"));if _eeec {if _gbee :=_ebce .Get ("\u0048\u0061\u006cf\u0074\u006f\u006e\u0065\u0054\u0079\u0070\u0065");!_dgcca &&_gbee !=nil {_gffae ,_eadaf :=_eb .GetInt (_gbee );if !_eadaf ||(_eadaf &&!(*_gffae ==1||*_gffae ==5)){_gddgb =append (_gddgb ,_ee ("\u0020\u0036\u002e\u0032\u002e\u0035\u002d\u0034","\u0041\u006c\u006c\u0020\u0068\u0061\u006c\u0066\u0074\u006f\u006e\u0065\u0073\u0020\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0032\u0020\u0066\u0069\u006ce\u0020\u0073h\u0061\u006c\u006c\u0020h\u0061\u0076\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061l\u0075\u0065\u0020\u0031\u0020\u006f\u0072\u0020\u0035 \u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0048\u0061l\u0066\u0074\u006fn\u0065\u0054\u0079\u0070\u0065\u0020\u006be\u0079\u002e"));
if _agfed (){return true ;};};};if _eefg :=_ebce .Get ("\u0048\u0061\u006cf\u0074\u006f\u006e\u0065\u004e\u0061\u006d\u0065");!_added &&_eefg !=nil {_added =true ;_gddgb =append (_gddgb ,_ee ("\u0036.\u0032\u002e\u0035\u002d\u0035","\u0048\u0061\u006c\u0066\u0074o\u006e\u0065\u0073\u0020\u0069\u006e\u0020a\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0032\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020\u0061\u0020\u0048\u0061\u006c\u0066\u0074\u006f\u006e\u0065N\u0061\u006d\u0065\u0020\u006b\u0065y\u002e"));
if _agfed (){return true ;};};};_ ,_edfef :=_cabcg (_afff );var _aecdb bool ;_decg ,_eeec :=_eb .GetDict (_efcg .Get ("\u0047\u0072\u006fu\u0070"));if _eeec {_ ,_beeb :=_eb .GetName (_decg .Get ("\u0043\u0053"));if _beeb {_aecdb =true ;};};if _bffde :=_efcg .Get ("\u0042\u004d");
!_egbd &&!_gcca &&_bffde !=nil {_gbdfd ,_gddc :=_eb .GetName (_bffde );if _gddc {switch _gbdfd .String (){case "\u004e\u006f\u0072\u006d\u0061\u006c","\u0043\u006f\u006d\u0070\u0061\u0074\u0069\u0062\u006c\u0065","\u004d\u0075\u006c\u0074\u0069\u0070\u006c\u0079","\u0053\u0063\u0072\u0065\u0065\u006e","\u004fv\u0065\u0072\u006c\u0061\u0079","\u0044\u0061\u0072\u006b\u0065\u006e","\u004ci\u0067\u0068\u0074\u0065\u006e","\u0043\u006f\u006c\u006f\u0072\u0044\u006f\u0064\u0067\u0065","\u0043o\u006c\u006f\u0072\u0042\u0075\u0072n","\u0048a\u0072\u0064\u004c\u0069\u0067\u0068t","\u0053o\u0066\u0074\u004c\u0069\u0067\u0068t","\u0044\u0069\u0066\u0066\u0065\u0072\u0065\u006e\u0063\u0065","\u0045x\u0063\u006c\u0075\u0073\u0069\u006fn","\u0048\u0075\u0065","\u0053\u0061\u0074\u0075\u0072\u0061\u0074\u0069\u006f\u006e","\u0043\u006f\u006co\u0072","\u004c\u0075\u006d\u0069\u006e\u006f\u0073\u0069\u0074\u0079":default:_egbd =true ;
_gddgb =append (_gddgb ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0031","\u004f\u006el\u0079\u0020\u0062\u006c\u0065\u006e\u0064\u0020\u006d\u006f\u0064\u0065\u0073\u0020\u0074h\u0061\u0074\u0020\u0061\u0072\u0065\u0020\u0073\u0070\u0065c\u0069\u0066\u0069ed\u0020\u0069\u006e\u0020\u0049\u0053O\u0020\u0033\u0032\u0030\u0030\u0030\u002d\u0031\u003a2\u0030\u0030\u0038\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065 \u0076\u0061\u006c\u0075e\u0020\u006f\u0066\u0020\u0074\u0068e\u0020\u0042M\u0020\u006b\u0065\u0079\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0065\u0078t\u0065\u006e\u0064\u0065\u0064\u0020\u0067\u0072\u0061\u0070\u0068\u0069\u0063\u0020\u0073\u0074\u0061\u0074\u0065 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
if _agfed (){return true ;};};if _gbdfd .String ()!="\u004e\u006f\u0072\u006d\u0061\u006c"&&!_edfef &&!_aecdb {_gcca =true ;_gddgb =append (_gddgb ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0032","\u0049\u0066\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020P\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0050\u0061\u0067\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0073\u0020\u0074\u0068a\u0074 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063l\u0075\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006b\u0065y\u002c a\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0061\u0074\u0020\u0047\u0072\u006fu\u0070\u0020\u006b\u0065y\u0020sh\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075d\u0065\u0020\u0061\u0020\u0043\u0053\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0077\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065 \u0075\u0073\u0065\u0064\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u0020\u0062\u006c\u0065\u006e\u0064\u0069n\u0067 \u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u002e"));
if _agfed (){return true ;};};};};if _ ,_eeec =_eb .GetDict (_efcg .Get ("\u0053\u004d\u0061s\u006b"));!_gcca &&_eeec &&!_edfef &&!_aecdb {_gcca =true ;_gddgb =append (_gddgb ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0032","\u0049\u0066\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020P\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0050\u0061\u0067\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0073\u0020\u0074\u0068a\u0074 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063l\u0075\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006b\u0065y\u002c a\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0061\u0074\u0020\u0047\u0072\u006fu\u0070\u0020\u006b\u0065y\u0020sh\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075d\u0065\u0020\u0061\u0020\u0043\u0053\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0077\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065 \u0075\u0073\u0065\u0064\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u0020\u0062\u006c\u0065\u006e\u0064\u0069n\u0067 \u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u002e"));
if _agfed (){return true ;};};if _ccbb :=_efcg .Get ("\u0043\u0041");!_gcca &&_ccbb !=nil &&!_edfef &&!_aecdb {_debc ,_cedac :=_eb .GetNumberAsFloat (_ccbb );if _cedac ==nil &&_debc < 1.0{_gcca =true ;_gddgb =append (_gddgb ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0032","\u0049\u0066\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020P\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0050\u0061\u0067\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0073\u0020\u0074\u0068a\u0074 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063l\u0075\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006b\u0065y\u002c a\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0061\u0074\u0020\u0047\u0072\u006fu\u0070\u0020\u006b\u0065y\u0020sh\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075d\u0065\u0020\u0061\u0020\u0043\u0053\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0077\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065 \u0075\u0073\u0065\u0064\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u0020\u0062\u006c\u0065\u006e\u0064\u0069n\u0067 \u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u002e"));
if _agfed (){return true ;};};};if _fgfeb :=_efcg .Get ("\u0063\u0061");!_gcca &&_fgfeb !=nil &&!_edfef &&!_aecdb {_edfgg ,_egec :=_eb .GetNumberAsFloat (_fgfeb );if _egec ==nil &&_edfgg < 1.0{_gcca =true ;_gddgb =append (_gddgb ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0032","\u0049\u0066\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0064\u006f\u0065\u0073\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020P\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0050\u0061\u0067\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0073\u0020\u0074\u0068a\u0074 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063l\u0075\u0064\u0065\u0020\u0074\u0068\u0065\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006b\u0065y\u002c a\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0061\u0074\u0074\u0072\u0069\u0062\u0075\u0074\u0065\u0020\u0064\u0069c\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0061\u0074\u0020\u0047\u0072\u006fu\u0070\u0020\u006b\u0065y\u0020sh\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075d\u0065\u0020\u0061\u0020\u0043\u0053\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0077\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065 \u0075\u0073\u0065\u0064\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0064\u0065\u0066\u0061\u0075\u006c\u0074\u0020\u0062\u006c\u0065\u006e\u0064\u0069n\u0067 \u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u002e"));
if _agfed (){return true ;};};};return false ;};for _ ,_bbgac :=range _afff .PageList {_ffgcg :=_bbgac .Resources ;if _ffgcg ==nil {continue ;};if _ffgcg .ExtGState ==nil {continue ;};_cabg ,_facda :=_eb .GetDict (_ffgcg .ExtGState );if !_facda {continue ;
};_caag :=_cabg .Keys ();for _ ,_ffgcf :=range _caag {_cbcfa ,_facad :=_eb .GetDict (_cabg .Get (_ffgcf ));if !_facad {continue ;};if _ded (_cbcfa ){return _gddgb ;};};};for _ ,_fgae :=range _afff .PageList {_daef :=_fgae .Resources ;if _daef ==nil {continue ;
};_dcedc ,_egbf :=_eb .GetDict (_daef .XObject );if !_egbf {continue ;};for _ ,_daff :=range _dcedc .Keys (){_gcfe ,_bcff :=_eb .GetStream (_dcedc .Get (_daff ));if !_bcff {continue ;};_fdfde ,_bcff :=_eb .GetDict (_gcfe .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_bcff {continue ;};_aafg ,_bcff :=_eb .GetDict (_fdfde .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if !_bcff {continue ;};for _ ,_bgdcg :=range _aafg .Keys (){_adfb ,_abac :=_eb .GetDict (_aafg .Get (_bgdcg ));if !_abac {continue ;};if _ded (_adfb ){return _gddgb ;
};};};};return _gddgb ;};func _ebda (_egga *_gde .Document ,_fcg *_gde .Page ,_beac []*_gde .Image )error {for _ ,_dfa :=range _beac {if _dfa .SMask ==nil {continue ;};_gfb ,_ecc :=_a .NewXObjectImageFromStream (_dfa .Stream );if _ecc !=nil {return _ecc ;
};_fdec ,_ecc :=_gfb .ToImage ();if _ecc !=nil {return _ecc ;};_gcf ,_ecc :=_fdec .ToGoImage ();if _ecc !=nil {return _ecc ;};_aecg ,_ecc :=_ec .RGBAConverter .Convert (_gcf );if _ecc !=nil {return _ecc ;};_aeb :=_aecg .Base ();_dcdg :=&_a .Image {Width :int64 (_aeb .Width ),Height :int64 (_aeb .Height ),BitsPerComponent :int64 (_aeb .BitsPerComponent ),ColorComponents :_aeb .ColorComponents ,Data :_aeb .Data };
_dcdg .SetDecode (_aeb .Decode );_dcdg .SetAlpha (_aeb .Alpha );if _ecc =_gfb .SetImage (_dcdg ,nil );_ecc !=nil {return _ecc ;};_gfb .SMask =_eb .MakeNull ();var _aafdf _eb .PdfObject ;_cgc :=-1;for _cgc ,_aafdf =range _egga .Objects {if _aafdf ==_dfa .SMask .Stream {break ;
};};if _cgc !=-1{_egga .Objects =append (_egga .Objects [:_cgc ],_egga .Objects [_cgc +1:]...);};_dfa .SMask =nil ;_gfb .ToPdfObject ();};return nil ;};type colorspaceModification struct{_fgf _ec .ColorConverter ;_fd _a .PdfColorspace ;};

// DefaultProfile1Options are the default options for the Profile1.
func DefaultProfile1Options ()*Profile1Options {return &Profile1Options {Now :_ca .Now ,Xmp :XmpOptions {MarshalIndent :"\u0009"}};};func _dc ()standardType {return standardType {_dda :2,_ff :"\u0041"}};

// NewProfile3B creates a new Profile3B with the given options.
func NewProfile3B (options *Profile3Options )*Profile3B {if options ==nil {options =DefaultProfile3Options ();};_cceg (options );return &Profile3B {profile3 {_afba :*options ,_fddb :_ge ()}};};

// NewProfile2A creates a new Profile2A with given options.
func NewProfile2A (options *Profile2Options )*Profile2A {if options ==nil {options =DefaultProfile2Options ();};_gcbb (options );return &Profile2A {profile2 {_efc :*options ,_fdecd :_dc ()}};};func _acf (_aad *_gde .Document ,_abd standardType ,_baed *_gde .OutputIntents )error {var (_dbda *_a .PdfOutputIntent ;
_ccb error ;);if _aad .Version .Minor <=7{_dbda ,_ccb =_bea .NewSRGBv2OutputIntent (_abd .outputIntentSubtype ());}else {_dbda ,_ccb =_bea .NewSRGBv4OutputIntent (_abd .outputIntentSubtype ());};if _ccb !=nil {return _ccb ;};if _ccb =_baed .Add (_dbda .ToPdfObject ());
_ccb !=nil {return _ccb ;};return nil ;};

// Profile3U is the implementation of the PDF/A-3U standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile3U struct{profile3 };func _begc (_bddb *_gde .Document )error {_baedg :=func (_fff *_eb .PdfObjectDictionary )error {if _fff .Get ("\u0054\u0052")!=nil {_bf .Log .Debug ("\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0054\u0052\u0020\u006b\u0065\u0079");
_fff .Remove ("\u0054\u0052");};_egdgb :=_fff .Get ("\u0054\u0052\u0032");if _egdgb !=nil {_ccgg :=_egdgb .String ();if _ccgg !="\u0044e\u0066\u0061\u0075\u006c\u0074"{_bf .Log .Debug ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074\u0065 o\u0062\u006a\u0065\u0063\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073 \u0054\u00522\u0020\u006b\u0065y\u0020\u0077\u0069\u0074\u0068\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0074\u0068\u0065r\u0020\u0074ha\u006e\u0020\u0044e\u0066\u0061\u0075\u006c\u0074");
_fff .Set ("\u0054\u0052\u0032",_eb .MakeName ("\u0044e\u0066\u0061\u0075\u006c\u0074"));};};if _fff .Get ("\u0048\u0054\u0050")!=nil {_bf .Log .Debug ("\u0045\u0078\u0074\u0047\u0053\u0074a\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0073\u0020\u0048\u0054P\u0020\u006b\u0065\u0079");
_fff .Remove ("\u0048\u0054\u0050");};_cge :=_fff .Get ("\u0042\u004d");if _cge !=nil {_cege ,_bfec :=_eb .GetName (_cge );if !_bfec {_bf .Log .Debug ("E\u0078\u0074\u0047\u0053\u0074\u0061t\u0065\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u0027\u0042\u004d\u0027\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u004e\u0061m\u0065");
_cege =_eb .MakeName ("");};_ffec :=_cege .String ();switch _ffec {case "\u004e\u006f\u0072\u006d\u0061\u006c","\u0043\u006f\u006d\u0070\u0061\u0074\u0069\u0062\u006c\u0065","\u004d\u0075\u006c\u0074\u0069\u0070\u006c\u0079","\u0053\u0063\u0072\u0065\u0065\u006e","\u004fv\u0065\u0072\u006c\u0061\u0079","\u0044\u0061\u0072\u006b\u0065\u006e","\u004ci\u0067\u0068\u0074\u0065\u006e","\u0043\u006f\u006c\u006f\u0072\u0044\u006f\u0064\u0067\u0065","\u0043o\u006c\u006f\u0072\u0042\u0075\u0072n","\u0048a\u0072\u0064\u004c\u0069\u0067\u0068t","\u0053o\u0066\u0074\u004c\u0069\u0067\u0068t","\u0044\u0069\u0066\u0066\u0065\u0072\u0065\u006e\u0063\u0065","\u0045x\u0063\u006c\u0075\u0073\u0069\u006fn","\u0048\u0075\u0065","\u0053\u0061\u0074\u0075\u0072\u0061\u0074\u0069\u006f\u006e","\u0043\u006f\u006co\u0072","\u004c\u0075\u006d\u0069\u006e\u006f\u0073\u0069\u0074\u0079":default:_fff .Set ("\u0042\u004d",_eb .MakeName ("\u004e\u006f\u0072\u006d\u0061\u006c"));
};};return nil ;};_gbe ,_efgc :=_bddb .GetPages ();if !_efgc {return nil ;};for _ ,_gabc :=range _gbe {_eed ,_gebd :=_gabc .GetResources ();if !_gebd {continue ;};_cbaf ,_gbc :=_eb .GetDict (_eed .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if !_gbc {return nil ;
};_cbc :=_cbaf .Keys ();for _ ,_ddag :=range _cbc {_cacg ,_cdfe :=_eb .GetDict (_cbaf .Get (_ddag ));if !_cdfe {continue ;};_afbg :=_baedg (_cacg );if _afbg !=nil {continue ;};};};for _ ,_aeba :=range _gbe {_dacb ,_afed :=_aeba .GetContents ();if !_afed {return nil ;
};for _ ,_adde :=range _dacb {_bbcc ,_addf :=_adde .GetData ();if _addf !=nil {continue ;};_daab :=_e .NewContentStreamParser (string (_bbcc ));_fgcd ,_addf :=_daab .Parse ();if _addf !=nil {continue ;};for _ ,_fbbb :=range *_fgcd {if len (_fbbb .Params )==0{continue ;
};_ ,_ffcb :=_eb .GetName (_fbbb .Params [0]);if !_ffcb {continue ;};_fecc ,_bffe :=_aeba .GetResourcesXObject ();if !_bffe {continue ;};for _ ,_dadg :=range _fecc .Keys (){_gfd ,_ecf :=_eb .GetStream (_fecc .Get (_dadg ));if !_ecf {continue ;};_efba ,_ecf :=_eb .GetDict (_gfd .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_ecf {continue ;};_egebd ,_ecf :=_eb .GetDict (_efba .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if !_ecf {continue ;};for _ ,_eada :=range _egebd .Keys (){_acg ,_ecce :=_eb .GetDict (_egebd .Get (_eada ));if !_ecce {continue ;};_ebfa :=_baedg (_acg );
if _ebfa !=nil {continue ;};};};};};};return nil ;};func _ged (_dfbf *_gde .Document )(*_eb .PdfObjectDictionary ,bool ){_fcf ,_bfe :=_dfbf .FindCatalog ();if !_bfe {return nil ,false ;};_dcag ,_bfe :=_eb .GetArray (_fcf .Object .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073"));
if !_bfe {return nil ,false ;};if _dcag .Len ()==0{return nil ,false ;};return _eb .GetDict (_dcag .Get (0));};func _gcbb (_addd *Profile2Options ){if _addd .Now ==nil {_addd .Now =_ca .Now ;};};

// DefaultProfile3Options the default options for the Profile3.
func DefaultProfile3Options ()*Profile3Options {return &Profile3Options {Now :_ca .Now ,Xmp :XmpOptions {MarshalIndent :"\u0009"}};};func _gdbb (_cdga *_a .CompliancePdfReader ,_deee standardType )(_edbf []ViolatedRule ){var _cfdfc ,_fafef ,_ddbe ,_ddbb ,_effbb ,_ddfe ,_cfbd ,_daca ,_eebda ,_ddee ,_dgaf bool ;
_agacd :=func ()bool {return _cfdfc &&_fafef &&_ddbe &&_ddbb &&_effbb &&_ddfe &&_cfbd &&_daca &&_eebda &&_ddee &&_dgaf ;};_efbe :=map[*_eb .PdfObjectStream ]*_ba .CMap {};_agbfc :=map[*_eb .PdfObjectStream ][]byte {};_efgcc :=map[_eb .PdfObject ]*_a .PdfFont {};
for _ ,_gfdf :=range _cdga .GetObjectNums (){_baaf ,_aegf :=_cdga .GetIndirectObjectByNumber (_gfdf );if _aegf !=nil {continue ;};_bfda ,_afge :=_eb .GetDict (_baaf );if !_afge {continue ;};_bcad ,_afge :=_eb .GetName (_bfda .Get ("\u0054\u0079\u0070\u0065"));
if !_afge {continue ;};if *_bcad !="\u0046\u006f\u006e\u0074"{continue ;};_dcce ,_aegf :=_a .NewPdfFontFromPdfObject (_bfda );if _aegf !=nil {_bf .Log .Debug ("g\u0065\u0074\u0074\u0069\u006e\u0067 \u0066\u006f\u006e\u0074\u0020\u0066r\u006f\u006d\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020%\u0076",_aegf );
continue ;};_efgcc [_bfda ]=_dcce ;};for _ ,_dbeg :=range _cdga .PageList {_fgbg ,_gafe :=_dbeg .GetContentStreams ();if _gafe !=nil {_bf .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067 \u0070\u0061\u0067\u0065\u0020\u0063o\u006e\u0074\u0065\u006e\u0074\u0020\u0073t\u0072\u0065\u0061\u006d\u0073\u0020\u0066\u0061\u0069\u006ce\u0064");
continue ;};for _ ,_deea :=range _fgbg {_ebgg :=_e .NewContentStreamParser (_deea );_cebff ,_faeeg :=_ebgg .Parse ();if _faeeg !=nil {_bf .Log .Debug ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074s\u0074r\u0065\u0061\u006d\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_faeeg );
continue ;};var _cbca bool ;for _ ,_gfcdd :=range *_cebff {if _gfcdd .Operand !="\u0054\u0072"{continue ;};if len (_gfcdd .Params )!=1{_bf .Log .Debug ("\u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0070\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0027\u0054\u0072\u0027\u0020\u006f\u0070\u0065\u0072\u0061\u006e\u0064\u002c\u0020\u0065\u0078\u0070e\u0063\u0074\u0065\u0064\u0020\u0027\u0031\u0027\u0020\u0062\u0075\u0074 \u0069\u0073\u003a\u0020\u0027\u0025d\u0027",len (_gfcdd .Params ));
continue ;};_bdaag ,_gccc :=_eb .GetIntVal (_gfcdd .Params [0]);if !_gccc {_bf .Log .Debug ("\u0072\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u006d\u006f\u0064\u0065\u0020i\u0073 \u006e\u006f\u0074\u0020\u0061\u006e\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072");
continue ;};if _bdaag ==3{_cbca =true ;break ;};};for _ ,_cdaf :=range *_cebff {if _cdaf .Operand !="\u0054\u0066"{continue ;};if len (_cdaf .Params )!=2{_bf .Log .Debug ("i\u006eva\u006ci\u0064 \u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066 \u0070\u0061\u0072\u0061\u006de\u0074\u0065\u0072s\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0027\u0054f\u0027\u0020\u006fper\u0061\u006e\u0064\u002c\u0020\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0027\u0032\u0027\u0020\u0069s\u003a \u0027\u0025\u0064\u0027",len (_cdaf .Params ));
continue ;};_ggdg ,_edcf :=_eb .GetName (_cdaf .Params [0]);if !_edcf {_bf .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0054\u0066\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074\u004ea\u006d\u0065\u0056\u0061\u006c\u0020\u0066a\u0069\u006c\u0065\u0064",_cdaf );
continue ;};_aeccg ,_ddfcd :=_dbeg .Resources .GetFontByName (*_ggdg );if !_ddfcd {_bf .Log .Debug ("\u0066\u006f\u006e\u0074\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");continue ;};_edef ,_edcf :=_eb .GetDict (_aeccg );if !_edcf {_bf .Log .Debug ("\u0066\u006f\u006e\u0074 d\u0069\u0063\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
continue ;};_ecec ,_edcf :=_efgcc [_edef ];if !_edcf {var _egcf error ;_ecec ,_egcf =_a .NewPdfFontFromPdfObject (_edef );if _egcf !=nil {_bf .Log .Debug ("\u0067\u0065\u0074\u0074i\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u0020\u0066\u0072o\u006d \u006f\u0062\u006a\u0065\u0063\u0074\u003a \u0025\u0076",_egcf );
continue ;};_efgcc [_edef ]=_ecec ;};if !_cfdfc {_acdb :=_beda (_edef ,_agbfc ,_efbe );if _acdb !=_ddd {_edbf =append (_edbf ,_acdb );_cfdfc =true ;if _agacd (){return _edbf ;};};};if !_fafef {_fabg :=_cbcac (_edef );if _fabg !=_ddd {_edbf =append (_edbf ,_fabg );
_fafef =true ;if _agacd (){return _edbf ;};};};if !_ddbe {_gaec :=_baeb (_edef ,_agbfc ,_efbe );if _gaec !=_ddd {_edbf =append (_edbf ,_gaec );_ddbe =true ;if _agacd (){return _edbf ;};};};if !_ddbb {_bdebg :=_bfbe (_edef ,_agbfc ,_efbe );if _bdebg !=_ddd {_edbf =append (_edbf ,_bdebg );
_ddbb =true ;if _agacd (){return _edbf ;};};};if !_effbb {_aebb :=_bafg (_ecec ,_edef ,_cbca );if _aebb !=_ddd {_effbb =true ;_edbf =append (_edbf ,_aebb );if _agacd (){return _edbf ;};};};if !_ddfe {_abdd :=_cbfcb (_ecec ,_edef );if _abdd !=_ddd {_ddfe =true ;
_edbf =append (_edbf ,_abdd );if _agacd (){return _edbf ;};};};if !_cfbd {_dbcb :=_afea (_ecec ,_edef );if _dbcb !=_ddd {_cfbd =true ;_edbf =append (_edbf ,_dbcb );if _agacd (){return _edbf ;};};};if !_daca {_gbgbg :=_cabf (_ecec ,_edef );if _gbgbg !=_ddd {_daca =true ;
_edbf =append (_edbf ,_gbgbg );if _agacd (){return _edbf ;};};};if !_eebda {_fgbdc :=_bbbeg (_ecec ,_edef );if _fgbdc !=_ddd {_eebda =true ;_edbf =append (_edbf ,_fgbdc );if _agacd (){return _edbf ;};};};if !_ddee {_gdbg :=_facc (_ecec ,_edef );if _gdbg !=_ddd {_ddee =true ;
_edbf =append (_edbf ,_gdbg );if _agacd (){return _edbf ;};};};if !_dgaf &&_deee ._ff =="\u0041"{_agde :=_dfcg (_edef ,_agbfc ,_efbe );if _agde !=_ddd {_dgaf =true ;_edbf =append (_edbf ,_agde );if _agacd (){return _edbf ;};};};};};};return _edbf ;};func _bgbcg (_baaa *_a .CompliancePdfReader )ViolatedRule {_bfge ,_fadg :=_baaa .GetTrailer ();
if _fadg !=nil {_bf .Log .Debug ("\u0043\u0061\u006en\u006f\u0074\u0020\u0067e\u0074\u0020\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0074\u0072\u0061\u0069\u006c\u0065\u0072\u003a\u0020\u0025\u0076",_fadg );return _ddd ;};_gefe ,_fccc :=_bfge .Get ("\u0052\u006f\u006f\u0074").(*_eb .PdfObjectReference );
if !_fccc {_bf .Log .Debug ("\u0043a\u006e\u006e\u006f\u0074 \u0066\u0069\u006e\u0064\u0020d\u006fc\u0075m\u0065\u006e\u0074\u0020\u0072\u006f\u006ft");return _ddd ;};_egee ,_fccc :=_eb .GetDict (_eb .ResolveReference (_gefe ));if !_fccc {_bf .Log .Debug ("\u0063\u0061\u006e\u006e\u006f\u0074 \u0072\u0065\u0073\u006f\u006c\u0076\u0065\u0020\u0063\u0061\u0074\u0061\u006co\u0067\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079");
return _ddd ;};if _egee .Get ("\u004f\u0043\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073")!=nil {return _ee ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0031","\u0054\u0068\u0065\u0020\u0064\u006f\u0063u\u006d\u0065\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020s\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020\u006b\u0065\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0074\u0068\u0065\u0020\u006e\u0061\u006d\u0065\u0020\u004f\u0043\u0050\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073");
};return _ddd ;};

// Error implements error interface.
func (_bc VerificationError )Error ()string {_bfc :=_bb .Builder {};_bfc .WriteString ("\u0053\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u003a\u0020");_bfc .WriteString (_d .Sprintf ("\u0050\u0044\u0046\u002f\u0041\u002d\u0025\u0064\u0025\u0073",_bc .ConformanceLevel ,_bc .ConformanceVariant ));
_bfc .WriteString ("\u0020\u0056\u0069\u006f\u006c\u0061\u0074\u0065\u0064\u0020\u0072\u0075l\u0065\u0073\u003a\u0020");for _ed ,_cd :=range _bc .ViolatedRules {_bfc .WriteString (_cd .String ());if _ed !=len (_bc .ViolatedRules )-1{_bfc .WriteRune ('\n');
};};return _bfc .String ();};func _egeec (_bgef *_a .CompliancePdfReader )(_cafg ViolatedRule ){_aadf ,_efcfb :=_efcc (_bgef );if !_efcfb {return _ddd ;};if _aadf .Get ("\u0041\u0041")!=nil {return _ee ("\u0036.\u0036\u002e\u0032\u002d\u0033","\u0054\u0068e\u0020\u0064\u006f\u0063\u0075\u006d\u0065n\u0074 \u0063\u0061\u0074a\u006c\u006f\u0067\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0041\u0020\u0065n\u0074r\u0079 \u0066\u006f\u0072 \u0061\u006e\u0020\u0061\u0064\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u002d\u0061\u0063\u0074i\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e");
};return _ddd ;};

// Profile3B is the implementation of the PDF/A-3B standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile3B struct{profile3 };func _dagec (_eggbc *_a .CompliancePdfReader )(_bbge []ViolatedRule ){_ecae :=_eggbc .GetObjectNums ();for _ ,_aged :=range _ecae {_cfgdf ,_cbce :=_eggbc .GetIndirectObjectByNumber (_aged );if _cbce !=nil {continue ;};_edce ,_agcd :=_eb .GetDict (_cfgdf );
if !_agcd {continue ;};_bged ,_agcd :=_eb .GetName (_edce .Get ("\u0054\u0079\u0070\u0065"));if !_agcd {continue ;};if _bged .String ()!="\u0046\u0069\u006c\u0065\u0073\u0070\u0065\u0063"{continue ;};_gcdd ,_cbce :=_a .NewPdfFilespecFromObj (_edce );if _cbce !=nil {continue ;
};if _gcdd .EF !=nil {if _gcdd .F ==nil ||_gcdd .UF ==nil {_bbge =append (_bbge ,_ee ("\u0036\u002e\u0038-\u0032","\u0054h\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0070\u0065\u0063\u0069\u0066i\u0063\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063t\u0069\u006fn\u0061\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020t\u0068\u0065\u0020\u0046\u0020a\u006e\u0064\u0020\u0055\u0046\u0020\u006b\u0065\u0079\u0073\u002e"));
break ;};if _gcdd .AFRelationship ==nil {_bbge =append (_bbge ,_ee ("\u0036\u002e\u0038-\u0033","\u0049\u006e\u0020\u006f\u0072d\u0065\u0072\u0020\u0074\u006f\u0020\u0065\u006e\u0061\u0062\u006c\u0065\u0020i\u0064\u0065nt\u0069\u0066\u0069c\u0061\u0074\u0069o\u006e\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0020\u0062\u0065\u0074\u0077\u0065\u0065\u006e\u0020\u0074\u0068\u0065\u0020fi\u006ce\u0020\u0073\u0070\u0065\u0063\u0069f\u0069c\u0061\u0074\u0069o\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020c\u006f\u006e\u0074e\u006e\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0073\u0020\u0072\u0065\u0066\u0065\u0072\u0072\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0069\u0074\u002c\u0020\u0061\u0020\u006e\u0065\u0077\u0020(\u0072\u0065\u0071\u0075i\u0072\u0065\u0064\u0029\u0020\u006be\u0079\u0020h\u0061\u0073\u0020\u0062e\u0065\u006e\u0020\u0064\u0065\u0066i\u006e\u0065\u0064\u0020a\u006e\u0064\u0020\u0069\u0074s \u0070\u0072e\u0073\u0065n\u0063\u0065\u0020\u0028\u0069\u006e\u0020\u0074\u0068e\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079\u0029\u0020\u0069\u0073\u0020\u0072\u0065q\u0075\u0069\u0072e\u0064\u002e"));
break ;};_fbcb ,_dgfcc :=_a .NewEmbeddedFileFromObject (_gcdd .EF );if _dgfcc !=nil {continue ;};if _bb .ToLower (_fbcb .FileType )!="\u0061p\u0070l\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u0070\u0064\u0066"{_bbge =append (_bbge ,_ee ("\u0036\u002e\u0038-\u0034","\u0041\u006c\u006c\u0020\u0065\u006d\u0062\u0065\u0064d\u0065\u0064 \u0066\u0069\u006c\u0065\u0073\u0020\u0073\u0068\u006fu\u006c\u0064\u0020\u0062e\u0020\u0061\u0020\u0050\u0044\u0046\u0020\u0066\u0069\u006c\u0065\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0020\u0074\u006f\u0020\u0050\u0044F\u002f\u0041\u002d1\u0020\u006f\u0072\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0032\u002e"));
break ;};};};return _bbge ;};

// Conformance gets the PDF/A conformance.
func (_dddfb *profile1 )Conformance ()string {return _dddfb ._ecd ._ff };func _deae (_adbde *_a .CompliancePdfReader )ViolatedRule {_defcf :=map[*_eb .PdfObjectStream ]struct{}{};for _ ,_egdb :=range _adbde .PageList {if _egdb .Resources ==nil &&_egdb .Contents ==nil {continue ;
};if _dafcc :=_egdb .GetPageDict ();_dafcc !=nil {_fcda ,_bfff :=_eb .GetDict (_dafcc .Get ("\u0047\u0072\u006fu\u0070"));if _bfff {if _aefced :=_fcda .Get ("\u0053");_aefced !=nil {_gffcf ,_bebgb :=_eb .GetName (_aefced );if _bebgb &&_gffcf .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{return _ee ("\u0036\u002e\u0034-\u0033","\u0041\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020\u0053\u0020\u0078Ob\u006a\u0065c\u0074\u0020\u0077\u0069\u0074h\u0020\u0061\u0020\u0076a\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0066\u006f\u0072\u006d\u0020\u0058\u004f\u0062je\u0063\u0074\u002e\n\u0041 \u0047\u0072\u006f\u0075p\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020S\u0020\u0078\u004fb\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020v\u0061\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006ec\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064e\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0070\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e");
};};};};if _egdb .Resources !=nil {if _bcdde ,_gagb :=_eb .GetDict (_egdb .Resources .XObject );_gagb {for _ ,_agbb :=range _bcdde .Keys (){_gagd ,_acfac :=_eb .GetStream (_bcdde .Get (_agbb ));if !_acfac {continue ;};if _ ,_cfdc :=_defcf [_gagd ];_cfdc {continue ;
};_efggf ,_acfac :=_eb .GetDict (_gagd .Get ("\u0047\u0072\u006fu\u0070"));if !_acfac {_defcf [_gagd ]=struct{}{};continue ;};_bacc :=_efggf .Get ("\u0053");if _bacc !=nil {_afcc ,_cgbf :=_eb .GetName (_bacc );if _cgbf &&_afcc .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{return _ee ("\u0036\u002e\u0034-\u0033","\u0041\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020\u0053\u0020\u0078Ob\u006a\u0065c\u0074\u0020\u0077\u0069\u0074h\u0020\u0061\u0020\u0076a\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0066\u006f\u0072\u006d\u0020\u0058\u004f\u0062je\u0063\u0074\u002e\n\u0041 \u0047\u0072\u006f\u0075p\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020S\u0020\u0078\u004fb\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020v\u0061\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006ec\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064e\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0070\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e");
};};_defcf [_gagd ]=struct{}{};continue ;};};};if _egdb .Contents !=nil {_acagc ,_abae :=_egdb .GetContentStreams ();if _abae !=nil {continue ;};for _ ,_edfd :=range _acagc {_aacdd ,_dcaga :=_e .NewContentStreamParser (_edfd ).Parse ();if _dcaga !=nil {continue ;
};for _ ,_aacfb :=range *_aacdd {if len (_aacfb .Params )==0{continue ;};_cbddd ,_abaf :=_eb .GetName (_aacfb .Params [0]);if !_abaf {continue ;};_deef ,_aedc :=_egdb .Resources .GetXObjectByName (*_cbddd );if _aedc !=_a .XObjectTypeForm {continue ;};if _ ,_bfdd :=_defcf [_deef ];
_bfdd {continue ;};_gfcdf ,_abaf :=_eb .GetDict (_deef .Get ("\u0047\u0072\u006fu\u0070"));if !_abaf {_defcf [_deef ]=struct{}{};continue ;};_babdd :=_gfcdf .Get ("\u0053");if _babdd !=nil {_fafc ,_fffc :=_eb .GetName (_babdd );if _fffc &&_fafc .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{return _ee ("\u0036\u002e\u0034-\u0033","\u0041\u0020\u0047\u0072\u006f\u0075\u0070\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020\u0053\u0020\u0078Ob\u006a\u0065c\u0074\u0020\u0077\u0069\u0074h\u0020\u0061\u0020\u0076a\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061\u006e\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0066\u006f\u0072\u006d\u0020\u0058\u004f\u0062je\u0063\u0074\u002e\n\u0041 \u0047\u0072\u006f\u0075p\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u006e\u0020S\u0020\u0078\u004fb\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020v\u0061\u006c\u0075\u0065\u0020o\u0066\u0020\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006ec\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020i\u006e\u0063\u006c\u0075\u0064e\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0070\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e");
};};_defcf [_deef ]=struct{}{};};};};};return _ddd ;};

// StandardName gets the name of the standard.
func (_ccbc *profile2 )StandardName ()string {return _d .Sprintf ("\u0050D\u0046\u002f\u0041\u002d\u0032\u0025s",_ccbc ._fdecd ._ff );};func _beda (_eeed *_eb .PdfObjectDictionary ,_dcbb map[*_eb .PdfObjectStream ][]byte ,_abff map[*_eb .PdfObjectStream ]*_ba .CMap )ViolatedRule {const (_afece ="\u0046\u006f\u0072 \u0061\u006e\u0079\u0020\u0067\u0069\u0076\u0065\u006e\u0020\u0063\u006f\u006d\u0070\u006f\u0073\u0069\u0074\u0065\u0020\u0028\u0054\u0079\u0070\u0065\u0020\u0030\u0029\u0020\u0066\u006f\u006et \u0072\u0065\u0066\u0065\u0072\u0065\u006ec\u0065\u0064 \u0077\u0069\u0074\u0068\u0069\u006e\u0020\u0061\u0020\u0063\u006fn\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0074\u0068\u0065\u0020\u0043I\u0044\u0053y\u0073\u0074\u0065\u006d\u0049nf\u006f\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u006f\u0066\u0020i\u0074\u0073\u0020\u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0061\u006e\u0064 \u0043\u004d\u0061\u0070 \u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0063\u006f\u006d\u0070\u0061\u0074i\u0062\u006c\u0065\u002e\u0020\u0049\u006e\u0020o\u0074\u0068\u0065\u0072\u0020\u0077\u006f\u0072\u0064\u0073\u002c\u0020\u0074\u0068\u0065\u0020R\u0065\u0067\u0069\u0073\u0074\u0072\u0079\u0020a\u006e\u0064\u0020\u004fr\u0064\u0065\u0072\u0069\u006e\u0067 \u0073\u0074\u0072i\u006e\u0067\u0073\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044\u0053\u0079\u0073\u0074\u0065\u006d\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0066\u006f\u0072 \u0074\u0068\u0061\u0074\u0020\u0066o\u006e\u0074\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0069\u0064\u0065\u006e\u0074\u0069\u0063\u0061\u006c\u002c\u0020u\u006el\u0065ss \u0074\u0068\u0065\u0020\u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006eg\u0020\u006b\u0065\u0079\u0020\u0069\u006e\u0020\u0074h\u0065 \u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0069\u0073 \u0049\u0064\u0065\u006e\u0074\u0069t\u0079\u002d\u0048\u0020o\u0072\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074y\u002dV\u002e";
_bdgc ="\u0036.\u0033\u002e\u0033\u002d\u0031";);var _fegde string ;if _cgab ,_egdfc :=_eb .GetName (_eeed .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_egdfc {_fegde =_cgab .String ();};if _fegde !="\u0054\u0079\u0070e\u0030"{return _ddd ;};_aceg :=_eeed .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _ebgbd ,_geed :=_eb .GetName (_aceg );_geed {switch _ebgbd .String (){case "\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048","\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056":return _ddd ;};_fdfdb ,_adec :=_ba .LoadPredefinedCMap (_ebgbd .String ());
if _adec !=nil {return _ee (_bdgc ,_afece );};_efbg :=_fdfdb .CIDSystemInfo ();if _efbg .Ordering !=_efbg .Registry {return _ee (_bdgc ,_afece );};return _ddd ;};_dbgb ,_dgac :=_eb .GetStream (_aceg );if !_dgac {return _ee (_bdgc ,_afece );};_ffca ,_fdadd :=_cafgb (_dbgb ,_dcbb ,_abff );
if _fdadd !=nil {return _ee (_bdgc ,_afece );};_efec :=_ffca .CIDSystemInfo ();if _efec .Ordering !=_efec .Registry {return _ee (_bdgc ,_afece );};return _ddd ;};

// DefaultProfile2Options are the default options for the Profile2.
func DefaultProfile2Options ()*Profile2Options {return &Profile2Options {Now :_ca .Now ,Xmp :XmpOptions {MarshalIndent :"\u0009"}};};func _decaf (_dafg *_a .CompliancePdfReader )(_ebed []ViolatedRule ){var _cbedg ,_bcdf ,_bcag ,_ecca bool ;_dfgbe :=func ()bool {return _cbedg &&_bcdf &&_bcag &&_ecca };
_gdcb ,_aggf :=_cabcg (_dafg );var _aecce _bea .ProfileHeader ;if _aggf {_aecce ,_ =_bea .ParseHeader (_gdcb .DestOutputProfile );};_gggad :=map[_eb .PdfObject ]struct{}{};var _bcac func (_feddf _a .PdfColorspace )bool ;_bcac =func (_cbbe _a .PdfColorspace )bool {switch _gfdg :=_cbbe .(type ){case *_a .PdfColorspaceDeviceGray :if !_cbedg {if !_aggf {_ebed =append (_ebed ,_ee ("\u0036.\u0032\u002e\u0034\u002e\u0033\u002d4","\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u006f\u006e\u006c\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064 \u0069\u0066\u0020\u0061\u0020\u0064\u0065v\u0069\u0063\u0065\u0020\u0069\u006e\u0064\u0065p\u0065\u006e\u0064\u0065\u006e\u0074\u0020\u0044\u0065\u0066\u0061\u0075\u006c\u0074\u0047\u0072\u0061\u0079\u0020\u0063\u006f\u006c\u006f\u0075r \u0073\u0070\u0061\u0063\u0065\u0020\u0068\u0061\u0073\u0020\u0062\u0065\u0065\u006e \u0073\u0065\u0074\u0020\u0077\u0068\u0065n \u0074\u0068\u0065\u0020\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072a\u0079\u0020\u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0069\u0073\u0020\u0075\u0073\u0065\u0064\u002c o\u0072\u0020\u0069\u0066\u0020\u0061\u0020\u0050\u0044\u0046\u002fA\u0020\u004f\u0075tp\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0069\u0073\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u002e"));
_cbedg =true ;if _dfgbe (){return true ;};};};case *_a .PdfColorspaceDeviceRGB :if !_bcdf {if !_aggf ||_aecce .ColorSpace !=_bea .ColorSpaceRGB {_ebed =append (_ebed ,_ee ("\u0036.\u0032\u002e\u0034\u002e\u0033\u002d2","\u0044\u0065\u0076\u0069c\u0065\u0052\u0047\u0042\u0020\u0073\u0068\u0061\u006cl\u0020\u006f\u006e\u006c\u0079\u0020\u0062e\u0020\u0075\u0073\u0065\u0064\u0020\u0069f\u0020\u0061\u0020\u0064\u0065\u0076\u0069\u0063e\u0020\u0069n\u0064\u0065\u0070e\u006e\u0064\u0065\u006et \u0044\u0065\u0066\u0061\u0075\u006c\u0074\u0052\u0047\u0042\u0020\u0063\u006fl\u006f\u0075r\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0068\u0061\u0073\u0020b\u0065\u0065\u006e\u0020s\u0065\u0074 \u0077\u0068\u0065\u006e\u0020\u0074\u0068\u0065\u0020\u0044\u0065\u0076\u0069\u0063\u0065\u0052\u0047\u0042\u0020c\u006flou\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020i\u0073\u0020\u0075\u0073\u0065\u0064\u002c\u0020\u006f\u0072\u0020if\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0050\u0044F\u002f\u0041\u0020\u004fut\u0070\u0075\u0074\u0049\u006e\u0074\u0065n\u0074\u0020t\u0068\u0061t\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0061\u006e\u0020\u0052\u0047\u0042\u0020\u0064\u0065\u0073\u0074\u0069\u006e\u0061\u0074io\u006e\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u002e"));
_bcdf =true ;if _dfgbe (){return true ;};};};case *_a .PdfColorspaceDeviceCMYK :if !_bcag {if !_aggf ||_aecce .ColorSpace !=_bea .ColorSpaceCMYK {_ebed =append (_ebed ,_ee ("\u0036.\u0032\u002e\u0034\u002e\u0033\u002d3","\u0044e\u0076\u0069c\u0065\u0043\u004d\u0059\u004b\u0020\u0073hal\u006c\u0020\u006f\u006e\u006c\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u0069\u0066\u0020\u0061\u0020\u0064\u0065\u0076\u0069\u0063\u0065\u0020\u0069\u006e\u0064\u0065\u0070\u0065\u006e\u0064\u0065\u006e\u0074\u0020\u0044ef\u0061\u0075\u006c\u0074\u0043\u004d\u0059K\u0020\u0063\u006f\u006c\u006f\u0075\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0068\u0061s\u0020\u0062\u0065\u0065\u006e \u0073\u0065\u0074\u0020\u006fr \u0069\u0066\u0020\u0061\u0020\u0044e\u0076\u0069\u0063\u0065\u004e\u002d\u0062\u0061\u0073\u0065\u0064\u0020\u0044\u0065f\u0061\u0075\u006c\u0074\u0043\u004d\u0059\u004b\u0020c\u006f\u006c\u006f\u0075r\u0020\u0073\u0070\u0061\u0063e\u0020\u0068\u0061\u0073\u0020\u0062\u0065\u0065\u006e\u0020\u0073\u0065\u0074\u0020\u0077\u0068\u0065\u006e\u0020\u0074h\u0065\u0020\u0044\u0065\u0076\u0069c\u0065\u0043\u004d\u0059\u004b\u0020c\u006f\u006c\u006fu\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u0069\u0073\u0020\u0075\u0073\u0065\u0064\u0020\u006f\u0072\u0020t\u0068\u0065\u0020\u0066\u0069l\u0065\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u004f\u0075\u0074p\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0061\u0020\u0043\u004d\u0059\u004b\u0020d\u0065\u0073\u0074\u0069\u006e\u0061t\u0069\u006f\u006e\u0020\u0070r\u006f\u0066\u0069\u006c\u0065\u002e"));
_bcag =true ;if _dfgbe (){return true ;};};};case *_a .PdfColorspaceICCBased :if !_ecca {_bdebd ,_debf :=_bea .ParseHeader (_gfdg .Data );if _debf !=nil {_bf .Log .Debug ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0049\u0043\u0043\u0042\u0061\u0073e\u0064 \u0068e\u0061d\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_debf );
_ebed =append (_ebed ,func ()ViolatedRule {return _ee ("\u0036.\u0032\u002e\u0034\u002e\u0032\u002d1","\u0054\u0068e\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0074\u0068\u0061\u0074\u0020\u0066o\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0073\u0074r\u0065\u0061\u006d o\u0066\u0020\u0061\u006e\u0020\u0049C\u0043\u0042\u0061\u0073\u0065\u0064\u0020\u0063\u006fl\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0020\u0074o\u0020\u0049\u0043\u0043.\u0031\u003a\u0031\u0039\u0039\u0038-\u0030\u0039,\u0020\u0049\u0043\u0043\u002e\u0031\u003a\u0032\u0030\u0030\u0031\u002d\u00312\u002c\u0020\u0049\u0043\u0043\u002e\u0031\u003a\u0032\u0030\u0030\u0033\u002d\u0030\u0039\u0020\u006f\u0072\u0020I\u0053\u004f\u0020\u0031\u0035\u0030\u0037\u0036\u002d\u0031\u002e");
}());_ecca =true ;if _dfgbe (){return true ;};};if !_ecca {var _dgfee ,_ccffa bool ;switch _bdebd .DeviceClass {case _bea .DeviceClassPRTR ,_bea .DeviceClassMNTR ,_bea .DeviceClassSCNR ,_bea .DeviceClassSPAC :default:_dgfee =true ;};switch _bdebd .ColorSpace {case _bea .ColorSpaceRGB ,_bea .ColorSpaceCMYK ,_bea .ColorSpaceGRAY ,_bea .ColorSpaceLAB :default:_ccffa =true ;
};if _dgfee ||_ccffa {_ebed =append (_ebed ,_ee ("\u0036.\u0032\u002e\u0034\u002e\u0032\u002d1","\u0054\u0068e\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0074\u0068\u0061\u0074\u0020\u0066o\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0073\u0074r\u0065\u0061\u006d o\u0066\u0020\u0061\u006e\u0020\u0049C\u0043\u0042\u0061\u0073\u0065\u0064\u0020\u0063\u006fl\u006f\u0075\u0072\u0020\u0073p\u0061\u0063\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0020\u0074o\u0020\u0049\u0043\u0043.\u0031\u003a\u0031\u0039\u0039\u0038-\u0030\u0039,\u0020\u0049\u0043\u0043\u002e\u0031\u003a\u0032\u0030\u0030\u0031\u002d\u00312\u002c\u0020\u0049\u0043\u0043\u002e\u0031\u003a\u0032\u0030\u0030\u0033\u002d\u0030\u0039\u0020\u006f\u0072\u0020I\u0053\u004f\u0020\u0031\u0035\u0030\u0037\u0036\u002d\u0031\u002e"));
_ecca =true ;if _dfgbe (){return true ;};};};};if _gfdg .Alternate !=nil {return _bcac (_gfdg .Alternate );};};return false ;};for _ ,_aggd :=range _dafg .GetObjectNums (){_ffff ,_gbddd :=_dafg .GetIndirectObjectByNumber (_aggd );if _gbddd !=nil {continue ;
};_cffb ,_gfag :=_eb .GetStream (_ffff );if !_gfag {continue ;};_bdagb ,_gfag :=_eb .GetName (_cffb .Get ("\u0054\u0079\u0070\u0065"));if !_gfag ||_bdagb .String ()!="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_ddaca ,_gfag :=_eb .GetName (_cffb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_gfag {continue ;};_gggad [_cffb ]=struct{}{};switch _ddaca .String (){case "\u0049\u006d\u0061g\u0065":_addeg ,_cgad :=_a .NewXObjectImageFromStream (_cffb );if _cgad !=nil {continue ;};_gggad [_cffb ]=struct{}{};if _bcac (_addeg .ColorSpace ){return _ebed ;
};case "\u0046\u006f\u0072\u006d":_agad ,_gacb :=_eb .GetDict (_cffb .Get ("\u0047\u0072\u006fu\u0070"));if !_gacb {continue ;};_faeg :=_agad .Get ("\u0043\u0053");if _faeg ==nil {continue ;};_facb ,_afab :=_a .NewPdfColorspaceFromPdfObject (_faeg );if _afab !=nil {continue ;
};if _bcac (_facb ){return _ebed ;};};};for _ ,_dced :=range _dafg .PageList {_fbada ,_baee :=_dced .GetContentStreams ();if _baee !=nil {continue ;};for _ ,_fgeed :=range _fbada {_dadcc ,_ffbda :=_e .NewContentStreamParser (_fgeed ).Parse ();if _ffbda !=nil {continue ;
};for _ ,_agddf :=range *_dadcc {if len (_agddf .Params )> 1{continue ;};switch _agddf .Operand {case "\u0042\u0049":_deace ,_ffgbd :=_agddf .Params [0].(*_e .ContentStreamInlineImage );if !_ffgbd {continue ;};_bdaf ,_bbag :=_deace .GetColorSpace (_dced .Resources );
if _bbag !=nil {continue ;};if _bcac (_bdaf ){return _ebed ;};case "\u0044\u006f":_aefgc ,_bedcc :=_eb .GetName (_agddf .Params [0]);if !_bedcc {continue ;};_fcea ,_gdaad :=_dced .Resources .GetXObjectByName (*_aefgc );if _ ,_bbcf :=_gggad [_fcea ];_bbcf {continue ;
};switch _gdaad {case _a .XObjectTypeImage :_edcfd ,_abaed :=_a .NewXObjectImageFromStream (_fcea );if _abaed !=nil {continue ;};_gggad [_fcea ]=struct{}{};if _bcac (_edcfd .ColorSpace ){return _ebed ;};case _a .XObjectTypeForm :_cbbc ,_dffc :=_eb .GetDict (_fcea .Get ("\u0047\u0072\u006fu\u0070"));
if !_dffc {continue ;};_agfe ,_dffc :=_eb .GetName (_cbbc .Get ("\u0043\u0053"));if !_dffc {continue ;};_cbgg ,_cbggg :=_a .NewPdfColorspaceFromPdfObject (_agfe );if _cbggg !=nil {continue ;};_gggad [_fcea ]=struct{}{};if _bcac (_cbgg ){return _ebed ;};
};};};};};return _ebed ;};type documentColorspaceOptimizeFunc func (_bbgd *_gde .Document ,_dbfc []*_gde .Image )error ;

// XmpOptions are the options used by the optimization of the XMP metadata.
type XmpOptions struct{

// Copyright information.
Copyright string ;

// OriginalDocumentID is the original document identifier.
// By default, if this field is empty the value is extracted from the XMP Metadata or generated UUID.
OriginalDocumentID string ;

// DocumentID is the original document identifier.
// By default, if this field is empty the value is extracted from the XMP Metadata or generated UUID.
DocumentID string ;

// InstanceID is the original document identifier.
// By default, if this field is empty the value is set to generated UUID.
InstanceID string ;

// NewDocumentVersion is a flag that defines if a document was overwritten.
// If the new document was created this should be true. On changing given document file, and overwriting it it should be true.
NewDocumentVersion bool ;

// MarshalIndent defines marshaling indent of the XMP metadata.
MarshalIndent string ;

// MarshalPrefix defines marshaling prefix of the XMP metadata.
MarshalPrefix string ;};func _ee (_ede string ,_bbc string )ViolatedRule {return ViolatedRule {RuleNo :_ede ,Detail :_bbc }};func _ggbe (_cgdb *_a .CompliancePdfReader )ViolatedRule {if _cgdb .ParserMetadata ().HeaderPosition ()!=0{return _ee ("\u0036.\u0031\u002e\u0032\u002d\u0031","h\u0065\u0061\u0064\u0065\u0072\u0020\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e\u0020\u0069\u0073\u0020n\u006f\u0074\u0020\u0061\u0074\u0020\u0074\u0068\u0065\u0020fi\u0072\u0073\u0074 \u0062y\u0074\u0065");
};return _ddd ;};func _fdde (_ccda *_gde .Document ,_gec bool )error {_aag ,_afb :=_ccda .GetPages ();if !_afb {return nil ;};for _ ,_gbga :=range _aag {_gbb ,_acbc :=_eb .GetArray (_gbga .Object .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));if !_acbc {continue ;
};for _ ,_fgfg :=range _gbb .Elements (){_fag ,_gag :=_eb .GetDict (_fgfg );if !_gag {continue ;};_fbee :=_fag .Get ("\u0043");if _fbee ==nil {continue ;};_cgfad ,_gag :=_eb .GetArray (_fbee );if !_gag {continue ;};_eeg ,_gbab :=_cgfad .GetAsFloat64Slice ();
if _gbab !=nil {return _gbab ;};switch _cgfad .Len (){case 0,1:if _gec {_fag .Set ("\u0043",_eb .MakeArrayFromIntegers ([]int {1,1,1,1}));}else {_fag .Set ("\u0043",_eb .MakeArrayFromIntegers ([]int {1,1,1}));};case 3:if _gec {_eaga ,_cbagc ,_cae ,_fgcb :=_b .RGBToCMYK (uint8 (_eeg [0]*255),uint8 (_eeg [1]*255),uint8 (_eeg [2]*255));
_fag .Set ("\u0043",_eb .MakeArrayFromFloats ([]float64 {float64 (_eaga )/255,float64 (_cbagc )/255,float64 (_cae )/255,float64 (_fgcb )/255}));};case 4:if !_gec {_cbff ,_cfd ,_bbed :=_b .CMYKToRGB (uint8 (_eeg [0]*255),uint8 (_eeg [1]*255),uint8 (_eeg [2]*255),uint8 (_eeg [3]*255));
_fag .Set ("\u0043",_eb .MakeArrayFromFloats ([]float64 {float64 (_cbff )/255,float64 (_cfd )/255,float64 (_bbed )/255}));};};};};return nil ;};func _afe (_dbc standardType ,_feaf *_gde .OutputIntents )error {_abed ,_cga :=_bea .NewCmykIsoCoatedV2OutputIntent (_dbc .outputIntentSubtype ());
if _cga !=nil {return _cga ;};if _cga =_feaf .Add (_abed .ToPdfObject ());_cga !=nil {return _cga ;};return nil ;};func _aebfb (_fcad *_a .CompliancePdfReader )(_fgafd []ViolatedRule ){return _fgafd };

// NewProfile3A creates a new Profile3A with given options.
func NewProfile3A (options *Profile3Options )*Profile3A {if options ==nil {options =DefaultProfile3Options ();};_cceg (options );return &Profile3A {profile3 {_afba :*options ,_fddb :_bgf ()}};};func _agdc (_dfcgb *_a .CompliancePdfReader )(_ggbcg []ViolatedRule ){for _ ,_ebbf :=range _dfcgb .GetObjectNums (){_feba ,_dddb :=_dfcgb .GetIndirectObjectByNumber (_ebbf );
if _dddb !=nil {continue ;};_fefb ,_abgcf :=_eb .GetDict (_feba );if !_abgcf {continue ;};_aggca ,_abgcf :=_eb .GetName (_fefb .Get ("\u0054\u0079\u0070\u0065"));if !_abgcf {continue ;};if _aggca .String ()!="\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"{continue ;
};_cbgc ,_abgcf :=_eb .GetBool (_fefb .Get ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073"));if _abgcf &&bool (*_cbgc ){_ggbcg =append (_ggbcg ,_ee ("\u0036.\u0034\u002e\u0031\u002d\u0033","\u0054\u0068\u0065\u0020\u004e\u0065e\u0064\u0041\u0070\u0070\u0065a\u0072\u0061\u006e\u0063\u0065\u0073\u0020\u0066\u006c\u0061\u0067\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0069\u006e\u0074\u0065\u0072\u0061\u0063\u0074\u0069\u0076e\u0020\u0066\u006f\u0072\u006d \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0065\u0069\u0074\u0068\u0065\u0072\u0020\u006e\u006f\u0074\u0020b\u0065\u0020\u0070\u0072\u0065se\u006e\u0074\u0020\u006f\u0072\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u002e"));
};if _fefb .Get ("\u0058\u0046\u0041")!=nil {_ggbcg =append (_ggbcg ,_ee ("\u0036.\u0034\u002e\u0032\u002d\u0031","\u0054\u0068\u0065\u0020\u0064o\u0063\u0075\u006d\u0065\u006e\u0074\u0027\u0073\u0020i\u006e\u0074\u0065\u0072\u0061\u0063\u0074\u0069\u0076\u0065\u0020\u0066\u006f\u0072\u006d\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020t\u0068\u0061\u0074\u0020f\u006f\u0072\u006d\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065 \u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d \u006b\u0065\u0079\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0027\u0073\u0020\u0043\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006f\u0066 \u0061 \u0050\u0044F\u002fA\u002d\u0032\u0020\u0066ile\u002c\u0020\u0069\u0066\u0020\u0070\u0072\u0065\u0073\u0065n\u0074\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0058\u0046\u0041\u0020\u006b\u0065y."));
};};_dgcf ,_aeada :=_efcc (_dfcgb );if _aeada &&_dgcf .Get ("\u004e\u0065\u0065\u0064\u0073\u0052\u0065\u006e\u0064e\u0072\u0069\u006e\u0067")!=nil {_ggbcg =append (_ggbcg ,_ee ("\u0036.\u0034\u002e\u0032\u002d\u0032","\u0041\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0027\u0073\u0020\u0043\u0061\u0074\u0061\u006cog\u0020s\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u004e\u0065\u0065\u0064\u0073\u0052\u0065\u006e\u0064e\u0072\u0069\u006e\u0067\u0020\u006b\u0065\u0079\u002e"));
};return _ggbcg ;};type standardType struct{_dda int ;_ff string ;};func _bcae (_cacgd *_a .CompliancePdfReader )(_ceeeb ViolatedRule ){for _ ,_daba :=range _cacgd .GetObjectNums (){_bdaa ,_aebd :=_cacgd .GetIndirectObjectByNumber (_daba );if _aebd !=nil {continue ;
};_ddef ,_edag :=_eb .GetStream (_bdaa );if !_edag {continue ;};_deac ,_edag :=_eb .GetName (_ddef .Get ("\u0054\u0079\u0070\u0065"));if !_edag {continue ;};if *_deac !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_daaaa ,_edag :=_eb .GetName (_ddef .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_edag {continue ;};if *_daaaa =="\u0050\u0053"{return _ee ("\u0036.\u0032\u002e\u0037\u002d\u0031","A \u0063\u006fn\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066i\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0050\u006f\u0073t\u0053c\u0072\u0069\u0070\u0074\u0020\u0058\u004f\u0062j\u0065c\u0074\u0073.");
};};return _ceeeb ;};func (_fg standardType )outputIntentSubtype ()_a .PdfOutputIntentType {switch _fg ._dda {case 1:return _a .PdfOutputIntentTypeA1 ;case 2:return _a .PdfOutputIntentTypeA2 ;case 3:return _a .PdfOutputIntentTypeA3 ;case 4:return _a .PdfOutputIntentTypeA4 ;
default:return 0;};};func _dfgg (_dfgb *_a .CompliancePdfReader )(_acbe []ViolatedRule ){_ebcc ,_egfbd :=_efcc (_dfgb );if !_egfbd {return _acbe ;};_feea :=_ee ("\u0036.\u0032\u002e\u0032\u002d\u0031","\u0041\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074p\u0075\u0074\u0049\u006e\u0074e\u006e\u0074\u0020\u0069\u0073\u0020a\u006e \u004f\u0075\u0074\u0070\u0075\u0074\u0049n\u0074\u0065\u006e\u0074\u0020\u0064i\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0062y\u0020\u0050\u0044F\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0039\u002e\u0031\u0030.4\u002c\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0073 \u0069\u006e\u0063\u006c\u0075\u0064e\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0027\u0073\u0020O\u0075\u0074p\u0075\u0074I\u006e\u0074\u0065\u006e\u0074\u0073\u0020\u0061\u0072\u0072\u0061\u0079\u0020a\u006e\u0064\u0020h\u0061\u0073\u0020\u0047\u0054\u0053\u005f\u0050\u0044\u0046\u0041\u0031\u0020\u0061\u0073 \u0074\u0068\u0065\u0020\u0076a\u006c\u0075e\u0020\u006f\u0066\u0020i\u0074\u0073 \u0053\u0020\u006b\u0065\u0079\u0020\u0061\u006e\u0064\u0020\u0061\u0020\u0076\u0061\u006c\u0069\u0064\u0020I\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006ce\u0020s\u0074\u0072\u0065\u0061\u006d \u0061\u0073\u0020\u0074h\u0065\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0074\u0073\u0020\u0044\u0065\u0073t\u004f\u0075t\u0070\u0075\u0074P\u0072\u006f\u0066\u0069\u006c\u0065 \u006b\u0065\u0079\u002e");
_dddc ,_egfbd :=_eb .GetArray (_ebcc .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073"));if !_egfbd {_acbe =append (_acbe ,_feea );return _acbe ;};_afd :=_ee ("\u0036.\u0032\u002e\u0032\u002d\u0032","\u0049\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065's\u0020O\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073 \u0061\u0072\u0072a\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0073\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068a\u006e\u0020\u006f\u006ee\u0020\u0065\u006e\u0074\u0072\u0079\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0065n\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e a \u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006cl\u0020\u0068\u0061\u0076\u0065 \u0061\u0073\u0020\u0074\u0068\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068a\u0074\u0020\u006b\u0065\u0079 \u0074\u0068\u0065\u0020\u0073\u0061\u006d\u0065\u0020\u0069\u006e\u0064\u0069\u0072\u0065c\u0074\u0020\u006fb\u006ae\u0063t\u002c\u0020\u0077h\u0069\u0063\u0068\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u0061\u0020\u0076\u0061\u006c\u0069d\u0020\u0049\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074r\u0065\u0061m\u002e");
if _dddc .Len ()> 1{_cgbg :=map[*_eb .PdfObjectDictionary ]struct{}{};for _fecg :=0;_fecg < _dddc .Len ();_fecg ++{_ggf ,_dddcg :=_eb .GetDict (_dddc .Get (_fecg ));if !_dddcg {_acbe =append (_acbe ,_feea );return _acbe ;};if _fecg ==0{_cgbg [_ggf ]=struct{}{};
continue ;};if _ ,_eggba :=_cgbg [_ggf ];!_eggba {_acbe =append (_acbe ,_afd );break ;};};}else if _dddc .Len ()==0{_acbe =append (_acbe ,_feea );return _acbe ;};_dgea ,_egfbd :=_eb .GetDict (_dddc .Get (0));if !_egfbd {_acbe =append (_acbe ,_feea );return _acbe ;
};if _dcbgc ,_degdd :=_eb .GetName (_dgea .Get ("\u0053"));!_degdd ||(*_dcbgc )!="\u0047T\u0053\u005f\u0050\u0044\u0046\u00411"{_acbe =append (_acbe ,_feea );return _acbe ;};_gecc ,_fbad :=_a .NewPdfOutputIntentFromPdfObject (_dgea );if _fbad !=nil {_bf .Log .Debug ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u006f\u0075\u0074\u0070\u0075\u0074\u0020i\u006et\u0065\u006e\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_fbad );
return _acbe ;};_eebc ,_fbad :=_bea .ParseHeader (_gecc .DestOutputProfile );if _fbad !=nil {_bf .Log .Debug ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0063\u006f\u006c\u006f\u0072\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0068\u0065\u0061d\u0065\u0072\u0020\u0066\u0061i\u006c\u0065d\u003a\u0020\u0025\u0076",_fbad );
return _acbe ;};if (_eebc .DeviceClass ==_bea .DeviceClassPRTR ||_eebc .DeviceClass ==_bea .DeviceClassMNTR )&&(_eebc .ColorSpace ==_bea .ColorSpaceRGB ||_eebc .ColorSpace ==_bea .ColorSpaceCMYK ||_eebc .ColorSpace ==_bea .ColorSpaceGRAY ){return _acbe ;
};_acbe =append (_acbe ,_feea );return _acbe ;};func _defc (_fge *_gde .Document )error {_fbd :=map[string ]*_eb .PdfObjectDictionary {};_gfg :=_bd .NewFinder (&_bd .FinderOpts {Extensions :[]string {"\u002e\u0074\u0074\u0066","\u002e\u0074\u0074\u0063"}});
_ffbb :=map[_eb .PdfObject ]struct{}{};_beae :=map[_eb .PdfObject ]struct{}{};for _ ,_dce :=range _fge .Objects {_eacaa ,_eaa :=_eb .GetDict (_dce );if !_eaa {continue ;};_ace :=_eacaa .Get ("\u0054\u0079\u0070\u0065");if _ace ==nil {continue ;};if _agd ,_fdf :=_eb .GetName (_ace );
_fdf &&_agd .String ()!="\u0046\u006f\u006e\u0074"{continue ;};if _ ,_fcd :=_ffbb [_dce ];_fcd {continue ;};_gacg ,_dcf :=_a .NewPdfFontFromPdfObject (_eacaa );if _dcf !=nil {_bf .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u006c\u006f\u0061\u0064\u0020\u0066\u006fn\u0074\u0020\u0066\u0072\u006fm\u0020\u006fb\u006a\u0065\u0063\u0074");
return _dcf ;};if _gacg .Encoder ()!=nil &&(_gacg .Encoder ().String ()=="\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048"||_gacg .Encoder ().String ()=="\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056"){continue ;};if _gacg .Subtype ()=="\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032"{_efe :=_gacg .GetCIDToGIDMapObject ();
if _efe !=nil {continue ;};};_adee ,_dcf :=_gacg .GetFontDescriptor ();if _dcf !=nil {return _dcf ;};if _adee !=nil &&(_adee .FontFile !=nil ||_adee .FontFile2 !=nil ||_adee .FontFile3 !=nil ){continue ;};_gba :=_gacg .BaseFont ();if _gba ==""{_adff ,_fdfb :=_gacg .GetFontDescriptor ();
if _fdfb !=nil {return _d .Errorf ("\u0063\u0061\u006e\u0027\u0074\u0020\u0067\u0065t\u0020\u0074\u0068e \u0066\u006f\u006e\u0074\u0020\u006ea\u006d\u0065\u0020\u0066\u0072\u006f\u006d\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065s\u0063\u0072\u0069\u0070\u0074\u006f\u0072\u003a \u0025\u0073",_eacaa .String ());
};_gba =_adff .FontName .String ();if _gba ==""{return _d .Errorf ("\u006f\u006e\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u006f\u0062\u006a\u0065c\u0074\u0073\u0020\u0073\u0079\u006e\u0074\u0061\u0078\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0076\u0061\u006c\u0069d\u0020\u002d\u0020\u0042\u0061\u0073\u0065\u0046\u006f\u006e\u0074\u0020\u0075\u006ed\u0065\u0066\u0069n\u0065\u0064\u003a\u0020\u0025\u0073",_eacaa .String ());
};};_ffa ,_fad :=_fbd [_gba ];if !_fad {if len (_gba )> 7&&_gba [6]=='+'{_gba =_gba [7:];};_cbf :=[]string {_gba ,"\u0054i\u006de\u0073\u0020\u004e\u0065\u0077\u0020\u0052\u006f\u006d\u0061\u006e","\u0041\u0072\u0069a\u006c","D\u0065\u006a\u0061\u0056\u0075\u0020\u0053\u0061\u006e\u0073"};
for _ ,_bbac :=range _cbf {_bf .Log .Debug ("\u0044\u0045\u0042\u0055\u0047\u003a \u0073\u0065\u0061\u0072\u0063\u0068\u0069\u006e\u0067\u0020\u0073\u0079\u0073t\u0065\u006d\u0020\u0066\u006f\u006e\u0074 \u0060\u0025\u0073\u0060",_bbac );if _ffa ,_fad =_fbd [_bbac ];
_fad {break ;};_cfef :=_gfg .Match (_bbac );if _cfef ==nil {_bf .Log .Debug ("c\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0066\u0069\u006e\u0064\u0020\u0066\u006fn\u0074\u0020\u0066i\u006ce\u0020\u0025\u0073",_bbac );continue ;};_bde ,_bcf :=_a .NewPdfFontFromTTFFile (_cfef .Filename );
if _bcf !=nil {return _bcf ;};_geb :=_bde .FontDescriptor ();if _geb .FontFile !=nil {if _ ,_fad =_beae [_geb .FontFile ];!_fad {_fge .Objects =append (_fge .Objects ,_geb .FontFile );_beae [_geb .FontFile ]=struct{}{};};};if _geb .FontFile2 !=nil {if _ ,_fad =_beae [_geb .FontFile2 ];
!_fad {_fge .Objects =append (_fge .Objects ,_geb .FontFile2 );_beae [_geb .FontFile2 ]=struct{}{};};};if _geb .FontFile3 !=nil {if _ ,_fad =_beae [_geb .FontFile3 ];!_fad {_fge .Objects =append (_fge .Objects ,_geb .FontFile3 );_beae [_geb .FontFile3 ]=struct{}{};
};};_agf ,_fee :=_bde .ToPdfObject ().(*_eb .PdfIndirectObject );if !_fee {_bf .Log .Debug ("\u0066\u006f\u006e\u0074\u0020\u0069\u0073\u0020\u006e\u006ft\u0020\u0061\u006e\u0020\u0069\u006e\u0064i\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074");
continue ;};_ffc ,_fee :=_agf .PdfObject .(*_eb .PdfObjectDictionary );if !_fee {_bf .Log .Debug ("\u0046\u006fn\u0074\u0020\u0074\u0079p\u0065\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u006e \u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079");
continue ;};_fbd [_bbac ]=_ffc ;_ffa =_ffc ;break ;};if _ffa ==nil {_bf .Log .Debug ("\u004e\u006f\u0020\u006d\u0061\u0074\u0063\u0068\u0069\u006eg\u0020\u0066\u006f\u006e\u0074\u0020\u0066o\u0075\u006e\u0064\u0020\u0066\u006f\u0072\u003a\u0020\u0025\u0073",_gacg .BaseFont ());
return _ce .New ("\u006e\u006f m\u0061\u0074\u0063h\u0069\u006e\u0067\u0020fon\u0074 f\u006f\u0075\u006e\u0064\u0020\u0069\u006e t\u0068\u0065\u0020\u0073\u0079\u0073\u0074e\u006d");};};for _ ,_adfg :=range _ffa .Keys (){_eacaa .Set (_adfg ,_ffa .Get (_adfg ));
};_bca :=_ffa .Get ("\u0057\u0069\u0064\u0074\u0068\u0073");if _bca !=nil {if _ ,_fad =_beae [_bca ];!_fad {_fge .Objects =append (_fge .Objects ,_bca );_beae [_bca ]=struct{}{};};};_ffbb [_dce ]=struct{}{};_dbd :=_eacaa .Get ("\u0046\u006f\u006e\u0074\u0044\u0065\u0073\u0063\u0072i\u0070\u0074\u006f\u0072");
if _dbd !=nil {_fge .Objects =append (_fge .Objects ,_dbd );_beae [_dbd ]=struct{}{};};};return nil ;};

// NewProfile2B creates a new Profile2B with the given options.
func NewProfile2B (options *Profile2Options )*Profile2B {if options ==nil {options =DefaultProfile2Options ();};_gcbb (options );return &Profile2B {profile2 {_efc :*options ,_fdecd :_ccg ()}};};

// Profile2U is the implementation of the PDF/A-2U standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile2U struct{profile2 };func _gbad (_eaea *_gde .Document )error {_fbb ,_eead :=_eaea .GetPages ();if !_eead {return nil ;};for _ ,_dddff :=range _fbb {_gbgf ,_edad :=_eb .GetArray (_dddff .Object .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));
if !_edad {continue ;};for _ ,_cbagg :=range _gbgf .Elements (){_cbagg =_eb .ResolveReference (_cbagg );if _ ,_gad :=_cbagg .(*_eb .PdfObjectNull );_gad {continue ;};_gagc ,_gfa :=_eb .GetDict (_cbagg );if !_gfa {continue ;};_ffdd ,_ :=_eb .GetIntVal (_gagc .Get ("\u0046"));
_ffdd &=^(1<<0);_ffdd &=^(1<<1);_ffdd &=^(1<<5);_ffdd |=1<<2;_gagc .Set ("\u0046",_eb .MakeInteger (int64 (_ffdd )));_bfdgf :=false ;if _egad :=_gagc .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065");_egad !=nil {_fafe ,_dgfe :=_eb .GetName (_egad );if _dgfe &&_fafe .String ()=="\u0057\u0069\u0064\u0067\u0065\u0074"{_bfdgf =true ;
if _gagc .Get ("\u0041\u0041")!=nil {_gagc .Remove ("\u0041\u0041");};};};if _gagc .Get ("\u0043")!=nil ||_gagc .Get ("\u0049\u0043")!=nil {_afag ,_gbac :=_ged (_eaea );if !_gbac {_gagc .Remove ("\u0043");_gagc .Remove ("\u0049\u0043");}else {_egb ,_gfc :=_eb .GetIntVal (_afag .Get ("\u004e"));
if !_gfc ||_egb !=3{_gagc .Remove ("\u0043");_gagc .Remove ("\u0049\u0043");};};};_cccf ,_gfa :=_eb .GetDict (_gagc .Get ("\u0041\u0050"));if _gfa {_bebg :=_cccf .Get ("\u004e");if _bebg ==nil {continue ;};if len (_cccf .Keys ())> 1{_cccf .Clear ();_cccf .Set ("\u004e",_bebg );
};if _bfdgf {_egcc ,_dddac :=_eb .GetName (_gagc .Get ("\u0046\u0054"));if _dddac &&*_egcc =="\u0042\u0074\u006e"{continue ;};};};};};return nil ;};func _fddc (_edca *_a .CompliancePdfReader )ViolatedRule {_gcgddd :=_edca .ParserMetadata ();if _gcgddd .HasInvalidSeparationAfterXRef (){return _ee ("\u0036.\u0031\u002e\u0034\u002d\u0032","\u0054\u0068\u0065 \u0078\u0072\u0065\u0066\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0063\u0072\u006f\u0073s\u0020\u0072\u0065\u0066e\u0072\u0065\u006e\u0063\u0065 s\u0075b\u0073\u0065\u0063ti\u006f\u006e\u0020\u0068\u0065\u0061\u0064e\u0072\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0065\u0070\u0061\u0072\u0061\u0074\u0065\u0064\u0020\u0062\u0079 \u0061\u0020\u0073i\u006e\u0067\u006c\u0065\u0020\u0045\u004fL\u0020\u006d\u0061\u0072\u006b\u0065\u0072\u002e");
};return _ddd ;};func _gbf (_egg *_gde .Document )error {_cdef ,_fbdg :=_egg .FindCatalog ();if !_fbdg {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};_cdef .SetVersion ();return nil ;
};

// Profile2A is the implementation of the PDF/A-2A standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile2A struct{profile2 };var _ Profile =(*Profile3B )(nil );func _gcff (_gdgc *_a .CompliancePdfReader )[]ViolatedRule {return nil };func _cabcg (_bedc *_a .CompliancePdfReader )(*_a .PdfOutputIntent ,bool ){_bceg ,_eaed :=_aaeg (_bedc );if !_eaed {return nil ,false ;
};_gbdc ,_aacc :=_a .NewPdfOutputIntentFromPdfObject (_bceg );if _aacc !=nil {return nil ,false ;};return _gbdc ,true ;};func _ecdde (_dbgge *_a .CompliancePdfReader )(_bdeef []ViolatedRule ){_cfcf :=true ;_bdbfe ,_bfbc :=_dbgge .GetCatalogMarkInfo ();
if !_bfbc {_cfcf =false ;}else {_gbgfa ,_gdbgc :=_eb .GetDict (_bdbfe );if _gdbgc {_bcgg ,_ceaee :=_eb .GetBool (_gbgfa .Get ("\u004d\u0061\u0072\u006b\u0065\u0064"));if !bool (*_bcgg )||!_ceaee {_cfcf =false ;};}else {_cfcf =false ;};};if !_cfcf {_bdeef =append (_bdeef ,_ee ("\u0036.\u0037\u002e\u0032\u002e\u0032\u002d1","\u0054\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006cog\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u0020M\u0061r\u006b\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061 \u004d\u0061\u0072\u006b\u0065\u0064\u0020\u0065\u006et\u0072\u0079\u0020\u0069\u006e\u0020\u0069\u0074,\u0020\u0077\u0068\u006f\u0073\u0065\u0020\u0076\u0061lu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0074\u0072\u0075\u0065"));
};_daag ,_bfbc :=_dbgge .GetCatalogStructTreeRoot ();if !_bfbc {_bdeef =append (_bdeef ,_ee ("\u0036.\u0037\u002e\u0033\u002e\u0033\u002d1","\u0054\u0068\u0065\u0020\u006c\u006f\u0067\u0069\u0063\u0061\u006c\u0020\u0073\u0074\u0072\u0075\u0063\u0074\u0075r\u0065\u0020\u006f\u0066\u0020\u0074\u0068e\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067 \u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065d \u0062\u0079\u0020a\u0020s\u0074\u0072\u0075\u0063\u0074\u0075\u0072e\u0020\u0068\u0069\u0065\u0072\u0061\u0072\u0063\u0068\u0079\u0020\u0072\u006f\u006ft\u0065\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0065\u006e\u0074r\u0079\u0020\u006f\u0066\u0020\u0074h\u0065\u0020d\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0063\u0061t\u0061\u006c\u006fg \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069n\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0039\u002e\u0036\u002e"));
};_cedd ,_bfbc :=_eb .GetDict (_daag );if _bfbc {_egaca ,_bedbc :=_eb .GetName (_cedd .Get ("\u0052o\u006c\u0065\u004d\u0061\u0070"));if _bedbc {_egef ,_gecg :=_eb .GetDict (_egaca );if _gecg {for _ ,_gaac :=range _egef .Keys (){_eedf :=_egef .Get (_gaac );
if _eedf ==nil {_bdeef =append (_bdeef ,_ee ("\u0036.\u0037\u002e\u0033\u002e\u0034\u002d1","\u0041\u006c\u006c\u0020\u006eo\u006e\u002ds\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0073t\u0072\u0075\u0063\u0074ure\u0020\u0074\u0079\u0070\u0065s\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u006d\u0061\u0070\u0070\u0065d\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020n\u0065\u0061\u0072\u0065\u0073\u0074\u0020\u0066\u0075\u006e\u0063t\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0073\u0074a\u006ed\u0061r\u0064\u0020\u0074\u0079\u0070\u0065\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006ee\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065re\u006e\u0063e\u0020\u0039\u002e\u0037\u002e\u0034\u002c\u0020i\u006e\u0020\u0074\u0068e\u0020\u0072\u006fl\u0065\u0020\u006d\u0061p \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006f\u0066 \u0074h\u0065\u0020\u0073\u0074\u0072\u0075c\u0074\u0075r\u0065\u0020\u0074\u0072e\u0065\u0020\u0072\u006f\u006ft\u002e"));
};};};};};return _bdeef ;};func _acaa (_aabb *_a .CompliancePdfReader )ViolatedRule {return _ddd };

// Profile3A is the implementation of the PDF/A-3A standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile3A struct{profile3 };func _bade (_ebc *_a .CompliancePdfReader )ViolatedRule {for _ ,_bdfg :=range _ebc .PageList {_adfff :=_bdfg .GetContentStreamObjs ();for _ ,_aead :=range _adfff {_aead =_eb .TraceToDirectObject (_aead );var _gfee string ;
switch _bdfe :=_aead .(type ){case *_eb .PdfObjectString :_gfee =_bdfe .Str ();case *_eb .PdfObjectStream :_feeb ,_ffad :=_eb .GetName (_eb .TraceToDirectObject (_bdfe .Get ("\u0046\u0069\u006c\u0074\u0065\u0072")));if _ffad {if *_feeb ==_eb .StreamEncodingFilterNameLZW {return _ee ("\u0036\u002e\u0031\u002e\u0031\u0030\u002d\u0032","\u0054h\u0065\u0020L\u005a\u0057\u0044\u0065c\u006f\u0064\u0065 \u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0073\u0068al\u006c\u0020\u006eo\u0074\u0020b\u0065\u0020\u0070\u0065\u0072\u006di\u0074\u0074e\u0064\u002e");
};};_bacb ,_cfgf :=_eb .DecodeStream (_bdfe );if _cfgf !=nil {_bf .Log .Debug ("\u0045r\u0072\u003a\u0020\u0025\u0076",_cfgf );continue ;};_gfee =string (_bacb );default:_bf .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069d\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0073\u0074\u0072\u0065a\u006d\u0020\u006f\u0062\u006a\u0065\u0063t\u003a\u0020\u0025\u0054",_aead );
continue ;};_abba :=_e .NewContentStreamParser (_gfee );_afbb ,_cffd :=_abba .Parse ();if _cffd !=nil {_bf .Log .Debug ("\u0049\u006e\u0076\u0061\u006c\u0069\u0064\u0020\u0063\u006f\u006et\u0065\u006e\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d:\u0020\u0025\u0076",_cffd );
continue ;};for _ ,_gcad :=range *_afbb {if !(_gcad .Operand =="\u0042\u0049"&&len (_gcad .Params )==1){continue ;};_egae ,_gefg :=_gcad .Params [0].(*_e .ContentStreamInlineImage );if !_gefg {continue ;};_bagf ,_dacfe :=_egae .GetEncoder ();if _dacfe !=nil {_bf .Log .Debug ("\u0067\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u0069\u006e\u006c\u0069\u006ee\u0020\u0069\u006d\u0061\u0067\u0065 \u0065\u006e\u0063\u006f\u0064\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065d\u003a\u0020\u0025\u0076",_dacfe );
continue ;};if _bagf .GetFilterName ()==_eb .StreamEncodingFilterNameLZW {return _ee ("\u0036\u002e\u0031\u002e\u0031\u0030\u002d\u0032","\u0054h\u0065\u0020L\u005a\u0057\u0044\u0065c\u006f\u0064\u0065 \u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0073\u0068al\u006c\u0020\u006eo\u0074\u0020b\u0065\u0020\u0070\u0065\u0072\u006di\u0074\u0074e\u0064\u002e");
};};};};return _ddd ;};

// Conformance gets the PDF/A conformance.
func (_gff *profile2 )Conformance ()string {return _gff ._fdecd ._ff };func _bgdaa (_cccec string ,_ffaga string ,_dfagb string )(string ,bool ){_gfgd :=_bb .Index (_cccec ,_ffaga );if _gfgd ==-1{return "",false ;};_gfgd +=len (_ffaga );_bdc :=_bb .Index (_cccec [_gfgd :],_dfagb );
if _bdc ==-1{return "",false ;};_bdc =_gfgd +_bdc ;return _cccec [_gfgd :_bdc ],true ;};func _fea (_daa standardType ,_adag *_gde .OutputIntents )error {_bcg ,_dgcc :=_bea .NewISOCoatedV2Gray1CBasOutputIntent (_daa .outputIntentSubtype ());if _dgcc !=nil {return _dgcc ;
};if _dgcc =_adag .Add (_bcg .ToPdfObject ());_dgcc !=nil {return _dgcc ;};return nil ;};type pageColorspaceOptimizeFunc func (_efb *_gde .Document ,_ddae *_gde .Page ,_acbb []*_gde .Image )error ;func _fbfa (_gfcd *_a .CompliancePdfReader )ViolatedRule {for _ ,_egac :=range _gfcd .PageList {_gabe ,_ceae :=_egac .GetContentStreams ();
if _ceae !=nil {continue ;};for _ ,_fgdd :=range _gabe {_bdda :=_e .NewContentStreamParser (_fgdd );_ ,_ceae =_bdda .Parse ();if _ceae !=nil {return _ee ("\u0036\u002e\u0032\u002e\u0031\u0030\u002d\u0031","\u0041\u0020\u0063onten\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u0073\u0068\u0061\u006c\u006c n\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079 \u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072\u0073\u0020\u006e\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0065\u0076\u0065\u006e\u0020\u0069\u0066\u0020s\u0075\u0063\u0068\u0020\u006f\u0070\u0065r\u0061\u0074\u006f\u0072\u0073\u0020\u0061\u0072\u0065\u0020\u0062\u0072\u0061\u0063\u006b\u0065\u0074\u0065\u0064\u0020\u0062\u0079\u0020\u0074\u0068\u0065\u0020\u0042\u0058\u002f\u0045\u0058\u0020\u0063\u006f\u006d\u0070\u0061\u0074\u0069\u0062i\u006c\u0069\u0074\u0079\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072\u0073\u002e");
};};};return _ddd ;};func _ffce (_bfbd *_gde .Document ,_edee int ){if _bfbd .Version .Major ==0{_bfbd .Version .Major =1;};if _bfbd .Version .Minor < _edee {_bfbd .Version .Minor =_edee ;};};func _bcc (_adf *_a .XObjectImage ,_bgb imageModifications )error {_gac ,_fgb :=_adf .ToImage ();
if _fgb !=nil {return _fgb ;};if _bgb ._cab !=nil {_adf .Filter =_bgb ._cab ;};_bce :=_eb .MakeDict ();_bce .Set ("\u0051u\u0061\u006c\u0069\u0074\u0079",_eb .MakeInteger (100));_bce .Set ("\u0050r\u0065\u0064\u0069\u0063\u0074\u006fr",_eb .MakeInteger (1));
_adf .Decode =nil ;if _fgb =_adf .SetImage (_gac ,nil );_fgb !=nil {return _fgb ;};_adf .ToPdfObject ();return nil ;};func _bdac (_cada *_a .CompliancePdfReader )(_eaefd []ViolatedRule ){var _aabae ,_efaff ,_beaca ,_dead ,_dcbf ,_gede bool ;_abgcc :=func ()bool {return _aabae &&_efaff &&_beaca &&_dead &&_dcbf &&_gede };
for _ ,_cgced :=range _cada .PageList {if _cgced .Resources ==nil {continue ;};_gfba ,_gbeed :=_eb .GetDict (_cgced .Resources .Font );if !_gbeed {continue ;};for _ ,_dfdc :=range _gfba .Keys (){_dedc ,_fcdg :=_eb .GetDict (_gfba .Get (_dfdc ));if !_fcdg {if !_aabae {_eaefd =append (_eaefd ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0031","\u0041\u006c\u006c\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0061\u006e\u0064\u0020\u0066on\u0074 \u0070\u0072\u006fg\u0072\u0061\u006ds\u0020\u0075\u0073\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072mi\u006e\u0067\u0020\u0066\u0069\u006ce\u002c\u0020\u0072\u0065\u0067\u0061\u0072\u0064\u006c\u0065s\u0073\u0020\u006f\u0066\u0020\u0072\u0065\u006e\u0064\u0065\u0072\u0069\u006eg m\u006f\u0064\u0065\u0020\u0075\u0073\u0061\u0067\u0065\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0020\u0074o\u0020\u0074\u0068e\u0020\u0070\u0072o\u0076\u0069\u0073\u0069\u006f\u006e\u0073\u0020\u0069\u006e \u0049\u0053\u004f\u0020\u0033\u0032\u0030\u0030\u0030\u002d\u0031:\u0032\u0030\u0030\u0038\u002c \u0039\u002e\u0036\u0020a\u006e\u0064\u0020\u0039.\u0037\u002e"));
_aabae =true ;if _abgcc (){return _eaefd ;};};continue ;};if _efde ,_daae :=_eb .GetName (_dedc .Get ("\u0054\u0079\u0070\u0065"));!_aabae &&(!_daae ||_efde .String ()!="\u0046\u006f\u006e\u0074"){_eaefd =append (_eaefd ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0031","\u0054\u0079\u0070e\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075i\u0072\u0065\u0064\u0029 Th\u0065\u0020\u0074\u0079\u0070\u0065\u0020\u006f\u0066 \u0050\u0044\u0046\u0020\u006fbj\u0065\u0063\u0074\u0020\u0074\u0068\u0061t\u0020\u0074\u0068\u0069s\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0064\u0065\u0073c\u0072\u0069\u0062\u0065\u0073\u003b\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0046\u006f\u006e\u0074\u0020\u0066\u006fr\u0020\u0061\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
_aabae =true ;if _abgcc (){return _eaefd ;};};_ebbac ,_edada :=_a .NewPdfFontFromPdfObject (_dedc );if _edada !=nil {continue ;};var _cgbcg string ;if _cdggf ,_bfbef :=_eb .GetName (_dedc .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_bfbef {_cgbcg =_cdggf .String ();
};if !_efaff {switch _cgbcg {case "\u0054\u0079\u0070e\u0030","\u0054\u0079\u0070e\u0031","\u004dM\u0054\u0079\u0070\u0065\u0031","\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0030","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032":default:_efaff =true ;
_eaefd =append (_eaefd ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0032","\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065d\u0029\u0020\u0054\u0068e \u0074\u0079\u0070\u0065 \u006f\u0066\u0020\u0066\u006f\u006et\u003b\u0020\u006d\u0075\u0073\u0074\u0020b\u0065\u0020\u0022\u0054\u0079\u0070\u0065\u0031\u0022\u0020f\u006f\u0072\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020f\u006f\u006e\u0074\u0073\u002c\u0020\u0022\u004d\u004d\u0054\u0079\u0070\u0065\u0031\u0022\u0020\u0066\u006f\u0072\u0020\u006d\u0075\u006c\u0074\u0069\u0070\u006c\u0065\u0020\u006da\u0073\u0074e\u0072\u0020\u0066\u006f\u006e\u0074s\u002c\u0020\u0022\u0054\u0072\u0075\u0065T\u0079\u0070\u0065\u0022\u0020\u0066\u006f\u0072\u0020\u0054\u0072\u0075\u0065T\u0079\u0070\u0065\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0022\u0054\u0079\u0070\u0065\u0033\u0022\u0020\u0066\u006f\u0072\u0020\u0054\u0079\u0070e\u0020\u0033\u0020\u0066\u006f\u006e\u0074\u0073\u002c\u0020\"\u0054\u0079\u0070\u0065\u0030\"\u0020\u0066\u006f\u0072\u0020\u0054\u0079\u0070\u0065\u0020\u0030\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0061\u006ed\u0020\u0022\u0043\u0049\u0044\u0046\u006fn\u0074\u0054\u0079\u0070\u0065\u0030\u0022 \u006f\u0072\u0020\u0022\u0043\u0049\u0044\u0046\u006f\u006e\u0074T\u0079\u0070e\u0032\u0022\u0020\u0066\u006f\u0072\u0020\u0043\u0049\u0044\u0020\u0066\u006f\u006e\u0074\u0073\u002e"));
if _abgcc (){return _eaefd ;};};};if !_beaca {if _cgbcg !="\u0054\u0079\u0070e\u0033"{_dgfc ,_gcdg :=_eb .GetName (_dedc .Get ("\u0042\u0061\u0073\u0065\u0046\u006f\u006e\u0074"));if !_gcdg ||_dgfc .String ()==""{_eaefd =append (_eaefd ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0033","B\u0061\u0073\u0065\u0046\u006f\u006e\u0074\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065\u0064)\u0020T\u0068\u0065\u0020\u0050o\u0073\u0074S\u0063\u0072\u0069\u0070\u0074\u0020\u006e\u0061\u006d\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u002e"));
_beaca =true ;if _abgcc (){return _eaefd ;};};};};if _cgbcg !="\u0054\u0079\u0070e\u0031"{continue ;};_cabcd :=_bad .IsStdFont (_bad .StdFontName (_ebbac .BaseFont ()));if _cabcd {continue ;};_dfae ,_gfef :=_eb .GetIntVal (_dedc .Get ("\u0046i\u0072\u0073\u0074\u0043\u0068\u0061r"));
if !_gfef &&!_dead {_eaefd =append (_eaefd ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0034","\u0046\u0069r\u0073t\u0043\u0068\u0061\u0072\u0020\u002d\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075i\u0072\u0065\u0064\u0020\u0065\u0078\u0063\u0065\u0070t\u0020\u0066\u006f\u0072\u0020\u0074h\u0065\u0020\u0073\u0074\u0061\u006e\u0064\u0061\u0072d\u0020\u0031\u0034\u0020\u0066\u006f\u006e\u0074\u0073\u0029\u0020\u0054\u0068\u0065\u0020\u0066\u0069\u0072\u0073\u0074\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u0064e\u0020\u0064\u0065\u0066i\u006ee\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0027\u0073\u0020\u0057i\u0064\u0074\u0068\u0073 \u0061r\u0072\u0061y\u002e"));
_dead =true ;if _abgcc (){return _eaefd ;};};_gabefd ,_acgae :=_eb .GetIntVal (_dedc .Get ("\u004c\u0061\u0073\u0074\u0043\u0068\u0061\u0072"));if !_acgae &&!_dcbf {_eaefd =append (_eaefd ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0035","\u004c\u0061\u0073t\u0043\u0068\u0061\u0072\u0020\u002d\u0020\u0069n\u0074\u0065\u0067e\u0072 \u002d\u0020\u0028\u0052\u0065\u0071u\u0069\u0072\u0065d\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0066\u006f\u0072\u0020t\u0068\u0065 s\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0031\u0034\u0020\u0066\u006f\u006ets\u0029\u0020\u0054\u0068\u0065\u0020\u006c\u0061\u0073t\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u0064\u0065\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0027\u0073\u0020\u0057\u0069\u0064\u0074h\u0073\u0020\u0061\u0072\u0072\u0061\u0079\u002e"));
_dcbf =true ;if _abgcc (){return _eaefd ;};};if !_gede {_daea ,_aefgg :=_eb .GetArray (_dedc .Get ("\u0057\u0069\u0064\u0074\u0068\u0073"));if !_aefgg ||!_gfef ||!_acgae ||_daea .Len ()!=_gabefd -_dfae +1{_eaefd =append (_eaefd ,_ee ("\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0032\u002d\u0036","\u0057\u0069\u0064\u0074\u0068\u0073\u0020\u002d a\u0072\u0072\u0061y \u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0065\u0078\u0063\u0065\u0070t\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0073\u0074a\u006e\u0064a\u0072\u0064\u00201\u0034\u0020\u0066\u006f\u006e\u0074\u0073\u003b\u0020\u0069\u006ed\u0069\u0072\u0065\u0063\u0074\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0070\u0072\u0065\u0066e\u0072\u0072e\u0064\u0029\u0020\u0041\u006e \u0061\u0072\u0072\u0061\u0079\u0020\u006f\u0066\u0020\u0028\u004c\u0061\u0073\u0074\u0043\u0068\u0061\u0072\u0020\u2212 F\u0069\u0072\u0073\u0074\u0043\u0068\u0061\u0072\u0020\u002b\u00201\u0029\u0020\u0077\u0069\u0064\u0074\u0068\u0073."));
_gede =true ;if _abgcc (){return _eaefd ;};};};};};return _eaefd ;};

// Profile is the model.StandardImplementer enhanced by the information about the profile conformance level.
type Profile interface{_a .StandardImplementer ;Conformance ()string ;Part ()int ;};func _bagb (_dgdc *_a .CompliancePdfReader )(_cagad ViolatedRule ){for _ ,_cfgd :=range _dgdc .GetObjectNums (){_cfdb ,_fdaa :=_dgdc .GetIndirectObjectByNumber (_cfgd );
if _fdaa !=nil {continue ;};_ddge ,_gace :=_eb .GetStream (_cfdb );if !_gace {continue ;};_gbbe ,_gace :=_eb .GetName (_ddge .Get ("\u0054\u0079\u0070\u0065"));if !_gace {continue ;};if *_gbbe !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_beedc ,_gace :=_eb .GetName (_ddge .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032"));
if !_gace {continue ;};if *_beedc =="\u0050\u0053"{return _ee ("\u0036.\u0032\u002e\u0035\u002d\u0031","A\u0020\u0066\u006fr\u006d\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032\u0020\u006b\u0065\u0079 \u0077\u0069\u0074\u0068\u0020a\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u0020o\u0072\u0020\u0074\u0068e\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};if _ddge .Get ("\u0050\u0053")!=nil {return _ee ("\u0036.\u0032\u002e\u0035\u002d\u0031","A\u0020\u0066\u006fr\u006d\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032\u0020\u006b\u0065\u0079 \u0077\u0069\u0074\u0068\u0020a\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u0020o\u0072\u0020\u0074\u0068e\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};};return _cagad ;};func _fdfa (_efddb *_a .CompliancePdfReader )(_eegf ViolatedRule ){for _ ,_gdedg :=range _efddb .GetObjectNums (){_gfabc ,_aaaa :=_efddb .GetIndirectObjectByNumber (_gdedg );if _aaaa !=nil {continue ;};_dbbb ,_aafgd :=_eb .GetStream (_gfabc );
if !_aafgd {continue ;};_fabdf ,_aafgd :=_eb .GetName (_dbbb .Get ("\u0054\u0079\u0070\u0065"));if !_aafgd {continue ;};if *_fabdf !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_ ,_aafgd =_eb .GetName (_dbbb .Get ("\u004f\u0050\u0049"));if _aafgd {return _ee ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072m\u0020\u0058\u004f\u0062\u006a\u0065c\u0074\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0020\u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u003a \u002d\u0020\u0074\u0068\u0065\u0020O\u0050\u0049\u0020\u006b\u0065\u0079\u003b \u002d\u0020\u0074\u0068e \u0053u\u0062\u0074\u0079\u0070\u0065\u0032 ke\u0079 \u0077\u0069t\u0068\u0020\u0061\u0020\u0076\u0061l\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u003b\u0020\u002d \u0074\u0068\u0065\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};_gdgd ,_aafgd :=_eb .GetName (_dbbb .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032"));if !_aafgd {continue ;};if *_gdgd =="\u0050\u0053"{return _ee ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072m\u0020\u0058\u004f\u0062\u006a\u0065c\u0074\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0020\u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u003a \u002d\u0020\u0074\u0068\u0065\u0020O\u0050\u0049\u0020\u006b\u0065\u0079\u003b \u002d\u0020\u0074\u0068e \u0053u\u0062\u0074\u0079\u0070\u0065\u0032 ke\u0079 \u0077\u0069t\u0068\u0020\u0061\u0020\u0076\u0061l\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u003b\u0020\u002d \u0074\u0068\u0065\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};if _dbbb .Get ("\u0050\u0053")!=nil {return _ee ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072m\u0020\u0058\u004f\u0062\u006a\u0065c\u0074\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0020\u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u003a \u002d\u0020\u0074\u0068\u0065\u0020O\u0050\u0049\u0020\u006b\u0065\u0079\u003b \u002d\u0020\u0074\u0068e \u0053u\u0062\u0074\u0079\u0070\u0065\u0032 ke\u0079 \u0077\u0069t\u0068\u0020\u0061\u0020\u0076\u0061l\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u003b\u0020\u002d \u0074\u0068\u0065\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e");
};};return _eegf ;};func _bgec (_ffabf *_a .CompliancePdfReader )(_aaba []ViolatedRule ){if _ffabf .ParserMetadata ().HasOddLengthHexStrings (){_aaba =append (_aaba ,_ee ("\u0036.\u0031\u002e\u0036\u002d\u0031","\u0068\u0065\u0078a\u0064\u0065\u0063\u0069\u006d\u0061\u006c\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0073\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020\u006f\u0066\u0020e\u0076\u0065\u006e\u0020\u0073\u0069\u007a\u0065"));
};if _ffabf .ParserMetadata ().HasOddLengthHexStrings (){_aaba =append (_aaba ,_ee ("\u0036.\u0031\u002e\u0036\u002d\u0032","\u0068\u0065\u0078\u0061\u0064\u0065\u0063\u0069\u006da\u006c\u0020s\u0074\u0072\u0069\u006e\u0067\u0073\u0020\u0073\u0068o\u0075\u006c\u0064\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u006f\u006e\u006c\u0079\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0073\u0020\u0066\u0072\u006f\u006d\u0020\u0072\u0061n\u0067\u0065\u0020[\u0030\u002d\u0039\u003b\u0041\u002d\u0046\u003b\u0061\u002d\u0066\u005d"));
};return _aaba ;};func _eecee (_aegg *_a .CompliancePdfReader )ViolatedRule {if _aegg .ParserMetadata ().HeaderPosition ()!=0{return _ee ("\u0036.\u0031\u002e\u0032\u002d\u0031","h\u0065\u0061\u0064\u0065\u0072\u0020\u0070\u006f\u0073\u0069\u0074\u0069\u006f\u006e\u0020\u0069\u0073\u0020n\u006f\u0074\u0020\u0061\u0074\u0020\u0074\u0068\u0065\u0020fi\u0072\u0073\u0074 \u0062y\u0074\u0065");
};if _aegg .PdfVersion ().Major !=1{return _ee ("\u0036.\u0031\u002e\u0032\u002d\u0031","\u0054\u0068\u0065\u0020\u0066\u0069l\u0065\u0020\u0068\u0065\u0061\u0064e\u0072 \u0073\u0068\u0061\u006c\u006c\u0020c\u006f\u006e\u0073\u0069s\u0074 \u006f\u0066\u0020\u201c%\u0050\u0044\u0046\u002d\u0031\u002e\u006e\u201d\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0065\u0064\u0020\u0062\u0079\u0020\u0061\u0020\u0073\u0069\u006e\u0067\u006c\u0065 \u0045\u004f\u004c\u0020ma\u0072\u006b\u0065\u0072\u002c \u0077\u0068\u0065\u0072\u0065\u0020\u0027\u006e\u0027\u0020\u0069s\u0020\u0061\u0020\u0073\u0069\u006e\u0067\u006c\u0065\u0020\u0064\u0069\u0067\u0069t\u0020\u006e\u0075\u006d\u0062e\u0072\u0020\u0062\u0065\u0074\u0077\u0065\u0065\u006e\u0020\u0030\u0020(\u0033\u0030h\u0029\u0020\u0061\u006e\u0064\u0020\u0037\u0020\u0028\u0033\u0037\u0068\u0029");
};if _aegg .PdfVersion ().Minor < 0||_aegg .PdfVersion ().Minor > 7{return _ee ("\u0036.\u0031\u002e\u0032\u002d\u0031","\u0054\u0068\u0065\u0020\u0066\u0069l\u0065\u0020\u0068\u0065\u0061\u0064e\u0072 \u0073\u0068\u0061\u006c\u006c\u0020c\u006f\u006e\u0073\u0069s\u0074 \u006f\u0066\u0020\u201c%\u0050\u0044\u0046\u002d\u0031\u002e\u006e\u201d\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0065\u0064\u0020\u0062\u0079\u0020\u0061\u0020\u0073\u0069\u006e\u0067\u006c\u0065 \u0045\u004f\u004c\u0020ma\u0072\u006b\u0065\u0072\u002c \u0077\u0068\u0065\u0072\u0065\u0020\u0027\u006e\u0027\u0020\u0069s\u0020\u0061\u0020\u0073\u0069\u006e\u0067\u006c\u0065\u0020\u0064\u0069\u0067\u0069t\u0020\u006e\u0075\u006d\u0062e\u0072\u0020\u0062\u0065\u0074\u0077\u0065\u0065\u006e\u0020\u0030\u0020(\u0033\u0030h\u0029\u0020\u0061\u006e\u0064\u0020\u0037\u0020\u0028\u0033\u0037\u0068\u0029");
};return _ddd ;};func _gedb (_ddfdb *_eb .PdfObjectDictionary )ViolatedRule {const (_cdegf ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0033\u002d\u0032";_fdabf ="IS\u004f\u0020\u0033\u0032\u0030\u0030\u0030\u002d\u0031\u003a\u0032\u0030\u0030\u0038\u002c\u00209\u002e\u0037\u002e\u0034\u002c\u0020\u0054\u0061\u0062\u006c\u0065\u0020\u0031\u0031\u0037\u0020\u0072\u0065\u0071\u0075\u0069\u0072\u0065\u0073\u0020\u0074\u0068a\u0074\u0020\u0061\u006c\u006c\u0020\u0065m\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0054\u0079\u0070\u0065\u0020\u0032\u0020\u0043\u0049\u0044\u0046\u006fn\u0074\u0073\u0020\u0069n\u0020t\u0068e\u0020\u0043\u0049D\u0046\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020\u0073\u0068a\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020\u0043\u0049\u0044\u0054\u006fG\u0049\u0044M\u0061\u0070\u0020\u0065\u006e\u0074\u0072\u0079 \u0074\u0068\u0061\u0074\u0020\u0073\u0068\u0061\u006c\u006c \u0062e\u0020\u0061\u0020\u0073t\u0072\u0065\u0061\u006d\u0020\u006d\u0061\u0070p\u0069\u006e\u0067 f\u0072\u006f\u006d \u0043\u0049\u0044\u0073\u0020\u0074\u006f\u0020\u0067\u006c\u0079p\u0068 \u0069\u006e\u0064\u0069c\u0065\u0073\u0020\u006fr\u0020\u0074\u0068\u0065\u0020\u006e\u0061\u006d\u0065\u0020\u0049d\u0065\u006e\u0074\u0069\u0074\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0049\u0053\u004f\u0020\u0033\u0032\u0030\u0030\u0030\u002d\u0031\u003a\u0032\u0030\u0030\u0038\u002c\u0020\u0039\u002e\u0037\u002e\u0034\u002c\u0020\u0054\u0061\u0062\u006c\u0065\u0020\u0031\u0031\u0037\u002e";
);var _gfgb string ;if _dfdcf ,_gcac :=_eb .GetName (_ddfdb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_gcac {_gfgb =_dfdcf .String ();};if _gfgb !="\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032"{return _ddd ;};if _ddfdb .Get ("C\u0049\u0044\u0054\u006f\u0047\u0049\u0044\u004d\u0061\u0070")==nil {return _ee (_cdegf ,_fdabf );
};return _ddd ;};func _ccfg (_effb *_gde .Document ,_fbgc int )error {_bee :=map[*_eb .PdfObjectStream ]struct{}{};for _ ,_dcbd :=range _effb .Objects {_acac ,_bcdbd :=_eb .GetStream (_dcbd );if !_bcdbd {continue ;};if _ ,_bcdbd =_bee [_acac ];_bcdbd {continue ;
};_bee [_acac ]=struct{}{};_edbd ,_bcdbd :=_eb .GetName (_acac .Get ("\u0053u\u0062\u0054\u0079\u0070\u0065"));if !_bcdbd {continue ;};if _acac .Get ("\u0052\u0065\u0066")!=nil {_acac .Remove ("\u0052\u0065\u0066");};if _edbd .String ()=="\u0050\u0053"{_acac .Remove ("\u0050\u0053");
continue ;};if _edbd .String ()=="\u0046\u006f\u0072\u006d"{if _acac .Get ("\u004f\u0050\u0049")!=nil {_acac .Remove ("\u004f\u0050\u0049");};if _acac .Get ("\u0050\u0053")!=nil {_acac .Remove ("\u0050\u0053");};if _dcg :=_acac .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032");
_dcg !=nil {if _dfbe ,_eecc :=_eb .GetName (_dcg );_eecc &&*_dfbe =="\u0050\u0053"{_acac .Remove ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032");};};continue ;};if _edbd .String ()=="\u0049\u006d\u0061g\u0065"{_gdee ,_eacf :=_eb .GetBool (_acac .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065"));
if _eacf &&bool (*_gdee ){_acac .Set ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065",_eb .MakeBool (false ));};if _fbgc ==2{if _acac .Get ("\u004f\u0050\u0049")!=nil {_acac .Remove ("\u004f\u0050\u0049");};};if _acac .Get ("\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073")!=nil {_acac .Remove ("\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073");
};continue ;};};return nil ;};

// Conformance gets the PDF/A conformance.
func (_caec *profile3 )Conformance ()string {return _caec ._fddb ._ff };func _adagc (_eagf *Profile1Options ){if _eagf .Now ==nil {_eagf .Now =_ca .Now ;};};

// NewProfile3U creates a new Profile3U with the given options.
func NewProfile3U (options *Profile3Options )*Profile3U {if options ==nil {options =DefaultProfile3Options ();};_cceg (options );return &Profile3U {profile3 {_afba :*options ,_fddb :_fgd ()}};};func _eg ()standardType {return standardType {_dda :2,_ff :"\u0055"}};
func _baeda (_aage *_a .PdfFont ,_deggg *_eb .PdfObjectDictionary )ViolatedRule {const (_fefd ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0036\u002d\u0033";_dbbdf ="\u0041l\u006c\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0069\u0063\u0020\u0054\u0072u\u0065\u0054\u0079p\u0065\u0020\u0066\u006f\u006e\u0074s\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0061\u006e\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0065n\u0074\u0072\u0079\u0020\u0069n\u0020\u0074\u0068e\u0020\u0066\u006f\u006e\u0074 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e";
);var _bfab string ;if _cbeb ,_eeedc :=_eb .GetName (_deggg .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_eeedc {_bfab =_cbeb .String ();};if _bfab !="\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065"{return _ddd ;};_gbcc :=_aage .FontDescriptor ();
_ggada ,_dcfe :=_eb .GetIntVal (_gbcc .Flags );if !_dcfe {_bf .Log .Debug ("\u0066\u006c\u0061\u0067\u0073 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0066o\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u006f\u0072");
return _ee (_fefd ,_dbbdf );};_eggd :=(uint32 (_ggada )>>3)&1;_ecdb :=_eggd !=0;if !_ecdb {return _ddd ;};if _deggg .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067")!=nil {return _ee (_fefd ,_dbbdf );};return _ddd ;};func _efeg (_cea *_a .CompliancePdfReader )(_baeg ViolatedRule ){for _ ,_aaaf :=range _cea .GetObjectNums (){_ced ,_afege :=_cea .GetIndirectObjectByNumber (_aaaf );
if _afege !=nil {continue ;};_bbfc ,_decf :=_eb .GetStream (_ced );if !_decf {continue ;};_abdg ,_decf :=_eb .GetName (_bbfc .Get ("\u0054\u0079\u0070\u0065"));if !_decf {continue ;};if *_abdg !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};if _bbfc .Get ("\u0052\u0065\u0066")!=nil {return _ee ("\u0036.\u0032\u002e\u0036\u002d\u0031","\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0058O\u0062\u006a\u0065\u0063\u0074s\u002e");
};};return _baeg ;};func _ddcc (_ffdde *_a .CompliancePdfReader ,_aefce bool )(_ffeca []ViolatedRule ){var _bfgga ,_eaeg ,_gfab ,_egdd ,_fgbfa ,_bbaac ,_ggeee bool ;_daaa :=func ()bool {return _bfgga &&_eaeg &&_gfab &&_egdd &&_fgbfa &&_bbaac &&_ggeee };
_ebeb ,_eagd :=_cabcg (_ffdde );var _aaff _bea .ProfileHeader ;if _eagd {_aaff ,_ =_bea .ParseHeader (_ebeb .DestOutputProfile );};var _dbccg bool ;_gaad :=map[_eb .PdfObject ]struct{}{};var _gcgd func (_dgad _a .PdfColorspace )bool ;_gcgd =func (_caadf _a .PdfColorspace )bool {switch _acfe :=_caadf .(type ){case *_a .PdfColorspaceDeviceGray :if !_bbaac {if !_eagd {_dbccg =true ;
_ffeca =append (_ffeca ,_ee ("\u0036.\u0032\u002e\u0033\u002d\u0034","\u0044\u0065\u0076\u0069\u0063\u0065G\u0072\u0061\u0079\u0020\u006da\u0079\u0020\u0062\u0065\u0020\u0075s\u0065\u0064\u0020\u006f\u006el\u0079\u0020\u0069\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006ce\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020O\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074e\u006e\u0074\u002e"));
_bbaac =true ;if _daaa (){return true ;};};};case *_a .PdfColorspaceDeviceRGB :if !_egdd {if !_eagd ||_aaff .ColorSpace !=_bea .ColorSpaceRGB {_dbccg =true ;_ffeca =append (_ffeca ,_ee ("\u0036.\u0032\u002e\u0033\u002d\u0032","\u0044\u0065\u0076\u0069\u0063\u0065\u0052\u0047\u0042\u0020\u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u006f\u006e\u006c\u0079\u0020\u0069\u0066\u0020\u0074\u0068\u0065 \u0066\u0069\u006c\u0065\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074\u0070\u0075\u0074In\u0074\u0065\u006e\u0074\u0020\u0074\u0068\u0061\u0074\u0020u\u0073es\u0020a\u006e\u0020\u0052\u0047\u0042\u0020\u0063o\u006c\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u002e"));
_egdd =true ;if _daaa (){return true ;};};};case *_a .PdfColorspaceDeviceCMYK :if !_fgbfa {if !_eagd ||_aaff .ColorSpace !=_bea .ColorSpaceCMYK {_dbccg =true ;_ffeca =append (_ffeca ,_ee ("\u0036.\u0032\u002e\u0033\u002d\u0033","\u0044\u0065\u0076\u0069\u0063e\u0043\u004d\u0059\u004b \u006d\u0061\u0079\u0020\u0062\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u006f\u006e\u006c\u0079\u0020\u0069\u0066\u0020\u0074h\u0065\u0020\u0066\u0069\u006ce \u0068\u0061\u0073\u0020\u0061 \u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074p\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0074\u0068a\u0074\u0020\u0075\u0073\u0065\u0073\u0020\u0061\u006e \u0043\u004d\u0059\u004b\u0020\u0063\u006f\u006c\u006f\u0072\u0020s\u0070\u0061\u0063e\u002e"));
_fgbfa =true ;if _daaa (){return true ;};};};case *_a .PdfColorspaceICCBased :if !_gfab ||!_ggeee {_ebegg ,_gdaa :=_bea .ParseHeader (_acfe .Data );if _gdaa !=nil {_bf .Log .Debug ("\u0070\u0061\u0072si\u006e\u0067\u0020\u0049\u0043\u0043\u0042\u0061\u0073e\u0064 \u0068e\u0061d\u0065\u0072\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_gdaa );
_ffeca =append (_ffeca ,func ()ViolatedRule {return _ee ("\u0036.\u0032\u002e\u0033\u002d\u0031","\u0041\u006cl \u0049\u0043\u0043\u0042\u0061\u0073\u0065\u0064\u0020\u0063\u006f\u006co\u0072\u0020\u0073\u0070a\u0063e\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0061\u0073\u0020\u0049\u0043\u0043 \u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074\u0072\u0065a\u006d\u0073 \u0061\u0073\u0020d\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020R\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0034\u002e\u0035");
}());_gfab =true ;if _daaa (){return true ;};};if !_gfab {var _eeab ,_gbfd bool ;switch _ebegg .DeviceClass {case _bea .DeviceClassPRTR ,_bea .DeviceClassMNTR ,_bea .DeviceClassSCNR ,_bea .DeviceClassSPAC :default:_eeab =true ;};switch _ebegg .ColorSpace {case _bea .ColorSpaceRGB ,_bea .ColorSpaceCMYK ,_bea .ColorSpaceGRAY ,_bea .ColorSpaceLAB :default:_gbfd =true ;
};if _eeab ||_gbfd {_ffeca =append (_ffeca ,_ee ("\u0036.\u0032\u002e\u0033\u002d\u0031","\u0041\u006cl \u0049\u0043\u0043\u0042\u0061\u0073\u0065\u0064\u0020\u0063\u006f\u006co\u0072\u0020\u0073\u0070a\u0063e\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0061\u0073\u0020\u0049\u0043\u0043 \u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074\u0072\u0065a\u006d\u0073 \u0061\u0073\u0020d\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020R\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0034\u002e\u0035"));
_gfab =true ;if _daaa (){return true ;};};};if !_ggeee {_cacfc ,_ :=_eb .GetStream (_acfe .GetContainingPdfObject ());if _cacfc .Get ("\u004e")==nil ||(_acfe .N ==1&&_ebegg .ColorSpace !=_bea .ColorSpaceGRAY )||(_acfe .N ==3&&!(_ebegg .ColorSpace ==_bea .ColorSpaceRGB ||_ebegg .ColorSpace ==_bea .ColorSpaceLAB ))||(_acfe .N ==4&&_ebegg .ColorSpace !=_bea .ColorSpaceCMYK ){_ffeca =append (_ffeca ,_ee ("\u0036.\u0032\u002e\u0033\u002d\u0035","\u0049\u0066\u0020a\u006e\u0020u\u006e\u0063\u0061\u006c\u0069\u0062\u0072a\u0074\u0065\u0064\u0020\u0063\u006fl\u006f\u0072 \u0073\u0070\u0061c\u0065\u0020\u0069\u0073\u0020\u0075\u0073\u0065\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0066\u0069\u006c\u0065 \u0074\u0068\u0065\u006e \u0074\u0068\u0061\u0074 \u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063o\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041-\u0031\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065d\u0020\u0069\u006e\u0020\u0036\u002e\u0032\u002e\u0032\u002e"));
_ggeee =true ;if _daaa (){return true ;};};};};if _acfe .Alternate !=nil {return _gcgd (_acfe .Alternate );};};return false ;};for _ ,_agab :=range _ffdde .GetObjectNums (){_ggbd ,_ggad :=_ffdde .GetIndirectObjectByNumber (_agab );if _ggad !=nil {continue ;
};_bffd ,_abg :=_eb .GetStream (_ggbd );if !_abg {continue ;};_bbfe ,_abg :=_eb .GetName (_bffd .Get ("\u0054\u0079\u0070\u0065"));if !_abg ||_bbfe .String ()!="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};_edeac ,_abg :=_eb .GetName (_bffd .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_abg {continue ;};_gaad [_bffd ]=struct{}{};switch _edeac .String (){case "\u0049\u006d\u0061g\u0065":_gdff ,_ccad :=_a .NewXObjectImageFromStream (_bffd );if _ccad !=nil {continue ;};_gaad [_bffd ]=struct{}{};if _gcgd (_gdff .ColorSpace ){return _ffeca ;
};case "\u0046\u006f\u0072\u006d":_beag ,_dabf :=_eb .GetDict (_bffd .Get ("\u0047\u0072\u006fu\u0070"));if !_dabf {continue ;};_bbeac :=_beag .Get ("\u0043\u0053");if _bbeac ==nil {continue ;};_aagbf ,_dcfa :=_a .NewPdfColorspaceFromPdfObject (_bbeac );
if _dcfa !=nil {continue ;};if _gcgd (_aagbf ){return _ffeca ;};};};for _ ,_bcee :=range _ffdde .PageList {_bfaa ,_aced :=_bcee .GetContentStreams ();if _aced !=nil {continue ;};for _ ,_bfac :=range _bfaa {_dbde ,_fedd :=_e .NewContentStreamParser (_bfac ).Parse ();
if _fedd !=nil {continue ;};for _ ,_bceea :=range *_dbde {if len (_bceea .Params )> 1{continue ;};switch _bceea .Operand {case "\u0042\u0049":_fcbe ,_baf :=_bceea .Params [0].(*_e .ContentStreamInlineImage );if !_baf {continue ;};_gcagf ,_cfff :=_fcbe .GetColorSpace (_bcee .Resources );
if _cfff !=nil {continue ;};if _gcgd (_gcagf ){return _ffeca ;};case "\u0044\u006f":_gbag ,_bdeb :=_eb .GetName (_bceea .Params [0]);if !_bdeb {continue ;};_abdb ,_cgbc :=_bcee .Resources .GetXObjectByName (*_gbag );if _ ,_fgge :=_gaad [_abdb ];_fgge {continue ;
};switch _cgbc {case _a .XObjectTypeImage :_abge ,_bfbgc :=_a .NewXObjectImageFromStream (_abdb );if _bfbgc !=nil {continue ;};_gaad [_abdb ]=struct{}{};if _gcgd (_abge .ColorSpace ){return _ffeca ;};case _a .XObjectTypeForm :_degdc ,_faeb :=_eb .GetDict (_abdb .Get ("\u0047\u0072\u006fu\u0070"));
if !_faeb {continue ;};_aaa ,_faeb :=_eb .GetName (_degdc .Get ("\u0043\u0053"));if !_faeb {continue ;};_edd ,_gcfa :=_a .NewPdfColorspaceFromPdfObject (_aaa );if _gcfa !=nil {continue ;};_gaad [_abdb ]=struct{}{};if _gcgd (_edd ){return _ffeca ;};};};
};};};if !_dbccg {return _ffeca ;};if (_aaff .DeviceClass ==_bea .DeviceClassPRTR ||_aaff .DeviceClass ==_bea .DeviceClassMNTR )&&(_aaff .ColorSpace ==_bea .ColorSpaceRGB ||_aaff .ColorSpace ==_bea .ColorSpaceCMYK ||_aaff .ColorSpace ==_bea .ColorSpaceGRAY ){return _ffeca ;
};if !_aefce {return _ffeca ;};_dacd ,_ecceg :=_efcc (_ffdde );if !_ecceg {return _ffeca ;};_bbeg ,_ecceg :=_eb .GetArray (_dacd .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073"));if !_ecceg {_ffeca =append (_ffeca ,_ee ("\u0036.\u0032\u002e\u0032\u002d\u0031","\u0041\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074p\u0075\u0074\u0049\u006e\u0074e\u006e\u0074\u0020\u0069\u0073\u0020a\u006e \u004f\u0075\u0074\u0070\u0075\u0074\u0049n\u0074\u0065\u006e\u0074\u0020\u0064i\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0062y\u0020\u0050\u0044F\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0039\u002e\u0031\u0030.4\u002c\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0073 \u0069\u006e\u0063\u006c\u0075\u0064e\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0027\u0073\u0020O\u0075\u0074p\u0075\u0074I\u006e\u0074\u0065\u006e\u0074\u0073\u0020\u0061\u0072\u0072\u0061\u0079\u0020a\u006e\u0064\u0020h\u0061\u0073\u0020\u0047\u0054\u0053\u005f\u0050\u0044\u0046\u0041\u0031\u0020\u0061\u0073 \u0074\u0068\u0065\u0020\u0076a\u006c\u0075e\u0020\u006f\u0066\u0020i\u0074\u0073 \u0053\u0020\u006b\u0065\u0079\u0020\u0061\u006e\u0064\u0020\u0061\u0020\u0076\u0061\u006c\u0069\u0064\u0020I\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006ce\u0020s\u0074\u0072\u0065\u0061\u006d \u0061\u0073\u0020\u0074h\u0065\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0074\u0073\u0020\u0044\u0065\u0073t\u004f\u0075t\u0070\u0075\u0074P\u0072\u006f\u0066\u0069\u006c\u0065 \u006b\u0065\u0079\u002e"),_ee ("\u0036.\u0032\u002e\u0032\u002d\u0032","\u0049\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065's\u0020O\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073 \u0061\u0072\u0072a\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0073\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068a\u006e\u0020\u006f\u006ee\u0020\u0065\u006e\u0074\u0072\u0079\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0065n\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e a \u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006cl\u0020\u0068\u0061\u0076\u0065 \u0061\u0073\u0020\u0074\u0068\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068a\u0074\u0020\u006b\u0065\u0079 \u0074\u0068\u0065\u0020\u0073\u0061\u006d\u0065\u0020\u0069\u006e\u0064\u0069\u0072\u0065c\u0074\u0020\u006fb\u006ae\u0063t\u002c\u0020\u0077h\u0069\u0063\u0068\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u0061\u0020\u0076\u0061\u006c\u0069d\u0020\u0049\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074r\u0065\u0061m\u002e"));
return _ffeca ;};if _bbeg .Len ()> 1{_caed :=map[*_eb .PdfObjectDictionary ]struct{}{};for _fecgg :=0;_fecgg < _bbeg .Len ();_fecgg ++{_aefa ,_cfed :=_eb .GetDict (_bbeg .Get (_fecgg ));if !_cfed {continue ;};if _fecgg ==0{_caed [_aefa ]=struct{}{};continue ;
};if _ ,_bgde :=_caed [_aefa ];!_bgde {_ffeca =append (_ffeca ,_ee ("\u0036.\u0032\u002e\u0032\u002d\u0032","\u0049\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065's\u0020O\u0075\u0074\u0070u\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073 \u0061\u0072\u0072a\u0079\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0073\u0020\u006d\u006f\u0072\u0065\u0020\u0074\u0068a\u006e\u0020\u006f\u006ee\u0020\u0065\u006e\u0074\u0072\u0079\u002c\u0020\u0074\u0068\u0065\u006e\u0020\u0061\u006c\u006c\u0020\u0065n\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e a \u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006cl\u0020\u0068\u0061\u0076\u0065 \u0061\u0073\u0020\u0074\u0068\u0065 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068a\u0074\u0020\u006b\u0065\u0079 \u0074\u0068\u0065\u0020\u0073\u0061\u006d\u0065\u0020\u0069\u006e\u0064\u0069\u0072\u0065c\u0074\u0020\u006fb\u006ae\u0063t\u002c\u0020\u0077h\u0069\u0063\u0068\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u0061\u0020\u0076\u0061\u006c\u0069d\u0020\u0049\u0043\u0043\u0020\u0070\u0072\u006f\u0066\u0069\u006c\u0065\u0020\u0073\u0074r\u0065\u0061m\u002e"));
break ;};};};return _ffeca ;};

// NewProfile1B creates a new Profile1B with the given options.
func NewProfile1B (options *Profile1Options )*Profile1B {if options ==nil {options =DefaultProfile1Options ();};_adagc (options );return &Profile1B {profile1 {_bdbb :*options ,_ecd :_eag ()}};};func _bcgbg (_abbc *_a .PdfFont ,_cecee *_eb .PdfObjectDictionary ,_ebfbc bool )ViolatedRule {const (_eaaca ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0034\u002d\u0031";
_ebfe ="\u0054\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0070\u0072\u006f\u0067\u0072\u0061\u006ds\u0020\u0066\u006fr\u0020\u0061\u006c\u006c\u0020f\u006f\u006e\u0074\u0073\u0020\u0075\u0073\u0065\u0064\u0020\u0066\u006f\u0072\u0020\u0072e\u006e\u0064\u0065\u0072\u0069\u006eg\u0020\u0077\u0069\u0074\u0068\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020w\u0069t\u0068\u0069\u006e\u0020\u0074\u0068\u0061\u0074\u0020\u0066\u0069\u006c\u0065\u002c \u0061\u0073\u0020\u0064\u0065\u0066\u0069n\u0065\u0064 \u0069\u006e\u0020\u0049S\u004f\u0020\u0033\u0032\u00300\u0030\u002d\u0031\u003a\u0032\u0030\u0030\u0038\u002c\u0020\u0039\u002e\u0039\u002e";
);if _ebfbc {return _ddd ;};_fdac :=_abbc .FontDescriptor ();var _acff string ;if _gddcg ,_gddb :=_eb .GetName (_cecee .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_gddb {_acff =_gddcg .String ();};switch _acff {case "\u0054\u0079\u0070e\u0031":if _fdac .FontFile ==nil {return _ee (_eaaca ,_ebfe );
};case "\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065":if _fdac .FontFile2 ==nil {return _ee (_eaaca ,_ebfe );};case "\u0054\u0079\u0070e\u0030","\u0054\u0079\u0070e\u0033":default:if _fdac .FontFile3 ==nil {return _ee (_eaaca ,_ebfe );};};return _ddd ;
};func _dadd (_faec *_a .CompliancePdfReader )ViolatedRule {if _faec .ParserMetadata ().HasDataAfterEOF (){return _ee ("\u0036.\u0031\u002e\u0033\u002d\u0033","\u004e\u006f\u0020\u0064\u0061ta\u0020\u0073h\u0061\u006c\u006c\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0020\u0074\u0068\u0065\u0020\u006c\u0061\u0073\u0074\u0020\u0065\u006e\u0064\u002d\u006f\u0066\u002d\u0066\u0069l\u0065\u0020\u006da\u0072\u006b\u0065\u0072\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0061 \u0073\u0069\u006e\u0067\u006ce\u0020\u006f\u0070\u0074\u0069\u006f\u006e\u0061\u006c \u0065\u006ed\u002do\u0066\u002d\u006c\u0069\u006e\u0065\u0020m\u0061\u0072\u006b\u0065\u0072\u002e");
};return _ddd ;};func _fdef (_bcgec *_gde .Document )error {_eeaa ,_bcdd :=_bcgec .FindCatalog ();if !_bcdd {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};_aeg ,_bcdd :=_eb .GetDict (_eeaa .Object .Get ("\u004e\u0061\u006de\u0073"));
if !_bcdd {return nil ;};if _aeg .Get ("\u0041\u006c\u0074\u0065rn\u0061\u0074\u0065\u0050\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006fn\u0073")!=nil {_aeg .Remove ("\u0041\u006c\u0074\u0065rn\u0061\u0074\u0065\u0050\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006fn\u0073");
};return nil ;};func _fac (_ggca *_a .CompliancePdfReader )[]ViolatedRule {return nil };

// Profile2B is the implementation of the PDF/A-2B standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile2B struct{profile2 };func _gead (_geag *_a .CompliancePdfReader )(_gce []ViolatedRule ){var (_ddbad ,_afg ,_cccfe ,_babd ,_fgfa ,_ebbd ,_ebbda bool ;_cacb func (_eb .PdfObject ););_cacb =func (_bffc _eb .PdfObject ){switch _cebdf :=_bffc .(type ){case *_eb .PdfObjectInteger :if !_ddbad &&(int64 (*_cebdf )> _g .MaxInt32 ||int64 (*_cebdf )< -_g .MaxInt32 ){_gce =append (_gce ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0031","L\u0061\u0072\u0067e\u0073\u0074\u0020\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u0032\u002c\u0031\u0034\u0037,\u0034\u0038\u0033,\u0036\u0034\u0037\u002e\u0020\u0053\u006d\u0061\u006c\u006c\u0065\u0073\u0074 \u0069\u006e\u0074\u0065g\u0065\u0072\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u002d\u0032\u002c\u0031\u0034\u0037\u002c\u0034\u0038\u0033,\u0036\u0034\u0038\u002e"));
_ddbad =true ;};case *_eb .PdfObjectFloat :if !_afg &&(_g .Abs (float64 (*_cebdf ))> 32767.0){_gce =append (_gce ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0032","\u0041\u0062\u0073\u006f\u006c\u0075\u0074\u0065\u0020\u0072\u0065\u0061\u006c\u0020\u0076\u0061\u006c\u0075\u0065\u0020m\u0075\u0073\u0074\u0020\u0062\u0065\u0020\u006c\u0065s\u0073\u0020\u0074\u0068\u0061\u006e\u0020\u006f\u0072\u0020\u0065\u0071\u0075a\u006c\u0020\u0074\u006f\u0020\u00332\u0037\u0036\u0037.\u0030\u002e"));
};case *_eb .PdfObjectString :if !_cccfe &&len ([]byte (_cebdf .Str ()))> 65535{_gce =append (_gce ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0033","M\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u006c\u0065n\u0067\u0074\u0068\u0020\u006f\u0066\u0020a \u0073\u0074\u0072\u0069n\u0067\u0020\u0028\u0069\u006e\u0020\u0062\u0079\u0074es\u0029\u0020i\u0073\u0020\u0036\u0035\u0035\u0033\u0035\u002e"));
_cccfe =true ;};case *_eb .PdfObjectName :if !_babd &&len ([]byte (*_cebdf ))> 127{_gce =append (_gce ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0034","\u004d\u0061\u0078\u0069\u006d\u0075\u006d \u006c\u0065\u006eg\u0074\u0068\u0020\u006ff\u0020\u0061\u0020\u006e\u0061\u006d\u0065\u0020\u0028\u0069\u006e\u0020\u0062\u0079\u0074\u0065\u0073\u0029\u0020\u0069\u0073\u0020\u0031\u0032\u0037\u002e"));
_babd =true ;};case *_eb .PdfObjectArray :if !_fgfa &&_cebdf .Len ()> 8191{_gce =append (_gce ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0035","\u004d\u0061\u0078\u0069\u006d\u0075m\u0020\u006c\u0065\u006e\u0067\u0074\u0068\u0020\u006f\u0066\u0020\u0061\u006e\u0020\u0061\u0072\u0072\u0061\u0079\u0020(\u0069\u006e\u0020\u0065\u006c\u0065\u006d\u0065\u006e\u0074\u0073\u0029\u0020\u0069s\u00208\u0031\u0039\u0031\u002e"));
_fgfa =true ;};for _ ,_ccbe :=range _cebdf .Elements (){_cacb (_ccbe );};if !_ebbda &&(_cebdf .Len ()==4||_cebdf .Len ()==5){_dbad ,_aagb :=_eb .GetName (_cebdf .Get (0));if !_aagb {return ;};if *_dbad !="\u0044e\u0076\u0069\u0063\u0065\u004e"{return ;
};_babf :=_cebdf .Get (1);_babf =_eb .TraceToDirectObject (_babf );_gadd ,_aagb :=_eb .GetArray (_babf );if !_aagb {return ;};if _gadd .Len ()> 8{_gce =append (_gce ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0039","\u004d\u0061\u0078i\u006d\u0075\u006d\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020\u0044\u0065\u0076\u0069\u0063\u0065\u004e\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065n\u0074\u0073\u0020\u0069\u0073\u0020\u0038\u002e"));
_ebbda =true ;};};case *_eb .PdfObjectDictionary :_dbbd :=_cebdf .Keys ();if !_ebbd &&len (_dbbd )> 4095{_gce =append (_gce ,_ee ("\u0036.\u0031\u002e\u0031\u0032\u002d\u00311","\u004d\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u0063\u0061\u0070\u0061\u0063\u0069\u0074y\u0020\u006f\u0066\u0020\u0061\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0028\u0069\u006e\u0020\u0065\u006e\u0074\u0072\u0069es\u0029\u0020\u0069\u0073\u0020\u0034\u0030\u0039\u0035\u002e"));
_ebbd =true ;};for _bdec ,_ecgb :=range _dbbd {_cacb (&_dbbd [_bdec ]);_cacb (_cebdf .Get (_ecgb ));};case *_eb .PdfObjectStream :_cacb (_cebdf .PdfObjectDictionary );case *_eb .PdfObjectStreams :for _ ,_gcaf :=range _cebdf .Elements (){_cacb (_gcaf );
};case *_eb .PdfObjectReference :_cacb (_cebdf .Resolve ());};};_deca :=_geag .GetObjectNums ();if len (_deca )> 8388607{_gce =append (_gce ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0037","\u004d\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020in\u0064i\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0073 \u0069\u006e\u0020\u0061\u0020\u0050\u0044\u0046\u0020\u0066\u0069\u006c\u0065\u0020\u0069\u0073\u00208\u002c\u0033\u0038\u0038\u002c\u0036\u0030\u0037\u002e"));
};for _ ,_ccfa :=range _deca {_aecd ,_fgefg :=_geag .GetIndirectObjectByNumber (_ccfa );if _fgefg !=nil {continue ;};_aeag :=_eb .TraceToDirectObject (_aecd );_cacb (_aeag );};return _gce ;};func _baeb (_bbdde *_eb .PdfObjectDictionary ,_gfbgc map[*_eb .PdfObjectStream ][]byte ,_cebc map[*_eb .PdfObjectStream ]*_ba .CMap )ViolatedRule {const (_cdd ="\u0036.\u0033\u002e\u0033\u002d\u0033";
_bfggb ="\u0041\u006cl \u0043\u004d\u0061\u0070\u0073\u0020\u0075\u0073e\u0064 \u0077i\u0074\u0068\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072m\u0069n\u0067\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048\u0020a\u006e\u0064\u0020\u0049\u0064\u0065\u006et\u0069\u0074\u0079-\u0056\u002c\u0020\u0073\u0068a\u006c\u006c \u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0069\u006e\u0020\u0074h\u0061\u0074\u0020\u0066\u0069\u006c\u0065\u0020\u0061\u0073\u0020\u0064es\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044F\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u00205\u002e\u0036\u002e\u0034\u002e";
);var _cbdbe string ;if _dcdf ,_acdba :=_eb .GetName (_bbdde .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_acdba {_cbdbe =_dcdf .String ();};if _cbdbe !="\u0054\u0079\u0070e\u0030"{return _ddd ;};_cfgc :=_bbdde .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _efcf ,_ceab :=_eb .GetName (_cfgc );_ceab {switch _efcf .String (){case "\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048","\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056":return _ddd ;default:return _ee (_cdd ,_bfggb );};
};_eggcg ,_aabe :=_eb .GetStream (_cfgc );if !_aabe {return _ee (_cdd ,_bfggb );};_ ,_cgge :=_cafgb (_eggcg ,_gfbgc ,_cebc );if _cgge !=nil {return _ee (_cdd ,_bfggb );};return _ddd ;};func _db (_ddc []*_gde .Image ,_aca bool )error {_gb :=_eb .PdfObjectName ("\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B");
if _aca {_gb ="\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b";};for _ ,_gc :=range _ddc {if _gc .Colorspace ==_gb {continue ;};_bgbe ,_dca :=_a .NewXObjectImageFromStream (_gc .Stream );if _dca !=nil {return _dca ;};_gg ,_dca :=_bgbe .ToImage ();
if _dca !=nil {return _dca ;};_cfa ,_dca :=_gg .ToGoImage ();if _dca !=nil {return _dca ;};var _gaf _a .PdfColorspace ;if _aca {_gaf =_a .NewPdfColorspaceDeviceCMYK ();_cfa ,_dca =_ec .CMYKConverter .Convert (_cfa );}else {_gaf =_a .NewPdfColorspaceDeviceRGB ();
_cfa ,_dca =_ec .NRGBAConverter .Convert (_cfa );};if _dca !=nil {return _dca ;};_ddac ,_cgf :=_cfa .(_ec .Image );if !_cgf {return _ce .New ("\u0069\u006d\u0061\u0067\u0065\u0020\u0064\u006f\u0065\u0073\u006e\u0027\u0074 \u0069\u006d\u0070\u006c\u0065\u006de\u006e\u0074\u0020\u0069\u006d\u0061\u0067\u0065\u0075\u0074\u0069\u006c\u002eI\u006d\u0061\u0067\u0065");
};_bcea :=_ddac .Base ();_ef :=&_a .Image {Width :int64 (_bcea .Width ),Height :int64 (_bcea .Height ),BitsPerComponent :int64 (_bcea .BitsPerComponent ),ColorComponents :_bcea .ColorComponents ,Data :_bcea .Data };_ef .SetDecode (_bcea .Decode );_ef .SetAlpha (_bcea .Alpha );
if _dca =_bgbe .SetImage (_ef ,_gaf );_dca !=nil {return _dca ;};_bgbe .ToPdfObject ();_gc .ColorComponents =_bcea .ColorComponents ;_gc .Colorspace =_gb ;};return nil ;};func _egbfg (_beec *_eb .PdfObjectDictionary ,_dcdfab map[*_eb .PdfObjectStream ][]byte ,_dcage map[*_eb .PdfObjectStream ]*_ba .CMap )ViolatedRule {const (_dbgbc ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0037\u002d\u0031";
_agegf ="\u0054\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006cl\u0020\u0069\u006e\u0063l\u0075\u0064e\u0020\u0061 \u0054\u006f\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u0020\u0065\u006e\u0074\u0072\u0079\u0020w\u0068\u006f\u0073\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073 \u0061\u0020\u0043M\u0061\u0070\u0020\u0073\u0074\u0072\u0065\u0061\u006d \u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u006d\u0061p\u0073\u0020\u0063\u0068\u0061\u0072ac\u0074\u0065\u0072\u0020\u0063\u006fd\u0065s\u0020\u0074\u006f\u0020\u0055\u006e\u0069\u0063\u006f\u0064e \u0076a\u006c\u0075\u0065\u0073,\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063r\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020P\u0044\u0046\u0020\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0035.\u0039\u002c\u0020\u0075\u006e\u006ce\u0073\u0073\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u006d\u0065\u0065\u0074\u0073 \u0061\u006e\u0079\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006c\u006c\u006f\u0077\u0069\u006e\u0067\u0020\u0074\u0068\u0072\u0065\u0065\u0020\u0063\u006f\u006e\u0064\u0069\u0074\u0069\u006f\u006e\u0073\u003a\u000a\u0020\u002d\u0020\u0066o\u006e\u0074\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0075\u0073\u0065\u0020\u0074\u0068\u0065\u0020\u0070\u0072\u0065\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0065\u006e\u0063\u006f\u0064\u0069n\u0067\u0073\u0020M\u0061\u0063\u0052o\u006d\u0061\u006e\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002c\u0020\u004d\u0061\u0063\u0045\u0078\u0070\u0065\u0072\u0074E\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006f\u0072\u0020\u0057\u0069\u006e\u0041n\u0073\u0069\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u002c\u0020\u006f\u0072\u0020\u0074\u0068\u0061\u0074\u0020\u0075\u0073\u0065\u0020t\u0068\u0065\u0020\u0070\u0072\u0065d\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048\u0020\u006f\u0072\u0020\u0049\u0064\u0065n\u0074\u0069\u0074\u0079\u002d\u0056\u0020C\u004d\u0061\u0070s\u003b\u000a\u0020\u002d\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0077\u0068\u006f\u0073\u0065\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u006e\u0061\u006d\u0065\u0073\u0020a\u0072\u0065 \u0074\u0061k\u0065\u006e\u0020\u0066\u0072\u006f\u006d\u0020\u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065\u0020\u0073\u0074\u0061n\u0064\u0061\u0072\u0064\u0020L\u0061t\u0069\u006e\u0020\u0063\u0068a\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0065\u0074\u0020\u006fr\u0020\u0074\u0068\u0065 \u0073\u0065\u0074\u0020\u006f\u0066 \u006e\u0061\u006d\u0065\u0064\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065r\u0073\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0079\u006d\u0062\u006f\u006c\u0020\u0066\u006f\u006e\u0074\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020i\u006e\u0020\u0050\u0044\u0046 \u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0041\u0070\u0070\u0065\u006e\u0064\u0069\u0078 \u0044\u003b\u000a\u0020\u002d\u0020\u0054\u0079\u0070\u0065\u0020\u0030\u0020\u0066\u006f\u006e\u0074\u0073\u0020w\u0068\u006f\u0073e\u0020d\u0065\u0073\u0063\u0065n\u0064\u0061\u006e\u0074 \u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0075\u0073\u0065\u0073\u0020\u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u0047B\u0031\u002c\u0020\u0041\u0064\u006fb\u0065\u002d\u0043\u004e\u0053\u0031\u002c\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u004a\u0061\u0070\u0061\u006e\u0031\u0020\u006f\u0072\u0020\u0041\u0064\u006f\u0062\u0065\u002d\u004b\u006fr\u0065\u0061\u0031\u0020\u0063\u0068\u0061r\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u006c\u006c\u0065\u0063\u0074\u0069\u006f\u006e\u0073\u002e";
);_eccd ,_deegg :=_eb .GetStream (_beec .Get ("\u0054o\u0055\u006e\u0069\u0063\u006f\u0064e"));if _deegg {_ ,_cfeb :=_cafgb (_eccd ,_dcdfab ,_dcage );if _cfeb !=nil {return _ee (_dbgbc ,_agegf );};return _ddd ;};_aggbb ,_deegg :=_eb .GetName (_beec .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));
if !_deegg {return _ee (_dbgbc ,_agegf );};switch _aggbb .String (){case "\u0054\u0079\u0070e\u0031":return _ddd ;};return _ee (_dbgbc ,_agegf );};func _cabb (_eaca *_gde .Document )error {_bab :=func (_dgg *_eb .PdfObjectDictionary )error {if _cef :=_dgg .Get ("\u0053\u004d\u0061s\u006b");
_cef !=nil {_dgg .Set ("\u0053\u004d\u0061s\u006b",_eb .MakeName ("\u004e\u006f\u006e\u0065"));};_ceb :=_dgg .Get ("\u0043\u0041");if _ceb !=nil {_acd ,_da :=_eb .GetNumberAsFloat (_ceb );if _da !=nil {_bf .Log .Debug ("\u0045x\u0074\u0047S\u0074\u0061\u0074\u0065 \u006f\u0062\u006ae\u0063\u0074\u0020\u0043\u0041\u0020\u0076\u0061\u006cue\u0020\u0069\u0073 \u006e\u006ft\u0020\u0061\u0020\u0066\u006c\u006fa\u0074\u003a \u0025\u0076",_da );
_acd =0;};if _acd !=1.0{_dgg .Set ("\u0043\u0041",_eb .MakeFloat (1.0));};};_ceb =_dgg .Get ("\u0063\u0061");if _ceb !=nil {_cca ,_fdc :=_eb .GetNumberAsFloat (_ceb );if _fdc !=nil {_bf .Log .Debug ("\u0045\u0078t\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0027\u0063\u0061\u0027\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u0066\u006c\u006f\u0061\u0074\u003a\u0020\u0025\u0076",_fdc );
_cca =0;};if _cca !=1.0{_dgg .Set ("\u0063\u0061",_eb .MakeFloat (1.0));};};_cfc :=_dgg .Get ("\u0042\u004d");if _cfc !=nil {_bbcb ,_cbag :=_eb .GetName (_cfc );if !_cbag {_bf .Log .Debug ("E\u0078\u0074\u0047\u0053\u0074\u0061t\u0065\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u0027\u0042\u004d\u0027\u0020i\u0073\u0020\u006e\u006f\u0074\u0020\u0061\u0020\u004e\u0061m\u0065");
_bbcb =_eb .MakeName ("");};_bcd :=_bbcb .String ();switch _bcd {case "\u004e\u006f\u0072\u006d\u0061\u006c","\u0043\u006f\u006d\u0070\u0061\u0074\u0069\u0062\u006c\u0065":default:_dgg .Set ("\u0042\u004d",_eb .MakeName ("\u004e\u006f\u0072\u006d\u0061\u006c"));
};};_ggd :=_dgg .Get ("\u0054\u0052");if _ggd !=nil {_bf .Log .Debug ("\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063t\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0073\u0020\u0054\u0052\u0020\u006b\u0065\u0079");
_dgg .Remove ("\u0054\u0052");};_ddda :=_dgg .Get ("\u0054\u0052\u0032");if _ddda !=nil {_cebf :=_ddda .String ();if _cebf !="\u0044e\u0066\u0061\u0075\u006c\u0074"{_bf .Log .Debug ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074\u0065 o\u0062\u006a\u0065\u0063\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0073 \u0054\u00522\u0020\u006b\u0065y\u0020\u0077\u0069\u0074\u0068\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0074\u0068\u0065r\u0020\u0074ha\u006e\u0020\u0044e\u0066\u0061\u0075\u006c\u0074");
_dgg .Set ("\u0054\u0052\u0032",_eb .MakeName ("\u0044e\u0066\u0061\u0075\u006c\u0074"));};};return nil ;};_cde ,_dge :=_eaca .GetPages ();if !_dge {return nil ;};for _ ,_adb :=range _cde {_adbf ,_cfe :=_adb .GetResources ();if !_cfe {continue ;};_def ,_dcb :=_eb .GetDict (_adbf .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));
if !_dcb {return nil ;};_bcca :=_def .Keys ();for _ ,_dbb :=range _bcca {_eff ,_gcg :=_eb .GetDict (_def .Get (_dbb ));if !_gcg {continue ;};_bgd :=_bab (_eff );if _bgd !=nil {continue ;};};};for _ ,_eec :=range _cde {_dab ,_egdg :=_eec .GetContents ();
if !_egdg {return nil ;};for _ ,_fdcc :=range _dab {_dag ,_efd :=_fdcc .GetData ();if _efd !=nil {continue ;};_fdg :=_e .NewContentStreamParser (string (_dag ));_aaf ,_efd :=_fdg .Parse ();if _efd !=nil {continue ;};for _ ,_ege :=range *_aaf {if len (_ege .Params )==0{continue ;
};_ ,_dfbg :=_eb .GetName (_ege .Params [0]);if !_dfbg {continue ;};_aafd ,_dde :=_eec .GetResourcesXObject ();if !_dde {continue ;};for _ ,_ccd :=range _aafd .Keys (){_ebe ,_cbg :=_eb .GetStream (_aafd .Get (_ccd ));if !_cbg {continue ;};_edc ,_cbg :=_eb .GetDict (_ebe .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_cbg {continue ;};_bbe ,_cbg :=_eb .GetDict (_edc .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if !_cbg {continue ;};for _ ,_badf :=range _bbe .Keys (){_bda ,_cabc :=_eb .GetDict (_bbe .Get (_badf ));if !_cabc {continue ;};_fdcf :=_bab (_bda );
if _fdcf !=nil {continue ;};};};};};};return nil ;};type imageModifications struct{_cbdd *colorspaceModification ;_cab _eb .StreamEncoder ;};func _gbdba (_cbdcff *_a .CompliancePdfReader )(_cdbg []ViolatedRule ){for _ ,_ffag :=range _cbdcff .GetObjectNums (){_baca ,_dacbb :=_cbdcff .GetIndirectObjectByNumber (_ffag );
if _dacbb !=nil {continue ;};_dccf ,_aadge :=_eb .GetDict (_baca );if !_aadge {continue ;};_bgbb ,_aadge :=_eb .GetName (_dccf .Get ("\u0054\u0079\u0070\u0065"));if !_aadge {continue ;};if _bgbb .String ()!="\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"{continue ;
};_cdae ,_aadge :=_eb .GetBool (_dccf .Get ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073"));if !_aadge {return _cdbg ;};if bool (*_cdae ){_cdbg =append (_cdbg ,_ee ("\u0036\u002e\u0039-\u0031","\u0054\u0068\u0065\u0020\u004e\u0065e\u0064\u0041\u0070\u0070\u0065a\u0072\u0061\u006e\u0063\u0065\u0073\u0020\u0066\u006c\u0061\u0067\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0069\u006e\u0074\u0065\u0072\u0061\u0063\u0074\u0069\u0076e\u0020\u0066\u006f\u0072\u006d \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0065\u0069\u0074\u0068\u0065\u0072\u0020\u006e\u006f\u0074\u0020b\u0065\u0020\u0070\u0072\u0065se\u006e\u0074\u0020\u006f\u0072\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u002e"));
};};return _cdbg ;};func _aadb (_bfbg *_gde .Document )error {_aeaf ,_fecd :=_bfbg .GetPages ();if !_fecd {return nil ;};for _ ,_bagge :=range _aeaf {_gdd ,_gacgf :=_eb .GetArray (_bagge .Object .Get ("\u0041\u006e\u006e\u006f\u0074\u0073"));if !_gacgf {continue ;
};for _ ,_gdf :=range _gdd .Elements (){_gdf =_eb .ResolveReference (_gdf );if _ ,_geeb :=_gdf .(*_eb .PdfObjectNull );_geeb {continue ;};_eedb ,_eebd :=_eb .GetDict (_gdf );if !_eebd {continue ;};_adaf ,_ :=_eb .GetIntVal (_eedb .Get ("\u0046"));_adaf &=^(1<<0);
_adaf &=^(1<<1);_adaf &=^(1<<5);_adaf &=^(1<<8);_adaf |=1<<2;_eedb .Set ("\u0046",_eb .MakeInteger (int64 (_adaf )));_baec :=false ;if _fagb :=_eedb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065");_fagb !=nil {_gdeae ,_ageg :=_eb .GetName (_fagb );if _ageg &&_gdeae .String ()=="\u0057\u0069\u0064\u0067\u0065\u0074"{_baec =true ;
if _eedb .Get ("\u0041\u0041")!=nil {_eedb .Remove ("\u0041\u0041");};if _eedb .Get ("\u0041")!=nil {_eedb .Remove ("\u0041");};};if _ageg &&_gdeae .String ()=="\u0054\u0065\u0078\u0074"{_aggc ,_ :=_eb .GetIntVal (_eedb .Get ("\u0046"));_aggc |=1<<3;_aggc |=1<<4;
_eedb .Set ("\u0046",_eb .MakeInteger (int64 (_aggc )));};};_ccdgb ,_eebd :=_eb .GetDict (_eedb .Get ("\u0041\u0050"));if _eebd {_ebgb :=_ccdgb .Get ("\u004e");if _ebgb ==nil {continue ;};if len (_ccdgb .Keys ())> 1{_ccdgb .Clear ();_ccdgb .Set ("\u004e",_ebgb );
};if _baec {_edae ,_afec :=_eb .GetName (_eedb .Get ("\u0046\u0054"));if _afec &&*_edae =="\u0042\u0074\u006e"{continue ;};};};};};return nil ;};

// ValidateStandard checks if provided input CompliancePdfReader matches rules that conforms PDF/A-2 standard.
func (_dcad *profile2 )ValidateStandard (r *_a .CompliancePdfReader )error {_afeb :=VerificationError {ConformanceLevel :_dcad ._fdecd ._dda ,ConformanceVariant :_dcad ._fdecd ._ff };if _aefg :=_eecee (r );_aefg !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_aefg );
};if _cdeg :=_ebgf (r );_cdeg !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_cdeg );};if _acfa :=_agaf (r );_acfa !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_acfa );};if _agdg :=_dadd (r );_agdg !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_agdg );
};if _gaae :=_fddc (r );_gaae !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_gaae );};if _beed :=_bgec (r );len (_beed )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_beed ...);};if _gdc :=_bcfgf (r );len (_gdc )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_gdc ...);
};if _gdfb :=_gcff (r );len (_gdfb )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_gdfb ...);};if _bfee :=_abgd (r );_bfee !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_bfee );};if _dfcd :=_fcge (r );len (_dfcd )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_dfcd ...);
};if _dccc :=_fcgef (r );len (_dccc )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_dccc ...);};if _fgcf :=_cceb (r );_fgcf !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_fgcf );};if _aeeg :=_decaf (r );len (_aeeg )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_aeeg ...);
};if _egcb :=_dged (r );len (_egcb )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_egcb ...);};if _gfeg :=_gaeg (r );_gfeg !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_gfeg );};if _dfdg :=_aedb (r );len (_dfdg )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_dfdg ...);
};if _degd :=_aebfb (r );len (_degd )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_degd ...);};if _ddba :=_fdfa (r );_ddba !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_ddba );};if _adab :=_bdac (r );len (_adab )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_adab ...);
};if _ffbe :=_gegc (r ,_dcad ._fdecd );len (_ffbe )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_ffbe ...);};if _adaa :=_afgb (r );len (_adaa )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_adaa ...);};if _badg :=_agdc (r );len (_badg )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_badg ...);
};if _eacag :=_afbgd (r );len (_eacag )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_eacag ...);};if _beaa :=_gcgfe (r );_beaa !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_beaa );};if _fbce :=_ccebb (r );len (_fbce )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_fbce ...);
};if _fdccb :=_aede (r );_fdccb !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_fdccb );};if _cdfd :=_bacg (r ,_dcad ._fdecd ,false );len (_cdfd )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_cdfd ...);};if _dcad ._fdecd ==_dc (){if _ceee :=_ecdde (r );
len (_ceee )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_ceee ...);};};if _dcdc :=_dagec (r );len (_dcdc )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_dcdc ...);};if _aebf :=_geeg (r );len (_aebf )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_aebf ...);
};if _cbda :=_aedf (r );len (_cbda )!=0{_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_cbda ...);};if _feae :=_geec (r );_feae !=_ddd {_afeb .ViolatedRules =append (_afeb .ViolatedRules ,_feae );};if len (_afeb .ViolatedRules )> 0{_c .Slice (_afeb .ViolatedRules ,func (_bdab ,_fccgd int )bool {return _afeb .ViolatedRules [_bdab ].RuleNo < _afeb .ViolatedRules [_fccgd ].RuleNo ;
});return _afeb ;};return nil ;};

// ApplyStandard tries to change the content of the writer to match the PDF/A-3 standard.
// Implements model.StandardApplier.
func (_feaef *profile3 )ApplyStandard (document *_gde .Document )(_bfbfc error ){_ffce (document ,7);if _bfbfc =_cefe (document ,_feaef ._afba .Now );_bfbfc !=nil {return _bfbfc ;};if _bfbfc =_gbf (document );_bfbfc !=nil {return _bfbfc ;};_ddbg ,_bbdg :=_dcbe (_feaef ._afba .CMYKDefaultColorSpace ,_feaef ._fddb );
_bfbfc =_bga (document ,[]pageColorspaceOptimizeFunc {_ddbg },[]documentColorspaceOptimizeFunc {_bbdg });if _bfbfc !=nil {return _bfbfc ;};_age (document );if _bfbfc =_bccf (document );_bfbfc !=nil {return _bfbfc ;};if _bfbfc =_ccfg (document ,_feaef ._fddb ._dda );
_bfbfc !=nil {return _bfbfc ;};if _bfbfc =_aadb (document );_bfbfc !=nil {return _bfbfc ;};if _bfbfc =_begc (document );_bfbfc !=nil {return _bfbfc ;};if _bfbfc =_defc (document );_bfbfc !=nil {return _bfbfc ;};if _bfbfc =_dddg (document );_bfbfc !=nil {return _bfbfc ;
};if _feaef ._fddb ._ff =="\u0041"{_gagf (document );};if _bfbfc =_ffeb (document ,_feaef ._fddb ._dda );_bfbfc !=nil {return _bfbfc ;};if _bfbfc =_fggg (document );_bfbfc !=nil {return _bfbfc ;};if _fefa :=_aedd (document ,_feaef ._fddb ,_feaef ._afba .Xmp );
_fefa !=nil {return _fefa ;};if _feaef ._fddb ==_bgf (){if _bfbfc =_cbdcf (document );_bfbfc !=nil {return _bfbfc ;};};if _bfbfc =_ggc (document );_bfbfc !=nil {return _bfbfc ;};if _bfbfc =_fdef (document );_bfbfc !=nil {return _bfbfc ;};if _bfbfc =_dfec (document );
_bfbfc !=nil {return _bfbfc ;};return nil ;};func _cg ()standardType {return standardType {_dda :1,_ff :"\u0041"}};func (_agb *documentImages )hasUncalibratedImages ()bool {return _agb ._cf ||_agb ._eeb ||_agb ._ac };func _cefe (_ega *_gde .Document ,_aed func ()_ca .Time )error {_ddeb ,_adbd :=_a .NewPdfInfoFromObject (_ega .Info );
if _adbd !=nil {return _adbd ;};if _bbcg :=_fceg (_ddeb ,_aed );_bbcg !=nil {return _bbcg ;};_ega .Info =_ddeb .ToPdfObject ();return nil ;};func _eeaaf (_fgeef *_a .CompliancePdfReader )(_bega []ViolatedRule ){var _daabe ,_dbfb bool ;_gdgf :=func ()bool {return _daabe &&_dbfb };
for _ ,_ddbadd :=range _fgeef .GetObjectNums (){_beabd ,_afee :=_fgeef .GetIndirectObjectByNumber (_ddbadd );if _afee !=nil {_bf .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0025\u0064\u0020fa\u0069\u006c\u0065d\u003a \u0025\u0076",_ddbadd ,_afee );
continue ;};_cccb ,_eadb :=_eb .GetDict (_beabd );if !_eadb {continue ;};_edgc ,_eadb :=_eb .GetName (_cccb .Get ("\u0054\u0079\u0070\u0065"));if !_eadb {continue ;};if *_edgc !="\u0041\u0063\u0074\u0069\u006f\u006e"{continue ;};_aacdf ,_eadb :=_eb .GetName (_cccb .Get ("\u0053"));
if !_eadb {if !_daabe {_bega =append (_bega ,_ee ("\u0036.\u0036\u002e\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u004c\u0061\u0075\u006e\u0063\u0068\u002c\u0020\u0053\u006f\u0075\u006e\u0064\u002c\u0020\u004d\u006f\u0076\u0069\u0065\u002c\u0020\u0052\u0065\u0073\u0065\u0074\u0046o\u0072\u006d\u002c\u0020\u0049\u006d\u0070\u006f\u0072\u0074\u0044\u0061\u0074\u0061\u0020\u0061\u006e\u0064 \u004a\u0061\u0076a\u0053\u0063\u0072\u0069\u0070\u0074\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020s\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064\u002e \u0041\u0064\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020th\u0065\u0020\u0064\u0065p\u0072\u0065\u0063\u0061\u0074\u0065\u0064\u0020s\u0065\u0074\u002d\u0073\u0074\u0061\u0074\u0065\u0020\u0061\u006e\u0064\u0020\u006e\u006f\u002d\u006f\u0070\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062e\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064\u002e\u0020T\u0068\u0065\u0020\u0048\u0069\u0064\u0065\u0020a\u0063\u0074\u0069\u006f\u006e \u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_daabe =true ;if _gdgf (){return _bega ;};};continue ;};switch _a .PdfActionType (*_aacdf ){case _a .ActionTypeLaunch ,_a .ActionTypeSound ,_a .ActionTypeMovie ,_a .ActionTypeResetForm ,_a .ActionTypeImportData ,_a .ActionTypeJavaScript :if !_daabe {_bega =append (_bega ,_ee ("\u0036.\u0036\u002e\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u004c\u0061\u0075\u006e\u0063\u0068\u002c\u0020\u0053\u006f\u0075\u006e\u0064\u002c\u0020\u004d\u006f\u0076\u0069\u0065\u002c\u0020\u0052\u0065\u0073\u0065\u0074\u0046o\u0072\u006d\u002c\u0020\u0049\u006d\u0070\u006f\u0072\u0074\u0044\u0061\u0074\u0061\u0020\u0061\u006e\u0064 \u004a\u0061\u0076a\u0053\u0063\u0072\u0069\u0070\u0074\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020s\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064\u002e \u0041\u0064\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020th\u0065\u0020\u0064\u0065p\u0072\u0065\u0063\u0061\u0074\u0065\u0064\u0020s\u0065\u0074\u002d\u0073\u0074\u0061\u0074\u0065\u0020\u0061\u006e\u0064\u0020\u006e\u006f\u002d\u006f\u0070\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062e\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074e\u0064\u002e\u0020T\u0068\u0065\u0020\u0048\u0069\u0064\u0065\u0020a\u0063\u0074\u0069\u006f\u006e \u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_daabe =true ;if _gdgf (){return _bega ;};};continue ;case _a .ActionTypeNamed :if !_dbfb {_begd ,_dgba :=_eb .GetName (_cccb .Get ("\u004e"));if !_dgba {_bega =append (_bega ,_ee ("\u0036.\u0036\u002e\u0031\u002d\u0032","N\u0061\u006d\u0065\u0064\u0020\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u006f\u0074\u0068e\u0072\u0020\u0074h\u0061\u006e\u0020\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u002c\u0020P\u0072\u0065v\u0050\u0061\u0067\u0065\u002c\u0020\u0046\u0069\u0072\u0073\u0074\u0050a\u0067e\u002c\u0020\u0061\u006e\u0064\u0020\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_dbfb =true ;if _gdgf (){return _bega ;};continue ;};switch *_begd {case "\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065","\u0050\u0072\u0065\u0076\u0050\u0061\u0067\u0065","\u0046i\u0072\u0073\u0074\u0050\u0061\u0067e","\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065":default:_bega =append (_bega ,_ee ("\u0036.\u0036\u002e\u0031\u002d\u0032","N\u0061\u006d\u0065\u0064\u0020\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u006f\u0074\u0068e\u0072\u0020\u0074h\u0061\u006e\u0020\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u002c\u0020P\u0072\u0065v\u0050\u0061\u0067\u0065\u002c\u0020\u0046\u0069\u0072\u0073\u0074\u0050a\u0067e\u002c\u0020\u0061\u006e\u0064\u0020\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_dbfb =true ;if _gdgf (){return _bega ;};continue ;};};};};return _bega ;};

// Part gets the PDF/A version level.
func (_afagd *profile1 )Part ()int {return _afagd ._ecd ._dda };func _gaca (_bccg *_a .CompliancePdfReader )ViolatedRule {return _ddd };func _efcc (_bcgb *_a .CompliancePdfReader )(*_eb .PdfObjectDictionary ,bool ){_affa ,_cgfg :=_bcgb .GetTrailer ();if _cgfg !=nil {_bf .Log .Debug ("\u0043\u0061\u006en\u006f\u0074\u0020\u0067e\u0074\u0020\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0074\u0072\u0061\u0069\u006c\u0065\u0072\u003a\u0020\u0025\u0076",_cgfg );
return nil ,false ;};_fdbf ,_dgfa :=_affa .Get ("\u0052\u006f\u006f\u0074").(*_eb .PdfObjectReference );if !_dgfa {_bf .Log .Debug ("\u0043a\u006e\u006e\u006f\u0074 \u0066\u0069\u006e\u0064\u0020d\u006fc\u0075m\u0065\u006e\u0074\u0020\u0072\u006f\u006ft");
return nil ,false ;};_cfcd ,_dgfa :=_eb .GetDict (_eb .ResolveReference (_fdbf ));if !_dgfa {_bf .Log .Debug ("\u0063\u0061\u006e\u006e\u006f\u0074 \u0072\u0065\u0073\u006f\u006c\u0076\u0065\u0020\u0063\u0061\u0074\u0061\u006co\u0067\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079");
return nil ,false ;};return _cfcd ,true ;};func _bafg (_gcage *_a .PdfFont ,_gcccb *_eb .PdfObjectDictionary ,_ccga bool )ViolatedRule {const (_eaef ="\u0036.\u0033\u002e\u0034\u002d\u0031";_cfge ="\u0054\u0068\u0065\u0020\u0066\u006f\u006et\u0020\u0070\u0072\u006f\u0067\u0072\u0061\u006d\u0073\u0020\u0066\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0075\u0073\u0065\u0064\u0020\u0077\u0069\u0074\u0068\u0069\u006e \u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069l\u0065\u0020s\u0068\u0061\u006cl\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0077\u0069\u0074\u0068i\u006e\u0020\u0074h\u0061\u0074\u0020\u0066\u0069\u006ce\u002c\u0020a\u0073\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052e\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0035\u002e\u0038\u002c\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0077h\u0065\u006e\u0020\u0074\u0068\u0065 \u0066\u006f\u006e\u0074\u0073\u0020\u0061\u0072\u0065\u0020\u0075\u0073\u0065\u0064\u0020\u0065\u0078\u0063\u006cu\u0073i\u0076\u0065\u006c\u0079\u0020\u0077\u0069t\u0068\u0020\u0074\u0065\u0078\u0074\u0020\u0072e\u006ed\u0065\u0072\u0069\u006e\u0067\u0020\u006d\u006f\u0064\u0065\u0020\u0033\u002e";
);if _ccga {return _ddd ;};_gfccf :=_gcage .FontDescriptor ();var _daaab string ;if _eeae ,_agabg :=_eb .GetName (_gcccb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_agabg {_daaab =_eeae .String ();};switch _daaab {case "\u0054\u0079\u0070e\u0031":if _gfccf .FontFile ==nil {return _ee (_eaef ,_cfge );
};case "\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065":if _gfccf .FontFile2 ==nil {return _ee (_eaef ,_cfge );};case "\u0054\u0079\u0070e\u0030","\u0054\u0079\u0070e\u0033":default:if _gfccf .FontFile3 ==nil {return _ee (_eaef ,_cfge );};};return _ddd ;
};

// ValidateStandard checks if provided input CompliancePdfReader matches rules that conforms PDF/A-3 standard.
func (_dcfg *profile3 )ValidateStandard (r *_a .CompliancePdfReader )error {_fbaad :=VerificationError {ConformanceLevel :_dcfg ._fddb ._dda ,ConformanceVariant :_dcfg ._fddb ._ff };if _gefd :=_eecee (r );_gefd !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_gefd );
};if _edcb :=_ebgf (r );_edcb !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_edcb );};if _bgad :=_agaf (r );_bgad !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_bgad );};if _ddfg :=_dadd (r );_ddfg !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_ddfg );
};if _agac :=_fddc (r );_agac !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_agac );};if _gdeb :=_bgec (r );len (_gdeb )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_gdeb ...);};if _gcdf :=_bcfgf (r );len (_gcdf )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_gcdf ...);
};if _cgce :=_gcff (r );len (_cgce )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_cgce ...);};if _eadc :=_abgd (r );_eadc !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_eadc );};if _fegd :=_fcge (r );len (_fegd )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_fegd ...);
};if _fgee :=_fcgef (r );len (_fgee )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_fgee ...);};if _bebf :=_cceb (r );_bebf !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_bebf );};if _bge :=_decaf (r );len (_bge )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_bge ...);
};if _dbec :=_dged (r );len (_dbec )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_dbec ...);};if _ffbd :=_gaeg (r );_ffbd !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_ffbd );};if _gbba :=_aedb (r );len (_gbba )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_gbba ...);
};if _abec :=_aebfb (r );len (_abec )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_abec ...);};if _ecbe :=_fdfa (r );_ecbe !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_ecbe );};if _cgdd :=_bdac (r );len (_cgdd )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_cgdd ...);
};if _ddgfb :=_gegc (r ,_dcfg ._fddb );len (_ddgfb )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_ddgfb ...);};if _cdec :=_afgb (r );len (_cdec )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_cdec ...);};if _ffece :=_agdc (r );
len (_ffece )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_ffece ...);};if _adbdb :=_afbgd (r );len (_adbdb )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_adbdb ...);};if _bede :=_gcgfe (r );_bede !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_bede );
};if _dgcd :=_ccebb (r );len (_dgcd )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_dgcd ...);};if _caad :=_aede (r );_caad !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_caad );};if _ccac :=_bacg (r ,_dcfg ._fddb ,false );len (_ccac )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_ccac ...);
};if _dcfg ._fddb ==_bgf (){if _dece :=_ecdde (r );len (_dece )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_dece ...);};};if _gdg :=_ddgd (r );len (_gdg )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_gdg ...);};if _cdcdaf :=_geeg (r );
len (_cdcdaf )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_cdcdaf ...);};if _bagcee :=_aedf (r );len (_bagcee )!=0{_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_bagcee ...);};if _cfeff :=_geec (r );_cfeff !=_ddd {_fbaad .ViolatedRules =append (_fbaad .ViolatedRules ,_cfeff );
};if len (_fbaad .ViolatedRules )> 0{_c .Slice (_fbaad .ViolatedRules ,func (_fdad ,_gggg int )bool {return _fbaad .ViolatedRules [_fdad ].RuleNo < _fbaad .ViolatedRules [_gggg ].RuleNo ;});return _fbaad ;};return nil ;};

// NewProfile2U creates a new Profile2U with the given options.
func NewProfile2U (options *Profile2Options )*Profile2U {if options ==nil {options =DefaultProfile2Options ();};_gcbb (options );return &Profile2U {profile2 {_efc :*options ,_fdecd :_eg ()}};};

// String gets a string representation of the violated rule.
func (_ad ViolatedRule )String ()string {return _d .Sprintf ("\u0025\u0073\u003a\u0020\u0025\u0073",_ad .RuleNo ,_ad .Detail );};

// Profile1B is the implementation of the PDF/A-1B standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile1B struct{profile1 };

// StandardName gets the name of the standard.
func (_dbcc *profile3 )StandardName ()string {return _d .Sprintf ("\u0050D\u0046\u002f\u0041\u002d\u0033\u0025s",_dbcc ._fddb ._ff );};func _bgg (_afa *_gde .Document )error {for _ ,_ffab :=range _afa .Objects {_gfe ,_bfdc :=_eb .GetDict (_ffab );if !_bfdc {continue ;
};_gda :=_gfe .Get ("\u0054\u0079\u0070\u0065");if _gda ==nil {continue ;};if _ccdc ,_fde :=_eb .GetName (_gda );_fde &&_ccdc .String ()!="\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"{continue ;};_fcb ,_aff :=_eb .GetBool (_gfe .Get ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073"));
if _aff {if bool (*_fcb ){_gfe .Set ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073",_eb .MakeBool (false ));};};_dgc :=_gfe .Get ("\u0041");if _dgc !=nil {_gfe .Remove ("\u0041");};_fgaf ,_aff :=_eb .GetArray (_gfe .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));
if _aff {for _cac :=0;_cac < _fgaf .Len ();_cac ++{_bbg ,_gee :=_eb .GetDict (_fgaf .Get (_cac ));if !_gee {continue ;};if _bbg .Get ("\u0041\u0041")!=nil {_bbg .Remove ("\u0041\u0041");};};};};return nil ;};

// ViolatedRule is the structure that defines violated PDF/A rule.
type ViolatedRule struct{RuleNo string ;Detail string ;};

// Profile1A is the implementation of the PDF/A-1A standard profile.
// Implements model.StandardImplementer, Profile interfaces.
type Profile1A struct{profile1 };func _cceg (_cacc *Profile3Options ){if _cacc .Now ==nil {_cacc .Now =_ca .Now ;};};func _dgfea (_debfa *_eb .PdfObjectDictionary ,_ddaec map[*_eb .PdfObjectStream ][]byte ,_dffbe map[*_eb .PdfObjectStream ]*_ba .CMap )ViolatedRule {const (_ccaa ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0033\u002d\u0033";
_eabbc ="\u0041\u006c\u006c \u0043\u004d\u0061\u0070s\u0020\u0075\u0073ed\u0020\u0077\u0069\u0074\u0068i\u006e\u0020\u0061\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0032\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0065\u0078\u0063\u0065\u0070\u0074 th\u006f\u0073\u0065\u0020\u006ci\u0073\u0074\u0065\u0064\u0020i\u006e\u0020\u0049\u0053\u004f\u0020\u0033\u00320\u00300\u002d1\u003a\u0032\u0030\u0030\u0038\u002c\u0020\u0039\u002e\u0037\u002e\u0035\u002e\u0032\u002c\u0020\u0054\u0061\u0062\u006c\u0065 \u0031\u00318,\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0069\u006e \u0074\u0068\u0061\u0074\u0020\u0066\u0069\u006c\u0065\u0020\u0061\u0073\u0020\u0064e\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e\u0020\u0049\u0053\u004f\u0020\u0033\u0032\u00300\u0030-\u0031\u003a\u0032\u0030\u0030\u0038\u002c\u00209\u002e\u0037\u002e\u0035\u002e";
);var _gfffe string ;if _dbfd ,_fbgfc :=_eb .GetName (_debfa .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_fbgfc {_gfffe =_dbfd .String ();};if _gfffe !="\u0054\u0079\u0070e\u0030"{return _ddd ;};_dagf :=_debfa .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _dfga ,_ecgda :=_eb .GetName (_dagf );_ecgda {switch _dfga .String (){case "\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0048","\u0049\u0064\u0065\u006e\u0074\u0069\u0074\u0079\u002d\u0056":return _ddd ;default:return _ee (_ccaa ,_eabbc );
};};_adaca ,_eeefg :=_eb .GetStream (_dagf );if !_eeefg {return _ee (_ccaa ,_eabbc );};_ ,_fdbb :=_cafgb (_adaca ,_ddaec ,_dffbe );if _fdbb !=nil {return _ee (_ccaa ,_eabbc );};return _ddd ;};func _cabf (_cgfda *_a .PdfFont ,_agga *_eb .PdfObjectDictionary )ViolatedRule {const (_fecb ="\u0036.\u0033\u002e\u0037\u002d\u0031";
_gabf ="\u0041\u006cl \u006e\u006f\u006e\u002d\u0073\u0079\u006db\u006f\u006c\u0069\u0063\u0020\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065\u0020\u0066o\u006e\u0074s\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0068\u0061\u0076\u0065\u0020e\u0069\u0074h\u0065\u0072\u0020\u004d\u0061\u0063\u0052\u006f\u006d\u0061\u006e\u0045\u006e\u0063\u006fd\u0069\u006e\u0067\u0020\u006f\u0072\u0020\u0057\u0069\u006e\u0041\u006e\u0073i\u0045n\u0063\u006f\u0064\u0069n\u0067\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0066o\u0072\u0020t\u0068\u0065 \u0045n\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006b\u0065\u0079 \u0069\u006e\u0020t\u0068e\u0020\u0046o\u006e\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006f\u0072\u0020\u0061\u0073\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0066\u006f\u0072 \u0074\u0068\u0065\u0020\u0042\u0061\u0073\u0065\u0045\u006e\u0063\u006fd\u0069\u006e\u0067\u0020\u006b\u0065\u0079\u0020\u0069\u006e\u0020\u0074\u0068\u0065 \u0064i\u0063\u0074i\u006fn\u0061\u0072\u0079\u0020\u0077\u0068\u0069\u0063\u0068\u0020\u0069s\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006ff\u0020\u0074\u0068e\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u006be\u0079\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0046\u006f\u006e\u0074 \u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079\u002e\u0020\u0049\u006e\u0020\u0061\u0064\u0064\u0069\u0074\u0069\u006f\u006e, \u006eo\u0020n\u006f\u006e\u002d\u0073\u0079\u006d\u0062\u006f\u006c\u0069\u0063\u0020\u0054\u0072\u0075\u0065\u0054\u0079p\u0065 \u0066\u006f\u006e\u0074\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0020\u0061\u0020\u0044\u0069\u0066\u0066e\u0072\u0065\u006e\u0063\u0065\u0073\u0020a\u0072\u0072\u0061\u0079\u0020\u0075n\u006c\u0065s\u0073\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0074h\u0065\u0020\u0067\u006c\u0079\u0070\u0068\u0020\u006e\u0061\u006d\u0065\u0073 \u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0044\u0069f\u0066\u0065\u0072\u0065\u006ec\u0065\u0073\u0020a\u0072\u0072\u0061\u0079\u0020\u0061\u0072\u0065\u0020\u006c\u0069\u0073\u0074\u0065\u0064 \u0069\u006e \u0074\u0068\u0065\u0020\u0041\u0064\u006f\u0062\u0065 G\u006c\u0079\u0070\u0068\u0020\u004c\u0069\u0073t\u0020\u0061\u006e\u0064\u0020\u0074h\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0066o\u006e\u0074\u0020\u0070\u0072\u006f\u0067\u0072a\u006d\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0073\u0020\u0061\u0074\u0020\u006c\u0065\u0061\u0073t\u0020\u0074\u0068\u0065\u0020\u004d\u0069\u0063\u0072o\u0073o\u0066\u0074\u0020\u0055\u006e\u0069\u0063\u006f\u0064\u0065\u0020\u0028\u0033\u002c\u0031 \u2013 P\u006c\u0061\u0074\u0066\u006f\u0072\u006d\u0020I\u0044\u003d\u0033\u002c\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067 I\u0044\u003d\u0031\u0029\u0020\u0065\u006e\u0063\u006f\u0064i\u006e\u0067 \u0069\u006e\u0020t\u0068\u0065\u0020'\u0063\u006d\u0061\u0070\u0027\u0020\u0074\u0061\u0062\u006c\u0065\u002e";
);var _decb string ;if _gaea ,_eef :=_eb .GetName (_agga .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_eef {_decb =_gaea .String ();};if _decb !="\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065"{return _ddd ;};_gbdd :=_cgfda .FontDescriptor ();_dbgc ,_bbgdc :=_eb .GetIntVal (_gbdd .Flags );
if !_bbgdc {_bf .Log .Debug ("\u0066\u006c\u0061\u0067\u0073 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0066o\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u006f\u0072");
return _ee (_fecb ,_gabf );};_gcgdd :=(uint32 (_dbgc )>>3)!=0;if _gcgdd {return _ddd ;};_bec ,_bbgdc :=_eb .GetName (_agga .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067"));if !_bbgdc {return _ee (_fecb ,_gabf );};switch _bec .String (){case "\u004d\u0061c\u0052\u006f\u006da\u006e\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067","\u0057i\u006eA\u006e\u0073\u0069\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067":return _ddd ;
default:return _ee (_fecb ,_gabf );};};func _gcgfe (_bdgd *_a .CompliancePdfReader )(_gbeac ViolatedRule ){_eeff ,_cdbaf :=_efcc (_bdgd );if !_cdbaf {return _ddd ;};_cdde ,_cdbaf :=_eb .GetDict (_eeff .Get ("\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"));
if !_cdbaf {return _ddd ;};_afdb ,_cdbaf :=_eb .GetArray (_cdde .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));if !_cdbaf {return _ddd ;};for _fccb :=0;_fccb < _afdb .Len ();_fccb ++{_dcedg ,_fabdc :=_eb .GetDict (_afdb .Get (_fccb ));if !_fabdc {continue ;
};if _dcedg .Get ("\u0041")!=nil {return _ee ("\u0036.\u0034\u002e\u0031\u002d\u0032","\u0041\u0020\u0046\u0069\u0065\u006c\u0064\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0041 o\u0072\u0020\u0041\u0041\u0020\u006b\u0065\u0079\u0073\u002e");
};if _dcedg .Get ("\u0041\u0041")!=nil {return _ee ("\u0036.\u0034\u002e\u0031\u002d\u0032","\u0041\u0020\u0046\u0069\u0065\u006c\u0064\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0041 o\u0072\u0020\u0041\u0041\u0020\u006b\u0065\u0079\u0073\u002e");
};};return _ddd ;};func _gfgcf (_cacfe *_a .CompliancePdfReader )(_bgbc []ViolatedRule ){_dcef :=_cacfe .ParserMetadata ();if _dcef .HasInvalidSubsectionHeader (){_bgbc =append (_bgbc ,_ee ("\u0036.\u0031\u002e\u0034\u002d\u0031","\u006e\u0020\u0061\u0020\u0063\u0072\u006f\u0073\u0073\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0073\u0065c\u0074\u0069\u006f\u006e\u0020h\u0065a\u0064\u0065\u0072\u0020t\u0068\u0065\u0020\u0073\u0074\u0061\u0072t\u0069\u006e\u0067\u0020\u006fb\u006a\u0065\u0063\u0074 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0072\u0061n\u0067e\u0020s\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0065\u0070\u0061\u0072\u0061\u0074\u0065\u0064\u0020\u0062\u0079\u0020\u0061\u0020s\u0069\u006e\u0067\u006c\u0065\u0020\u0053\u0050\u0041C\u0045\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074e\u0072\u0020\u0028\u0032\u0030\u0068\u0029\u002e"));
};if _dcef .HasInvalidSeparationAfterXRef (){_bgbc =append (_bgbc ,_ee ("\u0036.\u0031\u002e\u0034\u002d\u0032","\u0054\u0068\u0065 \u0078\u0072\u0065\u0066\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020\u0063\u0072\u006f\u0073s\u0020\u0072\u0065\u0066e\u0072\u0065\u006e\u0063\u0065 s\u0075b\u0073\u0065\u0063ti\u006f\u006e\u0020\u0068\u0065\u0061\u0064e\u0072\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0065\u0070\u0061\u0072\u0061\u0074\u0065\u0064\u0020\u0062\u0079 \u0061\u0020\u0073i\u006e\u0067\u006c\u0065\u0020\u0045\u004fL\u0020\u006d\u0061\u0072\u006b\u0065\u0072\u002e"));
};return _bgbc ;};func _cafgb (_gfdc *_eb .PdfObjectStream ,_ffaag map[*_eb .PdfObjectStream ][]byte ,_gcba map[*_eb .PdfObjectStream ]*_ba .CMap )(*_ba .CMap ,error ){_dddfg ,_dfdee :=_gcba [_gfdc ];if !_dfdee {var _ceec error ;_ccdca ,_egbc :=_ffaag [_gfdc ];
if !_egbc {_ccdca ,_ceec =_eb .DecodeStream (_gfdc );if _ceec !=nil {_bf .Log .Debug ("\u0064\u0065\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0073\u0074r\u0065\u0061\u006d\u0020\u0066\u0061\u0069\u006c\u0065\u0064:\u0020\u0025\u0076",_ceec );return nil ,_ceec ;
};_ffaag [_gfdc ]=_ccdca ;};_dddfg ,_ceec =_ba .LoadCmapFromData (_ccdca ,false );if _ceec !=nil {return nil ,_ceec ;};_gcba [_gfdc ]=_dddfg ;};return _dddfg ,nil ;};var _ Profile =(*Profile2A )(nil );func _gbaa (_edea *_a .PdfPageResources ,_fagg *_e .ContentStreamOperations ,_eda bool )([]byte ,error ){var _gebb bool ;
for _ ,_bfgd :=range *_fagg {_fba :switch _bfgd .Operand {case "\u0042\u0049":_gca ,_dad :=_bfgd .Params [0].(*_e .ContentStreamInlineImage );if !_dad {break ;};_bagce ,_gfgc :=_gca .GetColorSpace (_edea );if _gfgc !=nil {return nil ,_gfgc ;};switch _bagce .(type ){case *_a .PdfColorspaceDeviceCMYK :if _eda {break _fba ;
};case *_a .PdfColorspaceDeviceGray :case *_a .PdfColorspaceDeviceRGB :if !_eda {break _fba ;};default:break _fba ;};_gebb =true ;_eggf ,_gfgc :=_gca .ToImage (_edea );if _gfgc !=nil {return nil ,_gfgc ;};_dbdb ,_gfgc :=_eggf .ToGoImage ();if _gfgc !=nil {return nil ,_gfgc ;
};if _eda {_dbdb ,_gfgc =_ec .CMYKConverter .Convert (_dbdb );}else {_dbdb ,_gfgc =_ec .NRGBAConverter .Convert (_dbdb );};if _gfgc !=nil {return nil ,_gfgc ;};_cec ,_dad :=_dbdb .(_ec .Image );if !_dad {return nil ,_ce .New ("\u0069\u006d\u0061\u0067\u0065\u0020\u0064\u006f\u0065\u0073\u006e\u0027\u0074 \u0069\u006d\u0070\u006c\u0065\u006de\u006e\u0074\u0020\u0069\u006d\u0061\u0067\u0065\u0075\u0074\u0069\u006c\u002eI\u006d\u0061\u0067\u0065");
};_aecc :=_cec .Base ();_fcc :=_a .Image {Width :int64 (_aecc .Width ),Height :int64 (_aecc .Height ),BitsPerComponent :int64 (_aecc .BitsPerComponent ),ColorComponents :_aecc .ColorComponents ,Data :_aecc .Data };_fcc .SetDecode (_aecc .Decode );_fcc .SetAlpha (_aecc .Alpha );
_dfgd ,_gfgc :=_gca .GetEncoder ();if _gfgc !=nil {_dfgd =_eb .NewFlateEncoder ();};_fbda ,_gfgc :=_e .NewInlineImageFromImage (_fcc ,_dfgd );if _gfgc !=nil {return nil ,_gfgc ;};_bfgd .Params [0]=_fbda ;case "\u0047","\u0067":if len (_bfgd .Params )!=1{break ;
};_bbda ,_fbaa :=_eb .GetNumberAsFloat (_bfgd .Params [0]);if _fbaa !=nil {break ;};if _eda {_bfgd .Params =[]_eb .PdfObject {_eb .MakeFloat (0),_eb .MakeFloat (0),_eb .MakeFloat (0),_eb .MakeFloat (1-_bbda )};_gbbg :="\u004b";if _bfgd .Operand =="\u0067"{_gbbg ="\u006b";
};_bfgd .Operand =_gbbg ;}else {_bfgd .Params =[]_eb .PdfObject {_eb .MakeFloat (_bbda ),_eb .MakeFloat (_bbda ),_eb .MakeFloat (_bbda )};_eaac :="\u0052\u0047";if _bfgd .Operand =="\u0067"{_eaac ="\u0072\u0067";};_bfgd .Operand =_eaac ;};_gebb =true ;
case "\u0052\u0047","\u0072\u0067":if !_eda {break ;};if len (_bfgd .Params )!=3{break ;};_cfdf ,_gdb :=_eb .GetNumbersAsFloat (_bfgd .Params );if _gdb !=nil {break ;};_gebb =true ;_abe ,_ggda ,_bbga :=_cfdf [0],_cfdf [1],_cfdf [2];_bdd ,_gcb ,_caddg ,_dea :=_b .RGBToCMYK (uint8 (_abe *255),uint8 (_ggda *255),uint8 (255*_bbga ));
_bfgd .Params =[]_eb .PdfObject {_eb .MakeFloat (float64 (_bdd )/255),_eb .MakeFloat (float64 (_gcb )/255),_eb .MakeFloat (float64 (_caddg )/255),_eb .MakeFloat (float64 (_dea )/255)};_fged :="\u004b";if _bfgd .Operand =="\u0072\u0067"{_fged ="\u006b";
};_bfgd .Operand =_fged ;case "\u004b","\u006b":if _eda {break ;};if len (_bfgd .Params )!=4{break ;};_ebec ,_feg :=_eb .GetNumbersAsFloat (_bfgd .Params );if _feg !=nil {break ;};_ddfc ,_ebb ,_cacf ,_aefc :=_ebec [0],_ebec [1],_ebec [2],_ebec [3];_ebeg ,_bdeda ,_bfbf :=_b .CMYKToRGB (uint8 (255*_ddfc ),uint8 (255*_ebb ),uint8 (255*_cacf ),uint8 (255*_aefc ));
_bfgd .Params =[]_eb .PdfObject {_eb .MakeFloat (float64 (_ebeg )/255),_eb .MakeFloat (float64 (_bdeda )/255),_eb .MakeFloat (float64 (_bfbf )/255)};_cgfd :="\u0052\u0047";if _bfgd .Operand =="\u006b"{_cgfd ="\u0072\u0067";};_bfgd .Operand =_cgfd ;_gebb =true ;
};};if !_gebb {return nil ,nil ;};_bcdb :=_e .NewContentCreator ();for _ ,_ecg :=range *_fagg {_bcdb .AddOperand (*_ecg );};_fgde :=_bcdb .Bytes ();return _fgde ,nil ;};func _bbdb (_ebebd *_a .CompliancePdfReader )(_geac ViolatedRule ){_bfad ,_fced :=_efcc (_ebebd );
if !_fced {return _ddd ;};_badc ,_fced :=_eb .GetDict (_bfad .Get ("\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"));if !_fced {return _ddd ;};_caaf ,_fced :=_eb .GetArray (_badc .Get ("\u0046\u0069\u0065\u006c\u0064\u0073"));if !_fced {return _ddd ;
};for _ddbd :=0;_ddbd < _caaf .Len ();_ddbd ++{_abecf ,_gafc :=_eb .GetDict (_caaf .Get (_ddbd ));if !_gafc {continue ;};if _abecf .Get ("\u0041\u0041")!=nil {return _ee ("\u0036.\u0036\u002e\u0032\u002d\u0032","\u0041\u0020F\u0069\u0065\u006cd\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079 s\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061n\u0020A\u0041\u0020\u0065\u006e\u0074\u0072y f\u006f\u0072\u0020\u0061\u006e\u0020\u0061\u0064\u0064\u0069\u0074\u0069on\u0061l\u002d\u0061\u0063\u0074i\u006fn\u0073 \u0064\u0069c\u0074\u0069on\u0061\u0072\u0079\u002e");
};};return _ddd ;};func _cfec (_adabg *_a .PdfInfo ,_befbd *_bg .Document )bool {_gacab ,_cccc :=_befbd .GetPdfInfo ();if !_cccc {return false ;};if _gacab .InfoDict ==nil {return false ;};_gcdc ,_ebbg :=_a .NewPdfInfoFromObject (_gacab .InfoDict );if _ebbg !=nil {return false ;
};if _adabg .Creator !=nil {if _gcdc .Creator ==nil ||_gcdc .Creator .String ()!=_adabg .Creator .String (){return false ;};};if _adabg .CreationDate !=nil {if _gcdc .CreationDate ==nil ||!_gcdc .CreationDate .ToGoTime ().Equal (_adabg .CreationDate .ToGoTime ()){return false ;
};};if _adabg .ModifiedDate !=nil {if _gcdc .ModifiedDate ==nil ||!_gcdc .ModifiedDate .ToGoTime ().Equal (_adabg .ModifiedDate .ToGoTime ()){return false ;};};if _adabg .Producer !=nil {if _gcdc .Producer ==nil ||_gcdc .Producer .String ()!=_adabg .Producer .String (){return false ;
};};if _adabg .Keywords !=nil {if _gcdc .Keywords ==nil ||_gcdc .Keywords .String ()!=_adabg .Keywords .String (){return false ;};};if _adabg .Trapped !=nil {if _gcdc .Trapped ==nil {return false ;};switch _adabg .Trapped .String (){case "\u0054\u0072\u0075\u0065":if _gcdc .Trapped .String ()!="\u0054\u0072\u0075\u0065"{return false ;
};case "\u0046\u0061\u006cs\u0065":if _gcdc .Trapped .String ()!="\u0046\u0061\u006cs\u0065"{return false ;};default:if _gcdc .Trapped .String ()!="\u0046\u0061\u006cs\u0065"{return false ;};};};if _adabg .Title !=nil {if _gcdc .Title ==nil ||_gcdc .Title .String ()!=_adabg .Title .String (){return false ;
};};if _adabg .Subject !=nil {if _gcdc .Subject ==nil ||_gcdc .Subject .String ()!=_adabg .Subject .String (){return false ;};};return true ;};func _dcacb (_caf *_a .CompliancePdfReader )(_eedg []ViolatedRule ){var _cdac ,_edfe ,_gcef ,_cbdcba ,_ebbb ,_dbef ,_cbdb bool ;
_ebfag :=func ()bool {return _cdac &&_edfe &&_gcef &&_cbdcba &&_ebbb &&_dbef &&_cbdb };for _ ,_gbea :=range _caf .PageList {if _gbea .Resources ==nil {continue ;};_cbcb ,_bcfe :=_eb .GetDict (_gbea .Resources .Font );if !_bcfe {continue ;};for _ ,_dfeb :=range _cbcb .Keys (){_fbgf ,_accg :=_eb .GetDict (_cbcb .Get (_dfeb ));
if !_accg {if !_cdac {_eedg =append (_eedg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0031","\u0041\u006c\u006c\u0020\u0066\u006fn\u0074\u0073\u0020\u0075\u0073e\u0064\u0020\u0069\u006e\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020c\u006f\u006e\u0066\u006f\u0072m\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u0073\u0020d\u0065\u0066\u0069\u006e\u0065d \u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0035\u002e\u0035\u002e"));
_cdac =true ;if _ebfag (){return _eedg ;};};continue ;};if _gfeea ,_ccce :=_eb .GetName (_fbgf .Get ("\u0054\u0079\u0070\u0065"));!_cdac &&(!_ccce ||_gfeea .String ()!="\u0046\u006f\u006e\u0074"){_eedg =append (_eedg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0031","\u0054\u0079\u0070e\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075i\u0072\u0065\u0064\u0029 Th\u0065\u0020\u0074\u0079\u0070\u0065\u0020\u006f\u0066 \u0050\u0044\u0046\u0020\u006fbj\u0065\u0063\u0074\u0020\u0074\u0068\u0061t\u0020\u0074\u0068\u0069s\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0064\u0065\u0073c\u0072\u0069\u0062\u0065\u0073\u003b\u0020\u006d\u0075\u0073t\u0020\u0062\u0065\u0020\u0046\u006f\u006e\u0074\u0020\u0066\u006fr\u0020\u0061\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
_cdac =true ;if _ebfag (){return _eedg ;};};_daee ,_ecdd :=_a .NewPdfFontFromPdfObject (_fbgf );if _ecdd !=nil {continue ;};var _cbcc string ;if _efaf ,_ebfb :=_eb .GetName (_fbgf .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_ebfb {_cbcc =_efaf .String ();
};if !_edfe {switch _cbcc {case "\u0054\u0079\u0070e\u0030","\u0054\u0079\u0070e\u0031","\u004dM\u0054\u0079\u0070\u0065\u0031","\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0030","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032":default:_edfe =true ;
_eedg =append (_eedg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0032","\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065d\u0029\u0020\u0054\u0068e \u0074\u0079\u0070\u0065 \u006f\u0066\u0020\u0066\u006f\u006et\u003b\u0020\u006d\u0075\u0073\u0074\u0020b\u0065\u0020\u0022\u0054\u0079\u0070\u0065\u0031\u0022\u0020f\u006f\u0072\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020f\u006f\u006e\u0074\u0073\u002c\u0020\u0022\u004d\u004d\u0054\u0079\u0070\u0065\u0031\u0022\u0020\u0066\u006f\u0072\u0020\u006d\u0075\u006c\u0074\u0069\u0070\u006c\u0065\u0020\u006da\u0073\u0074e\u0072\u0020\u0066\u006f\u006e\u0074s\u002c\u0020\u0022\u0054\u0072\u0075\u0065T\u0079\u0070\u0065\u0022\u0020\u0066\u006f\u0072\u0020\u0054\u0072\u0075\u0065T\u0079\u0070\u0065\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0022\u0054\u0079\u0070\u0065\u0033\u0022\u0020\u0066\u006f\u0072\u0020\u0054\u0079\u0070e\u0020\u0033\u0020\u0066\u006f\u006e\u0074\u0073\u002c\u0020\"\u0054\u0079\u0070\u0065\u0030\"\u0020\u0066\u006f\u0072\u0020\u0054\u0079\u0070\u0065\u0020\u0030\u0020\u0066\u006f\u006e\u0074\u0073\u0020\u0061\u006ed\u0020\u0022\u0043\u0049\u0044\u0046\u006fn\u0074\u0054\u0079\u0070\u0065\u0030\u0022 \u006f\u0072\u0020\u0022\u0043\u0049\u0044\u0046\u006f\u006e\u0074T\u0079\u0070e\u0032\u0022\u0020\u0066\u006f\u0072\u0020\u0043\u0049\u0044\u0020\u0066\u006f\u006e\u0074\u0073\u002e"));
if _ebfag (){return _eedg ;};};};if !_gcef {if _cbcc !="\u0054\u0079\u0070e\u0033"{_afcg ,_cdb :=_eb .GetName (_fbgf .Get ("\u0042\u0061\u0073\u0065\u0046\u006f\u006e\u0074"));if !_cdb ||_afcg .String ()==""{_eedg =append (_eedg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0033","B\u0061\u0073\u0065\u0046\u006f\u006e\u0074\u0020\u002d\u0020\u006e\u0061\u006d\u0065\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065\u0064)\u0020T\u0068\u0065\u0020\u0050o\u0073\u0074S\u0063\u0072\u0069\u0070\u0074\u0020\u006e\u0061\u006d\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u002e"));
_gcef =true ;if _ebfag (){return _eedg ;};};};};if _cbcc !="\u0054\u0079\u0070e\u0031"{continue ;};_dabag :=_bad .IsStdFont (_bad .StdFontName (_daee .BaseFont ()));if _dabag {continue ;};_fege ,_cdgc :=_eb .GetIntVal (_fbgf .Get ("\u0046i\u0072\u0073\u0074\u0043\u0068\u0061r"));
if !_cdgc &&!_cbdcba {_eedg =append (_eedg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0034","\u0046\u0069r\u0073t\u0043\u0068\u0061\u0072\u0020\u002d\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072\u0020\u002d\u0020\u0028\u0052\u0065\u0071\u0075i\u0072\u0065\u0064\u0020\u0065\u0078\u0063\u0065\u0070t\u0020\u0066\u006f\u0072\u0020\u0074h\u0065\u0020\u0073\u0074\u0061\u006e\u0064\u0061\u0072d\u0020\u0031\u0034\u0020\u0066\u006f\u006e\u0074\u0073\u0029\u0020\u0054\u0068\u0065\u0020\u0066\u0069\u0072\u0073\u0074\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u0064e\u0020\u0064\u0065\u0066i\u006ee\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0027\u0073\u0020\u0057i\u0064\u0074\u0068\u0073 \u0061r\u0072\u0061y\u002e"));
_cbdcba =true ;if _ebfag (){return _eedg ;};};_cfde ,_dbeac :=_eb .GetIntVal (_fbgf .Get ("\u004c\u0061\u0073\u0074\u0043\u0068\u0061\u0072"));if !_dbeac &&!_ebbb {_eedg =append (_eedg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0035","\u004c\u0061\u0073t\u0043\u0068\u0061\u0072\u0020\u002d\u0020\u0069n\u0074\u0065\u0067e\u0072 \u002d\u0020\u0028\u0052\u0065\u0071u\u0069\u0072\u0065d\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0020\u0066\u006f\u0072\u0020t\u0068\u0065 s\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0031\u0034\u0020\u0066\u006f\u006ets\u0029\u0020\u0054\u0068\u0065\u0020\u006c\u0061\u0073t\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0063\u006f\u0064\u0065\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0027\u0073\u0020\u0057\u0069\u0064\u0074h\u0073\u0020\u0061\u0072\u0072\u0061\u0079\u002e"));
_ebbb =true ;if _ebfag (){return _eedg ;};};if !_dbef {_fabd ,_bgda :=_eb .GetArray (_fbgf .Get ("\u0057\u0069\u0064\u0074\u0068\u0073"));if !_bgda ||!_cdgc ||!_dbeac ||_fabd .Len ()!=_cfde -_fege +1{_eedg =append (_eedg ,_ee ("\u0036.\u0033\u002e\u0032\u002d\u0036","\u0057\u0069\u0064\u0074\u0068\u0073\u0020\u002d a\u0072\u0072\u0061y \u002d\u0020\u0028\u0052\u0065\u0071\u0075\u0069\u0072\u0065\u0064\u0020\u0065\u0078\u0063\u0065\u0070t\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0073\u0074a\u006e\u0064a\u0072\u0064\u00201\u0034\u0020\u0066\u006f\u006e\u0074\u0073\u003b\u0020\u0069\u006ed\u0069\u0072\u0065\u0063\u0074\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0070\u0072\u0065\u0066e\u0072\u0072e\u0064\u0029\u0020\u0041\u006e \u0061\u0072\u0072\u0061\u0079\u0020\u006f\u0066\u0020\u0028\u004c\u0061\u0073\u0074\u0043\u0068\u0061\u0072\u0020\u2212 F\u0069\u0072\u0073\u0074\u0043\u0068\u0061\u0072\u0020\u002b\u00201\u0029\u0020\u0077\u0069\u0064\u0074\u0068\u0073."));
_dbef =true ;if _ebfag (){return _eedg ;};};};};};return _eedg ;};func _cdecf (_edeae *_a .CompliancePdfReader )ViolatedRule {for _ ,_fcbf :=range _edeae .GetObjectNums (){_bdeg ,_ccaf :=_edeae .GetIndirectObjectByNumber (_fcbf );if _ccaf !=nil {continue ;
};_babbe ,_abcb :=_eb .GetStream (_bdeg );if !_abcb {continue ;};_gfff ,_abcb :=_eb .GetName (_babbe .Get ("\u0054\u0079\u0070\u0065"));if !_abcb {continue ;};if *_gfff !="\u0058O\u0062\u006a\u0065\u0063\u0074"{continue ;};if _babbe .Get ("\u0053\u004d\u0061s\u006b")!=nil {return _ee ("\u0036\u002e\u0034-\u0032","\u0041\u006e\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068e \u0053\u004d\u0061\u0073\u006b\u0020\u006b\u0065\u0079\u002e");
};};return _ddd ;};func _ccg ()standardType {return standardType {_dda :2,_ff :"\u0042"}};

// ApplyStandard tries to change the content of the writer to match the PDF/A-1 standard.
// Implements model.StandardApplier.
func (_faac *profile1 )ApplyStandard (document *_gde .Document )(_ccde error ){_ffce (document ,4);if _ccde =_cefe (document ,_faac ._bdbb .Now );_ccde !=nil {return _ccde ;};if _ccde =_gbf (document );_ccde !=nil {return _ccde ;};_cgb ,_ccggd :=_dcbe (_faac ._bdbb .CMYKDefaultColorSpace ,_faac ._ecd );
_ccde =_bga (document ,[]pageColorspaceOptimizeFunc {_ebda ,_cgb },[]documentColorspaceOptimizeFunc {_ccggd });if _ccde !=nil {return _ccde ;};_age (document );if _ccde =_ccfg (document ,_faac ._ecd ._dda );_ccde !=nil {return _ccde ;};if _ccde =_ccfc (document );
_ccde !=nil {return _ccde ;};if _ccde =_gbad (document );_ccde !=nil {return _ccde ;};if _ccde =_cabb (document );_ccde !=nil {return _ccde ;};if _ccde =_defc (document );_ccde !=nil {return _ccde ;};if _faac ._ecd ._ff =="\u0041"{_gagf (document );};if _ccde =_ffeb (document ,_faac ._ecd ._dda );
_ccde !=nil {return _ccde ;};if _ccde =_fggg (document );_ccde !=nil {return _ccde ;};if _caa :=_aedd (document ,_faac ._ecd ,_faac ._bdbb .Xmp );_caa !=nil {return _caa ;};if _faac ._ecd ==_cg (){if _ccde =_cbdcf (document );_ccde !=nil {return _ccde ;
};};if _ccde =_bgg (document );_ccde !=nil {return _ccde ;};return nil ;};var _ Profile =(*Profile2U )(nil );func _ge ()standardType {return standardType {_dda :3,_ff :"\u0042"}};func _bcfgf (_gefb *_a .CompliancePdfReader )(_dafa []ViolatedRule ){var _bdedad ,_afgg ,_ecfc bool ;
if _gefb .ParserMetadata ().HasNonConformantStream (){_dafa =[]ViolatedRule {_ee ("\u0036.\u0031\u002e\u0037\u002d\u0032","T\u0068\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020f\u006f\u006cl\u006fw\u0065\u0064\u0020e\u0069\u0074h\u0065\u0072\u0020\u0062\u0079\u0020\u0061 \u0043\u0041\u0052\u0052I\u0041\u0047\u0045\u0020\u0052E\u0054\u0055\u0052\u004e\u0020\u00280\u0044\u0068\u0029\u0020\u0061\u006e\u0064\u0020\u004c\u0049\u004e\u0045\u0020F\u0045\u0045\u0044\u0020\u0028\u0030\u0041\u0068\u0029\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0065\u0071\u0075\u0065\u006e\u0063\u0065\u0020o\u0072\u0020\u0062\u0079\u0020\u0061 \u0073\u0069ng\u006c\u0065\u0020\u004cIN\u0045 \u0046\u0045\u0045\u0044 \u0063\u0068\u0061r\u0061\u0063\u0074\u0065\u0072\u002e\u0020T\u0068\u0065\u0020e\u006e\u0064\u0073\u0074r\u0065\u0061\u006d\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0073\u0068\u0061\u006c\u006c \u0062e\u0020p\u0072\u0065\u0063\u0065\u0064\u0065\u0064\u0020\u0062\u0079\u0020\u0061n\u0020\u0045\u004f\u004c \u006d\u0061\u0072\u006b\u0065\u0072\u002e")};
};for _ ,_gefgg :=range _gefb .GetObjectNums (){_feeae ,_ :=_gefb .GetIndirectObjectByNumber (_gefgg );if _feeae ==nil {continue ;};_acdg ,_bfbeb :=_eb .GetStream (_feeae );if !_bfbeb {continue ;};if !_bdedad {_ddcca :=_acdg .Get ("\u004c\u0065\u006e\u0067\u0074\u0068");
if _ddcca ==nil {_dafa =append (_dafa ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0031","\u006e\u006f\u0020'\u004c\u0065\u006e\u0067\u0074\u0068\u0027\u0020\u006b\u0065\u0079\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0073\u0074\u0072\u0065a\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074"));
_bdedad =true ;}else {_afbc ,_abgc :=_eb .GetIntVal (_ddcca );if !_abgc {_dafa =append (_dafa ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0031","s\u0074\u0072\u0065\u0061\u006d\u0020\u0027\u004c\u0065\u006e\u0067\u0074\u0068\u0027\u0020\u006b\u0065\u0079 \u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020an\u0020\u0069\u006et\u0065g\u0065\u0072"));
_bdedad =true ;}else {if len (_acdg .Stream )!=_afbc {_dafa =append (_dafa ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0031","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u006c\u0065\u006e\u0067th\u0020v\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020m\u0061\u0074\u0063\u0068\u0020\u0074\u0068\u0065\u0020\u0073\u0069\u007a\u0065\u0020\u006f\u0066\u0020t\u0068\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d"));
_bdedad =true ;};};};};if !_afgg {if _acdg .Get ("\u0046")!=nil {_afgg =true ;_dafa =append (_dafa ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020'\u0046\u0027,\u0020\u0027F\u0046\u0069\u006c\u0074\u0065\u0072\u0027\u002c\u0020\u006f\u0072\u0020\u0027FD\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061m\u0073\u0027\u0020\u006b\u0065\u0079"));
};if _acdg .Get ("\u0046F\u0069\u006c\u0074\u0065\u0072")!=nil &&!_afgg {_afgg =true ;_dafa =append (_dafa ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020'\u0046\u0027,\u0020\u0027F\u0046\u0069\u006c\u0074\u0065\u0072\u0027\u002c\u0020\u006f\u0072\u0020\u0027FD\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061m\u0073\u0027\u0020\u006b\u0065\u0079"));
continue ;};if _acdg .Get ("\u0046\u0044\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061\u006d\u0073")!=nil &&!_afgg {_afgg =true ;_dafa =append (_dafa ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062j\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020'\u0046\u0027,\u0020\u0027F\u0046\u0069\u006c\u0074\u0065\u0072\u0027\u002c\u0020\u006f\u0072\u0020\u0027FD\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061m\u0073\u0027\u0020\u006b\u0065\u0079"));
continue ;};};if !_ecfc {_cdadf ,_cabbe :=_eb .GetName (_eb .TraceToDirectObject (_acdg .Get ("\u0046\u0069\u006c\u0074\u0065\u0072")));if !_cabbe {continue ;};if *_cdadf ==_eb .StreamEncodingFilterNameLZW {_ecfc =true ;_dafa =append (_dafa ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0034","\u0054h\u0065\u0020L\u005a\u0057\u0044\u0065c\u006f\u0064\u0065 \u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0073\u0068al\u006c\u0020\u006eo\u0074\u0020b\u0065\u0020\u0070\u0065\u0072\u006di\u0074\u0074e\u0064\u002e"));
};};};return _dafa ;};func _feec (_ccgb *_a .CompliancePdfReader ,_dbcd standardType ,_cebb bool )(_dffb []ViolatedRule ){_dbdbd ,_geafg :=_efcc (_ccgb );if !_geafg {return []ViolatedRule {_ee ("\u0036.\u0037\u002e\u0032\u002d\u0031","\u0063a\u0074a\u006c\u006f\u0067\u0020\u006eo\u0074\u0020f\u006f\u0075\u006e\u0064\u002e")};
};_ccff :=_dbdbd .Get ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061");if _ccff ==nil {return []ViolatedRule {_ee ("\u0036.\u0037\u002e\u0032\u002d\u0031","\u006e\u006f\u0020\u0027\u004d\u0065\u0074\u0061d\u0061\u0074\u0061' \u006b\u0065\u0079\u0020\u0066\u006fu\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006co\u0067\u002e"),_ee ("\u0036.\u0037\u002e\u0033\u002d\u0031","\u0049\u0066\u0020\u005b\u0061\u0020\u0064\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u006e\u0066o\u0072\u006d\u0061t\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u0070p\u0065\u0061r\u0073\u0020\u0069n\u0020\u0061 \u0064\u006f\u0063um\u0065\u006e\u0074\u005d\u002c\u0020\u0074\u0068\u0065n\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0069\u0074\u0073\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u006e\u0061\u006c\u006f\u0067\u006fu\u0073\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0069\u006e\u0020\u0070\u0072\u0065\u0064e\u0066\u0069\u006e\u0065\u0064\u0020\u0058\u004d\u0050\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u2026 \u0073\u0068\u0061\u006c\u006c\u0020\u0061\u006c\u0073\u006f\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0069\u006e\u0020\u0074he\u0020\u0066i\u006c\u0065 \u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0073\u002e")};
};_cbb ,_geafg :=_eb .GetStream (_ccff );if !_geafg {return []ViolatedRule {_ee ("\u0036.\u0037\u002e\u0032\u002d\u0032","\u0063\u0061\u0074a\u006c\u006f\u0067\u0020\u0027\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0027\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0069\u0073\u0020\u006e\u006f\u0074\u0020a\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"),_ee ("\u0036.\u0037\u002e\u0033\u002d\u0031","\u0049\u0066\u0020\u005b\u0061\u0020\u0064\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u006e\u0066o\u0072\u006d\u0061t\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u0070p\u0065\u0061r\u0073\u0020\u0069n\u0020\u0061 \u0064\u006f\u0063um\u0065\u006e\u0074\u005d\u002c\u0020\u0074\u0068\u0065n\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0069\u0074\u0073\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u006e\u0061\u006c\u006f\u0067\u006fu\u0073\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0069\u006e\u0020\u0070\u0072\u0065\u0064e\u0066\u0069\u006e\u0065\u0064\u0020\u0058\u004d\u0050\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u2026 \u0073\u0068\u0061\u006c\u006c\u0020\u0061\u006c\u0073\u006f\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0069\u006e\u0020\u0074he\u0020\u0066i\u006c\u0065 \u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0073\u002e")};
};if _cbb .Get ("\u0046\u0069\u006c\u0074\u0065\u0072")!=nil {_dffb =append (_dffb ,_ee ("\u0036.\u0037\u002e\u0032\u002d\u0032","M\u0065\u0074a\u0064\u0061\u0074\u0061\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u0064i\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0069\u0065\u0073\u0020\u0073\u0068\u0061\u006c\u006c \u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u0046\u0069\u006c\u0074\u0065\u0072\u0020\u006b\u0065y\u002e"));
};_gaecf ,_dcbea :=_bg .LoadDocument (_cbb .Stream );if _dcbea !=nil {return []ViolatedRule {_ee ("\u0036.\u0037\u002e\u0039\u002d\u0031","The\u0020\u006d\u0065\u0074a\u0064\u0061t\u0061\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063o\u006e\u0066\u006f\u0072\u006d\u0020\u0074o\u0020\u0058\u004d\u0050\u0020\u0053\u0070\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u0020\u0061\u006e\u0064\u0020\u0077\u0065\u006c\u006c\u0020\u0066\u006f\u0072\u006de\u0064\u0020\u0050\u0044\u0046\u0041\u0045\u0078\u0074e\u006e\u0073\u0069\u006f\u006e\u0020\u0053\u0063\u0068\u0065\u006da\u0020\u0066\u006fr\u0020\u0061\u006c\u006c\u0020\u0065\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u0073\u002e")};
};_fbde :=_gaecf .GetGoXmpDocument ();var _fdcb []*_cc .Namespace ;for _ ,_cacbe :=range _fbde .Namespaces (){switch _cacbe .Name {case _df .NsDc .Name ,_gd .NsPDF .Name ,_ag .NsXmp .Name ,_dd .NsXmpRights .Name ,_aga .Namespace .Name ,_ea .Namespace .Name ,_de .NsXmpMM .Name ,_ea .FieldNS .Name ,_ea .SchemaNS .Name ,_ea .PropertyNS .Name ,"\u0073\u0074\u0045v\u0074","\u0073\u0074\u0056e\u0072","\u0073\u0074\u0052e\u0066","\u0073\u0074\u0044i\u006d","\u0078a\u0070\u0047\u0049\u006d\u0067","\u0073\u0074\u004ao\u0062","\u0078\u006d\u0070\u0069\u0064\u0071":continue ;
};_fdcb =append (_fdcb ,_cacbe );};_bbfcd :=true ;_fbaaf ,_dcbea :=_gaecf .GetPdfaExtensionSchemas ();if _dcbea ==nil {for _ ,_degf :=range _fdcb {var _dcba bool ;for _gabb :=range _fbaaf {if _degf .URI ==_fbaaf [_gabb ].NamespaceURI {_dcba =true ;break ;
};};if !_dcba {_bbfcd =false ;break ;};};}else {_bbfcd =false ;};if !_bbfcd {_dffb =append (_dffb ,_ee ("\u0036.\u0037\u002e\u0039\u002d\u0032","\u0050\u0072\u006f\u0070\u0065\u0072\u0074i\u0065\u0073 \u0073\u0070\u0065\u0063\u0069\u0066\u0069ed\u0020\u0069\u006e\u0020\u0058M\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0073\u0068\u0061\u006cl\u0020\u0075\u0073\u0065\u0020\u0065\u0069\u0074\u0068\u0065\u0072\u0020\u0074\u0068\u0065\u0020\u0070\u0072\u0065\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073 \u0064\u0065\u0066i\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0053\u0070\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006fn\u002c\u0020\u006f\u0072\u0020\u0065\u0078\u0074\u0065ns\u0069\u006f\u006e\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u0074\u0068\u0061\u0074 \u0063\u006f\u006d\u0070\u006c\u0079\u0020\u0077\u0069\u0074h\u0020\u0058\u004d\u0050\u0020\u0053\u0070e\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002e"));
};_fcbfa ,_dcbea :=_ccgb .GetPdfInfo ();if _dcbea ==nil {if !_cfec (_fcbfa ,_gaecf ){_dffb =append (_dffb ,_ee ("\u0036.\u0037\u002e\u0033\u002d\u0031","\u0049\u0066\u0020\u005b\u0061\u0020\u0064\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u006e\u0066o\u0072\u006d\u0061t\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u0070p\u0065\u0061r\u0073\u0020\u0069n\u0020\u0061 \u0064\u006f\u0063um\u0065\u006e\u0074\u005d\u002c\u0020\u0074\u0068\u0065n\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0069\u0074\u0073\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u006e\u0061\u006c\u006f\u0067\u006fu\u0073\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0069\u006e\u0020\u0070\u0072\u0065\u0064e\u0066\u0069\u006e\u0065\u0064\u0020\u0058\u004d\u0050\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u2026 \u0073\u0068\u0061\u006c\u006c\u0020\u0061\u006c\u0073\u006f\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0069\u006e\u0020\u0074he\u0020\u0066i\u006c\u0065 \u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0073\u002e"));
};}else if _ ,_cadc :=_gaecf .GetMediaManagement ();_cadc {_dffb =append (_dffb ,_ee ("\u0036.\u0037\u002e\u0033\u002d\u0031","\u0049\u0066\u0020\u005b\u0061\u0020\u0064\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0069\u006e\u0066o\u0072\u006d\u0061t\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u0070p\u0065\u0061r\u0073\u0020\u0069n\u0020\u0061 \u0064\u006f\u0063um\u0065\u006e\u0074\u005d\u002c\u0020\u0074\u0068\u0065n\u0020\u0061\u006c\u006c\u0020\u006f\u0066\u0020\u0069\u0074\u0073\u0020\u0065\u006e\u0074\u0072\u0069\u0065\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u006e\u0061\u006c\u006f\u0067\u006fu\u0073\u0020\u0070\u0072\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073 \u0069\u006e\u0020\u0070\u0072\u0065\u0064e\u0066\u0069\u006e\u0065\u0064\u0020\u0058\u004d\u0050\u0020\u0073\u0063\u0068\u0065\u006d\u0061\u0073\u0020\u2026 \u0073\u0068\u0061\u006c\u006c\u0020\u0061\u006c\u0073\u006f\u0020\u0062\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065d\u0020\u0069\u006e\u0020\u0074he\u0020\u0066i\u006c\u0065 \u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072\u006d\u0020\u0077\u0069\u0074\u0068\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0076\u0061\u006c\u0075\u0065\u0073\u002e"));
};_gagfg ,_geafg :=_gaecf .GetPdfAID ();if !_geafg {_dffb =append (_dffb ,_ee ("\u0036\u002e\u0037\u002e\u0031\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u0020\u0061n\u0064\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020\u006c\u0065\u0076\u0065l\u0020\u006f\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0070e\u0063\u0069\u0066\u0069\u0065\u0064\u0020\u0075\u0073\u0069\u006e\u0067\u0020\u0074\u0068\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u0020\u0065\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u0020\u0073\u0063h\u0065\u006da."));
}else {if _gagfg .Part !=_dbcd ._dda {_dffb =append (_dffb ,_ee ("\u0036\u002e\u0037\u002e\u0031\u0031\u002d\u0032","\u0054h\u0065\u0020\u0076\u0061lue\u0020\u006f\u0066\u0020p\u0064\u0066\u0061\u0069\u0064\u003a\u0070\u0061\u0072\u0074 \u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0074\u0068\u0065\u0020\u0070\u0061\u0072\u0074\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0049\u0053\u004f\u002019\u0030\u0030\u0035 \u0074\u006f\u0020\u0077\u0068i\u0063h\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065 \u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0073\u002e"));
};if _dbcd ._ff =="\u0041"&&_gagfg .Conformance !="\u0041"{_dffb =append (_dffb ,_ee ("\u0036\u002e\u0037\u002e\u0031\u0031\u002d\u0033","\u0041\u0020\u004c\u0065\u0076e\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063i\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063o\u006e\u0066\u006fr\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0041\u002e\u0020\u0041\u0020\u004c\u0065\u0076e\u006c\u0020\u0042\u0020\u0063\u006f\u006e\u0066o\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0073\u0070e\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e"));
}else if _dbcd ._ff =="\u0042"&&(_gagfg .Conformance !="\u0041"&&_gagfg .Conformance !="\u0042"){_dffb =append (_dffb ,_ee ("\u0036\u002e\u0037\u002e\u0031\u0031\u002d\u0033","\u0041\u0020\u004c\u0065\u0076e\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063i\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063o\u006e\u0066\u006fr\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0041\u002e\u0020\u0041\u0020\u004c\u0065\u0076e\u006c\u0020\u0042\u0020\u0063\u006f\u006e\u0066o\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0073\u0070e\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e"));
};};return _dffb ;};func (_gdea standardType )String ()string {return _d .Sprintf ("\u0050\u0044\u0046\u002f\u0041\u002d\u0025\u0064\u0025\u0073",_gdea ._dda ,_gdea ._ff );};func _cceb (_bbegg *_a .CompliancePdfReader )ViolatedRule {for _ ,_gffe :=range _bbegg .PageList {_cbabg ,_ceac :=_gffe .GetContentStreams ();
if _ceac !=nil {continue ;};for _ ,_dfbgb :=range _cbabg {_gfdd :=_e .NewContentStreamParser (_dfbgb );_ ,_ceac =_gfdd .Parse ();if _ceac !=nil {return _ee ("\u0036.\u0032\u002e\u0032\u002d\u0031","\u0041\u0020\u0063onten\u0074\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u0073\u0068\u0061\u006c\u006c n\u006f\u0074\u0020c\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079 \u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072\u0073\u0020\u006e\u006ft\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065f\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0065\u0076\u0065\u006e\u0020\u0069\u0066\u0020s\u0075\u0063\u0068\u0020\u006f\u0070\u0065r\u0061\u0074\u006f\u0072\u0073\u0020\u0061\u0072\u0065\u0020\u0062\u0072\u0061\u0063\u006b\u0065\u0074\u0065\u0064\u0020\u0062\u0079\u0020\u0074\u0068\u0065\u0020\u0042\u0058\u002f\u0045\u0058\u0020\u0063\u006f\u006d\u0070\u0061\u0074\u0069\u0062i\u006c\u0069\u0074\u0079\u0020\u006f\u0070\u0065\u0072\u0061\u0074\u006f\u0072\u0073\u002e");
};};};return _ddd ;};func _cbcac (_cacfd *_eb .PdfObjectDictionary )ViolatedRule {const (_gffa ="\u0036.\u0033\u002e\u0033\u002d\u0032";_fegec ="\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0054y\u0070\u0065\u0020\u0032\u0020\u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0061\u0072\u0065\u0020\u0075\u0073\u0065\u0064\u0020f\u006f\u0072 \u0072\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067,\u0020\u0074\u0068\u0065\u0020\u0043\u0049\u0044\u0046\u006fn\u0074\u0020\u0064\u0069c\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c \u0063\u006f\u006e\u0074\u0061i\u006e\u0020\u0061\u0020\u0043\u0049\u0044\u0054\u006f\u0047\u0049D\u004d\u0061\u0070\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020a\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006d\u0061\u0070\u0070\u0069\u006e\u0067\u0020\u0066\u0072\u006f\u006d\u0020\u0043\u0049\u0044\u0073\u0020\u0074\u006f\u0020\u0067\u006c\u0079\u0070\u0068\u0020\u0069\u006e\u0064\u0069c\u0065\u0073\u0020\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u006e\u0061\u006d\u0065\u0020\u0049d\u0065\u006e\u0074\u0069\u0074\u0079\u002c\u0020\u0061s d\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069n\u0020P\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072e\u006e\u0063\u0065\u0020\u0054a\u0062\u006c\u0065\u0020\u0035\u002e\u00313";
);var _dfbb string ;if _cgfc ,_fgdea :=_eb .GetName (_cacfd .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_fgdea {_dfbb =_cgfc .String ();};if _dfbb !="\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032"{return _ddd ;};if _cacfd .Get ("C\u0049\u0044\u0054\u006f\u0047\u0049\u0044\u004d\u0061\u0070")==nil {return _ee (_gffa ,_fegec );
};return _ddd ;};func _age (_aae *_gde .Document ){if _aae .ID [0]!=""&&_aae .ID [1]!=""{return ;};_aae .UseHashBasedID =true ;};var _ Profile =(*Profile3A )(nil );

// NewProfile1A creates a new Profile1A with given options.
func NewProfile1A (options *Profile1Options )*Profile1A {if options ==nil {options =DefaultProfile1Options ();};_adagc (options );return &Profile1A {profile1 {_bdbb :*options ,_ecd :_cg ()}};};func _afea (_gcae *_a .PdfFont ,_feeec *_eb .PdfObjectDictionary )ViolatedRule {const (_aeege ="\u0036.\u0033\u002e\u0035\u002d\u0033";
_bgdca ="\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0043\u0049\u0044\u0046\u006f\u006e\u0074\u0020\u0073\u0075\u0062\u0073\u0065\u0074\u0073 \u0072e\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0064\u0020\u0077i\u0074\u0068\u0069n\u0020\u0061\u0020c\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069l\u0065\u002c\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006et\u0020\u0064\u0065s\u0063\u0072\u0069\u0070\u0074\u006f\u0072\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u0020\u0043\u0049\u0044\u0053\u0065\u0074\u0020s\u0074\u0072\u0065\u0061\u006d\u0020\u0069\u0064\u0065\u006e\u0074\u0069\u0066\u0079\u0069\u006eg\u0020\u0077\u0068i\u0063\u0068\u0020\u0043\u0049\u0044\u0073 \u0061\u0072e\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020\u0069\u006e \u0074\u0068\u0065\u0020\u0065\u006d\u0062\u0065\u0064d\u0065\u0064\u0020\u0043\u0049D\u0046\u006f\u006e\u0074\u0020\u0066\u0069l\u0065,\u0020\u0061\u0073 \u0064\u0065\u0073\u0063\u0072\u0069b\u0065\u0064 \u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063e\u0020\u0054ab\u006c\u0065\u0020\u0035.\u00320\u002e";
);var _ggba string ;if _egce ,_afdc :=_eb .GetName (_feeec .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_afdc {_ggba =_egce .String ();};switch _ggba {case "\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0030","\u0043\u0049\u0044F\u006f\u006e\u0074\u0054\u0079\u0070\u0065\u0032":_cbgb :=_gcae .FontDescriptor ();
if _cbgb .CIDSet ==nil {return _ee (_aeege ,_bgdca );};return _ddd ;default:return _ddd ;};};func _facc (_aeab *_a .PdfFont ,_agdb *_eb .PdfObjectDictionary )ViolatedRule {const (_cdeaa ="\u0036.\u0033\u002e\u0037\u002d\u0033";_aeaa ="\u0046\u006f\u006e\u0074\u0020\u0070\u0072\u006f\u0067\u0072\u0061\u006d\u0073\u0027\u0020\u0022\u0063\u006d\u0061\u0070\u0022\u0020\u0074\u0061\u0062\u006c\u0065\u0073\u0020\u0066\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0073\u0079\u006d\u0062o\u006c\u0069c\u0020\u0054\u0072\u0075e\u0054\u0079\u0070\u0065\u0020\u0066\u006f\u006e\u0074\u0073 \u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0020\u0065\u0078\u0061\u0063\u0074\u006cy\u0020\u006f\u006ee\u0020\u0065\u006e\u0063\u006f\u0064\u0069n\u0067\u002e";
);var _fdcff string ;if _abc ,_afbd :=_eb .GetName (_agdb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_afbd {_fdcff =_abc .String ();};if _fdcff !="\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065"{return _ddd ;};_dgbgb :=_aeab .FontDescriptor ();_acag ,_afcb :=_eb .GetIntVal (_dgbgb .Flags );
if !_afcb {_bf .Log .Debug ("\u0066\u006c\u0061\u0067\u0073 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0066o\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u006f\u0072");
return _ee (_cdeaa ,_aeaa );};_cded :=(uint32 (_acag )>>3)!=0;if !_cded {return _ddd ;};return _ddd ;};func _geec (_abdc *_a .CompliancePdfReader )(_dgdd ViolatedRule ){_aeca ,_fdff :=_efcc (_abdc );if !_fdff {return _ddd ;};if _aeca .Get ("\u0052\u0065\u0071u\u0069\u0072\u0065\u006d\u0065\u006e\u0074\u0073")!=nil {return _ee ("\u0036\u002e\u0031\u0031\u002d\u0031","Th\u0065\u0020d\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0063a\u0074\u0061\u006c\u006f\u0067\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020R\u0065q\u0075\u0069\u0072\u0065\u006d\u0065\u006e\u0074s\u0020k\u0065\u0079.");
};return _ddd ;};func _bccf (_bagg *_gde .Document )error {_bbdd ,_eebf :=_bagg .FindCatalog ();if !_eebf {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};_dgde ,_eebf :=_eb .GetDict (_bbdd .Object .Get ("\u0050\u0065\u0072m\u0073"));
if _eebf {_ceff :=_eb .MakeDict ();_abbf :=_dgde .Keys ();for _ ,_fed :=range _abbf {if _fed .String ()=="\u0055\u0052\u0033"||_fed .String ()=="\u0044\u006f\u0063\u004d\u0044\u0050"{_ceff .Set (_fed ,_dgde .Get (_fed ));};};_bbdd .Object .Set ("\u0050\u0065\u0072m\u0073",_ceff );
};return nil ;};func _bacg (_fcga *_a .CompliancePdfReader ,_edbe standardType ,_ccddf bool )(_beacc []ViolatedRule ){_dgdf ,_edgda :=_efcc (_fcga );if !_edgda {return []ViolatedRule {_ee ("\u0036.\u0036\u002e\u0032\u002e\u0031\u002d1","\u0063a\u0074a\u006c\u006f\u0067\u0020\u006eo\u0074\u0020f\u006f\u0075\u006e\u0064\u002e")};
};_eagce :=_dgdf .Get ("\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061");if _eagce ==nil {return []ViolatedRule {_ee ("\u0036.\u0036\u002e\u0032\u002e\u0031\u002d1","\u0054\u0068\u0065\u0020\u0043\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u0020\u006f\u0066\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074ai\u006e\u0020\u0074\u0068\u0065\u0020\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020\u006b\u0065\u0079\u0020\u0077\u0068\u006f\u0073\u0065\u0020v\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u0061\u0020m\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020s\u0074\u0072\u0065\u0061\u006d")};
};_gecae ,_edgda :=_eb .GetStream (_eagce );if !_edgda {return []ViolatedRule {_ee ("\u0036.\u0036\u002e\u0032\u002e\u0031\u002d1","\u0054\u0068\u0065\u0020\u0043\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u0020\u006f\u0066\u0020\u0061\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074ai\u006e\u0020\u0074\u0068\u0065\u0020\u004d\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020\u006b\u0065\u0079\u0020\u0077\u0068\u006f\u0073\u0065\u0020v\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u0061\u0020m\u0065\u0074\u0061\u0064\u0061\u0074\u0061\u0020s\u0074\u0072\u0065\u0061\u006d")};
};_gfeaf ,_feeda :=_bg .LoadDocument (_gecae .Stream );if _feeda !=nil {return []ViolatedRule {_ee ("\u0036.\u0036\u002e\u0032\u002e\u0031\u002d4","\u0041\u006c\u006c\u0020\u006de\u0074\u0061\u0064a\u0074\u0061\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0073\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020i\u006e \u0074\u0068\u0065\u0020\u0050\u0044\u0046 \u0073\u0068\u0061\u006c\u006c\u0020\u0063o\u006e\u0066\u006f\u0072\u006d\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020\u0058\u004d\u0050\u0020\u0053\u0070\u0065ci\u0066\u0069\u0063\u0061\u0074\u0069\u006fn\u002e\u0020\u0041\u006c\u006c\u0020c\u006fn\u0074\u0065\u006e\u0074\u0020\u006f\u0066\u0020\u0061\u006c\u006c\u0020\u0058\u004d\u0050\u0020p\u0061\u0063\u006b\u0065\u0074\u0073 \u0073h\u0061\u006c\u006c \u0062\u0065\u0020\u0077\u0065\u006c\u006c\u002d\u0066o\u0072\u006de\u0064")};
};_dcaa :=_gfeaf .GetGoXmpDocument ();var _ddebb []*_cc .Namespace ;for _ ,_ddfaf :=range _dcaa .Namespaces (){switch _ddfaf .Name {case _df .NsDc .Name ,_gd .NsPDF .Name ,_ag .NsXmp .Name ,_dd .NsXmpRights .Name ,_aga .Namespace .Name ,_ea .Namespace .Name ,_de .NsXmpMM .Name ,_ea .FieldNS .Name ,_ea .SchemaNS .Name ,_ea .PropertyNS .Name ,"\u0073\u0074\u0045v\u0074","\u0073\u0074\u0056e\u0072","\u0073\u0074\u0052e\u0066","\u0073\u0074\u0044i\u006d","\u0078a\u0070\u0047\u0049\u006d\u0067","\u0073\u0074\u004ao\u0062","\u0078\u006d\u0070\u0069\u0064\u0071":continue ;
};_ddebb =append (_ddebb ,_ddfaf );};_gaade :=true ;_afbgf ,_feeda :=_gfeaf .GetPdfaExtensionSchemas ();if _feeda ==nil {for _ ,_eecd :=range _ddebb {var _daeab bool ;for _fgafa :=range _afbgf {if _eecd .URI ==_afbgf [_fgafa ].NamespaceURI {_daeab =true ;
break ;};};if !_daeab {_gaade =false ;break ;};};}else {_gaade =false ;};if !_gaade {_beacc =append (_beacc ,_ee ("\u0036.\u0036\u002e\u0032\u002e\u0033\u002d7","\u0041\u006c\u006c\u0020\u0070\u0072\u006f\u0070e\u0072\u0074\u0069e\u0073\u0020\u0073\u0070\u0065\u0063i\u0066\u0069\u0065\u0064\u0020\u0069\u006e\u0020\u0058\u004d\u0050\u0020\u0066\u006f\u0072m\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0075s\u0065\u0020\u0065\u0069\u0074\u0068\u0065\u0072\u0020\u0074\u0068\u0065\u0020\u0070\u0072\u0065\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0073\u0063he\u006da\u0073 \u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0058\u004d\u0050\u0020\u0053\u0070\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002c\u0020\u0049\u0053\u004f\u0020\u0031\u00390\u0030\u0035-\u0031\u0020\u006f\u0072\u0020\u0074h\u0069s\u0020\u0070\u0061\u0072\u0074\u0020\u006f\u0066\u0020\u0049\u0053\u004f\u0020\u0031\u0039\u0030\u0030\u0035\u002c\u0020o\u0072\u0020\u0061\u006e\u0079\u0020e\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u0020\u0073c\u0068\u0065\u006das\u0020\u0074\u0068\u0061\u0074\u0020\u0063\u006fm\u0070\u006c\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0036\u002e\u0036\u002e\u0032.\u0033\u002e\u0032\u002e"));
};_gadgg ,_edgda :=_gfeaf .GetPdfAID ();if !_edgda {_beacc =append (_beacc ,_ee ("\u0036.\u0036\u002e\u0034\u002d\u0031","\u0054\u0068\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0076\u0065\u0072\u0073\u0069\u006f\u006e\u0020\u0061n\u0064\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020\u006c\u0065\u0076\u0065l\u0020\u006f\u0066\u0020\u0061\u0020\u0066\u0069\u006c\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073\u0070e\u0063\u0069\u0066\u0069\u0065\u0064\u0020\u0075\u0073\u0069\u006e\u0067\u0020\u0074\u0068\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u0020\u0049\u0064\u0065\u006e\u0074\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u0020\u0065\u0078\u0074\u0065\u006e\u0073\u0069\u006f\u006e\u0020\u0073\u0063h\u0065\u006da."));
}else {if _gadgg .Part !=_edbe ._dda {_beacc =append (_beacc ,_ee ("\u0036.\u0036\u002e\u0034\u002d\u0032","\u0054h\u0065\u0020\u0076\u0061lue\u0020\u006f\u0066\u0020p\u0064\u0066\u0061\u0069\u0064\u003a\u0070\u0061\u0072\u0074 \u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0074\u0068\u0065\u0020\u0070\u0061\u0072\u0074\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0049\u0053\u004f\u002019\u0030\u0030\u0035 \u0074\u006f\u0020\u0077\u0068i\u0063h\u0020\u0074\u0068\u0065\u0020\u0066\u0069\u006c\u0065 \u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0073\u002e"));
};if _edbe ._ff =="\u0041"&&_gadgg .Conformance !="\u0041"{_beacc =append (_beacc ,_ee ("\u0036.\u0036\u002e\u0034\u002d\u0033","\u0041\u0020\u004c\u0065\u0076\u0065\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061l\u006c\u0020\u0073\u0070ec\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020as\u0020\u0041\u002e\u0020\u0041 \u004c\u0065v\u0065\u006c\u0020\u0042\u0020c\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061lu\u0065\u0020o\u0066 \u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e\u0020\u0041\u0020\u004c\u0065\u0076\u0065\u006c \u0055\u0020\u0063\u006f\u006e\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063\u0069\u0066\u0079 \u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006ff\u0020\u0070\u0064f\u0061i\u0064\u003ac\u006fn\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065 \u0061\u0073\u0020\u0055."));
}else if _edbe ._ff =="\u0055"&&(_gadgg .Conformance !="\u0041"&&_gadgg .Conformance !="\u0055"){_beacc =append (_beacc ,_ee ("\u0036.\u0036\u002e\u0034\u002d\u0033","\u0041\u0020\u004c\u0065\u0076\u0065\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061l\u006c\u0020\u0073\u0070ec\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020as\u0020\u0041\u002e\u0020\u0041 \u004c\u0065v\u0065\u006c\u0020\u0042\u0020c\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061lu\u0065\u0020o\u0066 \u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e\u0020\u0041\u0020\u004c\u0065\u0076\u0065\u006c \u0055\u0020\u0063\u006f\u006e\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063\u0069\u0066\u0079 \u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006ff\u0020\u0070\u0064f\u0061i\u0064\u003ac\u006fn\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065 \u0061\u0073\u0020\u0055."));
}else if _edbe ._ff =="\u0042"&&(_gadgg .Conformance !="\u0041"&&_gadgg .Conformance !="\u0042"&&_gadgg .Conformance !="\u0055"){_beacc =append (_beacc ,_ee ("\u0036.\u0036\u002e\u0034\u002d\u0033","\u0041\u0020\u004c\u0065\u0076\u0065\u006c\u0020\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065 \u0073\u0068\u0061l\u006c\u0020\u0073\u0070ec\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0020\u006f\u0066\u0020\u0070\u0064\u0066\u0061\u0069\u0064\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006ec\u0065\u0020as\u0020\u0041\u002e\u0020\u0041 \u004c\u0065v\u0065\u006c\u0020\u0042\u0020c\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0074\u0068\u0065\u0020\u0076\u0061lu\u0065\u0020o\u0066 \u0070\u0064\u0066\u0061\u0069d\u003a\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065\u0020\u0061\u0073\u0020\u0042\u002e\u0020\u0041\u0020\u004c\u0065\u0076\u0065\u006c \u0055\u0020\u0063\u006f\u006e\u0066\u006fr\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020s\u0070\u0065\u0063\u0069\u0066\u0079 \u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006ff\u0020\u0070\u0064f\u0061i\u0064\u003ac\u006fn\u0066\u006f\u0072\u006d\u0061\u006e\u0063\u0065 \u0061\u0073\u0020\u0055."));
};};return _beacc ;};type imageInfo struct{ColorSpace _eb .PdfObjectName ;BitsPerComponent int ;ColorComponents int ;Width int ;Height int ;Stream *_eb .PdfObjectStream ;_fga bool ;};func _bfbe (_dceb *_eb .PdfObjectDictionary ,_edeaa map[*_eb .PdfObjectStream ][]byte ,_cgeg map[*_eb .PdfObjectStream ]*_ba .CMap )ViolatedRule {const (_aeede ="\u0036.\u0033\u002e\u0033\u002d\u0034";
_ffcg ="\u0046\u006f\u0072\u0020\u0074\u0068\u006fs\u0065\u0020\u0043\u004d\u0061\u0070\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0061\u0072e\u0020\u0065m\u0062\u0065\u0064de\u0064\u002c\u0020\u0074\u0068\u0065\u0020\u0069\u006et\u0065\u0067\u0065\u0072 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0057\u004d\u006f\u0064\u0065\u0020\u0065\u006e\u0074r\u0079\u0020i\u006e t\u0068\u0065\u0020CM\u0061\u0070\u0020\u0064\u0069\u0063\u0074\u0069o\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0069\u0064\u0065\u006e\u0074\u0069\u0063\u0061\u006c\u0020\u0074\u006f \u0074h\u0065\u0020\u0057\u004d\u006f\u0064e\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064ed\u0020\u0043\u004d\u0061\u0070\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e";
);var _ggded string ;if _ggbgf ,_bgfg :=_eb .GetName (_dceb .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_bgfg {_ggded =_ggbgf .String ();};if _ggded !="\u0054\u0079\u0070e\u0030"{return _ddd ;};_gedd :=_dceb .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _ ,_bgeg :=_eb .GetName (_gedd );_bgeg {return _ddd ;};_egca ,_efaa :=_eb .GetStream (_gedd );if !_efaa {return _ee (_aeede ,_ffcg );};_gacea ,_gdfg :=_cafgb (_egca ,_edeaa ,_cgeg );if _gdfg !=nil {return _ee (_aeede ,_ffcg );};_badb ,_abfb :=_eb .GetIntVal (_egca .Get ("\u0057\u004d\u006fd\u0065"));
_ffdf ,_adbdba :=_gacea .WMode ();if _abfb &&_adbdba {if _ffdf !=_badb {return _ee (_aeede ,_ffcg );};};if (_abfb &&!_adbdba )||(!_abfb &&_adbdba ){return _ee (_aeede ,_ffcg );};return _ddd ;};

// Validate checks if provided input document reader matches given PDF/A profile.
func Validate (d *_a .CompliancePdfReader ,profile Profile )error {return profile .ValidateStandard (d )};func _fcgef (_geff *_a .CompliancePdfReader )(_eeccb []ViolatedRule ){var (_febb ,_aggb ,_ccgd ,_bbbd ,_cggb bool ;_gdaf func (_eb .PdfObject ););
_gdaf =func (_aeegb _eb .PdfObject ){switch _eddca :=_aeegb .(type ){case *_eb .PdfObjectInteger :if !_febb &&(int64 (*_eddca )> _g .MaxInt32 ||int64 (*_eddca )< -_g .MaxInt32 ){_eeccb =append (_eeccb ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0031","L\u0061\u0072\u0067e\u0073\u0074\u0020\u0049\u006e\u0074\u0065\u0067\u0065\u0072\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u0032\u002c\u0031\u0034\u0037,\u0034\u0038\u0033,\u0036\u0034\u0037\u002e\u0020\u0053\u006d\u0061\u006c\u006c\u0065\u0073\u0074 \u0069\u006e\u0074\u0065g\u0065\u0072\u0020\u0076a\u006c\u0075\u0065\u0020\u0069\u0073\u0020\u002d\u0032\u002c\u0031\u0034\u0037\u002c\u0034\u0038\u0033,\u0036\u0034\u0038\u002e"));
_febb =true ;};case *_eb .PdfObjectFloat :if !_aggb &&(_g .Abs (float64 (*_eddca ))> _g .MaxFloat32 ){_eeccb =append (_eeccb ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0032","\u0041 \u0063\u006f\u006e\u0066orm\u0069\u006e\u0067\u0020f\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0072\u0065\u0061\u006c\u0020\u006e\u0075\u006db\u0065\u0072\u0020\u006f\u0075\u0074\u0073\u0069de\u0020\u0074\u0068e\u0020\u0072\u0061\u006e\u0067e\u0020o\u0066\u0020\u002b\u002f\u002d\u0033\u002e\u0034\u00303\u0020\u0078\u0020\u0031\u0030\u005e\u0033\u0038\u002e"));
};case *_eb .PdfObjectString :if !_ccgd &&len ([]byte (_eddca .Str ()))> 32767{_eeccb =append (_eeccb ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0033","M\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u006c\u0065n\u0067\u0074\u0068\u0020\u006f\u0066\u0020a \u0073\u0074\u0072\u0069n\u0067\u0020\u0028\u0069\u006e\u0020\u0062\u0079\u0074es\u0029\u0020i\u0073\u0020\u0033\u0032\u0037\u0036\u0037\u002e"));
_ccgd =true ;};case *_eb .PdfObjectName :if !_bbbd &&len ([]byte (*_eddca ))> 127{_eeccb =append (_eeccb ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0034","\u004d\u0061\u0078\u0069\u006d\u0075\u006d \u006c\u0065\u006eg\u0074\u0068\u0020\u006ff\u0020\u0061\u0020\u006e\u0061\u006d\u0065\u0020\u0028\u0069\u006e\u0020\u0062\u0079\u0074\u0065\u0073\u0029\u0020\u0069\u0073\u0020\u0031\u0032\u0037\u002e"));
_bbbd =true ;};case *_eb .PdfObjectArray :for _ ,_bebc :=range _eddca .Elements (){_gdaf (_bebc );};if !_cggb &&(_eddca .Len ()==4||_eddca .Len ()==5){_abgdb ,_feeea :=_eb .GetName (_eddca .Get (0));if !_feeea {return ;};if *_abgdb !="\u0044e\u0076\u0069\u0063\u0065\u004e"{return ;
};_acedb :=_eddca .Get (1);_acedb =_eb .TraceToDirectObject (_acedb );_dcgc ,_feeea :=_eb .GetArray (_acedb );if !_feeea {return ;};if _dcgc .Len ()> 32{_eeccb =append (_eeccb ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0039","\u004d\u0061\u0078\u0069\u006d\u0075\u006d \u006e\u0075\u006db\u0065\u0072\u0020\u006ff\u0020\u0044\u0065\u0076\u0069\u0063\u0065\u004e\u0020\u0063\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074\u0073\u0020\u0069\u0073\u0020\u0033\u0032\u002e"));
_cggb =true ;};};case *_eb .PdfObjectDictionary :_gcbg :=_eddca .Keys ();for _fgcg ,_fgad :=range _gcbg {_gdaf (&_gcbg [_fgcg ]);_gdaf (_eddca .Get (_fgad ));};case *_eb .PdfObjectStream :_gdaf (_eddca .PdfObjectDictionary );case *_eb .PdfObjectStreams :for _ ,_cbfg :=range _eddca .Elements (){_gdaf (_cbfg );
};case *_eb .PdfObjectReference :_gdaf (_eddca .Resolve ());};};_faca :=_geff .GetObjectNums ();if len (_faca )> 8388607{_eeccb =append (_eeccb ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0033\u002d\u0037","\u004d\u0061\u0078\u0069\u006d\u0075\u006d\u0020\u006e\u0075\u006d\u0062\u0065\u0072\u0020\u006f\u0066\u0020in\u0064i\u0072\u0065\u0063\u0074\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0073 \u0069\u006e\u0020\u0061\u0020\u0050\u0044\u0046\u0020\u0066\u0069\u006c\u0065\u0020\u0069\u0073\u00208\u002c\u0033\u0038\u0038\u002c\u0036\u0030\u0037\u002e"));
};for _ ,_efggb :=range _faca {_gcbac ,_fcgf :=_geff .GetIndirectObjectByNumber (_efggb );if _fcgf !=nil {continue ;};_ccccc :=_eb .TraceToDirectObject (_gcbac );_gdaf (_ccccc );};return _eeccb ;};func _fceg (_agg *_a .PdfInfo ,_gbd func ()_ca .Time )error {var _deg *_a .PdfDate ;
if _agg .CreationDate ==nil {_addb ,_gbdb :=_a .NewPdfDateFromTime (_gbd ());if _gbdb !=nil {return _gbdb ;};_deg =&_addb ;_agg .CreationDate =_deg ;};if _agg .ModifiedDate ==nil {if _deg !=nil {_cfg ,_fgef :=_a .NewPdfDateFromTime (_gbd ());if _fgef !=nil {return _fgef ;
};_deg =&_cfg ;};_agg .ModifiedDate =_deg ;};return nil ;};func _dcbg (_aba *_a .CompliancePdfReader )(_aagc []ViolatedRule ){var _fede ,_dafc ,_ddaea bool ;if _aba .ParserMetadata ().HasNonConformantStream (){_aagc =[]ViolatedRule {_ee ("\u0036.\u0031\u002e\u0037\u002d\u0031","T\u0068\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020f\u006f\u006cl\u006fw\u0065\u0064\u0020e\u0069\u0074h\u0065\u0072\u0020\u0062\u0079\u0020\u0061 \u0043\u0041\u0052\u0052I\u0041\u0047\u0045\u0020\u0052E\u0054\u0055\u0052\u004e\u0020\u00280\u0044\u0068\u0029\u0020\u0061\u006e\u0064\u0020\u004c\u0049\u004e\u0045\u0020F\u0045\u0045\u0044\u0020\u0028\u0030\u0041\u0068\u0029\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0073\u0065\u0071\u0075\u0065\u006e\u0063\u0065\u0020o\u0072\u0020\u0062\u0079\u0020\u0061 \u0073\u0069ng\u006c\u0065\u0020\u004cIN\u0045 \u0046\u0045\u0045\u0044 \u0063\u0068\u0061r\u0061\u0063\u0074\u0065\u0072\u002e\u0020T\u0068\u0065\u0020e\u006e\u0064\u0073\u0074r\u0065\u0061\u006d\u0020\u006b\u0065\u0079\u0077\u006fr\u0064\u0020\u0073\u0068\u0061\u006c\u006c \u0062e\u0020p\u0072\u0065\u0063\u0065\u0064\u0065\u0064\u0020\u0062\u0079\u0020\u0061n\u0020\u0045\u004f\u004c \u006d\u0061\u0072\u006b\u0065\u0072\u002e")};
};for _ ,_cbcf :=range _aba .GetObjectNums (){_bagga ,_ :=_aba .GetIndirectObjectByNumber (_cbcf );if _bagga ==nil {continue ;};_ecff ,_gbgb :=_eb .GetStream (_bagga );if !_gbgb {continue ;};if !_fede {_ebdg :=_ecff .Get ("\u004c\u0065\u006e\u0067\u0074\u0068");
if _ebdg ==nil {_aagc =append (_aagc ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0032","\u006e\u006f\u0020'\u004c\u0065\u006e\u0067\u0074\u0068\u0027\u0020\u006b\u0065\u0079\u0020\u0066\u006f\u0075\u006e\u0064\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0073\u0074\u0072\u0065a\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074"));
_fede =true ;}else {_ddeg ,_dccd :=_eb .GetIntVal (_ebdg );if !_dccd {_aagc =append (_aagc ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0032","s\u0074\u0072\u0065\u0061\u006d\u0020\u0027\u004c\u0065\u006e\u0067\u0074\u0068\u0027\u0020\u006b\u0065\u0079 \u0073\u0068\u006f\u0075\u006c\u0064\u0020\u0062\u0065\u0020an\u0020\u0069\u006et\u0065g\u0065\u0072"));
_fede =true ;}else {if len (_ecff .Stream )!=_ddeg {_aagc =append (_aagc ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0032","\u0073\u0074\u0072\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u006c\u0065\u006e\u0067th\u0020v\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020m\u0061\u0074\u0063\u0068\u0020\u0074\u0068\u0065\u0020\u0073\u0069\u007a\u0065\u0020\u006f\u0066\u0020t\u0068\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d"));
_fede =true ;};};};};if !_dafc {if _ecff .Get ("\u0046")!=nil {_dafc =true ;_aagc =append (_aagc ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074r\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020\u0027\u0046\u0027\u002c\u0027\u0046\u0046\u0069\u006c\u0074\u0065r\u0027\u002c'\u0046\u0044\u0065\u0063o\u0064\u0065\u0050\u0061\u0072a\u006d\u0073\u0027\u0020\u006b\u0065\u0079"));
};if _ecff .Get ("\u0046F\u0069\u006c\u0074\u0065\u0072")!=nil &&!_dafc {_dafc =true ;_aagc =append (_aagc ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074r\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020\u0027\u0046\u0027\u002c\u0027\u0046\u0046\u0069\u006c\u0074\u0065r\u0027\u002c'\u0046\u0044\u0065\u0063o\u0064\u0065\u0050\u0061\u0072a\u006d\u0073\u0027\u0020\u006b\u0065\u0079"));
continue ;};if _ecff .Get ("\u0046\u0044\u0065\u0063\u006f\u0064\u0065\u0050\u0061\u0072\u0061\u006d\u0073")!=nil &&!_dafc {_dafc =true ;_aagc =append (_aagc ,_ee ("\u0036.\u0031\u002e\u0037\u002d\u0033","\u0073\u0074r\u0065\u0061\u006d\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0073\u0068\u006f\u0075\u006c\u0064\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020\u0027\u0046\u0027\u002c\u0027\u0046\u0046\u0069\u006c\u0074\u0065r\u0027\u002c'\u0046\u0044\u0065\u0063o\u0064\u0065\u0050\u0061\u0072a\u006d\u0073\u0027\u0020\u006b\u0065\u0079"));
continue ;};};if !_ddaea {_ggbc ,_cfaa :=_eb .GetName (_eb .TraceToDirectObject (_ecff .Get ("\u0046\u0069\u006c\u0074\u0065\u0072")));if !_cfaa {continue ;};if *_ggbc ==_eb .StreamEncodingFilterNameLZW {_ddaea =true ;_aagc =append (_aagc ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0030\u002d\u0031","\u0054h\u0065\u0020L\u005a\u0057\u0044\u0065c\u006f\u0064\u0065 \u0066\u0069\u006c\u0074\u0065\u0072\u0020\u0073\u0068al\u006c\u0020\u006eo\u0074\u0020b\u0065\u0020\u0070\u0065\u0072\u006di\u0074\u0074e\u0064\u002e"));
};};};return _aagc ;};var _ddd =ViolatedRule {};func _ggc (_bgcb *_gde .Document )error {_daf ,_fgeb :=_bgcb .FindCatalog ();if !_fgeb {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};_agc ,_fgeb :=_eb .GetDict (_daf .Object .Get ("\u004f\u0043\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073"));if !_fgeb {return nil ;};_dba ,_fgeb :=_eb .GetDict (_agc .Get ("\u0044"));if _fgeb {if _dba .Get ("\u0041\u0053")!=nil {_dba .Remove ("\u0041\u0053");
};};_efgg ,_fgeb :=_eb .GetArray (_agc .Get ("\u0043o\u006e\u0066\u0069\u0067\u0073"));if _fgeb {for _dgfed :=0;_dgfed < _efgg .Len ();_dgfed ++{_eggc ,_dgbgg :=_eb .GetDict (_efgg .Get (_dgfed ));if !_dgbgg {continue ;};if _eggc .Get ("\u0041\u0053")!=nil {_eggc .Remove ("\u0041\u0053");
};};};return nil ;};func _cbfcb (_bbee *_a .PdfFont ,_fdgg *_eb .PdfObjectDictionary )ViolatedRule {const (_ggdd ="\u0036.\u0033\u002e\u0035\u002d\u0032";_acbg ="\u0046\u006f\u0072\u0020\u0061l\u006c\u0020\u0054\u0079\u0070\u0065\u0020\u0031\u0020\u0066\u006f\u006e\u0074 \u0073\u0075bs\u0065\u0074\u0073 \u0072\u0065\u0066e\u0072\u0065\u006e\u0063\u0065\u0064\u0020\u0077\u0069\u0074\u0068\u0069\u006e\u0020\u0061\u0020\u0063\u006fn\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u002c\u0020\u0074he\u0020f\u006f\u006e\u0074\u0020\u0064\u0065s\u0063r\u0069\u0070\u0074o\u0072\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006ec\u006c\u0075\u0064e\u0020\u0061\u0020\u0043\u0068\u0061\u0072\u0053\u0065\u0074\u0020\u0073\u0074\u0072\u0069\u006e\u0067\u0020\u006c\u0069\u0073\u0074\u0069\u006e\u0067\u0020\u0074\u0068\u0065\u0020\u0063\u0068\u0061\u0072a\u0063\u0074\u0065\u0072 \u006e\u0061\u006d\u0065\u0073\u0020d\u0065\u0066i\u006e\u0065\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020f\u006f\u006e\u0074\u0020s\u0075\u0062\u0073\u0065\u0074, \u0061\u0073 \u0064\u0065s\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069\u006e \u0050\u0044\u0046\u0020\u0052e\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0054\u0061\u0062\u006ce\u0020\u0035\u002e1\u0038\u002e";
);var _accge string ;if _afac ,_gbfa :=_eb .GetName (_fdgg .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_gbfa {_accge =_afac .String ();};if _accge !="\u0054\u0079\u0070e\u0031"{return _ddd ;};if _bad .IsStdFont (_bad .StdFontName (_bbee .BaseFont ())){return _ddd ;
};_bacd :=_bbee .FontDescriptor ();if _bacd .CharSet ==nil {return _ee (_ggdd ,_acbg );};return _ddd ;};func _bbbeg (_ebbab *_a .PdfFont ,_gebf *_eb .PdfObjectDictionary )ViolatedRule {const (_dfcb ="\u0036.\u0033\u002e\u0037\u002d\u0032";_bcb ="\u0041l\u006c\u0020\u0073\u0079\u006d\u0062\u006f\u006c\u0069\u0063\u0020\u0054\u0072u\u0065\u0054\u0079p\u0065\u0020\u0066\u006f\u006e\u0074s\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0073\u0070\u0065\u0063\u0069\u0066\u0079\u0020\u0061\u006e\u0020\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067\u0020\u0065n\u0074\u0072\u0079\u0020\u0069n\u0020\u0074\u0068e\u0020\u0066\u006f\u006e\u0074 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e";
);var _cfba string ;if _gdcf ,_gddg :=_eb .GetName (_gebf .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_gddg {_cfba =_gdcf .String ();};if _cfba !="\u0054\u0072\u0075\u0065\u0054\u0079\u0070\u0065"{return _ddd ;};_cbee :=_ebbab .FontDescriptor ();_edfcd ,_cdbe :=_eb .GetIntVal (_cbee .Flags );
if !_cdbe {_bf .Log .Debug ("\u0066\u006c\u0061\u0067\u0073 \u006e\u006f\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0066o\u0072\u0020\u0074\u0068\u0065\u0020\u0066\u006f\u006e\u0074\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0070\u0074\u006f\u0072");
return _ee (_dfcb ,_bcb );};_bffcc :=(uint32 (_edfcd )>>3)&1;_defe :=_bffcc !=0;if !_defe {return _ddd ;};if _gebf .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067")!=nil {return _ee (_dfcb ,_bcb );};return _ddd ;};func _gebdbc (_dcbc *_eb .PdfObjectDictionary ,_aedcf map[*_eb .PdfObjectStream ][]byte ,_ggcd map[*_eb .PdfObjectStream ]*_ba .CMap )ViolatedRule {const (_cgdga ="\u0036\u002e\u0032\u002e\u0031\u0031\u002e\u0033\u002d\u0034";
_cdebe ="\u0046\u006f\u0072\u0020\u0074\u0068\u006fs\u0065\u0020\u0043\u004d\u0061\u0070\u0073\u0020\u0074\u0068\u0061\u0074\u0020\u0061\u0072e\u0020\u0065m\u0062\u0065\u0064de\u0064\u002c\u0020\u0074\u0068\u0065\u0020\u0069\u006et\u0065\u0067\u0065\u0072 \u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0057\u004d\u006f\u0064\u0065\u0020\u0065\u006e\u0074r\u0079\u0020i\u006e t\u0068\u0065\u0020CM\u0061\u0070\u0020\u0064\u0069\u0063\u0074\u0069o\u006ea\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0069\u0064\u0065\u006e\u0074\u0069\u0063\u0061\u006c\u0020\u0074\u006f \u0074h\u0065\u0020\u0057\u004d\u006f\u0064e\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0065\u006d\u0062\u0065\u0064\u0064ed\u0020\u0043\u004d\u0061\u0070\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e";
);var _dfeg string ;if _fbaeb ,_cabe :=_eb .GetName (_dcbc .Get ("\u0053u\u0062\u0074\u0079\u0070\u0065"));_cabe {_dfeg =_fbaeb .String ();};if _dfeg !="\u0054\u0079\u0070e\u0030"{return _ddd ;};_fedf :=_dcbc .Get ("\u0045\u006e\u0063\u006f\u0064\u0069\u006e\u0067");
if _ ,_ffga :=_eb .GetName (_fedf );_ffga {return _ddd ;};_gdgce ,_cffdd :=_eb .GetStream (_fedf );if !_cffdd {return _ee (_cgdga ,_cdebe );};_fbcf ,_gagdd :=_cafgb (_gdgce ,_aedcf ,_ggcd );if _gagdd !=nil {return _ee (_cgdga ,_cdebe );};_ebbace ,_bgdg :=_eb .GetIntVal (_gdgce .Get ("\u0057\u004d\u006fd\u0065"));
_aafga ,_cdcg :=_fbcf .WMode ();if _bgdg &&_cdcg {if _aafga !=_ebbace {return _ee (_cgdga ,_cdebe );};};if (_bgdg &&!_cdcg )||(!_bgdg &&_cdcg ){return _ee (_cgdga ,_cdebe );};return _ddd ;};

// StandardName gets the name of the standard.
func (_aeed *profile1 )StandardName ()string {return _d .Sprintf ("\u0050D\u0046\u002f\u0041\u002d\u0031\u0025s",_aeed ._ecd ._ff );};

// ValidateStandard checks if provided input CompliancePdfReader matches rules that conforms PDF/A-1 standard.
func (_ecfg *profile1 )ValidateStandard (r *_a .CompliancePdfReader )error {_cce :=VerificationError {ConformanceLevel :_ecfg ._ecd ._dda ,ConformanceVariant :_ecfg ._ecd ._ff };if _fdab :=_ggbe (r );_fdab !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_fdab );
};if _ggbb :=_ebgf (r );_ggbb !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_ggbb );};if _degb :=_agaf (r );_degb !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_degb );};if _fcde :=_dadd (r );_fcde !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_fcde );
};if _ebgd :=_gaca (r );_ebgd !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_ebgd );};if _fccg :=_gfgcf (r );len (_fccg )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_fccg ...);};if _dgbc :=_acaa (r );_dgbc !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_dgbc );
};if _fgfe :=_bgec (r );len (_fgfe )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_fgfe ...);};if _fafd :=_dcbg (r );len (_fafd )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_fafd ...);};if _eeda :=_fac (r );len (_eeda )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_eeda ...);
};if _eggb :=_bade (r );_eggb !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_eggb );};if _feefb :=_cgcf (r );len (_feefb )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_feefb ...);};if _bcdg :=_gead (r );len (_bcdg )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_bcdg ...);
};if _ffgb :=_bgbcg (r );_ffgb !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_ffgb );};if _dbea :=_ddcc (r ,false );len (_dbea )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_dbea ...);};if _gbcf :=_ecgd (r );len (_gbcf )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_gbcf ...);
};if _gef :=_bagb (r );_gef !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_gef );};if _bgcg :=_efeg (r );_bgcg !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_bgcg );};if _dfdf :=_bcae (r );_dfdf !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_dfdf );
};if _geef :=_fadd (r );_geef !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_geef );};if _accd :=_fbfa (r );_accd !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_accd );};if _bdfd :=_dcacb (r );len (_bdfd )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_bdfd ...);
};if _abda :=_gdbb (r ,_ecfg ._ecd );len (_abda )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_abda ...);};if _bbaa :=_gddf (r );len (_bbaa )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_bbaa ...);};if _afc :=_cdecf (r );_afc !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_afc );
};if _ffdg :=_deae (r );_ffdg !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_ffdg );};if _ddgc :=_bcaed (r );len (_ddgc )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_ddgc ...);};if _dbee :=_eeaaf (r );len (_dbee )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_dbee ...);
};if _fcgc :=_bbdb (r );_fcgc !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_fcgc );};if _fdfbe :=_egeec (r );_fdfbe !=_ddd {_cce .ViolatedRules =append (_cce .ViolatedRules ,_fdfbe );};if _fecf :=_feec (r ,_ecfg ._ecd ,false );len (_fecf )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_fecf ...);
};if _ecfg ._ecd ==_cg (){if _aega :=_dfff (r );len (_aega )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_aega ...);};};if _bage :=_gbdba (r );len (_bage )!=0{_cce .ViolatedRules =append (_cce .ViolatedRules ,_bage ...);};if len (_cce .ViolatedRules )> 0{_c .Slice (_cce .ViolatedRules ,func (_aadbc ,_dggfd int )bool {return _cce .ViolatedRules [_aadbc ].RuleNo < _cce .ViolatedRules [_dggfd ].RuleNo ;
});return _cce ;};return nil ;};func _beaeg (_cdcda *_gde .Document ,_ccc bool )error {_aafa ,_gge :=_cdcda .GetPages ();if !_gge {return nil ;};for _ ,_gfea :=range _aafa {_eae :=_gfea .FindXObjectForms ();for _ ,_ggde :=range _eae {_egc ,_edg :=_a .NewXObjectFormFromStream (_ggde );
if _edg !=nil {return _edg ;};_aacd ,_edg :=_egc .GetContentStream ();if _edg !=nil {return _edg ;};_fbed :=_e .NewContentStreamParser (string (_aacd ));_bag ,_edg :=_fbed .Parse ();if _edg !=nil {return _edg ;};_adac ,_edg :=_gbaa (_egc .Resources ,_bag ,_ccc );
if _edg !=nil {return _edg ;};if len (_adac )==0{continue ;};if _edg =_egc .SetContentStream (_adac ,_eb .NewFlateEncoder ());_edg !=nil {return _edg ;};_egc .ToPdfObject ();};};return nil ;};func (_ffb *documentImages )hasOnlyDeviceGray ()bool {return _ffb ._ac &&!_ffb ._cf &&!_ffb ._eeb };
func _deec (_abbd ,_dfe ,_adef ,_agfc string )(string ,bool ){_cfdd :=_bb .Index (_abbd ,_dfe );if _cfdd ==-1{return "",false ;};_adda :=_bb .Index (_abbd ,_adef );if _adda ==-1{return "",false ;};if _adda < _cfdd {return "",false ;};return _abbd [:_cfdd ]+_dfe +_agfc +_abbd [_adda :],true ;
};func _ccebb (_geaa *_a .CompliancePdfReader )(_aagee []ViolatedRule ){var _acged ,_dbgg bool ;_baagf :=func ()bool {return _acged &&_dbgg };for _ ,_gacad :=range _geaa .GetObjectNums (){_cade ,_bgecb :=_geaa .GetIndirectObjectByNumber (_gacad );if _bgecb !=nil {_bf .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u0020\u0077\u0069\u0074\u0068 \u006e\u0075\u006d\u0062\u0065\u0072\u0020\u0025\u0064\u0020fa\u0069\u006c\u0065d\u003a \u0025\u0076",_gacad ,_bgecb );
continue ;};_gdfbd ,_edfa :=_eb .GetDict (_cade );if !_edfa {continue ;};_geggb ,_edfa :=_eb .GetName (_gdfbd .Get ("\u0054\u0079\u0070\u0065"));if !_edfa {continue ;};if *_geggb !="\u0041\u0063\u0074\u0069\u006f\u006e"{continue ;};_gbef ,_edfa :=_eb .GetName (_gdfbd .Get ("\u0053"));
if !_edfa {if !_acged {_aagee =append (_aagee ,_ee ("\u0036.\u0035\u002e\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u004caun\u0063\u0068\u002c\u0020S\u006f\u0075\u006e\u0064,\u0020\u004d\u006f\u0076\u0069\u0065\u002c\u0020\u0052\u0065\u0073\u0065\u0074\u0046\u006f\u0072\u006d\u002c\u0020\u0049\u006d\u0070\u006f\u0072\u0074\u0044a\u0074\u0061,\u0020\u0048\u0069\u0064\u0065\u002c\u0020\u0053\u0065\u0074\u004f\u0043\u0047\u0053\u0074\u0061\u0074\u0065\u002c\u0020\u0052\u0065\u006e\u0064\u0069\u0074\u0069\u006f\u006e\u002c\u0020T\u0072\u0061\u006e\u0073\u002c\u0020\u0047o\u0054\u006f\u0033\u0044\u0056\u0069\u0065\u0077\u0020\u0061\u006e\u0064\u0020\u004a\u0061v\u0061Sc\u0072\u0069p\u0074\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074 \u0062\u0065\u0020\u0070\u0065\u0072m\u0069\u0074\u0074\u0065\u0064\u002e \u0041\u0064d\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020t\u0068\u0065\u0020\u0064\u0065\u0070\u0072\u0065\u0063\u0061\u0074\u0065\u0064\u0020\u0073\u0065\u0074\u002d\u0073\u0074\u0061\u0074\u0065\u0020\u0061\u006e\u0064\u0020\u006e\u006f\u006f\u0070\u0020\u0061c\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070e\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_acged =true ;if _baagf (){return _aagee ;};};continue ;};switch _a .PdfActionType (*_gbef ){case _a .ActionTypeLaunch ,_a .ActionTypeSound ,_a .ActionTypeMovie ,_a .ActionTypeResetForm ,_a .ActionTypeImportData ,_a .ActionTypeJavaScript ,_a .ActionTypeHide ,_a .ActionTypeSetOCGState ,_a .ActionTypeRendition ,_a .ActionTypeTrans ,_a .ActionTypeGoTo3DView :if !_acged {_aagee =append (_aagee ,_ee ("\u0036.\u0035\u002e\u0031\u002d\u0031","\u0054\u0068\u0065\u0020\u004caun\u0063\u0068\u002c\u0020S\u006f\u0075\u006e\u0064,\u0020\u004d\u006f\u0076\u0069\u0065\u002c\u0020\u0052\u0065\u0073\u0065\u0074\u0046\u006f\u0072\u006d\u002c\u0020\u0049\u006d\u0070\u006f\u0072\u0074\u0044a\u0074\u0061,\u0020\u0048\u0069\u0064\u0065\u002c\u0020\u0053\u0065\u0074\u004f\u0043\u0047\u0053\u0074\u0061\u0074\u0065\u002c\u0020\u0052\u0065\u006e\u0064\u0069\u0074\u0069\u006f\u006e\u002c\u0020T\u0072\u0061\u006e\u0073\u002c\u0020\u0047o\u0054\u006f\u0033\u0044\u0056\u0069\u0065\u0077\u0020\u0061\u006e\u0064\u0020\u004a\u0061v\u0061Sc\u0072\u0069p\u0074\u0020\u0061\u0063\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074 \u0062\u0065\u0020\u0070\u0065\u0072m\u0069\u0074\u0074\u0065\u0064\u002e \u0041\u0064d\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020t\u0068\u0065\u0020\u0064\u0065\u0070\u0072\u0065\u0063\u0061\u0074\u0065\u0064\u0020\u0073\u0065\u0074\u002d\u0073\u0074\u0061\u0074\u0065\u0020\u0061\u006e\u0064\u0020\u006e\u006f\u006f\u0070\u0020\u0061c\u0074\u0069\u006f\u006e\u0073\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070e\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_acged =true ;if _baagf (){return _aagee ;};};continue ;case _a .ActionTypeNamed :if !_dbgg {_bgac ,_cbcff :=_eb .GetName (_gdfbd .Get ("\u004e"));if !_cbcff {_aagee =append (_aagee ,_ee ("\u0036.\u0035\u002e\u0031\u002d\u0032","N\u0061\u006d\u0065\u0064\u0020\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u006f\u0074\u0068e\u0072\u0020\u0074h\u0061\u006e\u0020\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u002c\u0020P\u0072\u0065v\u0050\u0061\u0067\u0065\u002c\u0020\u0046\u0069\u0072\u0073\u0074\u0050a\u0067e\u002c\u0020\u0061\u006e\u0064\u0020\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_dbgg =true ;if _baagf (){return _aagee ;};continue ;};switch *_bgac {case "\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065","\u0050\u0072\u0065\u0076\u0050\u0061\u0067\u0065","\u0046i\u0072\u0073\u0074\u0050\u0061\u0067e","\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065":default:_aagee =append (_aagee ,_ee ("\u0036.\u0035\u002e\u0031\u002d\u0032","N\u0061\u006d\u0065\u0064\u0020\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u006f\u0074\u0068e\u0072\u0020\u0074h\u0061\u006e\u0020\u004e\u0065\u0078\u0074\u0050\u0061\u0067\u0065\u002c\u0020P\u0072\u0065v\u0050\u0061\u0067\u0065\u002c\u0020\u0046\u0069\u0072\u0073\u0074\u0050a\u0067e\u002c\u0020\u0061\u006e\u0064\u0020\u004c\u0061\u0073\u0074\u0050\u0061\u0067\u0065\u0020\u0073h\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_dbgg =true ;if _baagf (){return _aagee ;};continue ;};};};};return _aagee ;};

// ApplyStandard tries to change the content of the writer to match the PDF/A-2 standard.
// Implements model.StandardApplier.
func (_cfae *profile2 )ApplyStandard (document *_gde .Document )(_fgdg error ){_ffce (document ,7);if _fgdg =_cefe (document ,_cfae ._efc .Now );_fgdg !=nil {return _fgdg ;};if _fgdg =_gbf (document );_fgdg !=nil {return _fgdg ;};_dgbd ,_eagc :=_dcbe (_cfae ._efc .CMYKDefaultColorSpace ,_cfae ._fdecd );
_fgdg =_bga (document ,[]pageColorspaceOptimizeFunc {_dgbd },[]documentColorspaceOptimizeFunc {_eagc });if _fgdg !=nil {return _fgdg ;};_age (document );if _fgdg =_bccf (document );_fgdg !=nil {return _fgdg ;};if _fgdg =_ccfg (document ,_cfae ._fdecd ._dda );
_fgdg !=nil {return _fgdg ;};if _fgdg =_aadb (document );_fgdg !=nil {return _fgdg ;};if _fgdg =_begc (document );_fgdg !=nil {return _fgdg ;};if _fgdg =_defc (document );_fgdg !=nil {return _fgdg ;};if _fgdg =_dddg (document );_fgdg !=nil {return _fgdg ;
};if _cfae ._fdecd ._ff =="\u0041"{_gagf (document );};if _fgdg =_ffeb (document ,_cfae ._fdecd ._dda );_fgdg !=nil {return _fgdg ;};if _fgdg =_fggg (document );_fgdg !=nil {return _fgdg ;};if _ecab :=_aedd (document ,_cfae ._fdecd ,_cfae ._efc .Xmp );
_ecab !=nil {return _ecab ;};if _cfae ._fdecd ==_dc (){if _fgdg =_cbdcf (document );_fgdg !=nil {return _fgdg ;};};if _fgdg =_ggc (document );_fgdg !=nil {return _fgdg ;};if _fgdg =_fdef (document );_fgdg !=nil {return _fgdg ;};if _fgdg =_dfec (document );
_fgdg !=nil {return _fgdg ;};return nil ;};func _gddf (_fagc *_a .CompliancePdfReader )(_ecge []ViolatedRule ){var _ceeec ,_efgb ,_cffdc ,_gfcf ,_gcdb ,_ggaf bool ;_fada :=func ()bool {return _ceeec &&_efgb &&_cffdc &&_gfcf &&_gcdb &&_ggaf };_bdff :=func (_acaf *_eb .PdfObjectDictionary )bool {if !_ceeec &&_acaf .Get ("\u0054\u0052")!=nil {_ceeec =true ;
_ecge =append (_ecge ,_ee ("\u0036.\u0032\u002e\u0038\u002d\u0031","\u0041\u006e\u0020\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0054\u0052\u0020\u006b\u0065\u0079\u002e"));
};if _gbfag :=_acaf .Get ("\u0054\u0052\u0032");!_efgb &&_gbfag !=nil {_dcdfa ,_agbd :=_eb .GetName (_gbfag );if !_agbd ||(_agbd &&*_dcdfa !="\u0044e\u0066\u0061\u0075\u006c\u0074"){_efgb =true ;_ecge =append (_ecge ,_ee ("\u0036.\u0032\u002e\u0038\u002d\u0032","\u0041\u006e \u0045\u0078\u0074G\u0053\u0074\u0061\u0074\u0065 \u0064\u0069\u0063\u0074\u0069on\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074a\u0069n\u0020\u0074\u0068\u0065\u0020\u0054R2 \u006b\u0065\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020\u0076al\u0075e\u0020\u006f\u0074\u0068e\u0072 \u0074h\u0061\u006e \u0044\u0065fa\u0075\u006c\u0074\u002e"));
if _fada (){return true ;};};};if _eeaag :=_acaf .Get ("\u0053\u004d\u0061s\u006b");!_cffdc &&_eeaag !=nil {_eefc ,_adge :=_eb .GetName (_eeaag );if !_adge ||(_adge &&*_eefc !="\u004e\u006f\u006e\u0065"){_cffdc =true ;_ecge =append (_ecge ,_ee ("\u0036\u002e\u0034-\u0031","\u0049\u0066\u0020\u0061\u006e \u0053\u004d\u0061\u0073\u006b\u0020\u006be\u0079\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0073\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0045\u0078\u0074\u0047\u0053\u0074\u0061\u0074\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0069\u0074s\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u004e\u006f\u006ee\u002e"));
if _fada (){return true ;};};};if _edcc :=_acaf .Get ("\u0043\u0041");!_gcdb &&_edcc !=nil {_fbfaf ,_defa :=_eb .GetNumberAsFloat (_edcc );if _defa ==nil &&_fbfaf !=1.0{_gcdb =true ;_ecge =append (_ecge ,_ee ("\u0036\u002e\u0034-\u0035","\u0054\u0068\u0065\u0020\u0066ol\u006c\u006fw\u0069\u006e\u0067\u0020\u006b\u0065\u0079\u0073\u002c\u0020\u0069\u0066\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0045\u0078t\u0047\u0053\u0074a\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u002c\u0020\u0073\u0068a\u006c\u006c\u0020\u0068\u0061v\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0073 \u0073h\u006f\u0077\u006e\u003a\u0020\u0043\u0041 \u002d\u0020\u0031\u002e\u0030\u002e"));
if _fada (){return true ;};};};if _cdbb :=_acaf .Get ("\u0063\u0061");!_ggaf &&_cdbb !=nil {_acca ,_fdge :=_eb .GetNumberAsFloat (_cdbb );if _fdge ==nil &&_acca !=1.0{_ggaf =true ;_ecge =append (_ecge ,_ee ("\u0036\u002e\u0034-\u0036","\u0054\u0068\u0065\u0020\u0066ol\u006c\u006fw\u0069\u006e\u0067\u0020\u006b\u0065\u0079\u0073\u002c\u0020\u0069\u0066\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0045\u0078t\u0047\u0053\u0074a\u0074\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u002c\u0020\u0073\u0068a\u006c\u006c\u0020\u0068\u0061v\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006cu\u0065\u0073 \u0073h\u006f\u0077\u006e\u003a\u0020\u0063\u0061 \u002d\u0020\u0031\u002e\u0030\u002e"));
if _fada (){return true ;};};};if _babbd :=_acaf .Get ("\u0042\u004d");!_gfcf &&_babbd !=nil {_gdbbe ,_ggdab :=_eb .GetName (_babbd );if _ggdab {switch _gdbbe .String (){case "\u004e\u006f\u0072\u006d\u0061\u006c","\u0043\u006f\u006d\u0070\u0061\u0074\u0069\u0062\u006c\u0065":default:_gfcf =true ;
_ecge =append (_ecge ,_ee ("\u0036\u002e\u0034-\u0034","T\u0068\u0065\u0020\u0066\u006f\u006cl\u006f\u0077\u0069\u006e\u0067 \u006b\u0065y\u0073\u002c\u0020\u0069\u0066 \u0070res\u0065\u006e\u0074\u0020\u0069\u006e\u0020\u0061\u006e\u0020\u0045\u0078\u0074\u0047S\u0074\u0061t\u0065\u0020\u006f\u0062\u006a\u0065\u0063\u0074\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0068\u0061\u0076\u0065 \u0074\u0068\u0065 \u0076\u0061\u006c\u0075\u0065\u0073\u0020\u0073\u0068\u006f\u0077n\u003a\u0020\u0042\u004d\u0020\u002d\u0020\u004e\u006f\u0072m\u0061\u006c\u0020\u006f\u0072\u0020\u0043\u006f\u006d\u0070\u0061t\u0069\u0062\u006c\u0065\u002e"));
if _fada (){return true ;};};};};return false ;};for _ ,_gabef :=range _fagc .PageList {_eddc :=_gabef .Resources ;if _eddc ==nil {continue ;};if _eddc .ExtGState ==nil {continue ;};_beaeb ,_dacab :=_eb .GetDict (_eddc .ExtGState );if !_dacab {continue ;
};_dfag :=_beaeb .Keys ();for _ ,_cggc :=range _dfag {_cfgcb ,_cdcbe :=_eb .GetDict (_beaeb .Get (_cggc ));if !_cdcbe {continue ;};if _bdff (_cfgcb ){return _ecge ;};};};for _ ,_ggcad :=range _fagc .PageList {_fddd :=_ggcad .Resources ;if _fddd ==nil {continue ;
};_fdb ,_gbfe :=_eb .GetDict (_fddd .XObject );if !_gbfe {continue ;};for _ ,_cffg :=range _fdb .Keys (){_ccfd ,_fgbga :=_eb .GetStream (_fdb .Get (_cffg ));if !_fgbga {continue ;};_gdde ,_fgbga :=_eb .GetDict (_ccfd .Get ("\u0052e\u0073\u006f\u0075\u0072\u0063\u0065s"));
if !_fgbga {continue ;};_dbdag ,_fgbga :=_eb .GetDict (_gdde .Get ("\u0045x\u0074\u0047\u0053\u0074\u0061\u0074e"));if !_fgbga {continue ;};for _ ,_gecfe :=range _dbdag .Keys (){_agdge ,_addce :=_eb .GetDict (_dbdag .Get (_gecfe ));if !_addce {continue ;
};if _bdff (_agdge ){return _ecge ;};};};};return _ecge ;};func _dfff (_gbfbf *_a .CompliancePdfReader )(_efdd []ViolatedRule ){_egfdf :=true ;_daabc ,_dage :=_gbfbf .GetCatalogMarkInfo ();if !_dage {_egfdf =false ;}else {_cccd ,_ggdf :=_eb .GetDict (_daabc );
if _ggdf {_edagb ,_cdab :=_eb .GetBool (_cccd .Get ("\u004d\u0061\u0072\u006b\u0065\u0064"));if !bool (*_edagb )||!_cdab {_egfdf =false ;};}else {_egfdf =false ;};};if !_egfdf {_efdd =append (_efdd ,_ee ("\u0036.\u0038\u002e\u0032\u002e\u0032\u002d1","\u0054\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006cog\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u0020M\u0061r\u006b\u0049\u006e\u0066\u006f\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061 \u004d\u0061\u0072\u006b\u0065\u0064\u0020\u0065\u006et\u0072\u0079\u0020\u0069\u006e\u0020\u0069\u0074,\u0020\u0077\u0068\u006f\u0073\u0065\u0020\u0076\u0061lu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0074\u0072\u0075\u0065"));
};_cfcb ,_dage :=_gbfbf .GetCatalogStructTreeRoot ();if !_dage {_efdd =append (_efdd ,_ee ("\u0036.\u0038\u002e\u0033\u002e\u0033\u002d1","\u0054\u0068\u0065\u0020\u006c\u006f\u0067\u0069\u0063\u0061\u006c\u0020\u0073\u0074\u0072\u0075\u0063\u0074\u0075r\u0065\u0020\u006f\u0066\u0020\u0074\u0068e\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067 \u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065d \u0062\u0079\u0020a\u0020s\u0074\u0072\u0075\u0063\u0074\u0075\u0072e\u0020\u0068\u0069\u0065\u0072\u0061\u0072\u0063\u0068\u0079\u0020\u0072\u006f\u006ft\u0065\u0064\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0074\u0072\u0075\u0063\u0074\u0054\u0072\u0065\u0065\u0052\u006f\u006f\u0074\u0020\u0065\u006e\u0074r\u0079\u0020\u006f\u0066\u0020\u0074h\u0065\u0020d\u006fc\u0075\u006d\u0065\u006e\u0074\u0020\u0063\u0061t\u0061\u006c\u006fg \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0073\u0063\u0072\u0069\u0062\u0065\u0064\u0020\u0069n\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065 \u0039\u002e\u0036\u002e"));
};_gabg ,_dage :=_eb .GetDict (_cfcb );if _dage {_gecfb ,_ceda :=_eb .GetName (_gabg .Get ("\u0052o\u006c\u0065\u004d\u0061\u0070"));if _ceda {_gbec ,_ccgaf :=_eb .GetDict (_gecfb );if _ccgaf {for _ ,_dfdfg :=range _gbec .Keys (){_geca :=_gbec .Get (_dfdfg );
if _geca ==nil {_efdd =append (_efdd ,_ee ("\u0036.\u0038\u002e\u0033\u002e\u0034\u002d1","\u0041\u006c\u006c\u0020\u006eo\u006e\u002ds\u0074\u0061\u006e\u0064\u0061\u0072\u0064\u0020\u0073t\u0072\u0075\u0063\u0074ure\u0020\u0074\u0079\u0070\u0065s\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u006d\u0061\u0070\u0070\u0065d\u0020\u0074\u006f\u0020\u0074\u0068\u0065\u0020n\u0065\u0061\u0072\u0065\u0073\u0074\u0020\u0066\u0075\u006e\u0063t\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u0020\u0065\u0071\u0075\u0069\u0076\u0061\u006c\u0065\u006e\u0074\u0020\u0073\u0074a\u006ed\u0061r\u0064\u0020\u0074\u0079\u0070\u0065\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066\u0069\u006ee\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065re\u006e\u0063e\u0020\u0039\u002e\u0037\u002e\u0034\u002c\u0020i\u006e\u0020\u0074\u0068e\u0020\u0072\u006fl\u0065\u0020\u006d\u0061p \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u006f\u0066 \u0074h\u0065\u0020\u0073\u0074\u0072\u0075c\u0074\u0075r\u0065\u0020\u0074\u0072e\u0065\u0020\u0072\u006f\u006ft\u002e"));
};};};};};return _efdd ;};func _gagf (_cbdcb *_gde .Document ){_ffgbe ,_ddgcg :=_cbdcb .FindCatalog ();if !_ddgcg {return ;};_ddfd ,_ddgcg :=_ffgbe .GetMarkInfo ();if !_ddgcg {_ddfd =_eb .MakeDict ();};_ece ,_ddgcg :=_eb .GetBool (_ddfd .Get ("\u004d\u0061\u0072\u006b\u0065\u0064"));
if !_ddgcg ||!bool (*_ece ){_ddfd .Set ("\u004d\u0061\u0072\u006b\u0065\u0064",_eb .MakeBool (true ));_ffgbe .SetMarkInfo (_ddfd );};};func _bcaed (_cbgaa *_a .CompliancePdfReader )(_gaeb []ViolatedRule ){var _abfa ,_faddc ,_fgcde ,_ecag ,_gaaf ,_feaa ,_ebga bool ;
_dbbg :=func ()bool {return _abfa &&_faddc &&_fgcde &&_ecag &&_gaaf &&_feaa &&_ebga };for _ ,_dded :=range _cbgaa .PageList {_ggac ,_bcfg :=_dded .GetAnnotations ();if _bcfg !=nil {_bf .Log .Trace ("\u006c\u006f\u0061\u0064\u0069\u006e\u0067\u0020\u0061\u006en\u006f\u0074\u0061\u0074\u0069\u006f\u006es\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_bcfg );
continue ;};for _ ,_cbab :=range _ggac {if !_abfa {switch _cbab .GetContext ().(type ){case *_a .PdfAnnotationFileAttachment ,*_a .PdfAnnotationSound ,*_a .PdfAnnotationMovie ,nil :_gaeb =append (_gaeb ,_ee ("\u0036.\u0035\u002e\u0032\u002d\u0031","\u0041\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0074\u0079\u0070\u0065\u0073\u0020\u006e\u006f\u0074 \u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020i\u006e\u0020\u0050\u0044\u0046\u0020\u0052\u0065\u0066\u0065\u0072\u0065\u006ec\u0065\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074 \u0062\u0065\u0020p\u0065\u0072m\u0069\u0074\u0074\u0065\u0064\u002e\u0020\u0041d\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u006c\u0079\u002c\u0020\u0074\u0068\u0065\u0020F\u0069\u006c\u0065\u0041\u0074\u0074\u0061\u0063\u0068\u006de\u006e\u0074\u002c\u0020\u0053\u006f\u0075\u006e\u0064\u0020\u0061\u006e\u0064\u0020\u004d\u006f\u0076\u0069e\u0020\u0074\u0079\u0070\u0065s \u0073ha\u006c\u006c\u0020\u006eo\u0074\u0020\u0062\u0065\u0020\u0070\u0065\u0072\u006d\u0069\u0074\u0074\u0065\u0064\u002e"));
_abfa =true ;if _dbbg (){return _gaeb ;};};};_afgef ,_fdgd :=_eb .GetDict (_cbab .GetContainingPdfObject ());if !_fdgd {continue ;};if !_faddc {_abaa ,_gbddf :=_eb .GetFloatVal (_afgef .Get ("\u0043\u0041"));if _gbddf &&_abaa !=1.0{_gaeb =append (_gaeb ,_ee ("\u0036.\u0035\u002e\u0033\u002d\u0031","\u0041\u006e\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073h\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0043\u0041\u0020\u006b\u0065\u0079\u0020\u0077\u0069\u0074\u0068\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0074\u0068\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0031\u002e\u0030\u002e"));
_faddc =true ;if _dbbg (){return _gaeb ;};};};if !_fgcde {_fage ,_fgag :=_eb .GetIntVal (_afgef .Get ("\u0046"));if !(_fgag &&_fage &4==4&&_fage &1==0&&_fage &2==0&&_fage &32==0){_gaeb =append (_gaeb ,_ee ("\u0036.\u0035\u002e\u0033\u002d\u0032","\u0041\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069n\u0020\u0074\u0068\u0065\u0020\u0046\u0020\u006b\u0065\u0079\u002e\u0020\u0054\u0068\u0065\u0020\u0046\u0020\u006b\u0065\u0079\u0027\u0073\u0020\u0050\u0072\u0069\u006e\u0074\u0020\u0066\u006c\u0061\u0067\u0020\u0062\u0069\u0074\u0020\u0073h\u0061\u006c\u006c\u0020\u0062\u0065 s\u0065\u0074\u0020\u0074\u006f\u0020\u0031\u0020\u0061\u006e\u0064\u0020\u0069\u0074\u0073\u0020\u0048\u0069\u0064\u0064\u0065\u006e\u002c\u0020I\u006e\u0076\u0069\u0073\u0069\u0062\u006c\u0065\u0020\u0061\u006e\u0064\u0020\u004e\u006f\u0056\u0069\u0065\u0077\u0020\u0066\u006c\u0061\u0067\u0020b\u0069\u0074\u0073 \u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0073e\u0074\u0020t\u006f\u0020\u0030\u002e"));
_fgcde =true ;if _dbbg (){return _gaeb ;};};};if !_ecag {_cgcb ,_efbb :=_eb .GetDict (_afgef .Get ("\u0041\u0050"));if _efbb {_dddcc :=_cgcb .Get ("\u004e");if _dddcc ==nil ||len (_cgcb .Keys ())> 1{_gaeb =append (_gaeb ,_ee ("\u0036.\u0035\u002e\u0033\u002d\u0034","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_ecag =true ;if _dbbg (){return _gaeb ;};continue ;};_ ,_gfde :=_cbab .GetContext ().(*_a .PdfAnnotationWidget );if _gfde {_bfeg ,_fgff :=_eb .GetName (_afgef .Get ("\u0046\u0054"));if _fgff &&*_bfeg =="\u0042\u0074\u006e"{if _ ,_gcde :=_eb .GetDict (_dddcc );
!_gcde {_gaeb =append (_gaeb ,_ee ("\u0036.\u0035\u002e\u0033\u002d\u0034","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_ecag =true ;if _dbbg (){return _gaeb ;};continue ;};};};_ ,_eefca :=_eb .GetStream (_dddcc );if !_eefca {_gaeb =append (_gaeb ,_ee ("\u0036.\u0035\u002e\u0033\u002d\u0034","\u0046\u006f\u0072\u0020\u0061\u006c\u006c\u0020\u0061\u006e\u006e\u006ft\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063t\u0069\u006f\u006ea\u0072\u0069\u0065\u0073 \u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0069\u006e\u0067\u0020\u0061\u006e\u0020\u0041\u0050 \u006b\u0065\u0079\u002c\u0020\u0074\u0068\u0065\u0020\u0061p\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0074\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0073\u0020\u0061\u0073\u0020it\u0073\u0020\u0076\u0061\u006cu\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061i\u006e\u0020o\u006e\u006c\u0079\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u002e\u0020\u0049\u0066\u0020\u0061\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064i\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0027\u0073\u0020\u0053\u0075\u0062ty\u0070\u0065\u0020\u006b\u0065\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0057\u0069\u0064g\u0065\u0074\u0020\u0061\u006e\u0064\u0020\u0069\u0074s\u0020\u0046\u0054 \u006be\u0079\u0020\u0068\u0061\u0073\u0020\u0061\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020B\u0074\u006e,\u0020\u0074he \u0076a\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u004e\u0020\u006b\u0065\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0020\u0073\u0075\u0062\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079; \u006f\u0074\u0068\u0065\u0072\u0077\u0069s\u0065\u0020\u0074\u0068\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020th\u0065\u0020N\u0020\u006b\u0065y\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062e\u0020\u0061\u006e\u0020\u0061\u0070\u0070\u0065\u0061\u0072\u0061n\u0063\u0065\u0020\u0073\u0074\u0072\u0065\u0061\u006d\u002e"));
_ecag =true ;if _dbbg (){return _gaeb ;};continue ;};};};if !_gaaf {if _afgef .Get ("\u0043")!=nil ||_afgef .Get ("\u0049\u0043")!=nil {_acdab ,_fffcf :=_aaeg (_cbgaa );if !_fffcf {_gaeb =append (_gaeb ,_ee ("\u0036.\u0035\u002e\u0033\u002d\u0033","\u0041\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006fn\u0074a\u0069\u006e\u0020t\u0068e\u0020\u0043\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u006f\u0072\u0020\u0074\u0068e\u0020\u0049\u0043\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u0075\u006e\u006c\u0065\u0073\u0073\u0020\u0074\u0068\u0065\u0020\u0063o\u006c\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006ff\u0069\u006ce\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069n\u0020\u0036\u002e\u0032\u002e2\u002c\u0020\u0069\u0073\u0020\u0052\u0047\u0042."));
_gaaf =true ;if _dbbg (){return _gaeb ;};}else {_dfde ,_ecaga :=_eb .GetIntVal (_acdab .Get ("\u004e"));if !_ecaga ||_dfde !=3{_gaeb =append (_gaeb ,_ee ("\u0036.\u0035\u002e\u0033\u002d\u0033","\u0041\u006e\u0020\u0061\u006e\u006e\u006f\u0074\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006ea\u0072\u0079\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006fn\u0074a\u0069\u006e\u0020t\u0068e\u0020\u0043\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u006f\u0072\u0020\u0074\u0068e\u0020\u0049\u0043\u0020\u0061\u0072\u0072\u0061\u0079\u0020\u0075\u006e\u006c\u0065\u0073\u0073\u0020\u0074\u0068\u0065\u0020\u0063o\u006c\u006f\u0072\u0020\u0073\u0070\u0061\u0063\u0065\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0044\u0065\u0073\u0074\u004f\u0075\u0074\u0070\u0075\u0074\u0050\u0072\u006ff\u0069\u006ce\u0020\u0069\u006e\u0020\u0074h\u0065\u0020\u0050\u0044\u0046\u002f\u0041\u002d\u0031\u0020\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0064\u0065\u0066\u0069\u006e\u0065\u0064\u0020\u0069n\u0020\u0036\u002e\u0032\u002e2\u002c\u0020\u0069\u0073\u0020\u0052\u0047\u0042."));
_gaaf =true ;if _dbbg (){return _gaeb ;};};};};};_gegg ,_cbfa :=_cbab .GetContext ().(*_a .PdfAnnotationWidget );if !_cbfa {continue ;};if !_feaa {if _gegg .A !=nil {_gaeb =append (_gaeb ,_ee ("\u0036.\u0036\u002e\u0031\u002d\u0033","A \u0057\u0069d\u0067\u0065\u0074\u0020\u0061\u006e\u006e\u006f\u0074a\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0069\u006ec\u006cu\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0020e\u006et\u0072\u0079."));
_feaa =true ;if _dbbg (){return _gaeb ;};};};if !_ebga {if _gegg .AA !=nil {_gaeb =append (_gaeb ,_ee ("\u0036.\u0036\u002e\u0032\u002d\u0031","\u0041\u0020\u0057\u0069\u0064\u0067\u0065\u0074\u0020\u0061\u006e\u006eo\u0074\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u0020\u0073h\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020\u0061\u006e\u0020\u0041\u0041\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0061d\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u002d\u0061\u0063t\u0069\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
_ebga =true ;if _dbbg (){return _gaeb ;};};};};};return _gaeb ;};type profile3 struct{_fddb standardType ;_afba Profile3Options ;};func _ddgd (_cfag *_a .CompliancePdfReader )(_cega []ViolatedRule ){_egbff :=_cfag .GetObjectNums ();for _ ,_ebff :=range _egbff {_fddce ,_bdbfa :=_cfag .GetIndirectObjectByNumber (_ebff );
if _bdbfa !=nil {continue ;};_fabb ,_bggg :=_eb .GetDict (_fddce );if !_bggg {continue ;};_gbecb ,_bggg :=_eb .GetName (_fabb .Get ("\u0054\u0079\u0070\u0065"));if !_bggg {continue ;};if _gbecb .String ()!="\u0046\u0069\u006c\u0065\u0073\u0070\u0065\u0063"{continue ;
};_efbd ,_bdbfa :=_a .NewPdfFilespecFromObj (_fabb );if _bdbfa !=nil {continue ;};if _efbd .EF !=nil {if _efbd .F ==nil ||_efbd .UF ==nil {_cega =append (_cega ,_ee ("\u0036\u002e\u0038-\u0032","\u0054h\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0070\u0065\u0063\u0069\u0066i\u0063\u0061\u0074i\u006f\u006e\u0020\u0064\u0069\u0063t\u0069\u006fn\u0061\u0072\u0079\u0020\u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0065\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068\u0061\u006cl\u0020\u0063\u006f\u006e\u0074a\u0069\u006e\u0020t\u0068\u0065\u0020\u0046\u0020a\u006e\u0064\u0020\u0055\u0046\u0020\u006b\u0065\u0079\u0073\u002e"));
break ;};if _efbd .AFRelationship ==nil {_cega =append (_cega ,_ee ("\u0036\u002e\u0038-\u0033","\u0049\u006e\u0020\u006f\u0072d\u0065\u0072\u0020\u0074\u006f\u0020\u0065\u006e\u0061\u0062\u006c\u0065\u0020i\u0064\u0065nt\u0069\u0066\u0069c\u0061\u0074\u0069o\u006e\u0020\u006f\u0066\u0020\u0074\u0068\u0065\u0020\u0072\u0065\u006c\u0061\u0074\u0069\u006f\u006e\u0073h\u0069\u0070\u0020\u0062\u0065\u0074\u0077\u0065\u0065\u006e\u0020\u0074\u0068\u0065\u0020fi\u006ce\u0020\u0073\u0070\u0065\u0063\u0069f\u0069c\u0061\u0074\u0069o\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0061\u006e\u0064\u0020\u0074\u0068\u0065\u0020c\u006f\u006e\u0074e\u006e\u0074\u0020\u0074\u0068\u0061\u0074\u0020\u0069\u0073\u0020\u0072\u0065\u0066\u0065\u0072\u0072\u0069\u006e\u0067\u0020\u0074\u006f\u0020\u0069\u0074\u002c\u0020\u0061\u0020\u006e\u0065\u0077\u0020(\u0072\u0065\u0071\u0075i\u0072\u0065\u0064\u0029\u0020\u006be\u0079\u0020h\u0061\u0073\u0020\u0062e\u0065\u006e\u0020\u0064\u0065\u0066i\u006e\u0065\u0064\u0020a\u006e\u0064\u0020\u0069\u0074s \u0070\u0072e\u0073\u0065n\u0063\u0065\u0020\u0028\u0069\u006e\u0020\u0074\u0068e\u0020\u0064\u0069\u0063\u0074i\u006f\u006e\u0061\u0072\u0079\u0029\u0020\u0069\u0073\u0020\u0072\u0065q\u0075\u0069\u0072e\u0064\u002e"));
break ;};};};return _cega ;};func _ccfc (_geg *_gde .Document )error {_fbab ,_cdf :=_geg .GetPages ();if !_cdf {return nil ;};for _ ,_agdd :=range _fbab {_baac :=_agdd .FindXObjectForms ();for _ ,_dgab :=range _baac {_bceb ,_edfc :=_eb .GetDict (_dgab .Get ("\u0047\u0072\u006fu\u0070"));
if _edfc {if _cag :=_bceb .Get ("\u0053");_cag !=nil {_bbbe ,_dfdb :=_eb .GetName (_cag );if _dfdb &&_bbbe .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{_dgab .Remove ("\u0047\u0072\u006fu\u0070");};};};};_caga ,_ggdeb :=_agdd .GetResourcesXObject ();
if _ggdeb {_fddg ,_bdg :=_eb .GetDict (_caga .Get ("\u0047\u0072\u006fu\u0070"));if _bdg {_cefd :=_fddg .Get ("\u0053");if _cefd !=nil {_bcge ,_gecf :=_eb .GetName (_cefd );if _gecf &&_bcge .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{_caga .Remove ("\u0047\u0072\u006fu\u0070");
};};};};_aea ,_gcag :=_eb .GetDict (_agdd .Object .Get ("\u0047\u0072\u006fu\u0070"));if _gcag {_fdee :=_aea .Get ("\u0053");if _fdee !=nil {_bdf ,_bdad :=_eb .GetName (_fdee );if _bdad &&_bdf .String ()=="\u0054\u0072\u0061n\u0073\u0070\u0061\u0072\u0065\u006e\u0063\u0079"{_agdd .Object .Remove ("\u0047\u0072\u006fu\u0070");
};};};};return nil ;};func _aedb (_dgfaf *_a .CompliancePdfReader )(_bddf []ViolatedRule ){var _ggbaa ,_ecbf ,_daeeg ,_fcedf ,_cece ,_dafe ,_fdba bool ;_ggcb :=map[*_eb .PdfObjectStream ]struct{}{};for _ ,_cdbgc :=range _dgfaf .GetObjectNums (){if _ggbaa &&_ecbf &&_cece &&_daeeg &&_fcedf &&_dafe &&_fdba {return _bddf ;
};_gbfc ,_fbae :=_dgfaf .GetIndirectObjectByNumber (_cdbgc );if _fbae !=nil {continue ;};_effc ,_eaec :=_eb .GetStream (_gbfc );if !_eaec {continue ;};if _ ,_eaec =_ggcb [_effc ];_eaec {continue ;};_ggcb [_effc ]=struct{}{};_ccafd ,_eaec :=_eb .GetName (_effc .Get ("\u0053u\u0062\u0054\u0079\u0070\u0065"));
if !_eaec {continue ;};if !_fcedf {if _effc .Get ("\u0052\u0065\u0066")!=nil {_bddf =append (_bddf ,_ee ("\u0036.\u0032\u002e\u0039\u002d\u0032","\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0058O\u0062\u006a\u0065\u0063\u0074s\u002e"));
_fcedf =true ;};};if _ccafd .String ()=="\u0050\u0053"{if !_dafe {_bddf =append (_bddf ,_ee ("\u0036.\u0032\u002e\u0039\u002d\u0033","A \u0063\u006fn\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066i\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0050\u006f\u0073t\u0053c\u0072\u0069\u0070\u0074\u0020\u0058\u004f\u0062j\u0065c\u0074\u0073."));
_dafe =true ;continue ;};};if _ccafd .String ()=="\u0046\u006f\u0072\u006d"{if _ecbf &&_daeeg &&_fcedf {continue ;};if !_ecbf &&_effc .Get ("\u004f\u0050\u0049")!=nil {_bddf =append (_bddf ,_ee ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072\u006d \u0058\u004f\u0062j\u0065\u0063\u0074 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079 \u0073\u0068\u0061\u006c\u006c n\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u004f\u0050\u0049\u0020\u006b\u0065\u0079\u002e"));
_ecbf =true ;};if !_daeeg {if _effc .Get ("\u0050\u0053")!=nil {_daeeg =true ;};if _fgcfg :=_effc .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032");_fgcfg !=nil &&!_daeeg {if _effa ,_cefb :=_eb .GetName (_fgcfg );_cefb &&*_effa =="\u0050\u0053"{_daeeg =true ;
};};if _daeeg {_bddf =append (_bddf ,_ee ("\u0036.\u0032\u002e\u0039\u002d\u0031","\u0041\u0020\u0066\u006f\u0072\u006d\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006eo\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032\u0020\u006b\u0065y \u0077\u0069\u0074\u0068\u0020\u0061\u0020\u0076\u0061\u006cu\u0065 o\u0066 \u0050\u0053\u0020\u0061\u006e\u0064\u0020t\u0068\u0065\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e"));
};};continue ;};if _ccafd .String ()!="\u0049\u006d\u0061g\u0065"{continue ;};if !_ggbaa &&_effc .Get ("\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073")!=nil {_bddf =append (_bddf ,_ee ("\u0036.\u0032\u002e\u0038\u002d\u0031","\u0041\u006e\u0020\u0049m\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073\u0020\u006b\u0065\u0079\u002e"));
_ggbaa =true ;};if !_fdba &&_effc .Get ("\u004f\u0050\u0049")!=nil {_bddf =append (_bddf ,_ee ("\u0036.\u0032\u002e\u0038\u002d\u0032","\u0041\u006e\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u0020\u0073\u0068\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020t\u0068\u0065\u0020\u004f\u0050\u0049\u0020\u006b\u0065\u0079\u002e"));
_fdba =true ;};if !_cece &&_effc .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065")!=nil {_bcba ,_deeg :=_eb .GetBool (_effc .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065"));if _deeg &&bool (*_bcba ){continue ;
};_bddf =append (_bddf ,_ee ("\u0036.\u0032\u002e\u0038\u002d\u0033","\u0049\u0066 a\u006e\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0063o\u006e\u0074\u0061\u0069n\u0073\u0020\u0074\u0068e \u0049\u006et\u0065r\u0070\u006f\u006c\u0061\u0074\u0065 \u006b\u0065\u0079,\u0020\u0069t\u0073\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020b\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u002e"));
_cece =true ;};};return _bddf ;};

// Profile1Options are the options that changes the way how optimizer may try to adapt document into PDF/A standard.
type Profile1Options struct{

// CMYKDefaultColorSpace is an option that refers PDF/A-1
CMYKDefaultColorSpace bool ;

// Now is a function that returns current time.
Now func ()_ca .Time ;

// Xmp is the xmp options information.
Xmp XmpOptions ;};func _aaeg (_eece *_a .CompliancePdfReader )(*_eb .PdfObjectDictionary ,bool ){_effd ,_fedde :=_efcc (_eece );if !_fedde {return nil ,false ;};_bdegg ,_fedde :=_eb .GetArray (_effd .Get ("\u004f\u0075\u0074\u0070\u0075\u0074\u0049\u006e\u0074\u0065\u006e\u0074\u0073"));
if !_fedde {return nil ,false ;};if _bdegg .Len ()==0{return nil ,false ;};return _eb .GetDict (_bdegg .Get (0));};

// Profile2Options are the options that changes the way how optimizer may try to adapt document into PDF/A standard.
type Profile2Options struct{

// CMYKDefaultColorSpace is an option that refers PDF/A
CMYKDefaultColorSpace bool ;

// Now is a function that returns current time.
Now func ()_ca .Time ;

// Xmp is the xmp options information.
Xmp XmpOptions ;};func _abgd (_fedc *_a .CompliancePdfReader )ViolatedRule {return _ddd };func _gegc (_geccg *_a .CompliancePdfReader ,_afcgc standardType )(_gfcfa []ViolatedRule ){var _ecde ,_dgae ,_cebbc ,_eacc ,_ecac ,_fdcg ,_geea bool ;_cbbf :=func ()bool {return _ecde &&_dgae &&_cebbc &&_eacc &&_ecac &&_fdcg &&_geea };
_afae :=map[*_eb .PdfObjectStream ]*_ba .CMap {};_cagg :=map[*_eb .PdfObjectStream ][]byte {};_cbbff :=map[_eb .PdfObject ]*_a .PdfFont {};for _ ,_fbgfe :=range _geccg .GetObjectNums (){_fbgca ,_gfffb :=_geccg .GetIndirectObjectByNumber (_fbgfe );if _gfffb !=nil {continue ;
};_afef ,_eafac :=_eb .GetDict (_fbgca );if !_eafac {continue ;};_cfcbf ,_eafac :=_eb .GetName (_afef .Get ("\u0054\u0079\u0070\u0065"));if !_eafac {continue ;};if *_cfcbf !="\u0046\u006f\u006e\u0074"{continue ;};_efegg ,_gfffb :=_a .NewPdfFontFromPdfObject (_afef );
if _gfffb !=nil {_bf .Log .Debug ("g\u0065\u0074\u0074\u0069\u006e\u0067 \u0066\u006f\u006e\u0074\u0020\u0066r\u006f\u006d\u0020\u006f\u0062\u006a\u0065c\u0074\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020%\u0076",_gfffb );continue ;};_cbbff [_afef ]=_efegg ;
};for _ ,_edgde :=range _geccg .PageList {_bafce ,_faea :=_edgde .GetContentStreams ();if _faea !=nil {_bf .Log .Debug ("G\u0065\u0074\u0074\u0069\u006e\u0067 \u0070\u0061\u0067\u0065\u0020\u0063o\u006e\u0074\u0065\u006e\u0074\u0020\u0073t\u0072\u0065\u0061\u006d\u0073\u0020\u0066\u0061\u0069\u006ce\u0064");
continue ;};for _ ,_afga :=range _bafce {_eabeg :=_e .NewContentStreamParser (_afga );_faef ,_eabb :=_eabeg .Parse ();if _eabb !=nil {_bf .Log .Debug ("\u0070\u0061\u0072\u0073\u0069\u006e\u0067\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074s\u0074r\u0065\u0061\u006d\u0020\u0066\u0061\u0069\u006c\u0065\u0064\u003a\u0020\u0025\u0076",_eabb );
continue ;};var _caab bool ;for _ ,_edgf :=range *_faef {if _edgf .Operand !="\u0054\u0072"{continue ;};if len (_edgf .Params )!=1{_bf .Log .Debug ("\u0069\u006e\u0076\u0061\u006ci\u0064\u0020\u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066\u0020\u0070\u0061\u0072\u0061\u006d\u0065\u0074\u0065\u0072\u0073\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0027\u0054\u0072\u0027\u0020\u006f\u0070\u0065\u0072\u0061\u006e\u0064\u002c\u0020\u0065\u0078\u0070e\u0063\u0074\u0065\u0064\u0020\u0027\u0031\u0027\u0020\u0062\u0075\u0074 \u0069\u0073\u003a\u0020\u0027\u0025d\u0027",len (_edgf .Params ));
continue ;};_cbbb ,_dcebb :=_eb .GetIntVal (_edgf .Params [0]);if !_dcebb {_bf .Log .Debug ("\u0072\u0065\u006e\u0064\u0065\u0072\u0069\u006e\u0067\u0020\u006d\u006f\u0064\u0065\u0020i\u0073 \u006e\u006f\u0074\u0020\u0061\u006e\u0020\u0069\u006e\u0074\u0065\u0067\u0065\u0072");
continue ;};if _cbbb ==3{_caab =true ;break ;};};for _ ,_degbf :=range *_faef {if _degbf .Operand !="\u0054\u0066"{continue ;};if len (_degbf .Params )!=2{_bf .Log .Debug ("i\u006eva\u006ci\u0064 \u006e\u0075\u006d\u0062\u0065r\u0020\u006f\u0066 \u0070\u0061\u0072\u0061\u006de\u0074\u0065\u0072s\u0020\u0066\u006f\u0072\u0020\u0074\u0068\u0065\u0020\u0027\u0054f\u0027\u0020\u006fper\u0061\u006e\u0064\u002c\u0020\u0065x\u0070\u0065\u0063\u0074\u0065\u0064\u0020\u0027\u0032\u0027\u0020\u0069s\u003a \u0027\u0025\u0064\u0027",len (_degbf .Params ));
continue ;};_bedb ,_acge :=_eb .GetName (_degbf .Params [0]);if !_acge {_bf .Log .Debug ("\u0045\u0052\u0052\u004f\u0052\u003a \u0054\u0066\u0020\u006f\u0070\u003d\u0025\u0073\u0020\u0047\u0065\u0074\u004ea\u006d\u0065\u0056\u0061\u006c\u0020\u0066a\u0069\u006c\u0065\u0064",_degbf );
continue ;};_bbgg ,_bedf :=_edgde .Resources .GetFontByName (*_bedb );if !_bedf {_bf .Log .Debug ("\u0066\u006f\u006e\u0074\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");continue ;};_baeea ,_acge :=_eb .GetDict (_bbgg );if !_acge {_bf .Log .Debug ("\u0066\u006f\u006e\u0074 d\u0069\u0063\u0074\u0020\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064");
continue ;};_befd ,_acge :=_cbbff [_baeea ];if !_acge {var _acaca error ;_befd ,_acaca =_a .NewPdfFontFromPdfObject (_baeea );if _acaca !=nil {_bf .Log .Debug ("\u0067\u0065\u0074\u0074i\u006e\u0067\u0020\u0066\u006f\u006e\u0074\u0020\u0066\u0072o\u006d \u006f\u0062\u006a\u0065\u0063\u0074\u003a \u0025\u0076",_acaca );
continue ;};_cbbff [_baeea ]=_befd ;};if !_ecde {_aaee :=_efag (_baeea ,_cagg ,_afae );if _aaee !=_ddd {_gfcfa =append (_gfcfa ,_aaee );_ecde =true ;if _cbbf (){return _gfcfa ;};};};if !_dgae {_egecc :=_gedb (_baeea );if _egecc !=_ddd {_gfcfa =append (_gfcfa ,_egecc );
_dgae =true ;if _cbbf (){return _gfcfa ;};};};if !_cebbc {_ecfd :=_dgfea (_baeea ,_cagg ,_afae );if _ecfd !=_ddd {_gfcfa =append (_gfcfa ,_ecfd );_cebbc =true ;if _cbbf (){return _gfcfa ;};};};if !_eacc {_aedbf :=_gebdbc (_baeea ,_cagg ,_afae );if _aedbf !=_ddd {_gfcfa =append (_gfcfa ,_aedbf );
_eacc =true ;if _cbbf (){return _gfcfa ;};};};if !_ecac {_eddb :=_bcgbg (_befd ,_baeea ,_caab );if _eddb !=_ddd {_ecac =true ;_gfcfa =append (_gfcfa ,_eddb );if _cbbf (){return _gfcfa ;};};};if !_fdcg {_caef :=_baeda (_befd ,_baeea );if _caef !=_ddd {_fdcg =true ;
_gfcfa =append (_gfcfa ,_caef );if _cbbf (){return _gfcfa ;};};};if !_geea &&(_afcgc ._ff =="\u0041"||_afcgc ._ff =="\u0055"){_ddabg :=_egbfg (_baeea ,_cagg ,_afae );if _ddabg !=_ddd {_geea =true ;_gfcfa =append (_gfcfa ,_ddabg );if _cbbf (){return _gfcfa ;
};};};};};};return _gfcfa ;};func _ecgd (_agcg *_a .CompliancePdfReader )(_fgbd []ViolatedRule ){var _gbbb ,_ggfc ,_fafa ,_fgfae ,_eacac ,_ffgd bool ;_ecba :=map[*_eb .PdfObjectStream ]struct{}{};for _ ,_bafb :=range _agcg .GetObjectNums (){if _gbbb &&_ggfc &&_eacac &&_fafa &&_fgfae &&_ffgd {return _fgbd ;
};_debd ,_gfcc :=_agcg .GetIndirectObjectByNumber (_bafb );if _gfcc !=nil {continue ;};_agcb ,_adeb :=_eb .GetStream (_debd );if !_adeb {continue ;};if _ ,_adeb =_ecba [_agcb ];_adeb {continue ;};_ecba [_agcb ]=struct{}{};_ebcg ,_adeb :=_eb .GetName (_agcb .Get ("\u0053u\u0062\u0054\u0079\u0070\u0065"));
if !_adeb {continue ;};if !_fgfae {if _agcb .Get ("\u0052\u0065\u0066")!=nil {_fgbd =append (_fgbd ,_ee ("\u0036.\u0032\u002e\u0036\u002d\u0031","\u0041\u0020\u0063\u006f\u006e\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066\u0069\u006c\u0065\u0020\u0073\u0068a\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0072\u0065\u0066\u0065\u0072\u0065\u006e\u0063\u0065\u0020\u0058O\u0062\u006a\u0065\u0063\u0074s\u002e"));
_fgfae =true ;};};if _ebcg .String ()=="\u0050\u0053"{if !_ffgd {_fgbd =append (_fgbd ,_ee ("\u0036.\u0032\u002e\u0037\u002d\u0031","A \u0063\u006fn\u0066\u006f\u0072\u006d\u0069\u006e\u0067\u0020\u0066i\u006c\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u0050\u006f\u0073t\u0053c\u0072\u0069\u0070\u0074\u0020\u0058\u004f\u0062j\u0065c\u0074\u0073."));
_ffgd =true ;continue ;};};if _ebcg .String ()=="\u0046\u006f\u0072\u006d"{if _ggfc &&_fafa &&_fgfae {continue ;};if !_ggfc &&_agcb .Get ("\u004f\u0050\u0049")!=nil {_fgbd =append (_fgbd ,_ee ("\u0036.\u0032\u002e\u0034\u002d\u0032","\u0041\u006e\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u0020\u0028\u0049\u006d\u0061\u0067\u0065\u0020\u006f\u0072\u0020\u0046\u006f\u0072\u006d\u0029\u0020\u0073\u0068\u0061\u006cl\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u004fP\u0049\u0020\u006b\u0065\u0079\u002e"));
_ggfc =true ;};if !_fafa {if _agcb .Get ("\u0050\u0053")!=nil {_fafa =true ;};if _dbg :=_agcb .Get ("\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032");_dbg !=nil &&!_fafa {if _egff ,_dadda :=_eb .GetName (_dbg );_dadda &&*_egff =="\u0050\u0053"{_fafa =true ;
};};if _fafa {_fgbd =append (_fgbd ,_ee ("\u0036.\u0032\u002e\u0035\u002d\u0031","A\u0020\u0066\u006fr\u006d\u0020\u0058\u004f\u0062\u006a\u0065\u0063\u0074\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006ft\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e \u0074\u0068\u0065\u0020\u0053\u0075\u0062\u0074\u0079\u0070\u0065\u0032\u0020\u006b\u0065\u0079 \u0077\u0069\u0074\u0068\u0020a\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u006f\u0066\u0020\u0050\u0053\u0020o\u0072\u0020\u0074\u0068e\u0020\u0050\u0053\u0020\u006b\u0065\u0079\u002e"));
};};continue ;};if _ebcg .String ()!="\u0049\u006d\u0061g\u0065"{continue ;};if !_gbbb &&_agcb .Get ("\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073")!=nil {_fgbd =append (_fgbd ,_ee ("\u0036.\u0032\u002e\u0034\u002d\u0031","\u0041\u006e\u0020\u0049m\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069o\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006et\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0073\u0020\u006b\u0065\u0079\u002e"));
_gbbb =true ;};if !_eacac &&_agcb .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065")!=nil {_baae ,_faee :=_eb .GetBool (_agcb .Get ("I\u006e\u0074\u0065\u0072\u0070\u006f\u006c\u0061\u0074\u0065"));if _faee &&bool (*_baae ){continue ;
};_fgbd =append (_fgbd ,_ee ("\u0036.\u0032\u002e\u0034\u002d\u0033","\u0049\u0066 a\u006e\u0020\u0049\u006d\u0061\u0067\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0063o\u006e\u0074\u0061\u0069n\u0073\u0020\u0074\u0068e \u0049\u006et\u0065r\u0070\u006f\u006c\u0061\u0074\u0065 \u006b\u0065\u0079,\u0020\u0069t\u0073\u0020\u0076\u0061\u006c\u0075\u0065\u0020\u0073\u0068\u0061\u006c\u006c\u0020b\u0065\u0020\u0066\u0061\u006c\u0073\u0065\u002e"));
_eacac =true ;};};return _fgbd ;};var _ Profile =(*Profile2B )(nil );func _eag ()standardType {return standardType {_dda :1,_ff :"\u0042"}};func (_fb *documentImages )hasOnlyDeviceCMYK ()bool {return _fb ._eeb &&!_fb ._cf &&!_fb ._ac };func _dddg (_acgg *_gde .Document )error {for _ ,_cgea :=range _acgg .Objects {_aadg ,_fdea :=_eb .GetDict (_cgea );
if !_fdea {continue ;};_ccdg :=_aadg .Get ("\u0054\u0079\u0070\u0065");if _ccdg ==nil {continue ;};if _gada ,_feef :=_eb .GetName (_ccdg );_feef &&_gada .String ()!="\u0041\u0063\u0072\u006f\u0046\u006f\u0072\u006d"{continue ;};_cdg ,_edcg :=_eb .GetBool (_aadg .Get ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073"));
if _edcg &&bool (*_cdg ){_aadg .Set ("\u004ee\u0065d\u0041\u0070\u0070\u0065\u0061\u0072\u0061\u006e\u0063\u0065\u0073",_eb .MakeBool (false ));};if _aadg .Get ("\u0058\u0046\u0041")!=nil {_aadg .Remove ("\u0058\u0046\u0041");};};_edgd ,_agfcg :=_acgg .FindCatalog ();
if !_agfcg {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");};if _edgd .Object .Get ("\u004e\u0065\u0065\u0064\u0073\u0052\u0065\u006e\u0064e\u0072\u0069\u006e\u0067")!=nil {_edgd .Object .Remove ("\u004e\u0065\u0065\u0064\u0073\u0052\u0065\u006e\u0064e\u0072\u0069\u006e\u0067");
};return nil ;};func _cgcf (_bccad *_a .CompliancePdfReader )(_abbaf []ViolatedRule ){_edfg :=_bccad .GetObjectNums ();for _ ,_cdeca :=range _edfg {_ddab ,_cggg :=_bccad .GetIndirectObjectByNumber (_cdeca );if _cggg !=nil {continue ;};_fbga ,_eaaf :=_eb .GetDict (_ddab );
if !_eaaf {continue ;};_gfega ,_eaaf :=_eb .GetName (_fbga .Get ("\u0054\u0079\u0070\u0065"));if !_eaaf {continue ;};if _gfega .String ()!="\u0046\u0069\u006c\u0065\u0073\u0070\u0065\u0063"{continue ;};if _fbga .Get ("\u0045\u0046")!=nil {_abbaf =append (_abbaf ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0031\u002d\u0031","\u0041 \u0066\u0069\u006c\u0065 \u0073p\u0065\u0063\u0069\u0066\u0069\u0063\u0061\u0074\u0069o\u006e\u0020\u0064\u0069\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020\u0064\u0065\u0066i\u006e\u0065\u0064\u0020\u0069\u006e\u0020\u0050\u0044\u0046 \u0033\u002e\u0031\u0030\u002e\u0032\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063o\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0045\u0046 \u006be\u0079\u002e"));
break ;};};_gbfb ,_feee :=_efcc (_bccad );if !_feee {return _abbaf ;};_bcaf ,_feee :=_eb .GetDict (_gbfb .Get ("\u004e\u0061\u006de\u0073"));if !_feee {return _abbaf ;};if _bcaf .Get ("\u0045\u006d\u0062\u0065\u0064\u0064\u0065\u0064\u0046\u0069\u006c\u0065\u0073")!=nil {_abbaf =append (_abbaf ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0031\u002d\u0032","\u0041\u0020\u0066i\u006c\u0065\u0027\u0073\u0020\u006e\u0061\u006d\u0065\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002c\u0020\u0061\u0073\u0020d\u0065\u0066\u0069\u006ee\u0064\u0020\u0069\u006e\u0020PD\u0046 \u0052\u0065\u0066er\u0065\u006e\u0063\u0065\u0020\u0033\u002e6\u002e\u0033\u002c\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074h\u0065\u0020\u0045m\u0062\u0065\u0064\u0064\u0065\u0064\u0046i\u006c\u0065\u0073\u0020\u006b\u0065\u0079\u002e"));
};return _abbaf ;};func _afbgd (_fddgf *_a .CompliancePdfReader )(_edcgf []ViolatedRule ){return _edcgf };func _geeg (_ddfec *_a .CompliancePdfReader )(_fcgag []ViolatedRule ){_cbcae :=func (_ebeda *_eb .PdfObjectDictionary ,_bbcd *[]string ,_adecd *[]ViolatedRule )error {_dffg :=_ebeda .Get ("\u004e\u0061\u006d\u0065");
if _dffg ==nil ||len (_dffg .String ())==0{*_adecd =append (*_adecd ,_ee ("\u0036\u002e\u0039-\u0031","\u0045\u0061\u0063\u0068\u0020o\u0070\u0074\u0069\u006f\u006e\u0061l\u0020\u0063\u006f\u006e\u0074\u0065\u006et\u0020\u0063\u006fn\u0066\u0069\u0067\u0075r\u0061\u0074\u0069\u006f\u006e\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0063o\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u004e\u0061\u006d\u0065\u0020\u006b\u0065\u0079\u002e"));
};for _ ,_cedf :=range *_bbcd {if _cedf ==_dffg .String (){*_adecd =append (*_adecd ,_ee ("\u0036\u002e\u0039-\u0032","\u0045\u0061\u0063\u0068\u0020\u006f\u0070\u0074\u0069\u006f\u006e\u0061l\u0020\u0063\u006f\u006e\u0074\u0065\u006e\u0074\u0020\u0063\u006f\u006e\u0066\u0069\u0067\u0075\u0072a\u0074\u0069\u006fn\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068a\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068\u0065\u0020N\u0061\u006d\u0065\u0020\u006b\u0065\u0079\u002c w\u0068\u006fs\u0065\u0020\u0076\u0061\u006c\u0075\u0065\u0020s\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u0075ni\u0071\u0075\u0065 \u0061\u006d\u006f\u006e\u0067\u0073\u0074\u0020\u0061\u006c\u006c\u0020o\u0070\u0074\u0069\u006f\u006e\u0061\u006c\u0020\u0063\u006fn\u0074\u0065\u006e\u0074 \u0063\u006f\u006e\u0066\u0069\u0067u\u0072\u0061\u0074\u0069\u006f\u006e\u0020\u0064\u0069\u0063\u0074i\u006fn\u0061\u0072\u0069\u0065\u0073\u0020\u0077\u0069\u0074\u0068\u0069\u006e\u0020\u0074\u0068e\u0020\u0050\u0044\u0046\u002fA\u002d\u0032\u0020\u0066\u0069l\u0065\u002e"));
}else {*_bbcd =append (*_bbcd ,_dffg .String ());};};if _ebeda .Get ("\u0041\u0053")!=nil {*_adecd =append (*_adecd ,_ee ("\u0036\u002e\u0039-\u0034","Th\u0065\u0020\u0041\u0053\u0020\u006b\u0065y \u0073\u0068\u0061\u006c\u006c\u0020\u006e\u006f\u0074\u0020\u0061\u0070\u0070\u0065\u0061r\u0020\u0069\u006e\u0020\u0061\u006e\u0079\u0020\u006f\u0070\u0074\u0069\u006f\u006e\u0061\u006c\u0020\u0063\u006f\u006et\u0065\u006e\u0074\u0020\u0063\u006fn\u0066\u0069\u0067\u0075\u0072\u0061\u0074\u0069\u006fn\u0020d\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072\u0079\u002e"));
};return nil ;};_gadf ,_dcbef :=_efcc (_ddfec );if !_dcbef {return _fcgag ;};_cfee ,_dcbef :=_eb .GetDict (_gadf .Get ("\u004f\u0043\u0050r\u006f\u0070\u0065\u0072\u0074\u0069\u0065\u0073"));if !_dcbef {return _fcgag ;};var _cbde []string ;_gaddg ,_dcbef :=_eb .GetDict (_cfee .Get ("\u0044"));
if _dcbef {_cbcae (_gaddg ,&_cbde ,&_fcgag );};_fabf ,_dcbef :=_eb .GetArray (_cfee .Get ("\u0043o\u006e\u0066\u0069\u0067\u0073"));if _dcbef {for _fffbe :=0;_fffbe < _fabf .Len ();_fffbe ++{_bfadc ,_cgfde :=_eb .GetDict (_fabf .Get (_fffbe ));if !_cgfde {continue ;
};_cbcae (_bfadc ,&_cbde ,&_fcgag );};};return _fcgag ;};func _dfec (_ddec *_gde .Document )error {_cbgd ,_faag :=_ddec .FindCatalog ();if !_faag {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};if _cbgd .Object .Get ("\u0052\u0065\u0071u\u0069\u0072\u0065\u006d\u0065\u006e\u0074\u0073")!=nil {_cbgd .Object .Remove ("\u0052\u0065\u0071u\u0069\u0072\u0065\u006d\u0065\u006e\u0074\u0073");};return nil ;};

// Part gets the PDF/A version level.
func (_gfbg *profile2 )Part ()int {return _gfbg ._fdecd ._dda };func (_bac *documentImages )hasOnlyDeviceRGB ()bool {return _bac ._cf &&!_bac ._eeb &&!_bac ._ac };func _aede (_bbeacc *_a .CompliancePdfReader )(_aagcf ViolatedRule ){_cddd ,_dddfbb :=_efcc (_bbeacc );
if !_dddfbb {return _ddd ;};if _cddd .Get ("\u0041\u0041")!=nil {return _ee ("\u0036.\u0035\u002e\u0032\u002d\u0031","\u0054h\u0065\u0020\u0064\u006fc\u0075m\u0065\u006e\u0074\u0020\u0063\u0061\u0074\u0061\u006co\u0067\u0020\u0073\u0068\u0061\u006c\u006c\u0020n\u006f\u0074\u0020\u0069\u006e\u0063\u006c\u0075\u0064\u0065\u0020a\u006e\u0020\u0041\u0041\u0020\u0065\u006e\u0074\u0072\u0079 \u0066\u006f\u0072\u0020\u0061\u006e\u0020\u0061\u0064\u0064\u0069\u0074\u0069\u006f\u006e\u0061\u006c\u002d\u0061c\u0074\u0069\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006fn\u0061r\u0079\u002e");
};return _ddd ;};func _fcge (_cdeb *_a .CompliancePdfReader )(_bcaef []ViolatedRule ){_bgbef ,_cagd :=_efcc (_cdeb );if !_cagd {return _bcaef ;};_facd ,_cagd :=_eb .GetDict (_bgbef .Get ("\u0050\u0065\u0072m\u0073"));if !_cagd {return _bcaef ;};_adefc :=_facd .Keys ();
for _ ,_cgggb :=range _adefc {if _cgggb .String ()!="\u0055\u0052\u0033"&&_cgggb .String ()!="\u0044\u006f\u0063\u004d\u0044\u0050"{_bcaef =append (_bcaef ,_ee ("\u0036\u002e\u0031\u002e\u0031\u0032\u002d\u0031","\u004e\u006f\u0020\u006b\u0065\u0079\u0073 \u006f\u0074\u0068\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0055\u0052\u0033 \u0061n\u0064\u0020\u0044\u006f\u0063\u004dD\u0050\u0020\u0073\u0068\u0061\u006c\u006c \u0062\u0065\u0020\u0070\u0072\u0065\u0073\u0065\u006e\u0074\u0020\u0069\u006e\u0020\u0061\u0020\u0070\u0065\u0072\u006d\u0069\u0073\u0073i\u006f\u006e\u0073\u0020\u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061\u0072y\u002e"));
};};return _bcaef ;};func _aedd (_bdaga *_gde .Document ,_dbbc standardType ,_gbg XmpOptions )error {_cdea ,_cgdg :=_bdaga .FindCatalog ();if !_cgdg {return nil ;};var _feed *_bg .Document ;_bdbd ,_cgdg :=_cdea .GetMetadata ();if !_cgdg {_feed =_bg .NewDocument ();
}else {var _bbacb error ;_feed ,_bbacb =_bg .LoadDocument (_bdbd .Stream );if _bbacb !=nil {return _bbacb ;};};_bggb :=_bg .PdfInfoOptions {InfoDict :_bdaga .Info ,PdfVersion :_d .Sprintf ("\u0025\u0064\u002e%\u0064",_bdaga .Version .Major ,_bdaga .Version .Minor ),Copyright :_gbg .Copyright ,Overwrite :true };
_bbf ,_cgdg :=_cdea .GetMarkInfo ();if _cgdg {_ccgc ,_aeda :=_eb .GetBool (_bbf .Get ("\u004d\u0061\u0072\u006b\u0065\u0064"));if _aeda &&bool (*_ccgc ){_bggb .Marked =true ;};};if _cdc :=_feed .SetPdfInfo (&_bggb );_cdc !=nil {return _cdc ;};if _cad :=_feed .SetPdfAID (_dbbc ._dda ,_dbbc ._ff );
_cad !=nil {return _cad ;};_dgd :=_bg .MediaManagementOptions {OriginalDocumentID :_gbg .OriginalDocumentID ,DocumentID :_gbg .DocumentID ,InstanceID :_gbg .InstanceID ,NewDocumentID :!_gbg .NewDocumentVersion ,ModifyComment :"O\u0070\u0074\u0069\u006d\u0069\u007ae\u0020\u0064\u006f\u0063\u0075\u006de\u006e\u0074\u0020\u0074\u006f\u0020\u0050D\u0046\u002f\u0041\u0020\u0073\u0074\u0061\u006e\u0064\u0061r\u0064"};
_gab ,_cgdg :=_eb .GetDict (_bdaga .Info );if _cgdg {if _bced ,_ggb :=_eb .GetString (_gab .Get ("\u004do\u0064\u0044\u0061\u0074\u0065"));_ggb &&_bced .String ()!=""{_cbfc ,_dee :=_be .ParsePdfTime (_bced .String ());if _dee !=nil {return _d .Errorf ("\u0069n\u0076\u0061\u006c\u0069d\u0020\u004d\u006f\u0064\u0044a\u0074e\u0020f\u0069\u0065\u006c\u0064\u003a\u0020\u0025w",_dee );
};_dgd .ModifyDate =_cbfc ;};};if _dcd :=_feed .SetMediaManagement (&_dgd );_dcd !=nil {return _dcd ;};if _acc :=_feed .SetPdfAExtension ();_acc !=nil {return _acc ;};_cdcc ,_dbf :=_feed .MarshalIndent (_gbg .MarshalPrefix ,_gbg .MarshalIndent );if _dbf !=nil {return _dbf ;
};if _fbg :=_cdea .SetMetadata (_cdcc );_fbg !=nil {return _fbg ;};return nil ;};func _gaeg (_gadg *_a .CompliancePdfReader )ViolatedRule {return _ddd };func _fadd (_ebee *_a .CompliancePdfReader )ViolatedRule {return _ddd };func _fgd ()standardType {return standardType {_dda :3,_ff :"\u0055"}};
var _ Profile =(*Profile3U )(nil );var _ Profile =(*Profile1B )(nil );type documentImages struct{_cf ,_eeb ,_ac bool ;_fc map[_eb .PdfObject ]struct{};_dfb []*imageInfo ;};func _bef (_eac []_eb .PdfObject )(*documentImages ,error ){_gea :=_eb .PdfObjectName ("\u0053u\u0062\u0074\u0079\u0070\u0065");
_cb :=make (map[*_eb .PdfObjectStream ]struct{});_bbb :=make (map[_eb .PdfObject ]struct{});var (_dg ,_bdb ,_fgc bool ;_cba []*imageInfo ;_ga error ;);for _ ,_egd :=range _eac {_cbd ,_bgfc :=_eb .GetStream (_egd );if !_bgfc {continue ;};if _ ,_ade :=_cb [_cbd ];
_ade {continue ;};_cb [_cbd ]=struct{}{};_dfg :=_cbd .PdfObjectDictionary .Get (_gea );_cgd ,_bgfc :=_eb .GetName (_dfg );if !_bgfc ||string (*_cgd )!="\u0049\u006d\u0061g\u0065"{continue ;};if _ffe :=_cbd .PdfObjectDictionary .Get ("\u0053\u004d\u0061s\u006b");
_ffe !=nil {_bbb [_ffe ]=struct{}{};};_edf :=imageInfo {BitsPerComponent :8,Stream :_cbd };_edf .ColorSpace ,_ga =_a .DetermineColorspaceNameFromPdfObject (_cbd .PdfObjectDictionary .Get ("\u0043\u006f\u006c\u006f\u0072\u0053\u0070\u0061\u0063\u0065"));
if _ga !=nil {return nil ,_ga ;};if _ada ,_cda :=_eb .GetIntVal (_cbd .PdfObjectDictionary .Get ("\u0042\u0069t\u0073\u0050\u0065r\u0043\u006f\u006d\u0070\u006f\u006e\u0065\u006e\u0074"));_cda {_edf .BitsPerComponent =_ada ;};if _cbdc ,_bfd :=_eb .GetIntVal (_cbd .PdfObjectDictionary .Get ("\u0057\u0069\u0064t\u0068"));
_bfd {_edf .Width =_cbdc ;};if _cgg ,_faf :=_eb .GetIntVal (_cbd .PdfObjectDictionary .Get ("\u0048\u0065\u0069\u0067\u0068\u0074"));_faf {_edf .Height =_cgg ;};switch _edf .ColorSpace {case "\u0044\u0065\u0076\u0069\u0063\u0065\u0047\u0072\u0061\u0079":_fgc =true ;
_edf .ColorComponents =1;case "\u0044e\u0076\u0069\u0063\u0065\u0052\u0047B":_dg =true ;_edf .ColorComponents =3;case "\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u004d\u0059\u004b":_bdb =true ;_edf .ColorComponents =4;default:_edf ._fga =true ;};_cba =append (_cba ,&_edf );
};if len (_bbb )> 0{if len (_bbb )==len (_cba ){_cba =nil ;}else {_dgb :=make ([]*imageInfo ,len (_cba )-len (_bbb ));var _agbg int ;for _ ,_gaa :=range _cba {if _ ,_baa :=_bbb [_gaa .Stream ];_baa {continue ;};_dgb [_agbg ]=_gaa ;_agbg ++;};_cba =_dgb ;
};};return &documentImages {_cf :_dg ,_eeb :_bdb ,_ac :_fgc ,_fc :_bbb ,_dfb :_cba },nil ;};func _fggg (_bdee *_gde .Document )error {_baag ,_aee :=_bdee .FindCatalog ();if !_aee {return _ce .New ("\u0063\u0061\u0074\u0061\u006c\u006f\u0067\u0020\u006e\u006f\u0074\u0020f\u006f\u0075\u006e\u0064");
};_ ,_aee =_eb .GetDict (_baag .Object .Get ("\u0041\u0041"));if !_aee {return nil ;};_baag .Object .Remove ("\u0041\u0041");return nil ;};func _fab (_aafb *_gde .Document ,_ddgf bool )error {_dcc ,_bbea :=_aafb .GetPages ();if !_bbea {return nil ;};for _ ,_dga :=range _dcc {_ccdd ,_gded :=_dga .GetContents ();
if !_gded {continue ;};var _fef *_a .PdfPageResources ;_cadd ,_gded :=_dga .GetResources ();if _gded {_fef ,_ =_a .NewPdfPageResourcesFromDict (_cadd );};for _eca ,_cdcd :=range _ccdd {_fbf ,_fdcca :=_cdcd .GetData ();if _fdcca !=nil {continue ;};_gafb :=_e .NewContentStreamParser (string (_fbf ));
_eea ,_fdcca :=_gafb .Parse ();if _fdcca !=nil {continue ;};_bbad ,_fdcca :=_gbaa (_fef ,_eea ,_ddgf );if _fdcca !=nil {return _fdcca ;};if _bbad ==nil {continue ;};if _fdcca =(&_ccdd [_eca ]).SetData (_bbad );_fdcca !=nil {return _fdcca ;};};};return nil ;
};func _agaf (_bbgc *_a .CompliancePdfReader )ViolatedRule {_acgb ,_faaf :=_bbgc .PdfReader .GetTrailer ();if _faaf !=nil {return _ee ("\u0036.\u0031\u002e\u0033\u002d\u0031","\u006d\u0069\u0073s\u0069\u006e\u0067\u0020t\u0072\u0061\u0069\u006c\u0065\u0072\u0020i\u006e\u0020\u0074\u0068\u0065\u0020\u0064\u006f\u0063\u0075\u006d\u0065\u006e\u0074");
};if _acgb .Get ("\u0049\u0044")==nil {return _ee ("\u0036.\u0031\u002e\u0033\u002d\u0031","\u0054\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0074\u0072\u0061\u0069\u006c\u0065\u0072\u0020\u0064\u0069\u0063t\u0069\u006f\u006e\u0061\u0072\u0079\u0020\u0073\u0068a\u006c\u006c\u0020\u0063\u006f\u006e\u0074\u0061\u0069\u006e\u0020\u0074\u0068e\u0020\u0027\u0049\u0044\u0027\u0020k\u0065\u0079\u0077o\u0072\u0064");
};if _acgb .Get ("\u0045n\u0063\u0072\u0079\u0070\u0074")!=nil {return _ee ("\u0036.\u0031\u002e\u0033\u002d\u0032","\u0054\u0068\u0065\u0020\u006b\u0065y\u0077\u006f\u0072\u0064\u0020'\u0045\u006e\u0063\u0072\u0079\u0070t\u0027\u0020\u0073\u0068\u0061l\u006c\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0075\u0073\u0065d\u0020\u0069\u006e\u0020\u0074\u0068\u0065\u0020\u0074\u0072\u0061\u0069\u006c\u0065\u0072 \u0064\u0069\u0063\u0074\u0069\u006f\u006e\u0061r\u0079\u002e\u0020");
};return _ddd ;};func _cbdcf (_fce *_gde .Document )error {_befb ,_gcd :=_fce .FindCatalog ();if !_gcd {return nil ;};_ ,_gcd =_befb .GetStructTreeRoot ();if !_gcd {_fdfd :=_a .NewStructTreeRoot ();_bdbf :=_fdfd .ToPdfObject ().(*_eb .PdfIndirectObject );
_beb :=_bdbf .PdfObject .(*_eb .PdfObjectDictionary );_befb .SetStructTreeRoot (_beb );};return nil ;};

// VerificationError is the PDF/A verification error structure, that contains all violated rules.
type VerificationError struct{

// ViolatedRules are the rules that were violated during error verification.
ViolatedRules []ViolatedRule ;

// ConformanceLevel defines the standard on verification failed.
ConformanceLevel int ;

// ConformanceVariant is the standard variant used on verification.
ConformanceVariant string ;};func _ebgf (_dgabe *_a .CompliancePdfReader )ViolatedRule {_beab :=_dgabe .ParserMetadata ().HeaderCommentBytes ();if _beab [0]> 127&&_beab [1]> 127&&_beab [2]> 127&&_beab [3]> 127{return _ddd ;};return _ee ("\u0036.\u0031\u002e\u0032\u002d\u0032","\u0054\u0068\u0065\u0020\u0066\u0069\u006c\u0065\u0020\u0068\u0065\u0061\u0064\u0065\u0072\u0020\u006c\u0069\u006e\u0065\u0020\u0073\u0068\u0061\u006c\u006c b\u0065\u0020i\u006d\u006d\u0065\u0064\u0069a\u0074\u0065\u006c\u0079 \u0066\u006f\u006c\u006co\u0077\u0065\u0064\u0020\u0062\u0079\u0020\u0061\u0020\u0063\u006f\u006d\u006d\u0065n\u0074\u0020\u0063\u006f\u006e\u0073\u0069s\u0074\u0069\u006e\u0067\u0020o\u0066\u0020\u0061\u0020\u0025\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0020\u0066\u006f\u006c\u006c\u006fwe\u0064\u0020\u0062y\u0020a\u0074\u0009\u006c\u0065a\u0073\u0074\u0020f\u006f\u0075\u0072\u0020\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065r\u0073\u002c\u0020e\u0061\u0063\u0068\u0020\u006f\u0066\u0020\u0077\u0068\u006f\u0073\u0065 \u0065\u006e\u0063\u006f\u0064e\u0064\u0020\u0062\u0079\u0074e\u0020\u0076\u0061\u006c\u0075\u0065s\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0068\u0061\u0076\u0065\u0020\u0061\u0020\u0064e\u0063\u0069\u006d\u0061\u006c \u0076\u0061\u006c\u0075\u0065\u0020\u0067\u0072\u0065\u0061\u0074\u0065\u0072\u0020\u0074\u0068\u0061\u006e\u0020\u0031\u0032\u0037\u002e");
};

// Profile3Options are the options that changes the way how optimizer may try to adapt document into PDF/A standard.
type Profile3Options struct{

// CMYKDefaultColorSpace is an option that refers PDF/A
CMYKDefaultColorSpace bool ;

// Now is a function that returns current time.
Now func ()_ca .Time ;

// Xmp is the xmp options information.
Xmp XmpOptions ;};func _aedf (_gdcbf *_a .CompliancePdfReader )(_gdabd []ViolatedRule ){_cegc ,_gfge :=_efcc (_gdcbf );if !_gfge {return _gdabd ;};_dadbc ,_gfge :=_eb .GetDict (_cegc .Get ("\u004e\u0061\u006de\u0073"));if !_gfge {return _gdabd ;};if _dadbc .Get ("\u0041\u006c\u0074\u0065rn\u0061\u0074\u0065\u0050\u0072\u0065\u0073\u0065\u006e\u0074\u0061\u0074\u0069\u006fn\u0073")!=nil {_gdabd =append (_gdabd ,_ee ("\u0036\u002e\u0031\u0030\u002d\u0031","T\u0068\u0065\u0072e\u0020\u0073\u0068\u0061\u006c\u006c\u0020\u0062\u0065\u0020\u006e\u006f\u0020\u0041\u006c\u0074\u0065\u0072\u006e\u0061\u0074\u0065\u0050\u0072\u0065s\u0065\u006e\u0074a\u0074\u0069\u006f\u006e\u0073\u0020\u0065\u006e\u0074\u0072\u0079\u0020\u0069n\u0020\u0074\u0068\u0065 \u0064\u006f\u0063\u0075m\u0065\u006e\u0074\u0027\u0073\u0020\u006e\u0061\u006d\u0065\u0020\u0064\u0069\u0063\u0074\u0069\u006fn\u0061\u0072\u0079\u002e"));
};return _gdabd ;};type profile2 struct{_fdecd standardType ;_efc Profile2Options ;};