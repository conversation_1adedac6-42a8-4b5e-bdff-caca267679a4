//
// Copyright 2020 FoxyUtils ehf. All rights reserved.
//
// This is a commercial product and requires a license to operate.
// A trial license can be obtained at https://unidoc.io
//
// DO NOT EDIT: generated by unitwist Go source code obfuscator.
//
// Use of this source code is governed by the UniDoc End User License Agreement
// terms that can be accessed at https://unidoc.io/eula/

package sigutil ;import (_d "bytes";_a "crypto";_be "crypto/x509";_db "encoding/asn1";_bg "encoding/pem";_f "errors";_cb "fmt";_bf "github.com/unidoc/timestamp";_fee "github.com/unidoc/unipdf/v4/common";_cbd "golang.org/x/crypto/ocsp";_ab "io";_fe "net/http";
_b "time";);

// NewTimestampRequest returns a new timestamp request based
// on the specified options.
func NewTimestampRequest (body _ab .Reader ,opts *_bf .RequestOptions )(*_bf .Request ,error ){if opts ==nil {opts =&_bf .RequestOptions {};};if opts .Hash ==0{opts .Hash =_a .SHA256 ;};if !opts .Hash .Available (){return nil ,_be .ErrUnsupportedAlgorithm ;
};_dfc :=opts .Hash .New ();if _ ,_ce :=_ab .Copy (_dfc ,body );_ce !=nil {return nil ,_ce ;};return &_bf .Request {HashAlgorithm :opts .Hash ,HashedMessage :_dfc .Sum (nil ),Certificates :opts .Certificates ,TSAPolicyOID :opts .TSAPolicyOID ,Nonce :opts .Nonce },nil ;
};

// NewOCSPClient returns a new OCSP client.
func NewOCSPClient ()*OCSPClient {return &OCSPClient {HTTPClient :_gf (),Hash :_a .SHA1 }};

// IsCA returns true if the provided certificate appears to be a CA certificate.
func (_cd *CertClient )IsCA (cert *_be .Certificate )bool {return cert .IsCA &&_d .Equal (cert .RawIssuer ,cert .RawSubject );};

// NewCRLClient returns a new CRL client.
func NewCRLClient ()*CRLClient {return &CRLClient {HTTPClient :_gf ()}};

// NewCertClient returns a new certificate client.
func NewCertClient ()*CertClient {return &CertClient {HTTPClient :_gf ()}};

// TimestampClient represents a RFC 3161 timestamp client.
// It is used to obtain signed tokens from timestamp authority servers.
type TimestampClient struct{

// HTTPClient is the HTTP client used to make timestamp requests.
// By default, an HTTP client with a 5 second timeout per request is used.
HTTPClient *_fe .Client ;

// Callbacks.
BeforeHTTPRequest func (_bfb *_fe .Request )error ;};

// GetEncodedToken executes the timestamp request and returns the DER encoded
// timestamp token bytes.
func (_ag *TimestampClient )GetEncodedToken (serverURL string ,req *_bf .Request )([]byte ,error ){if serverURL ==""{return nil ,_cb .Errorf ("\u006d\u0075\u0073\u0074\u0020\u0070r\u006f\u0076\u0069\u0064\u0065\u0020\u0074\u0069\u006d\u0065\u0073\u0074\u0061m\u0070\u0020\u0073\u0065\u0072\u0076\u0065r\u0020\u0055\u0052\u004c");
};if req ==nil {return nil ,_cb .Errorf ("\u0074\u0069\u006de\u0073\u0074\u0061\u006dp\u0020\u0072\u0065\u0071\u0075\u0065\u0073t\u0020\u0063\u0061\u006e\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u006e\u0069\u006c");};_cc ,_acd :=req .Marshal ();if _acd !=nil {return nil ,_acd ;
};_aafb ,_acd :=_fe .NewRequest ("\u0050\u004f\u0053\u0054",serverURL ,_d .NewBuffer (_cc ));if _acd !=nil {return nil ,_acd ;};_aafb .Header .Set ("\u0043\u006f\u006et\u0065\u006e\u0074\u002d\u0054\u0079\u0070\u0065","a\u0070\u0070\u006c\u0069\u0063\u0061t\u0069\u006f\u006e\u002f\u0074\u0069\u006d\u0065\u0073t\u0061\u006d\u0070-\u0071u\u0065\u0072\u0079");
if _ag .BeforeHTTPRequest !=nil {if _agg :=_ag .BeforeHTTPRequest (_aafb );_agg !=nil {return nil ,_agg ;};};_agc :=_ag .HTTPClient ;if _agc ==nil {_agc =_gf ();};_bb ,_acd :=_agc .Do (_aafb );if _acd !=nil {return nil ,_acd ;};defer _bb .Body .Close ();
_bee ,_acd :=_ab .ReadAll (_bb .Body );if _acd !=nil {return nil ,_acd ;};if _bb .StatusCode !=_fe .StatusOK {return nil ,_cb .Errorf ("\u0075\u006e\u0065x\u0070\u0065\u0063\u0074e\u0064\u0020\u0048\u0054\u0054\u0050\u0020s\u0074\u0061\u0074\u0075\u0073\u0020\u0063\u006f\u0064\u0065\u003a\u0020\u0025\u0064",_bb .StatusCode );
};var _cbe struct{Version _db .RawValue ;Content _db .RawValue ;};if _ ,_acd =_db .Unmarshal (_bee ,&_cbe );_acd !=nil {return nil ,_acd ;};return _cbe .Content .FullBytes ,nil ;};

// OCSPClient represents a OCSP (Online Certificate Status Protocol) client.
// It is used to request revocation data from OCSP servers.
type OCSPClient struct{

// HTTPClient is the HTTP client used to make OCSP requests.
// By default, an HTTP client with a 5 second timeout per request is used.
HTTPClient *_fe .Client ;

// Hash is the hash function  used when constructing the OCSP
// requests. If zero, SHA-1 will be used.
Hash _a .Hash ;};

// MakeRequest makes a OCSP request to the specified server and returns
// the parsed and raw responses. If a server URL is not provided, it is
// extracted from the certificate.
func (_dfe *OCSPClient )MakeRequest (serverURL string ,cert ,issuer *_be .Certificate )(*_cbd .Response ,[]byte ,error ){if _dfe .HTTPClient ==nil {_dfe .HTTPClient =_gf ();};if serverURL ==""{if len (cert .OCSPServer )==0{return nil ,nil ,_f .New ("\u0063e\u0072\u0074i\u0066\u0069\u0063a\u0074\u0065\u0020\u0064\u006f\u0065\u0073 \u006e\u006f\u0074\u0020\u0073\u0070e\u0063\u0069\u0066\u0079\u0020\u0061\u006e\u0079\u0020\u004f\u0043S\u0050\u0020\u0073\u0065\u0072\u0076\u0065\u0072\u0073");
};serverURL =cert .OCSPServer [0];};_aag ,_fa :=_cbd .CreateRequest (cert ,issuer ,&_cbd .RequestOptions {Hash :_dfe .Hash });if _fa !=nil {return nil ,nil ,_fa ;};_fb ,_fa :=_dfe .HTTPClient .Post (serverURL ,"\u0061p\u0070\u006c\u0069\u0063\u0061\u0074\u0069\u006f\u006e\u002f\u006fc\u0073\u0070\u002d\u0072\u0065\u0071\u0075\u0065\u0073\u0074",_d .NewReader (_aag ));
if _fa !=nil {return nil ,nil ,_fa ;};defer _fb .Body .Close ();_eg ,_fa :=_ab .ReadAll (_fb .Body );if _fa !=nil {return nil ,nil ,_fa ;};if _eb ,_ :=_bg .Decode (_eg );_eb !=nil {_eg =_eb .Bytes ;};_abb ,_fa :=_cbd .ParseResponseForCert (_eg ,cert ,issuer );
if _fa !=nil {return nil ,nil ,_fa ;};return _abb ,_eg ,nil ;};

// MakeRequest makes a CRL request to the specified server and returns the
// response. If a server URL is not provided, it is extracted from the certificate.
func (_dba *CRLClient )MakeRequest (serverURL string ,cert *_be .Certificate )([]byte ,error ){if _dba .HTTPClient ==nil {_dba .HTTPClient =_gf ();};if serverURL ==""{if len (cert .CRLDistributionPoints )==0{return nil ,_f .New ("\u0063e\u0072\u0074i\u0066\u0069\u0063\u0061t\u0065\u0020\u0064o\u0065\u0073\u0020\u006e\u006f\u0074\u0020\u0073\u0070ec\u0069\u0066\u0079 \u0061\u006ey\u0020\u0043\u0052\u004c\u0020\u0073e\u0072\u0076e\u0072\u0073");
};serverURL =cert .CRLDistributionPoints [0];};_cda ,_bge :=_dba .HTTPClient .Get (serverURL );if _bge !=nil {return nil ,_bge ;};defer _cda .Body .Close ();_fd ,_bge :=_ab .ReadAll (_cda .Body );if _bge !=nil {return nil ,_bge ;};if _aaf ,_ :=_bg .Decode (_fd );
_aaf !=nil {_fd =_aaf .Bytes ;};return _fd ,nil ;};

// CertClient represents a X.509 certificate client. Its primary purpose
// is to download certificates.
type CertClient struct{

// HTTPClient is the HTTP client used to make certificate requests.
// By default, an HTTP client with a 5 second timeout per request is used.
HTTPClient *_fe .Client ;};

// Get retrieves the certificate at the specified URL.
func (_ae *CertClient )Get (url string )(*_be .Certificate ,error ){if _ae .HTTPClient ==nil {_ae .HTTPClient =_gf ();};_bea ,_bgb :=_ae .HTTPClient .Get (url );if _bgb !=nil {return nil ,_bgb ;};defer _bea .Body .Close ();_aa ,_bgb :=_ab .ReadAll (_bea .Body );
if _bgb !=nil {return nil ,_bgb ;};if _ac ,_ :=_bg .Decode (_aa );_ac !=nil {_aa =_ac .Bytes ;};_df ,_bgb :=_be .ParseCertificate (_aa );if _bgb !=nil {return nil ,_bgb ;};return _df ,nil ;};

// CRLClient represents a CRL (Certificate revocation list) client.
// It is used to request revocation data from CRL servers.
type CRLClient struct{

// HTTPClient is the HTTP client used to make CRL requests.
// By default, an HTTP client with a 5 second timeout per request is used.
HTTPClient *_fe .Client ;};

// GetIssuer retrieves the issuer of the provided certificate.
func (_e *CertClient )GetIssuer (cert *_be .Certificate )(*_be .Certificate ,error ){for _ ,_cbc :=range cert .IssuingCertificateURL {_g ,_ba :=_e .Get (_cbc );if _ba !=nil {_fee .Log .Debug ("\u0057\u0041\u0052\u004e\u003a\u0020\u0063\u006f\u0075\u006c\u0064\u0020\u006e\u006f\u0074 \u0064\u006f\u0077\u006e\u006c\u006f\u0061\u0064\u0020\u0069\u0073\u0073\u0075e\u0072\u0020\u0066\u006f\u0072\u0020\u0063\u0065\u0072\u0074\u0069\u0066ic\u0061\u0074\u0065\u0020\u0025\u0076\u003a\u0020\u0025\u0076",cert .Subject .CommonName ,_ba );
continue ;};return _g ,nil ;};return nil ,_cb .Errorf ("\u0069\u0073\u0073\u0075e\u0072\u0020\u0063\u0065\u0072\u0074\u0069\u0066\u0069\u0063a\u0074e\u0020\u006e\u006f\u0074\u0020\u0066\u006fu\u006e\u0064");};

// NewTimestampClient returns a new timestamp client.
func NewTimestampClient ()*TimestampClient {return &TimestampClient {HTTPClient :_gf ()}};func _gf ()*_fe .Client {return &_fe .Client {Timeout :5*_b .Second }};