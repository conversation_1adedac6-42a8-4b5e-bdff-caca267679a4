module github.com/unidoc/unipdf/v4

go 1.20

require (
	github.com/adrg/sysfont v0.1.2
	github.com/boombuler/barcode v1.0.2
	github.com/gabriel-vasile/mimetype v1.4.8
	github.com/gorilla/i18n v0.0.0-20150820051429-8b358169da46
	github.com/stretchr/testify v1.10.0
	github.com/trimmer-io/go-xmp v1.0.0
	github.com/unidoc/freetype v0.2.3
	github.com/unidoc/garabic v0.0.0-20220702200334-8c7cb25baa11
	github.com/unidoc/pkcs7 v0.2.0
	github.com/unidoc/timestamp v0.0.0-20200412005513-91597fd3793a
	github.com/unidoc/unichart v0.4.0
	github.com/unidoc/unitype v0.5.1
	golang.org/x/crypto v0.33.0
	golang.org/x/image v0.24.0
	golang.org/x/net v0.35.0
	golang.org/x/text v0.22.0
	golang.org/x/xerrors v0.0.0-20240903120638-7835f813f4da
)

require (
	github.com/adrg/strutil v0.3.1 // indirect
	github.com/adrg/xdg v0.5.3 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	golang.org/x/sys v0.30.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	software.sslmate.com/src/go-pkcs12 v0.5.0 // indirect
)
