package main

import (
	"fmt"
	"os"

	"github.com/unidoc/unipdf/v4/creator"
	"github.com/unidoc/unipdf/v4/extractor"
	"github.com/unidoc/unipdf/v4/model"
)

func main() {
	fmt.Println("Testing UniPDF License-Free Version...")

	// Test 1: Create a simple PDF
	fmt.Println("1. Creating a simple PDF...")
	err := createSimplePDF()
	if err != nil {
		fmt.Printf("Error creating PDF: %v\n", err)
		return
	}
	fmt.Println("✅ PDF created successfully!")

	// Test 2: Read the created PDF
	fmt.Println("2. Reading the created PDF...")
	err = readPDF("test_output.pdf")
	if err != nil {
		fmt.Printf("Error reading PDF: %v\n", err)
		return
	}
	fmt.Println("✅ PDF read successfully!")

	// Test 3: Extract text from PDF
	fmt.Println("3. Extracting text from PDF...")
	err = extractTextFromPDF("test_output.pdf")
	if err != nil {
		fmt.Printf("Error extracting text: %v\n", err)
		return
	}
	fmt.Println("✅ Text extracted successfully!")

	fmt.Println("\n🎉 All tests passed! UniPDF license-free version is working correctly.")
	fmt.Println("📄 Output file: test_output.pdf")
}

func createSimplePDF() error {
	// Create a new PDF creator
	c := creator.New()
	c.NewPage()

	// Add title
	title := c.NewParagraph("UniPDF License-Free Test")
	title.SetFontSize(20)
	title.SetColor(creator.ColorRGBFromHex("#2E86AB"))
	c.Draw(title)

	// Add content
	content := c.NewParagraph("This PDF was created using the license-free version of UniPDF!")
	content.SetFontSize(12)
	content.SetMargins(0, 0, 20, 0)
	c.Draw(content)

	// Add features list
	features := c.NewParagraph(`
Features working without license:
• PDF creation and editing
• Text extraction
• Image processing
• Form handling
• Security operations
• Page manipulation
• No watermarks
• No usage tracking
• Completely offline operation`)
	features.SetFontSize(10)
	features.SetMargins(0, 0, 20, 0)
	c.Draw(features)

	// Add footer
	footer := c.NewParagraph("Generated on: " + "2024")
	footer.SetFontSize(8)
	footer.SetColor(creator.ColorRGBFromHex("#666666"))
	c.Draw(footer)

	// Save to file
	return c.WriteToFile("test_output.pdf")
}

func readPDF(filename string) error {
	// Open the PDF file
	f, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer f.Close()

	// Create PDF reader
	reader, err := model.NewPdfReader(f)
	if err != nil {
		return fmt.Errorf("failed to create PDF reader: %v", err)
	}

	// Get basic information
	numPages, err := reader.GetNumPages()
	if err != nil {
		return fmt.Errorf("failed to get number of pages: %v", err)
	}

	isEncrypted, err := reader.IsEncrypted()
	if err != nil {
		return fmt.Errorf("failed to check encryption: %v", err)
	}

	fmt.Printf("   📊 Number of pages: %d\n", numPages)
	fmt.Printf("   🔒 Is encrypted: %v\n", isEncrypted)

	return nil
}

func extractTextFromPDF(filename string) error {
	// Open the PDF file
	f, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer f.Close()

	// Create PDF reader
	reader, err := model.NewPdfReader(f)
	if err != nil {
		return fmt.Errorf("failed to create PDF reader: %v", err)
	}

	// Get the first page
	page, err := reader.GetPage(1)
	if err != nil {
		return fmt.Errorf("failed to get page: %v", err)
	}

	// Create extractor
	ex, err := extractor.New(page)
	if err != nil {
		return fmt.Errorf("failed to create extractor: %v", err)
	}

	// Extract text
	text, err := ex.ExtractText()
	if err != nil {
		return fmt.Errorf("failed to extract text: %v", err)
	}

	fmt.Printf("   📝 Extracted text preview: %.100s...\n", text)

	return nil
}
