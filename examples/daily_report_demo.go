package main

import (
	"fmt"
	"lens/internal/config"
	"lens/internal/service"
	"log"
	"time"
)

// DemoConfig 演示配置
type DemoConfig struct {
	*config.Config
}

func main() {
	fmt.Println("=== 每日广告花费报告功能演示 ===")

	// 创建演示配置
	cfg := &config.Config{
		WBHubApiKey:         "your-wb-hub-api-key",
		DingTalkAccessToken: "your-dingtalk-access-token", 
		DingTalkSecret:      "your-dingtalk-secret",
		MonitorInterval:     5 * time.Minute,
	}

	// 创建广播通道
	broadcast := make(chan []byte, 100)

	// 创建产品监控服务
	fmt.Println("1. 创建产品监控服务...")
	monitorService := service.NewProductMonitorService(5*time.Minute, broadcast, cfg)

	// 启动每日报告服务
	fmt.Println("2. 启动每日报告服务...")
	monitorService.StartDailyReportService()

	// 演示时区计算
	fmt.Println("\n3. 时区计算演示:")
	demonstrateTimeZoneCalculation()

	// 演示手动触发报告
	fmt.Println("\n4. 手动触发报告演示...")
	fmt.Println("注意：这将尝试调用真实的API，请确保配置正确")
	
	// 等待用户确认
	fmt.Print("是否继续执行手动触发？(y/N): ")
	var input string
	fmt.Scanln(&input)
	
	if input == "y" || input == "Y" {
		fmt.Println("触发每日报告...")
		monitorService.TriggerDailyReport()
		fmt.Println("报告已触发，请检查钉钉群消息")
	} else {
		fmt.Println("跳过手动触发")
	}

	// 停止服务
	fmt.Println("\n5. 停止服务...")
	monitorService.StopDailyReportService()

	fmt.Println("\n=== 演示完成 ===")
}

// demonstrateTimeZoneCalculation 演示时区计算
func demonstrateTimeZoneCalculation() {
	// 加载时区
	chinaLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Printf("加载中国时区失败: %v", err)
		return
	}

	moscowLocation, err := time.LoadLocation("Europe/Moscow")
	if err != nil {
		log.Printf("加载莫斯科时区失败: %v", err)
		return
	}

	// 模拟中国时间早上8点
	chinaTime := time.Date(2024, 1, 15, 8, 0, 0, 0, chinaLocation)
	fmt.Printf("  中国时间触发时间: %s\n", chinaTime.Format("2006-01-02 15:04:05 MST"))

	// 转换为莫斯科时间
	moscowTime := chinaTime.In(moscowLocation)
	fmt.Printf("  对应莫斯科时间: %s\n", moscowTime.Format("2006-01-02 15:04:05 MST"))

	// 计算莫斯科时间的昨天
	yesterday := moscowTime.AddDate(0, 0, -1)
	dateFrom := yesterday.Format("2006-01-02")
	fmt.Printf("  统计日期范围: %s (莫斯科时间昨天)\n", dateFrom)

	// 显示时差
	timeDiff := chinaTime.Sub(moscowTime)
	fmt.Printf("  时差: 中国时间比莫斯科时间快 %v\n", timeDiff)
}
