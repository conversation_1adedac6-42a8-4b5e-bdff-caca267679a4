# 字体文件说明

## 概述

此目录用于存放支持俄语（西里尔字母）的字体文件，以确保PDF中的俄语文本能够正确显示。

## 推荐字体

### 1. DejaVu Sans (推荐)
- **文件名**: `DejaVuSans.ttf`
- **特点**: 开源字体，完全支持俄语和其他Unicode字符
- **下载地址**: https://dejavu-fonts.github.io/
- **许可证**: 免费使用

### 2. Arial Unicode MS
- **文件名**: `arial.ttf` 或 `ArialUnicodeMS.ttf`
- **特点**: 微软字体，广泛支持Unicode字符
- **来源**: Windows系统字体或Microsoft Office

### 3. Liberation Sans
- **文件名**: `LiberationSans-Regular.ttf`
- **特点**: 开源字体，Red Hat开发
- **下载地址**: https://github.com/liberationfonts/liberation-fonts

## 安装说明

1. 下载上述任一字体文件
2. 将字体文件复制到此 `fonts/` 目录
3. 确保文件名与代码中的路径匹配

## 字体加载优先级

代码会按以下顺序尝试加载字体：

1. **系统字体路径**:
   - macOS: `/System/Library/Fonts/Arial Unicode.ttf`
   - Linux: `/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf`
   - Windows: `/Windows/Fonts/arial.ttf`

2. **项目本地字体**:
   - `./fonts/DejaVuSans.ttf`
   - `./fonts/arial.ttf`

3. **回退字体**:
   - Times-Roman (标准字体)
   - Helvetica (最后选择)

## 测试俄语字符

以下是一些测试用的俄语文本：
- Паллета 1
- Паллета 2
- ID ГРУЗОВОГО МЕСТА
- Русский текст

## 注意事项

1. **字体文件大小**: TrueType字体文件通常较大（几MB），请确保有足够的存储空间
2. **许可证**: 使用商业字体时请确保遵守相应的许可证条款
3. **性能**: 加载外部字体可能会稍微影响PDF生成速度
4. **兼容性**: 确保字体文件与unipdf v4.2.0兼容

## 故障排除

如果俄语文本仍然显示为方块或问号：

1. 检查字体文件是否存在于正确路径
2. 验证字体文件是否损坏
3. 确认字体确实支持西里尔字母
4. 查看控制台输出的字体加载错误信息

## 快速设置 (推荐)

```bash
# 下载 DejaVu Sans 字体 (Linux/macOS)
wget https://github.com/dejavu-fonts/dejavu-fonts/releases/download/version_2_37/dejavu-fonts-ttf-2.37.tar.bz2
tar -xjf dejavu-fonts-ttf-2.37.tar.bz2
cp dejavu-fonts-ttf-2.37/ttf/DejaVuSans.ttf ./fonts/

# 或者从系统复制 (如果存在)
cp /usr/share/fonts/truetype/dejavu/DejaVuSans.ttf ./fonts/  # Linux
cp /System/Library/Fonts/Helvetica.ttc ./fonts/             # macOS
```
