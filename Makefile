# Makefile for lens Project

# Service names
SERVICES = crawler-keyword monitor-product advert-manager warehouse-service

# Default target
.PHONY: all
all: build

# Build all services
.PHONY: build
build:
	@echo "Building all services..."
	@for service in $(SERVICES); do \
		echo "Building $$service..."; \
		go build -o bin/$$service cmd/$$service/main.go; \
	done
	@echo "All services built successfully!"

# Run a specific service
.PHONY: run-%
run-%:
	@echo "Running $*..."
	@go run cmd/$*/main.go

# Build a specific service
.PHONY: build-%
build-%:
	@echo "Building $*..."
	@go build -o bin/$* cmd/$*/main.go
	@echo "$* built successfully!"

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf bin/
	@echo "Clean completed!"

# Install dependencies
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	@go mod tidy
	@echo "Dependencies installed!"

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	@go test ./...

# Help
.PHONY: help
help:
	@echo "lens Project Makefile"
	@echo ""
	@echo "Available targets:"
	@echo "  all              - Build all services (default)"
	@echo "  build            - Build all services"
	@echo "  run-%            - Run a specific service (e.g., make run-monitor-product)"
	@echo "  build-%          - Build a specific service (e.g., make build-crawler-keyword)"
	@echo "  clean            - Clean build artifacts"
	@echo "  deps             - Install dependencies"
	@echo "  test             - Run tests"
	@echo "  help             - Show this help message"
	@echo ""
	@echo "Note: Each service loads its configuration from configs/{service-name}.env"
	@echo "Make sure to create the appropriate config files before running services."